module.exports = (sequelize, DataTypes) => {
  const Notification = sequelize.define(
    'Notification',
    {
      ProjectId: DataTypes.INTEGER,
      MemberId: DataTypes.INTEGER,
      DeliveryRequestId: DataTypes.INTEGER,
      ConcreteRequestId: DataTypes.INTEGER,
      requestType: DataTypes.STRING,
      isDeleted: DataTypes.BOOLEAN,
      seen: DataTypes.BOOLEAN,
      description: DataTypes.STRING,
      CraneRequestId: DataTypes.INTEGER,
      isDeliveryRequest: DataTypes.BOOLEAN,
      publicSchemaId: {
        type: DataTypes.INTEGER,
      },
      title: DataTypes.STRING,
      type: DataTypes.STRING,
      recurrenceType: DataTypes.STRING,
      LocationId: DataTypes.INTEGER,
      locationFollowDescription: DataTypes.TEXT,
    },
    {},
  );
  Notification.associate = (models) => {
    // associations can be defined here
    Notification.belongsTo(models.Project);
    Notification.belongsTo(models.DeliveryRequest);
    Notification.belongsTo(models.CraneRequest);
    Notification.belongsTo(models.ConcreteRequest);
    Notification.hasMany(models.DeliveryPersonNotification, {
      as: 'DeliveryNotification',
      foreignKey: 'NotificationId',
    });
  };
  Notification.createInstance = async (paramData) => {
    const newNotification = await Notification.create(paramData);
    return newNotification;
  };
  Notification.getAll = async (attr, searchCondition, inputData, subCondition) => {
    const notification = await Notification.findAndCountAll({
      subQuery: false,
      distinct: true,
      include: [
        {
          association: 'Project',
          attributes: ['projectName', 'id'],
          include: [
            {
              association: 'memberDetails',
              attributes: ['id'],
              where: { UserId: inputData.id, isDeleted: false, isActive: true },
              required: true,
            },
          ],
          required: true,
        },
        {
          association: 'DeliveryRequest',
          attributes: ['description', 'DeliveryId', 'id'],
          include: [
            {
              association: 'memberDetails',
              where: subCondition,
              attributes: ['id'],
              required: true,
            },
          ],
          required: false,
        },
        {
          association: 'CraneRequest',
          attributes: ['description', 'CraneRequestId', 'id'],
          include: [
            {
              association: 'memberDetails',
              where: subCondition,
              attributes: ['id'],
              required: true,
            },
          ],
          required: false,
        },
        {
          association: 'ConcreteRequest',
          attributes: ['description', 'ConcreteRequestId', 'id'],
          include: [
            {
              association: 'memberDetails',
              where: subCondition,
              attributes: ['id'],
              required: true,
            },
          ],
          required: false,
        },
        {
          association: 'DeliveryNotification',
          attributes: ['id', 'seen', 'isLocationFollowNotification'],
          include: [
            {
              association: 'Member',
              attributes: ['id'],
              where: { UserId: inputData.id },
              required: true,
            },
          ],
          required: true,
        },
      ],
      where: { ...attr, ...searchCondition },
      order: [['id', 'DESC']],
    });
    return notification;
  };
  Notification.getUnSeenCount = async (attr, inputData, subCondition) => {
    const notification = await Notification.findAll({
      subQuery: false,
      distinct: true,
      include: [
        {
          association: 'Project',
          attributes: ['projectName', 'id'],
          include: [
            {
              association: 'memberDetails',
              attributes: ['id'],
              where: { UserId: inputData.id, isDeleted: false, isActive: true },
              required: true,
            },
          ],
          required: true,
        },
        {
          association: 'DeliveryRequest',
          attributes: ['description', 'DeliveryId', 'id'],
          include: [
            {
              association: 'memberDetails',
              where: subCondition,
              attributes: ['id'],
              required: true,
            },
          ],
          required: false,
        },
        {
          association: 'CraneRequest',
          attributes: ['description', 'CraneRequestId', 'id'],
          include: [
            {
              association: 'memberDetails',
              where: subCondition,
              attributes: ['id'],
              required: true,
            },
          ],
          required: false,
        },
        {
          association: 'ConcreteRequest',
          attributes: ['description', 'ConcreteRequestId', 'id'],
          include: [
            {
              association: 'memberDetails',
              where: subCondition,
              attributes: ['id'],
              required: true,
            },
          ],
          required: false,
        },
      ],
      where: { ...attr },
    });
    return notification;
  };
  return Notification;
};
