module.exports = (sequelize, DataTypes) => {
  const Plan = sequelize.define(
    'Plan',
    {
      planType: DataTypes.STRING,
      isDeleted: DataTypes.BOOLEAN,
      features: DataTypes.TEXT,
    },
    {},
  );
  Plan.associate = (models) => {
    Plan.hasMany(models.StripePlan);
  };
  Plan.createInstance = async (paramData) => {
    const newPlan = await Plan.create(paramData);
    return newPlan;
  };
  Plan.getBy = async (attr) => {
    const plan = await Plan.findOne({ where: { ...attr } });
    return plan;
  };
  Plan.getAllPlanList = async () => {
    return Plan.findAll({
      where: { isDeleted: false },
      attributes: ['id', 'planType', 'features'],
      include: [
        {
          required: false,
          duplicating: false,
          association: 'StripePlans',
          attributes: ['id', 'stripeAmount', 'stripeCurrency', 'stripePlanName'],
        },
      ],
      group: ['Plan.id', 'StripePlans.id'],
      order: [['id', 'DESC']],
    });
  };
  Plan.updatePlan = async (id, attributes) => {
    const updatedPlan = await Plan.update(attributes, { where: { id } });
    return updatedPlan;
  };
  return Plan;
};
