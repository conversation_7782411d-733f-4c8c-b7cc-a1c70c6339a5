const { Router } = require('express');
const { validate } = require('express-validation');
const { deviceTokenValidation } = require('../middlewares/validations');
const { DeviceTokenController } = require('../controllers');
const passportConfig = require('../config/passport');

const deviceRoute = {
  get router() {
    const router = Router();
    router.post(
      '/set_device_token',
      validate(deviceTokenValidation.setDeviceToken, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      DeviceTokenController.setDeviceToken,
    );
    router.get(
      '/clear_device_token',
      passportConfig.isAuthenticated,
      DeviceTokenController.clearDeviceToken,
    );

    return router;
  },
};
module.exports = deviceRoute;
