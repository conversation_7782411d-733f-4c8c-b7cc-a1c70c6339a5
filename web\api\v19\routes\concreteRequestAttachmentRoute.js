const { Router } = require('express');
const { validate } = require('express-validation');
const multer = require('multer');
const passportConfig = require('../config/passport');
const { concreteRequestAttachmentController } = require('../controllers');
const { concreteRequestAttachmentValidation } = require('../middlewares/validations');

const storage = multer.memoryStorage();
const upload = multer({ storage });

const concreteRequestAttachmentRoute = {
  get router() {
    const router = Router();
    router.post(
      '/add_concrete_request_attachment/:ConcreteRequestId/?:ParentCompanyId/:ProjectId',
      upload.array('attachment', 12),
      validate(
        concreteRequestAttachmentValidation.createConcreteRequestAttachment,
        { keyByField: true },
        { abortEarly: false },
      ),
      passportConfig.isAuthenticated,
      concreteRequestAttachmentController.createConcreteRequestAttachment,
    );
    router.get(
      '/remove_concrete_request_attachment/:id/?:ParentCompanyId/:ProjectId',
      validate(
        concreteRequestAttachmentValidation.deleteConcreteRequestAttachment,
        { keyByField: true },
        { abortEarly: false },
      ),
      passportConfig.isAuthenticated,
      concreteRequestAttachmentController.deleteConcreteRequestAttachment,
    );
    router.get(
      '/get_concrete_request_attachments/:ConcreteRequestId/?:ParentCompanyId/:ProjectId',
      validate(
        concreteRequestAttachmentValidation.getConcreteRequestAttachment,
        { keyByField: true },
        { abortEarly: false },
      ),
      passportConfig.isAuthenticated,
      concreteRequestAttachmentController.getConcreteRequestAttachments,
    );

    return router;
  },
};
module.exports = concreteRequestAttachmentRoute;
