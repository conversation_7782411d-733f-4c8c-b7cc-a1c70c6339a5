/* eslint-disable no-restricted-syntax */
/* eslint-disable no-loop-func */
/* eslint-disable no-await-in-loop */
/* eslint-disable no-new */
const moment = require('moment');
const Moment = require('moment');
const momenttz = require('moment-timezone');
const MomentRange = require('moment-range');
const httpStatus = require('http-status');

const momentRange = MomentRange.extendMoment(Moment);
const ExcelJS = require('exceljs');
const { Worker } = require('worker_threads');
const { stringify } = require('flatted');
const path = require('path');
const Cryptr = require('cryptr');
// const mixpanelService = require('./mixpanelService');
const MAILER = require('../mailer');

const bulkNdrProcess = path.join(__dirname, './bulkNdrProcess.js');
const ApiError = require('../helpers/apiError');
const {
    Sequelize,
    Enterprise,
    NotificationPreference,
    NotificationPreferenceItem,
    DigestNotification,
    TimeZone,
    CraneRequest,
    ProjectSettings,
    RequestRecurrenceSeries,
    CalendarSetting,
    LocationNotificationPreferences,
    Locations,
} = require('../models');
let {
    InspectionRequest,
    Member,
    InspectionPerson,
    InspectionGate,
    InspectionEquipment,
    InspectionCompany,
    Role,
    Gates,
    Equipments,
    DeliverDefineWork,
    Company,
    Project,
    DeliverDefine,
    InspectionHistory,
    DeliveryPersonNotification,
    VoidList,
    User,
    Notification
} = require('../models');
const helper = require('../helpers/domainHelper');
const notificationHelper = require('../helpers/notificationHelper');
const pushNotification = require('../config/fcm');
const calendarSettingsService = require('./calendarSettingsService');
const voidService = require('./voidService');
const concreteRequestService = require('./concreteRequestService');

let publicUser;
let publicMember;
const { Op } = Sequelize;

const inspectionService = {
    async newRequest(inputData, done) {
        try {
            await this.getDynamicModel(inputData);
            const eventTimeZone = await TimeZone.findOne({
                where: {
                    isDeleted: false,
                    id: +inputData.body.TimeZoneId,
                },
                attributes: [
                    'id',
                    'location',
                    'isDayLightSavingEnabled',
                    'timeZoneOffsetInMinutes',
                    'dayLightSavingTimeInMinutes',
                    'timezone',
                ],
            });
            if (!eventTimeZone) {
                return done(null, { message: 'Provide a valid timezone' });
            }
            const inspectionData = inputData.body;
            const loginUser = inputData.user;
            const projectDetails = await Project.getProjectAndSettings({
                isDeleted: false,
                id: +inspectionData.ProjectId,
            });
            let startDate;
            let endDate;
            if (inspectionData.recurrence) {
                startDate = await this.compareinspectionDateWithinspectionWindowDate(
                    inspectionData.inspectionStart,
                    inspectionData.startPicker,
                    eventTimeZone.timezone,
                    projectDetails.ProjectSettings.inspectionWindowTime,
                    projectDetails.ProjectSettings.inspectionWindowTimeUnit,
                );
                endDate = await this.compareinspectionDateWithinspectionWindowDate(
                    inspectionData.inspectionEnd,
                    inspectionData.endPicker,
                    eventTimeZone.timezone,
                    projectDetails.ProjectSettings.inspectionWindowTime,
                    projectDetails.ProjectSettings.inspectionWindowTimeUnit,
                );
            }
            if (inspectionData.startPicker === inspectionData.endPicker) {
                return done(null, { message: 'Inspection Start time and End time should not be the same' });
            }
            if (inspectionData.startPicker > inspectionData.endPicker) {
                return done(null, { message: 'Please enter From Time lesser than To Time' });
            }
            if (startDate || endDate) {
                if (projectDetails.ProjectSettings.inspectionWindowTime === 0) {
                    if (inspectionData.recurrence === 'Does Not Repeat') {
                        return done(null, { message: 'Please enter Future Date/Time' });
                    }
                    return done(null, { message: 'Please enter Future Start or End Date/Time' });
                }
                return done(null, {
                    message: `Bookings can not be submitted within ${projectDetails.ProjectSettings.inspectionWindowTime} ${projectDetails.ProjectSettings.inspectionWindowTimeUnit} prior to the event`,
                });
            }

            if (projectDetails && projectDetails.ProjectSettings) {
                this.checkInputDatas(inputData, async (checkResponse, checkError) => {
                    if (checkError) {
                        return done(null, checkError);
                    }
                    const memberDetails = await Member.getBy({
                        UserId: loginUser.id,
                        ProjectId: inspectionData.ProjectId,
                        isActive: true,
                        isDeleted: false,
                    });
                    if (memberDetails) {
                        const range = momentRange.range(
                            moment(inspectionData.inspectionStart),
                            moment(inspectionData.inspectionEnd),
                        );
                        let totalDays = Array.from(range.by('day'));
                        const eventsArray = [];
                        let InspectionParam = {};
                        const lastIdValue = await InspectionRequest.findOne({
                            where: { ProjectId: memberDetails.ProjectId, isDeleted: false },
                            order: [['InspectionId', 'DESC']],
                        });
                        let id = 0;
                        const newValue = JSON.parse(JSON.stringify(lastIdValue));
                        if (newValue && newValue.InspectionId !== null && newValue.InspectionId !== undefined) {
                            id = newValue.InspectionId;
                        }
                        let lastData = {};
                        lastData = await CraneRequest.findOne({
                            where: { ProjectId: +memberDetails.ProjectId, isDeleted: false },
                            order: [['CraneRequestId', 'DESC']],
                        });
                        const InspectionRequestList = await InspectionRequest.findOne({
                            where: {
                                ProjectId: +memberDetails.ProjectId,
                                isDeleted: false,
                                isAssociatedWithCraneRequest: true,
                            },
                            order: [['CraneRequestId', 'DESC']],
                        });
                        if (InspectionRequestList) {
                            if (lastData) {
                                if (InspectionRequestList.CraneRequestId > lastData.CraneRequestId) {
                                    lastData.CraneRequestId = InspectionRequestList.CraneRequestId;
                                }
                            } else {
                                lastData = {};
                                lastData.CraneRequestId = InspectionRequestList.CraneRequestId;
                            }
                        }
                        if (lastData) {
                            const data = lastData.CraneRequestId;
                            lastData.CraneRequestId = 0;
                            lastData.CraneRequestId = data + 1;
                        } else {
                            lastData = {};
                            lastData.CraneRequestId = 1;
                        }
                        let craneId = 0;
                        const newId = JSON.parse(JSON.stringify(lastData));
                        if (newId && newId.CraneRequestId !== null && newId.CraneRequestId !== undefined) {
                            craneId = newId.CraneRequestId;
                        }
                        const roleDetails = await Role.getBy('Project Admin');
                        const accountRoleDetails = await Role.getBy('Account Admin');
                        if (inspectionData.recurrence === 'Daily') {
                            const startTime = inspectionData.startPicker;
                            const endTime = inspectionData.endPicker;
                            let dailyIndex = 0;
                            const recurrenceId = await concreteRequestService.insertRecurrenceSeries(
                                inspectionData,
                                inputData.user,
                                inspectionData.requestType,
                                eventTimeZone.timezone,
                            );

                            while (dailyIndex < totalDays.length) {
                                const data = totalDays[dailyIndex];
                                if (
                                    moment(data).isBetween(
                                        moment(inspectionData.inspectionStart),
                                        moment(inspectionData.inspectionEnd),
                                        null,
                                        '[]',
                                    ) ||
                                    moment(data).isSame(inspectionData.inspectionStart) ||
                                    moment(data).isSame(inspectionData.inspectionEnd)
                                ) {
                                    id += 1;
                                    craneId += 1;
                                    const date = moment(`${data}`).format('MM/DD/YYYY');
                                    const chosenTimezoneinspectionStart = moment.tz(
                                        `${date} ${startTime}`,
                                        'MM/DD/YYYY HH:mm',
                                        eventTimeZone.timezone,
                                    );
                                    const chosenTimezoneinspectionEnd = moment.tz(
                                        `${date} ${endTime}`,
                                        'MM/DD/YYYY HH:mm',
                                        eventTimeZone.timezone,
                                    );
                                    const inspectionStart = chosenTimezoneinspectionStart
                                        .clone()
                                        .tz('UTC')
                                        .format('YYYY-MM-DD HH:mm:ssZ');
                                    const inspectionEnd = chosenTimezoneinspectionEnd
                                        .clone()
                                        .tz('UTC')
                                        .format('YYYY-MM-DD HH:mm:ssZ');
                                    InspectionParam = {
                                        description: inspectionData.description,
                                        escort: inspectionData.escort,
                                        vehicleDetails: inspectionData.vehicleDetails,
                                        notes: inspectionData.notes,
                                        InspectionId: id,
                                        inspectionStart,
                                        inspectionEnd,
                                        ProjectId: inspectionData.ProjectId,
                                        createdBy: memberDetails.id,
                                        isAssociatedWithCraneRequest: inspectionData.isAssociatedWithCraneRequest,
                                        requestType: inspectionData.requestType,
                                        cranePickUpLocation: inspectionData.cranePickUpLocation,
                                        craneDropOffLocation: inspectionData.craneDropOffLocation,
                                        CraneRequestId:
                                            inspectionData.requestType === 'InspectionRequestWithCrane' ? craneId : null,
                                        recurrenceId,
                                        LocationId: inspectionData.LocationId,
                                        inspectionType: inspectionData.inspectionType,
                                        OriginationAddress: inspectionData.originationAddress,
                                        vehicleType: inspectionData.vehicleType
                                    };
                                    if (
                                        memberDetails.RoleId === roleDetails.id ||
                                        memberDetails.RoleId === accountRoleDetails.id ||
                                        memberDetails.isAutoApproveEnabled ||
                                        projectDetails.ProjectSettings.isAutoApprovalEnabled
                                    ) {
                                        InspectionParam.status = 'Approved';
                                        InspectionParam.approvedBy = memberDetails.id;
                                        InspectionParam.approved_at = new Date();
                                    }
                                    eventsArray.push(InspectionParam);
                                    // eslint-disable-next-line no-const-assign
                                    dailyIndex += +inspectionData.repeatEveryCount;
                                }
                            }
                            if (eventsArray && eventsArray.length > 0) {
                                const isOverlapping = await this.checkDoubleBookingAllowedOrNot(
                                    eventsArray,
                                    projectDetails,
                                    'add',
                                    inspectionData.GateId,
                                );
                                if (isOverlapping && isOverlapping.error) {
                                    return done(null, {
                                        message: isOverlapping.message,
                                    });
                                }
                            }
                        }
                        if (inspectionData.recurrence === 'Weekly') {
                            const startTime = inspectionData.startPicker;
                            const endTime = inspectionData.endPicker;
                            const startDayWeek = moment(inspectionData.inspectionStart).startOf('week');
                            const endDayWeek = moment(inspectionData.inspectionEnd).endOf('week');
                            const range1 = momentRange.range(moment(startDayWeek), moment(endDayWeek));
                            const totalDaysOfRecurrence = Array.from(range1.by('day'));
                            totalDays = totalDaysOfRecurrence;
                            const recurrenceId = await concreteRequestService.insertRecurrenceSeries(
                                inspectionData,
                                inputData.user,
                                inspectionData.requestType,
                                eventTimeZone.timezone,
                            );
                            let count;
                            let weekIncrement;
                            if (+inspectionData.repeatEveryCount > 1) {
                                count = +inspectionData.repeatEveryCount - 1;
                                weekIncrement = 7;
                            } else {
                                count = 1;
                                weekIncrement = 0;
                            }
                            for (
                                let indexba = 0;
                                indexba < totalDaysOfRecurrence.length;
                                indexba += weekIncrement * count
                            ) {
                                const totalLength = indexba + 6;
                                for (let indexb = indexba; indexb <= totalLength; indexb += 1) {
                                    const data = totalDaysOfRecurrence[indexb];
                                    indexba += 1;
                                    if (
                                        data &&
                                        !moment(data).isBefore(inspectionData.inspectionStart) &&
                                        !moment(data).isAfter(inspectionData.inspectionEnd)
                                    ) {
                                        const day = moment(data).format('dddd');
                                        const indexVal = inspectionData.days.includes(day);
                                        if (indexVal) {
                                            id += 1;
                                            craneId += 1;
                                            const date = moment(`${data}`).format('MM/DD/YYYY');
                                            const chosenTimezoneinspectionStart = moment.tz(
                                                `${date} ${startTime}`,
                                                'MM/DD/YYYY HH:mm',
                                                eventTimeZone.timezone,
                                            );
                                            const chosenTimezoneinspectionEnd = moment.tz(
                                                `${date} ${endTime}`,
                                                'MM/DD/YYYY HH:mm',
                                                eventTimeZone.timezone,
                                            );
                                            const inspectionStart = chosenTimezoneinspectionStart
                                                .clone()
                                                .tz('UTC')
                                                .format('YYYY-MM-DD HH:mm:ssZ');
                                            const inspectionEnd = chosenTimezoneinspectionEnd
                                                .clone()
                                                .tz('UTC')
                                                .format('YYYY-MM-DD HH:mm:ssZ');
                                            InspectionParam = {
                                                description: inspectionData.description,
                                                escort: inspectionData.escort,
                                                vehicleDetails: inspectionData.vehicleDetails,
                                                notes: inspectionData.notes,
                                                InspectionId: id,
                                                inspectionStart,
                                                inspectionEnd,
                                                ProjectId: inspectionData.ProjectId,
                                                createdBy: memberDetails.id,
                                                isAssociatedWithCraneRequest: inspectionData.isAssociatedWithCraneRequest,
                                                requestType: inspectionData.requestType,
                                                cranePickUpLocation: inspectionData.cranePickUpLocation,
                                                craneDropOffLocation: inspectionData.craneDropOffLocation,
                                                CraneRequestId:
                                                    inspectionData.requestType === 'InspectionRequestWithCrane' ? craneId : null,
                                                recurrenceId,
                                                LocationId: inspectionData.LocationId,
                                                inspectionType: inspectionData.inspectionType,
                                                OriginationAddress: inspectionData.originationAddress,
                                                vehicleType: inspectionData.vehicleType
                                            };
                                            if (
                                                memberDetails.RoleId === roleDetails.id ||
                                                memberDetails.RoleId === accountRoleDetails.id ||
                                                memberDetails.isAutoApproveEnabled ||
                                                projectDetails.ProjectSettings.isAutoApprovalEnabled
                                            ) {
                                                InspectionParam.status = 'Approved';
                                                InspectionParam.approvedBy = memberDetails.id;
                                                InspectionParam.approved_at = new Date();
                                            }
                                            eventsArray.push(InspectionParam);
                                        }
                                    }
                                }
                            }
                            if (eventsArray && eventsArray.length > 0) {
                                const isOverlapping = await this.checkDoubleBookingAllowedOrNot(
                                    eventsArray,
                                    projectDetails,
                                    'add',
                                    inspectionData.GateId,
                                );
                                if (isOverlapping && isOverlapping.error) {
                                    return done(null, {
                                        message: isOverlapping.message,
                                    });
                                }
                            }
                        }
                        if (inspectionData.recurrence === 'Monthly') {
                            const startTime = inspectionData.startPicker;
                            const endTime = inspectionData.endPicker;
                            const startMonth = moment(inspectionData.inspectionStart).startOf('month');
                            const startMonthNumber = moment(startMonth).format('MM');
                            const endMonth = moment(inspectionData.inspectionEnd).endOf('month');
                            const endMonthNumber = moment(endMonth).format('MM');
                            let startDate1 = moment(inspectionData.inspectionStart);
                            const endDate1 = moment(inspectionData.inspectionEnd).endOf('month');
                            const allMonthsInPeriod = [];
                            const recurrenceId = await concreteRequestService.insertRecurrenceSeries(
                                inspectionData,
                                inputData.user,
                                inspectionData.requestType,
                                eventTimeZone.timezone,
                            );
                            while (startDate1.isBefore(endDate1)) {
                                allMonthsInPeriod.push(startDate1.format('YYYY-MM'));
                                startDate1 = startDate1.add(1, 'month');
                            }
                            let currentMonthDates = [];
                            let totalNumberOfMonths = endMonthNumber - startMonthNumber;
                            if (totalNumberOfMonths < 0) {
                                totalNumberOfMonths *= -1;
                            }
                            let k = 0;
                            while (k < allMonthsInPeriod.length + 1) {
                                currentMonthDates = Array.from(
                                    { length: moment(allMonthsInPeriod[k], 'YYYY-MM').daysInMonth() },
                                    (x, j) => moment(allMonthsInPeriod[k], 'YYYY-MM').startOf('month').add(j, 'days'),
                                );

                                if (inspectionData.chosenDateOfMonth) {
                                    const getDate = currentMonthDates.filter(
                                        (value) => moment(value).format('DD') === inspectionData.dateOfMonth,
                                    );
                                    if (getDate.length === 1) {
                                        if (
                                            moment(getDate[0]).isBetween(
                                                moment(inspectionData.inspectionStart),
                                                moment(inspectionData.inspectionEnd),
                                                null,
                                                '[]',
                                            ) ||
                                            moment(getDate[0]).isSame(inspectionData.inspectionStart) ||
                                            moment(getDate[0]).isSame(inspectionData.inspectionEnd)
                                        ) {
                                            id += 1;
                                            craneId += 1;
                                            const date = moment(getDate[0].toDate()).format('MM/DD/YYYY');
                                            const chosenTimezoneinspectionStart = moment.tz(
                                                `${date} ${startTime}`,
                                                'MM/DD/YYYY HH:mm',
                                                eventTimeZone.timezone,
                                            );
                                            const chosenTimezoneinspectionEnd = moment.tz(
                                                `${date} ${endTime}`,
                                                'MM/DD/YYYY HH:mm',
                                                eventTimeZone.timezone,
                                            );
                                            const inspectionStart = chosenTimezoneinspectionStart
                                                .clone()
                                                .tz('UTC')
                                                .format('YYYY-MM-DD HH:mm:ssZ');
                                            const inspectionEnd = chosenTimezoneinspectionEnd
                                                .clone()
                                                .tz('UTC')
                                                .format('YYYY-MM-DD HH:mm:ssZ');
                                            InspectionParam = {
                                                description: inspectionData.description,
                                                escort: inspectionData.escort,
                                                vehicleDetails: inspectionData.vehicleDetails,
                                                notes: inspectionData.notes,
                                                InspectionId: id,
                                                inspectionStart,
                                                inspectionEnd,
                                                ProjectId: inspectionData.ProjectId,
                                                createdBy: memberDetails.id,
                                                isAssociatedWithCraneRequest: inspectionData.isAssociatedWithCraneRequest,
                                                requestType: inspectionData.requestType,
                                                cranePickUpLocation: inspectionData.cranePickUpLocation,
                                                craneDropOffLocation: inspectionData.craneDropOffLocation,
                                                CraneRequestId:
                                                    inspectionData.requestType === 'InspectionRequestWithCrane' ? craneId : null,
                                                recurrenceId,
                                                LocationId: inspectionData.LocationId,
                                                inspectionType: inspectionData.inspectionType,
                                                OriginationAddress: inspectionData.originationAddress,
                                                vehicleType: inspectionData.vehicleType
                                            };
                                            if (
                                                memberDetails.RoleId === roleDetails.id ||
                                                memberDetails.RoleId === accountRoleDetails.id ||
                                                memberDetails.isAutoApproveEnabled ||
                                                projectDetails.ProjectSettings.isAutoApprovalEnabled
                                            ) {
                                                InspectionParam.status = 'Approved';
                                                InspectionParam.approvedBy = memberDetails.id;
                                                InspectionParam.approved_at = new Date();
                                            }
                                            eventsArray.push(InspectionParam);
                                        }
                                    }
                                } else if (allMonthsInPeriod[k]) {
                                    const dayOfMonth = inspectionData.monthlyRepeatType;
                                    const week = dayOfMonth.split(' ')[0].toLowerCase();
                                    const day = dayOfMonth.split(' ')[1].toLowerCase();
                                    const chosenDay = moment(allMonthsInPeriod[k], 'YYYY-MM')
                                        .startOf('month')
                                        .day(day);
                                    const getAllDays = [];
                                    if (chosenDay.date() > 7) chosenDay.add(7, 'd');
                                    const month = chosenDay.month();
                                    while (month === chosenDay.month()) {
                                        getAllDays.push(chosenDay.toString());
                                        chosenDay.add(7, 'd');
                                    }
                                    let i = 0;
                                    if (week === 'second') {
                                        i += 1;
                                    } else if (week === 'third') {
                                        i += 2;
                                    } else if (week === 'fourth') {
                                        i += 3;
                                    } else if (week === 'last') {
                                        i = getAllDays.length - 1;
                                    }
                                    const finalDay = getAllDays[i];
                                    if (
                                        moment(finalDay).isBetween(
                                            moment(inspectionData.inspectionStart),
                                            moment(inspectionData.inspectionEnd),
                                            null,
                                            '[]',
                                        ) ||
                                        moment(finalDay).isSame(inspectionData.inspectionStart) ||
                                        moment(finalDay).isSame(inspectionData.inspectionEnd)
                                    ) {
                                        id += 1;
                                        craneId += 1;
                                        const date = moment(finalDay).format('MM/DD/YYYY');
                                        const chosenTimezoneinspectionStart = moment.tz(
                                            `${date} ${startTime}`,
                                            'MM/DD/YYYY HH:mm',
                                            eventTimeZone.timezone,
                                        );
                                        const chosenTimezoneinspectionEnd = moment.tz(
                                            `${date} ${endTime}`,
                                            'MM/DD/YYYY HH:mm',
                                            eventTimeZone.timezone,
                                        );
                                        const inspectionStart = chosenTimezoneinspectionStart
                                            .clone()
                                            .tz('UTC')
                                            .format('YYYY-MM-DD HH:mm:ssZ');
                                        const inspectionEnd = chosenTimezoneinspectionEnd
                                            .clone()
                                            .tz('UTC')
                                            .format('YYYY-MM-DD HH:mm:ssZ');
                                        InspectionParam = {
                                            description: inspectionData.description,
                                            escort: inspectionData.escort,
                                            vehicleDetails: inspectionData.vehicleDetails,
                                            notes: inspectionData.notes,
                                            InspectionId: id,
                                            inspectionStart,
                                            inspectionEnd,
                                            ProjectId: inspectionData.ProjectId,
                                            createdBy: memberDetails.id,
                                            isAssociatedWithCraneRequest: inspectionData.isAssociatedWithCraneRequest,
                                            requestType: inspectionData.requestType,
                                            cranePickUpLocation: inspectionData.cranePickUpLocation,
                                            craneDropOffLocation: inspectionData.craneDropOffLocation,
                                            CraneRequestId:
                                                inspectionData.requestType === 'InspectionRequestWithCrane' ? craneId : null,
                                            recurrenceId,
                                            LocationId: inspectionData.LocationId,
                                            inspectionType: inspectionData.inspectionType,
                                            OriginationAddress: inspectionData.originationAddress,
                                            vehicleType: inspectionData.vehicleType
                                        };
                                        if (
                                            memberDetails.RoleId === roleDetails.id ||
                                            memberDetails.RoleId === accountRoleDetails.id ||
                                            memberDetails.isAutoApproveEnabled ||
                                            projectDetails.ProjectSettings.isAutoApprovalEnabled
                                        ) {
                                            InspectionParam.status = 'Approved';
                                            InspectionParam.approvedBy = memberDetails.id;
                                            InspectionParam.approved_at = new Date();
                                        }
                                        eventsArray.push(InspectionParam);
                                    }
                                }
                                k += +inspectionData.repeatEveryCount;
                            }
                            if (eventsArray && eventsArray.length > 0) {
                                const isOverlapping = await this.checkDoubleBookingAllowedOrNot(
                                    eventsArray,
                                    projectDetails,
                                    'add',
                                    inspectionData.GateId,
                                );
                                if (isOverlapping && isOverlapping.error) {
                                    return done(null, {
                                        message: isOverlapping.message,
                                    });
                                }
                            }
                        }
                        if (inspectionData.recurrence === 'Yearly') {
                            const startTime = inspectionData.startPicker;
                            const endTime = inspectionData.endPicker;
                            const startMonth = moment(inspectionData.inspectionStart).startOf('month');
                            const startMonthNumber = moment(startMonth).format('MM');
                            const endMonth = moment(inspectionData.inspectionEnd).endOf('month');
                            const endMonthNumber = moment(endMonth).format('MM');
                            let startDate1 = moment(inspectionData.inspectionStart);
                            const endDate1 = moment(inspectionData.inspectionEnd).endOf('month');
                            const allMonthsInPeriod = [];
                            const recurrenceId = await concreteRequestService.insertRecurrenceSeries(
                                inspectionData,
                                inputData.user,
                                inspectionData.requestType,
                                eventTimeZone.timezone,
                            );
                            while (startDate1.isBefore(endDate1)) {
                                allMonthsInPeriod.push(startDate1.format('YYYY-MM'));
                                startDate1 = startDate1.add(12, 'month');
                            }
                            let currentMonthDates = [];
                            let totalNumberOfMonths = endMonthNumber - startMonthNumber;
                            if (totalNumberOfMonths < 0) {
                                totalNumberOfMonths *= -1;
                            }
                            for (let k = 0; k < allMonthsInPeriod.length + 1; k += 1) {
                                currentMonthDates = Array.from(
                                    { length: moment(allMonthsInPeriod[k], 'YYYY-MM').daysInMonth() },
                                    (x, j) => moment(allMonthsInPeriod[k], 'YYYY-MM').startOf('month').add(j, 'days'),
                                );
                                if (inspectionData.chosenDateOfMonth) {
                                    const getDate = currentMonthDates.filter(
                                        (value) => moment(value).format('DD') === inspectionData.dateOfMonth,
                                    );
                                    if (getDate.length === 1) {
                                        if (
                                            moment(getDate[0]).isBetween(
                                                moment(inspectionData.inspectionStart),
                                                moment(inspectionData.inspectionEnd),
                                                null,
                                                '[]',
                                            ) ||
                                            moment(getDate[0]).isSame(inspectionData.inspectionStart) ||
                                            moment(getDate[0]).isSame(inspectionData.inspectionEnd)
                                        ) {
                                            id += 1;
                                            craneId += 1;
                                            const date = moment(getDate[0].toDate()).format('MM/DD/YYYY');
                                            const chosenTimezoneinspectionStart = moment.tz(
                                                `${date} ${startTime}`,
                                                'MM/DD/YYYY HH:mm',
                                                eventTimeZone.timezone,
                                            );
                                            const chosenTimezoneinspectionEnd = moment.tz(
                                                `${date} ${endTime}`,
                                                'MM/DD/YYYY HH:mm',
                                                eventTimeZone.timezone,
                                            );
                                            const inspectionStart = chosenTimezoneinspectionStart
                                                .clone()
                                                .tz('UTC')
                                                .format('YYYY-MM-DD HH:mm:ssZ');
                                            const inspectionEnd = chosenTimezoneinspectionEnd
                                                .clone()
                                                .tz('UTC')
                                                .format('YYYY-MM-DD HH:mm:ssZ');
                                            InspectionParam = {
                                                description: inspectionData.description,
                                                escort: inspectionData.escort,
                                                vehicleDetails: inspectionData.vehicleDetails,
                                                notes: inspectionData.notes,
                                                InspectionId: id,
                                                inspectionStart,
                                                inspectionEnd,
                                                ProjectId: inspectionData.ProjectId,
                                                createdBy: memberDetails.id,
                                                isAssociatedWithCraneRequest: inspectionData.isAssociatedWithCraneRequest,
                                                requestType: inspectionData.requestType,
                                                cranePickUpLocation: inspectionData.cranePickUpLocation,
                                                craneDropOffLocation: inspectionData.craneDropOffLocation,
                                                CraneRequestId:
                                                    inspectionData.requestType === 'InspectionRequestWithCrane' ? craneId : null,
                                                recurrenceId,
                                                LocationId: inspectionData.LocationId,
                                                inspectionType: inspectionData.inspectionType,
                                                OriginationAddress: inspectionData.originationAddress,
                                                vehicleType: inspectionData.vehicleType
                                            };
                                            if (
                                                memberDetails.RoleId === roleDetails.id ||
                                                memberDetails.RoleId === accountRoleDetails.id ||
                                                memberDetails.isAutoApproveEnabled ||
                                                projectDetails.ProjectSettings.isAutoApprovalEnabled
                                            ) {
                                                InspectionParam.status = 'Approved';
                                                InspectionParam.approvedBy = memberDetails.id;
                                                InspectionParam.approved_at = new Date();
                                            }
                                            eventsArray.push(InspectionParam);
                                        }
                                    }
                                } else if (allMonthsInPeriod[k]) {
                                    const dayOfMonth = inspectionData.monthlyRepeatType;
                                    const week = dayOfMonth.split(' ')[0].toLowerCase();
                                    const day = dayOfMonth.split(' ')[1].toLowerCase();
                                    const chosenDay = moment(allMonthsInPeriod[k], 'YYYY-MM')
                                        .startOf('month')
                                        .day(day);
                                    const getAllDays = [];
                                    if (chosenDay.date() > 7) chosenDay.add(7, 'd');
                                    const month = chosenDay.month();
                                    while (month === chosenDay.month()) {
                                        getAllDays.push(chosenDay.toString());
                                        chosenDay.add(7, 'd');
                                    }
                                    let i = 0;
                                    if (week === 'second') {
                                        i += 1;
                                    } else if (week === 'third') {
                                        i += 2;
                                    } else if (week === 'fourth') {
                                        i += 3;
                                    } else if (week === 'last') {
                                        i = getAllDays.length - 1;
                                    }
                                    const finalDay = getAllDays[i];
                                    if (
                                        moment(finalDay).isBetween(
                                            moment(inspectionData.inspectionStart),
                                            moment(inspectionData.inspectionEnd),
                                            null,
                                            '[]',
                                        ) ||
                                        moment(finalDay).isSame(inspectionData.inspectionStart) ||
                                        moment(finalDay).isSame(inspectionData.inspectionEnd)
                                    ) {
                                        id += 1;
                                        craneId += 1;
                                        const date = moment(finalDay).format('MM/DD/YYYY');
                                        const chosenTimezoneinspectionStart = moment.tz(
                                            `${date} ${startTime}`,
                                            'MM/DD/YYYY HH:mm',
                                            eventTimeZone.timezone,
                                        );
                                        const chosenTimezoneinspectionEnd = moment.tz(
                                            `${date} ${endTime}`,
                                            'MM/DD/YYYY HH:mm',
                                            eventTimeZone.timezone,
                                        );
                                        const inspectionStart = chosenTimezoneinspectionStart
                                            .clone()
                                            .tz('UTC')
                                            .format('YYYY-MM-DD HH:mm:ssZ');
                                        const inspectionEnd = chosenTimezoneinspectionEnd
                                            .clone()
                                            .tz('UTC')
                                            .format('YYYY-MM-DD HH:mm:ssZ');
                                        InspectionParam = {
                                            description: inspectionData.description,
                                            escort: inspectionData.escort,
                                            vehicleDetails: inspectionData.vehicleDetails,
                                            notes: inspectionData.notes,
                                            InspectionId: id,
                                            inspectionStart,
                                            inspectionEnd,
                                            ProjectId: inspectionData.ProjectId,
                                            createdBy: memberDetails.id,
                                            isAssociatedWithCraneRequest: inspectionData.isAssociatedWithCraneRequest,
                                            requestType: inspectionData.requestType,
                                            cranePickUpLocation: inspectionData.cranePickUpLocation,
                                            craneDropOffLocation: inspectionData.craneDropOffLocation,
                                            CraneRequestId:
                                                inspectionData.requestType === 'InspectionRequestWithCrane' ? craneId : null,
                                            recurrenceId,
                                            LocationId: inspectionData.LocationId,
                                            inspectionType: inspectionData.inspectionType,
                                            OriginationAddress: inspectionData.originationAddress,
                                            vehicleType: inspectionData.vehicleType
                                        };
                                        if (
                                            memberDetails.RoleId === roleDetails.id ||
                                            memberDetails.RoleId === accountRoleDetails.id ||
                                            memberDetails.isAutoApproveEnabled ||
                                            projectDetails.ProjectSettings.isAutoApprovalEnabled
                                        ) {
                                            InspectionParam.status = 'Approved';
                                            InspectionParam.approvedBy = memberDetails.id;
                                            InspectionParam.approved_at = new Date();
                                        }
                                        eventsArray.push(InspectionParam);
                                    }
                                }
                            }
                            if (eventsArray && eventsArray.length > 0) {
                                const isOverlapping = await this.checkDoubleBookingAllowedOrNot(
                                    eventsArray,
                                    projectDetails,
                                    'add',
                                    inspectionData.GateId,
                                );
                                if (isOverlapping && isOverlapping.error) {
                                    return done(null, {
                                        message: isOverlapping.message,
                                    });
                                }
                            }
                        }
                        if (inspectionData.recurrence === 'Does Not Repeat') {
                            id += 1;
                            craneId += 1;
                            const chosenTimezoneinspectionStart = moment.tz(
                                `${inspectionData.inspectionStart} ${inspectionData.startPicker}`,
                                'YYYY MM DD 00:00:00 HH:mm',
                                eventTimeZone.timezone,
                            );
                            const chosenTimezoneinspectionEnd = moment.tz(
                                `${inspectionData.inspectionEnd} ${inspectionData.endPicker}`,
                                'YYYY MM DD 00:00:00 HH:mm',
                                eventTimeZone.timezone,
                            );
                            const inspectionStart = chosenTimezoneinspectionStart
                                .clone()
                                .tz('UTC')
                                .format('YYYY-MM-DD HH:mm:ssZ');
                            const inspectionEnd = chosenTimezoneinspectionEnd
                                .clone()
                                .tz('UTC')
                                .format('YYYY-MM-DD HH:mm:ssZ');
                            const recurrenceId = await concreteRequestService.insertRecurrenceSeries(
                                inspectionData,
                                inputData.user,
                                inspectionData.requestType,
                                eventTimeZone.timezone,
                            );
                            InspectionParam = {
                                description: inspectionData.description,
                                escort: inspectionData.escort,
                                vehicleDetails: inspectionData.vehicleDetails,
                                notes: inspectionData.notes,
                                InspectionId: id,
                                inspectionStart,
                                inspectionEnd,
                                ProjectId: inspectionData.ProjectId,
                                createdBy: memberDetails.id,
                                isAssociatedWithCraneRequest: inspectionData.isAssociatedWithCraneRequest,
                                requestType: inspectionData.requestType,
                                cranePickUpLocation: inspectionData.cranePickUpLocation,
                                craneDropOffLocation: inspectionData.craneDropOffLocation,
                                CraneRequestId:
                                    inspectionData.requestType === 'InspectionRequestWithCrane' ? craneId : null,
                                recurrenceId,
                                LocationId: inspectionData.LocationId,
                                inspectionType: inspectionData.inspectionType,
                                OriginationAddress: inspectionData.originationAddress,
                                vehicleType: inspectionData.vehicleType
                            };
                            if (
                                memberDetails.RoleId === roleDetails.id ||
                                memberDetails.RoleId === accountRoleDetails.id ||
                                memberDetails.isAutoApproveEnabled ||
                                projectDetails.ProjectSettings.isAutoApprovalEnabled
                            ) {
                                InspectionParam.status = 'Approved';
                                InspectionParam.approvedBy = memberDetails.id;
                                InspectionParam.approved_at = new Date();
                            }
                            eventsArray.push(InspectionParam);
                            if (eventsArray && eventsArray.length > 0) {
                                const isOverlapping = await this.checkDoubleBookingAllowedOrNot(
                                    eventsArray,
                                    projectDetails,
                                    'add',
                                    inspectionData.GateId,
                                );
                                if (isOverlapping && isOverlapping.error) {
                                    return done(null, {
                                        message: isOverlapping.message,
                                    });
                                }
                            }
                        }
                        let newInspectionData = {};
                        if (eventsArray.length > 0) {
                            for (let i = 0; i < eventsArray.length; i += 1) {
                                newInspectionData = await InspectionRequest.createInstance(eventsArray[i]);
                                const { companies, persons, define } = inspectionData;
                                const gates = [inspectionData.GateId];
                                const equipments = inspectionData.EquipmentId;
                                const updateParam = {
                                    InspectionId: newInspectionData.id,
                                    InspectionCode: newInspectionData.InspectionId,
                                    ProjectId: inspectionData.ProjectId,
                                };
                                companies.forEach(async (element) => {
                                    const companyParam = updateParam;
                                    companyParam.CompanyId = element;
                                    await InspectionCompany.createInstance(companyParam);
                                });
                                gates.forEach(async (element) => {
                                    const gateParam = updateParam;
                                    gateParam.GateId = element;
                                    await InspectionGate.createInstance(gateParam);
                                });
                                equipments.forEach(async (element) => {
                                    const equipmentParam = updateParam;
                                    equipmentParam.EquipmentId = element;
                                    await InspectionEquipment.createInstance(equipmentParam);
                                });
                                persons.forEach(async (element) => {
                                    const memberParam = updateParam;
                                    memberParam.MemberId = element;
                                    await InspectionPerson.createInstance(memberParam);
                                });
                                define.forEach(async (element) => {
                                    const defineParam = updateParam;
                                    defineParam.DeliverDefineWorkId = element;
                                    await DeliverDefine.createInstance(defineParam);
                                });
                                const history = {
                                    InspectionRequestId: newInspectionData.id,
                                    InspectionId: newInspectionData.InspectionId,
                                    MemberId: memberDetails.id,
                                    type: 'create',
                                    description: `${loginUser.firstName} ${loginUser.lastName} Created Inspection Booking, ${inspectionData.description}.`,
                                };
                                const notification = history;
                                notification.ProjectId = eventsArray[i].ProjectId;
                                notification.title = 'Inspection Booking Creation';
                                await InspectionHistory.createInstance(history);
                                if (newInspectionData.status === 'Approved') {
                                    const object = {
                                        ProjectId: inspectionData.ProjectId,
                                        MemberId: memberDetails.id,
                                        InspectionRequestId: newInspectionData.id,
                                        isDeleted: false,
                                        type: 'approved',
                                        description: `${loginUser.firstName} ${loginUser.lastName} Approved Inspection Booking, ${inspectionData.description}.`,
                                    };
                                    await InspectionHistory.createInstance(object);
                                }
                            }
                        }
                        if (Object.keys(newInspectionData).length > 0 && typeof newInspectionData === 'object') {
                            const { persons } = inspectionData;
                            const locationChosen = await Locations.findOne({
                                where: {
                                    ProjectId: inspectionData.ProjectId,
                                    id: inspectionData.LocationId,
                                },
                            });

                            const history = {
                                InspectionRequestId: newInspectionData.id,
                                InspectionId: newInspectionData.InspectionId,
                                MemberId: memberDetails.id,
                                type: 'create',
                                description: `${loginUser.firstName} ${loginUser.lastName} Created Inspection Booking, ${inspectionData.description}.`,
                                locationFollowDescription: `${loginUser.firstName} ${loginUser.lastName} Created Inspection Booking, ${inspectionData.description}. Location: ${locationChosen.locationPath}.`,
                                memberData: [],
                            };
                            const notification = history;
                            notification.ProjectId = inspectionData.ProjectId;
                            notification.LocationId = inspectionData.LocationId;
                            notification.title = 'Inspection Booking Creation';
                            notification.recurrenceType = `${inspectionData.recurrence} From ${moment(
                                inspectionData.inspectionStart,
                            ).format('MM/DD/YYYY')} to ${moment(inspectionData.inspectionEnd).format('MM/DD/YYYY')}`;
                            notification.requestType = 'InspectionRequest';
                            const newNotification = await Notification.createInstance(notification);
                            const memberLocationPreference = await LocationNotificationPreferences.findAll({
                                where: {
                                    ProjectId: inspectionData.ProjectId,
                                    LocationId: inspectionData.LocationId,
                                    follow: true,
                                },
                                include: [
                                    {
                                        association: 'Member',
                                        attributes: ['id', 'RoleId'],
                                        where: {
                                            [Op.and]: [
                                                {
                                                    id: { [Op.ne]: memberDetails.id },
                                                },
                                            ],
                                        },
                                        include: [
                                            {
                                                association: 'User',
                                                attributes: ['id', 'firstName', 'lastName', 'email'],
                                            },
                                        ],
                                    },
                                ],
                            });
                            const locationFollowMembers = [];
                            memberLocationPreference.forEach(async (element) => {
                                locationFollowMembers.push(element.Member.id);
                            });
                            const adminData = await Member.findAll({
                                where: {
                                    [Op.and]: [
                                        { ProjectId: inspectionData.ProjectId },
                                        { isDeleted: false },
                                        { id: { [Op.in]: persons } },
                                        { id: { [Op.ne]: newNotification.MemberId } },
                                        { id: { [Op.notIn]: locationFollowMembers } },
                                    ],
                                },
                                include: [
                                    {
                                        association: 'User',
                                        attributes: ['id', 'firstName', 'lastName', 'email'],
                                    },
                                ],
                                attributes: ['id', 'RoleId'],
                            });
                            if (memberLocationPreference && memberLocationPreference.length > 0) {
                                // here 3-(NotificationPreferenceItemId - When a new inspection/crane/concrete request added to the project)
                                await pushNotification.sendMemberLocationPreferencePushNotification(
                                    memberLocationPreference,
                                    newInspectionData.InspectionRequestId,
                                    history.locationFollowDescription,
                                    newInspectionData.requestType,
                                    newInspectionData.ProjectId,
                                    newInspectionData.id,
                                    3,
                                );
                                // here 3-(NotificationPreferenceItemId - When a new inspection/crane/concrete request added to the project)
                                await notificationHelper.createMemberDeliveryLocationInAppNotification(
                                    DeliveryPersonNotification,
                                    inspectionData.ProjectId,
                                    newNotification.id,
                                    memberLocationPreference,
                                    3,
                                );
                            }
                            history.adminData = adminData;
                            history.firstName = loginUser.firstName;
                            history.profilePic = loginUser.profilePic;
                            history.createdAt = new Date();
                            history.ProjectId = inspectionData.ProjectId;
                            history.projectName = projectDetails.projectName;
                            // here 3-(NotificationPreferenceItemId - When a new inspection/crane/concrete request added to the project)
                            await notificationHelper.createDeliveryPersonNotification(
                                adminData,
                                [],
                                projectDetails,
                                newNotification,
                                DeliveryPersonNotification,
                                memberDetails,
                                loginUser,
                                3,
                                'created a',
                                'Inspection Request',
                                `Inspection Booking (${newInspectionData.InspectionId} - ${newInspectionData.description})`,
                                newInspectionData.id,
                            );
                            const checkMemberNotification = await NotificationPreference.findAll({
                                where: {
                                    ProjectId: inspectionData.ProjectId,
                                    isDeleted: false,
                                },
                                attributes: [
                                    'id',
                                    'MemberId',
                                    'ProjectId',
                                    'ParentCompanyId',
                                    'NotificationPreferenceItemId',
                                    'instant',
                                    'dailyDigest',
                                ],
                                include: [
                                    {
                                        association: 'NotificationPreferenceItem',
                                        where: {
                                            id: 3,
                                            isDeleted: false,
                                        },
                                        attributes: ['id', 'description', 'inappNotification', 'emailNotification'],
                                    },
                                ],
                            });
                            history.notificationPreference = checkMemberNotification;
                            // here 3-(NotificationPreferenceItemId - When a new inspection/crane/concrete request added to the project)
                            await pushNotification.sendDeviceTokenForInspection(history, 3, inspectionData.ProjectId);
                            await this.sendEmailNotificationToUser(
                                history,
                                memberDetails,
                                loginUser,
                                newInspectionData,
                                inspectionData,
                                memberLocationPreference,
                            );
                            const memberLocationPreferenceNotify = await LocationNotificationPreferences.findAll({
                                where: {
                                    ProjectId: +inspectionData.ProjectId,
                                    LocationId: +inspectionData.LocationId,
                                    follow: true,
                                },
                                include: [
                                    {
                                        association: 'Member',
                                        attributes: ['id', 'RoleId'],
                                        where: {
                                            [Op.and]: [
                                                {
                                                    id: { [Op.ne]: memberDetails.id },
                                                },
                                            ],
                                        },
                                        include: [
                                            {
                                                association: 'User',
                                                attributes: ['id', 'firstName', 'lastName', 'email'],
                                            },
                                        ],
                                    },
                                ],
                            });
                            if (+memberDetails.RoleId === 4 || +memberDetails.RoleId === 3) {
                                const userEmails = await this.getMemberDetailData(
                                    history,
                                    memberLocationPreference,
                                );
                                if (userEmails.length > 0) {
                                    userEmails.forEach(async (element) => {
                                        if (+element.RoleId === 2 && +element.MemberId !== +memberDetails.id) {
                                            let name;
                                            if (!element.firstName) {
                                                name = 'user';
                                            } else {
                                                name = `${element.firstName} ${element.lastName}`;
                                            }
                                            const memberRole = await Role.findOne({
                                                where: {
                                                    id: memberDetails.RoleId,
                                                    isDeleted: false,
                                                },
                                            });
                                            const mailPayload = {
                                                name,
                                                email: element.email,
                                                content: `We would like to inform you that 
                          ${loginUser.firstName} ${loginUser.lastName} - ${memberRole.roleName} has created a Inspection booking ${newInspectionData.InspectionId} and waiting for your approval.Kindly review the booking and update the status.`,
                                            };
                                            const isMemberFollowLocation = await LocationNotificationPreferences.findOne({
                                                where: {
                                                    MemberId: +element.MemberId,
                                                    ProjectId: +inspectionData.ProjectId,
                                                    LocationId: +inspectionData.LocationId,
                                                    isDeleted: false,
                                                    // follow: true,
                                                },
                                            });
                                            if (isMemberFollowLocation) {
                                                const memberNotification = await NotificationPreference.findOne({
                                                    where: {
                                                        MemberId: +element.MemberId,
                                                        ProjectId: +inspectionData.ProjectId,
                                                        isDeleted: false,
                                                    },
                                                    include: [
                                                        {
                                                            association: 'NotificationPreferenceItem',
                                                            where: {
                                                                id: 8,
                                                                isDeleted: false,
                                                            },
                                                        },
                                                    ],
                                                });
                                                if (memberNotification && memberNotification.instant) {
                                                    await MAILER.sendMail(
                                                        mailPayload,
                                                        'notifyPAForApproval',
                                                        `Inspection Booking created by ${loginUser.firstName} ${loginUser.lastName} - ${memberRole.roleName}`,
                                                        `Inspection Booking created by ${loginUser.firstName} ${loginUser.lastName} - ${memberRole.roleName}`,
                                                        async (info, err) => {
                                                            console.log(info, err);
                                                        },
                                                    );
                                                }
                                                if (memberNotification && memberNotification.dailyDigest) {
                                                    await this.createDailyDigestDataApproval(
                                                        +memberDetails.RoleId,
                                                        +element.MemberId,
                                                        +inspectionData.ProjectId,
                                                        +inspectionData.ParentCompanyId,
                                                        loginUser,
                                                        'created a',
                                                        'Inspection Request',
                                                        `Inspection Booking (${newInspectionData.InspectionId} - ${newInspectionData.description})`,
                                                        'and waiting for your approval',
                                                        newInspectionData.id,
                                                    );
                                                }
                                            }
                                        }
                                    });
                                    if (memberLocationPreferenceNotify && memberLocationPreferenceNotify.length > 0) {
                                        history.memberData = [];
                                        history.memberData.push(...memberLocationPreferenceNotify);
                                    }
                                    return done(history, false);
                                }
                                if (memberLocationPreferenceNotify && memberLocationPreferenceNotify.length > 0) {
                                    history.memberData = [];
                                    history.memberData.push(...memberLocationPreferenceNotify);
                                }
                                return done(history, false);
                            }
                            if (memberLocationPreferenceNotify && memberLocationPreferenceNotify.length > 0) {
                                history.memberData = [];
                                history.memberData.push(...memberLocationPreferenceNotify);
                            }
                            return done(history, false);
                        }
                        return done(null, {
                            message: 'Bookings will not be created for the scheduled date/time',
                        });
                    }
                    return done(null, {
                        message: 'You are not allowed create Inspection Booking for this project.',
                    });
                });
            } else {
                return done(null, { message: 'Project does not exist.' });
            }
        } catch (e) {
            return done(null, e);
        }
    },
    async compareinspectionDateWithinspectionWindowDate(
        dateStr,
        timeStr,
        timezoneStr,
        inspectionWindowTime,
        inspectionWindowTimeUnit,
    ) {
        const datetimeStr = `${moment(dateStr).format('YYYY-MM-DD')}T${timeStr}`;
        const datetime = moment.tz(datetimeStr, timezoneStr);
        const currentDatetime = moment
            .tz(timezoneStr)
            .add(inspectionWindowTime, inspectionWindowTimeUnit)
            .startOf('minute');
        return datetime.isSameOrBefore(currentDatetime);
    },
    async ReadAllnotification(inputData, done) {
        try {
            await this.getDynamicModel(inputData);
            const loginUser = inputData.user;
            const memberDetails = await Member.findOne({
                where: Sequelize.and({
                    UserId: loginUser.id,
                    isActive: true,
                    isDeleted: false,
                    ProjectId: inputData.query.ProjectId,
                }),
            });
            const newNot = await DeliveryPersonNotification.update(
                { seen: true },
                {
                    where: {
                        ProjectId: inputData.query.ProjectId,
                        MemberId: memberDetails.id,
                        isDeleted: false,
                    },
                },
            );
            const condition = {
                seen: false,
                ProjectId: inputData.query.ProjectId,
            };
            const count = await DeliveryPersonNotification.getUnSeenCount(condition, loginUser);
            done(count.length, false);
        } catch (e) {
            return done(null, e);
        }
    },
    async getDynamicModel(inputData) {
        await this.returnProjectModel();
        let { domainName } = inputData.user;
        const incomeData = inputData;
        let enterpriseValue;
        let ProjectId;
        const ParentCompanyId = inputData.body.ParentCompanyId
            ? inputData.body.ParentCompanyId
            : inputData.params.ParentCompanyId;
        let domainEnterpriseValue;
        if (domainName) {
            domainEnterpriseValue = await Enterprise.findOne({
                where: { name: domainName.toLowerCase() },
            });
            if (!domainEnterpriseValue) {
                domainName = '';
            }
        }
        if (!domainName && ParentCompanyId !== undefined && ParentCompanyId !== 'undefined') {
            const { email } = inputData.user;
            let userData;
            if (email) {
                userData = await publicUser.findOne({ where: { email } });
            }
            if (userData) {
                const memberData = await publicMember.findOne({
                    where: { UserId: userData.id, RoleId: { [Op.ne]: 4 }, isDeleted: false },
                });
                if (memberData) {
                    if (memberData.isAccount) {
                        enterpriseValue = await Enterprise.findOne({
                            where: { id: memberData.EnterpriseId, status: 'completed' },
                        });
                        if (enterpriseValue) {
                            domainName = enterpriseValue.name.toLowerCase();
                        }
                    } else {
                        enterpriseValue = await Enterprise.findOne({
                            where: { ParentCompanyId, status: 'completed' },
                        });
                        if (enterpriseValue) {
                            domainName = enterpriseValue.name.toLowerCase();
                        }
                    }
                } else {
                    enterpriseValue = await Enterprise.findOne({
                        where: { ParentCompanyId, status: 'completed' },
                    });
                    if (enterpriseValue) {
                        domainName = enterpriseValue.name.toLowerCase();
                    }
                }
            }
        }
        const modelObj = await helper.getDynamicModel(domainName);
        InspectionRequest = modelObj.InspectionRequest;
        Member = modelObj.Member;
        InspectionPerson = modelObj.InspectionPerson;
        InspectionGate = modelObj.InspectionGate;
        InspectionEquipment = modelObj.InspectionEquipment;
        InspectionCompany = modelObj.InspectionCompany;
        Role = modelObj.Role;
        Gates = modelObj.Gates;
        Equipments = modelObj.Equipments;
        DeliverDefineWork = modelObj.DeliverDefineWork;
        Company = modelObj.Company;
        Project = modelObj.Project;
        User = modelObj.User;
        DeliverDefine = modelObj.DeliverDefine;
        InspectionHistory = modelObj.InspectionHistory;
        VoidList = modelObj.VoidList;
        DeliveryPersonNotification = modelObj.DeliveryPersonNotification;
        Notification = modelObj.Notification;
        if (enterpriseValue) {
            const newUser = await User.findOne({ where: { email: inputData.user.email } });
            incomeData.user = newUser;
        }
        return ProjectId;
    },

    async Markallnotification(inputData) {
        try {
            await this.getDynamicModel(inputData);
            const loginUser = inputData.user;
            const { params } = inputData;

            const memberDetails = await Member.findOne({
                where: Sequelize.and({
                    UserId: loginUser.id,
                    isActive: true,
                    isDeleted: false,
                    ProjectId: params.ProjectId,
                }),
            });
            const newNot = await DeliveryPersonNotification.update(
                { seen: true },
                {
                    where: {
                        ProjectId: params.ProjectId,
                        MemberId: memberDetails.id,
                        isDeleted: false,
                    },
                },
            );
            const condition = {
                seen: false,
                ProjectId: params.ProjectId,
            };
            const count = await DeliveryPersonNotification.getUnSeenCount(condition, loginUser);
            return { status: 200, data: count.length };
        } catch (e) {
            console.log(e);
        }
    },
    async returnProjectModel() {
        const modelData = await helper.returnProjectModel();
        publicMember = modelData.Member;
        publicUser = modelData.User;
    },
    async checkInputDatas(inputData, done) {
        await this.getDynamicModel(inputData);
        const inspectionData = inputData.body;
        const { companies, persons, define } = inspectionData;
        const gates = [inspectionData.GateId];
        const equipments = inspectionData.EquipmentId;
        const inputProjectId = inspectionData.ProjectId;
        const memberList = await Member.count({
            where: { id: { [Op.in]: persons }, ProjectId: inputProjectId, isDeleted: false },
        });
        const gateList = await Gates.count({
            where: { id: { [Op.in]: gates }, ProjectId: inputProjectId, isDeleted: false },
        });
        const equipmentList = await Equipments.count({
            where: { id: { [Op.in]: equipments }, ProjectId: inputProjectId, isDeleted: false },
        });
        const defineList = await DeliverDefineWork.count({
            where: { id: { [Op.in]: define }, ProjectId: inputProjectId, isDeleted: false },
        });
        const companyList = await Company.count({
            where: {
                [Op.or]: [
                    {
                        id: { [Op.in]: companies },
                        ProjectId: +inputProjectId,
                        isDeleted: false,
                    },
                    {
                        id: {
                            [Op.in]: companies,
                        },
                        isParent: true,
                        ParentCompanyId: +inspectionData.ParentCompanyId,
                        isDeleted: false,
                    },
                ],
            },
        });
        if (inspectionData.persons && inspectionData.persons.length > 0 && memberList !== persons.length) {
            return done(null, { message: 'Some Member is not in the project' });
        }
        if (inspectionData.GateId && gateList !== gates.length) {
            return done(null, { message: 'Mentioned Gate is not in the project' });
        }
        if (inspectionData.EquipmentId && equipmentList !== equipments.length) {
            return done(null, { message: 'Mentioned Equipment is not in this project' });
        }
        if (
            inspectionData.companies &&
            inspectionData.companies.length > 0 &&
            companyList !== companies.length
        ) {
            return done(null, { message: 'Some Company is not in the project' });
        }
        if (inspectionData.define && inspectionData.define.length > 0 && defineList !== define.length) {
            return done(null, { message: 'Some Definable Feature of Work is not in the project' });
        }
        return done(true, false);
    },
    async editRequest(inputData, done) {
        try {
            await this.getDynamicModel(inputData);
            const inspectionData = inputData.body;
            const loginUser = inputData.user;
            const { recurrenceId } = inspectionData;
            let editSeriesRequests;
            let newRecurrenceId;
            let previousRecordInSeries;
            const projectSettingDetails = await Project.getProjectAndSettings({
                isDeleted: false,
                id: +inspectionData.ProjectId,
            });
            if (projectSettingDetails) {
                const requestData = await InspectionRequest.getSingleInspectionRequestData({
                    id: inspectionData.id,
                });
                if (inspectionData.seriesOption === 1) {
                    const requestArray = [];
                    requestArray.push({
                        ProjectId: inspectionData.ProjectId,
                        inspectionStart: inspectionData.inspectionStart,
                        inspectionEnd: inspectionData.inspectionEnd,
                        id: inspectionData.id,
                    });
                    const isOverlapping = await this.checkDoubleBookingAllowedOrNot(
                        requestArray,
                        projectSettingDetails,
                        'edit',
                        inspectionData.GateId,
                    );
                    if (isOverlapping && isOverlapping.error) {
                        return done(null, {
                            message: isOverlapping.message,
                        });
                    }
                }
                if (inspectionData.seriesOption === 2 || inspectionData.seriesOption === 3) {
                    let requestSeries = [];
                    if (inspectionData.seriesOption === 2) {
                        requestSeries = await InspectionRequest.findAll({
                            where: [
                                Sequelize.and({
                                    recurrenceId,
                                    id: {
                                        [Op.gte]: inspectionData.id,
                                    },
                                }),
                            ],
                        });
                    }
                    if (inspectionData.seriesOption === 3) {
                        requestSeries = await InspectionRequest.findAll({
                            where: [
                                Sequelize.and({
                                    recurrenceId,
                                    inspectionStart: {
                                        [Op.gte]: moment.tz(inspectionData.timeZone).utc().format('YYYY-MM-DD HH:mm:ssZ'),
                                    },
                                }),
                            ],
                        });
                    }
                    const requestArray = [];
                    for (let i = 0; i < requestSeries.length; i += 1) {
                        const inspectionStartDate = await this.convertTimezoneToUtc(
                            moment
                                .utc(requestSeries[i].inspectionStart)
                                .tz(inspectionData.timezone)
                                .format('MM/DD/YYYY'),
                            inspectionData.timezone,
                            inspectionData.inspectionStartTime,
                        );
                        const inspectionEndDate = await this.convertTimezoneToUtc(
                            moment
                                .utc(requestSeries[i].inspectionEnd)
                                .tz(inspectionData.timezone)
                                .format('MM/DD/YYYY'),
                            inspectionData.timezone,
                            inspectionData.inspectionEndTime,
                        );
                        requestArray.push({
                            ProjectId: inspectionData.ProjectId,
                            inspectionStart: !moment(inspectionStartDate).isSame(
                                moment(requestSeries[i].inspectionStart),
                            )
                                ? inspectionStartDate
                                : requestSeries[i].inspectionStart,
                            inspectionEnd: !moment(inspectionEndDate).isSame(moment(requestSeries[i].inspectionEnd))
                                ? inspectionEndDate
                                : requestSeries[i].inspectionEnd,
                            id: requestSeries[i].id,
                        });
                    }
                    const utcRecurrenceEndDate = requestData.recurrence.recurrenceEndDate;
                    const existingRecurrenceEndDate = moment(utcRecurrenceEndDate)
                        .tz(inspectionData.timezone)
                        .format('YYYY-MM-DD');
                    const newRecurrenceEndDate = inspectionData.recurrenceEndDate;
                    if (!moment(existingRecurrenceEndDate).isSame(moment(newRecurrenceEndDate))) {
                        const startDate = moment(existingRecurrenceEndDate).add(1, 'day');
                        const endDate = moment(newRecurrenceEndDate);
                        for (let date = startDate; date.isSameOrBefore(endDate); date.add(1, 'day')) {
                            requestArray.push({
                                ProjectId: inspectionData.ProjectId,
                                inspectionStart: await this.convertTimezoneToUtc(
                                    moment(date).format('MM/DD/YYYY'),
                                    inspectionData.timezone,
                                    inspectionData.inspectionStartTime,
                                ),
                                inspectionEnd: await this.convertTimezoneToUtc(
                                    moment(date).format('MM/DD/YYYY'),
                                    inspectionData.timezone,
                                    inspectionData.inspectionEndTime,
                                ),
                            });
                        }
                        if (requestArray.length > 0) {
                            const isOverlapping = await this.checkDoubleBookingAllowedOrNot(
                                requestArray,
                                projectSettingDetails,
                                'edit',
                                inspectionData.GateId,
                            );
                            if (isOverlapping && isOverlapping.error) {
                                return done(null, {
                                    message: isOverlapping.message,
                                });
                            }
                        }
                    } else if (requestArray.length > 0) {
                        const isOverlapping = await this.checkDoubleBookingAllowedOrNot(
                            requestArray,
                            projectSettingDetails,
                            'edit',
                            inspectionData.GateId,
                        );
                        if (isOverlapping && isOverlapping.error) {
                            return done(null, {
                                message: isOverlapping.message,
                            });
                        }
                    }
                }
            }
            const roleDetails = await Role.getBy('Project Admin');
            const accountRoleDetails = await Role.getBy('Account Admin');
            console.log("Processed =----------1")
            // This event
            if (inspectionData.seriesOption === 1) {
                editSeriesRequests = await InspectionRequest.findAll({
                    where: [
                        Sequelize.and({
                            id: inspectionData.id,
                        }),
                    ],
                });
                console.log(editSeriesRequests, "editSeriesRequests=====")
                if (editSeriesRequests && editSeriesRequests[0] && inspectionData.recurrenceId) {
                    const previousRecordInThisEventSeries = await InspectionRequest.findAll({
                        where: [
                            Sequelize.and({
                                recurrenceId,
                                id: {
                                    [Op.lt]: inspectionData.id,
                                },
                            }),
                        ],
                        order: [['id', 'DESC']],
                    });
                    console.log(previousRecordInThisEventSeries, "previousRecordInThisEventSeries======")
                    const NextSeriesLastRecord = await InspectionRequest.findAll({
                        where: [
                            Sequelize.and({
                                recurrenceId,
                                id: {
                                    [Op.gt]: inspectionData.id,
                                },
                            }),
                        ],
                        order: [['id', 'DESC']],
                    });
                    console.log(NextSeriesLastRecord, "previousRecordInThisEventSeries======")

                    if (
                        ((NextSeriesLastRecord && NextSeriesLastRecord.length > 0) ||
                            (previousRecordInThisEventSeries && previousRecordInThisEventSeries.length > 0)) &&
                        !(
                            NextSeriesLastRecord &&
                            NextSeriesLastRecord.length > 0 &&
                            previousRecordInThisEventSeries &&
                            previousRecordInThisEventSeries.length > 0
                        )
                    ) {
                        if (NextSeriesLastRecord && NextSeriesLastRecord.length > 0) {
                            const chosenTimezoneinspectionStart = moment.tz(
                                `${inspectionData.nextSeriesRecurrenceStartDate}  '00:00'`,
                                'YYYY-MM-DD HH:mm',
                                inspectionData.timezone,
                            );
                            const utcDate = chosenTimezoneinspectionStart
                                .clone()
                                .tz('UTC')
                                .format('YYYY-MM-DD HH:mm:ssZ');
                            await RequestRecurrenceSeries.update(
                                {
                                    recurrenceStartDate: utcDate,
                                },
                                {
                                    where: {
                                        id: NextSeriesLastRecord[0].recurrenceId,
                                    },
                                },
                            );
                        }
                        if (previousRecordInThisEventSeries && previousRecordInThisEventSeries.length > 0) {
                            // const chosenTimezoneinspectionStart = moment.tz(
                            //   `${previousRecordInThisEventSeries[0].inspectionData.previousSeriesRecurrenceEndDate}  '00:00'`,
                            //   'YYYY-MM-DD HH:mm',
                            //   inspectionData.timezone,
                            // );
                            // const utcDate = chosenTimezoneinspectionStart
                            //   .clone()
                            //   .tz('UTC')
                            //   .format('YYYY-MM-DD HH:mm:ssZ');
                            await RequestRecurrenceSeries.update(
                                {
                                    recurrenceEndDate: previousRecordInThisEventSeries[0].inspectionStart,
                                },
                                {
                                    where: {
                                        id: previousRecordInThisEventSeries[0].recurrenceId,
                                    },
                                },
                            );
                        }
                    }
                }
            }
            // This and all following events
            if (inspectionData.seriesOption === 2) {
                editSeriesRequests = await InspectionRequest.findAll({
                    where: [
                        Sequelize.and({
                            recurrenceId,
                            id: {
                                [Op.gte]: inspectionData.id,
                            },
                        }),
                    ],
                });
                previousRecordInSeries = await InspectionRequest.findOne({
                    where: [
                        Sequelize.and({
                            recurrenceId,
                            id: {
                                [Op.lt]: inspectionData.id,
                            },
                        }),
                    ],
                    order: [['id', 'DESC']],
                });
            }
            // All events in the series
            if (inspectionData.seriesOption === 3) {
                editSeriesRequests = await InspectionRequest.findAll({
                    where: [
                        Sequelize.and({
                            recurrenceId,
                            inspectionStart: {
                                [Op.gte]: moment.tz(inspectionData.timeZone).utc().format('YYYY-MM-DD HH:mm:ssZ'),
                            },
                        }),
                    ],
                });
            }

            if (editSeriesRequests && editSeriesRequests[0] && editSeriesRequests.length > 0) {
                console.log("Getting into edit series=======")
                const requestData = await InspectionRequest.getSingleInspectionRequestData({
                    id: editSeriesRequests[0].id,
                });
                if (requestData && requestData.recurrence) {
                    requestData.recurrence.ParentCompanyId = inspectionData.ParentCompanyId;
                    requestData.recurrence.ProjectId = inspectionData.ProjectId;
                    if (inspectionData.seriesOption === 1) {
                        requestData.recurrence.inspectionStart = inspectionData.recurrenceSeriesStartDate;
                        requestData.recurrence.inspectionEnd = inspectionData.recurrenceSeriesEndDate;
                    }
                    if (inspectionData.seriesOption === 2) {
                        requestData.recurrence.inspectionStart = inspectionData.recurrenceSeriesStartDate;
                        requestData.recurrence.inspectionEnd = inspectionData.recurrenceEndDate;
                    }
                    if (inspectionData.seriesOption === 2 && previousRecordInSeries) {
                        newRecurrenceId = await concreteRequestService.insertRecurrenceSeries(
                            requestData.recurrence,
                            loginUser,
                            requestData.requestType,
                            inspectionData.timezone,
                        );
                    }
                }
                if (inspectionData.seriesOption === 2 || inspectionData.seriesOption === 3) {
                    const utcRecurrenceEndDate = requestData.recurrence.recurrenceEndDate;
                    let existingRecurrenceEndDate = moment(utcRecurrenceEndDate)
                        .tz(inspectionData.timezone)
                        .format('YYYY-MM-DD');
                    const newRecurrenceEndDate = inspectionData.recurrenceEndDate;
                    if (!moment(existingRecurrenceEndDate).isSame(moment(newRecurrenceEndDate))) {
                        const dates = [];
                        const chosenTimezoneinspectionStart = moment.tz(
                            `${inspectionData.recurrenceEndDate}  '00:00'`,
                            'YYYY-MM-DD HH:mm',
                            inspectionData.timezone,
                        );
                        const utcDate = chosenTimezoneinspectionStart
                            .clone()
                            .tz('UTC')
                            .format('YYYY-MM-DD HH:mm:ssZ');
                        await RequestRecurrenceSeries.update(
                            {
                                recurrenceEndDate: utcDate,
                            },
                            {
                                where: {
                                    id: inspectionData.recurrenceId,
                                },
                            },
                        );
                        while (moment(existingRecurrenceEndDate).isBefore(moment(newRecurrenceEndDate))) {
                            existingRecurrenceEndDate = moment(existingRecurrenceEndDate).add(1, 'day');
                            dates.push(moment(existingRecurrenceEndDate).format('MM/DD/YYYY'));
                        }
                        await this.createCopyofInspectionRequest(
                            requestData,
                            inspectionData,
                            dates,
                            loginUser,
                            newRecurrenceId || inspectionData.recurrenceId,
                        );
                    }
                }
                if (inspectionData.seriesOption === 2 && previousRecordInSeries) {
                    // const chosenTimezoneinspectionStart = moment.tz(
                    //   `${inspectionData.previousSeriesRecurrenceEndDate}  '00:00'`,
                    //   'YYYY-MM-DD HH:mm',
                    //   inspectionData.timezone,
                    // );
                    // const utcDate = chosenTimezoneinspectionStart
                    //   .clone()
                    //   .tz('UTC')
                    //   .format('YYYY-MM-DD HH:mm:ssZ');
                    await RequestRecurrenceSeries.update(
                        {
                            recurrenceEndDate: previousRecordInSeries.inspectionStart,
                        },
                        {
                            where: {
                                id: previousRecordInSeries.recurrenceId,
                            },
                        },
                    );
                }
            }
            for (let indexLoop = 0; indexLoop < editSeriesRequests.length; indexLoop += 1) {
                const seriesData = editSeriesRequests[indexLoop];
                const idDetails = await InspectionRequest.findOne({
                    where: [
                        Sequelize.and({
                            id: seriesData.id,
                        }),
                    ],
                });
                if (!idDetails) {
                    return done(null, { message: 'Inspection Booking id is not available' });
                }
                const existsInspectionRequest = await InspectionRequest.getSingleInspectionRequestData({
                    id: +idDetails.id,
                });
                if (existsInspectionRequest) {
                    await this.checkInputDatas(inputData, async (checkResponse, checkError) => {
                        if (checkError) {
                            return done(null, checkError);
                        }
                        const memberData = await Member.getBy({
                            UserId: loginUser.id,
                            ProjectId: inspectionData.ProjectId,
                        });
                        const history = {
                            InspectionRequestId: idDetails.id,
                            InspectionId: idDetails.InspectionId,
                            MemberId: memberData.id,
                            type: 'edit',
                            description: `${loginUser.firstName} ${loginUser.lastName} Edited this Inspection Booking.`,
                        };
                        const notification = history;
                        let lastData = {};
                        lastData = await CraneRequest.findOne({
                            where: { ProjectId: +inspectionData.ProjectId, isDeleted: false },
                            order: [['CraneRequestId', 'DESC']],
                        });
                        const InspectionRequestList = await InspectionRequest.findOne({
                            where: {
                                ProjectId: +inspectionData.ProjectId,
                                isDeleted: false,
                                isAssociatedWithCraneRequest: true,
                            },
                            order: [['CraneRequestId', 'DESC']],
                        });
                        if (InspectionRequestList) {
                            if (lastData) {
                                if (InspectionRequestList.CraneRequestId > lastData.CraneRequestId) {
                                    lastData.CraneRequestId = InspectionRequestList.CraneRequestId;
                                }
                            } else {
                                lastData = {};
                                lastData.CraneRequestId = InspectionRequestList.CraneRequestId;
                            }
                        }
                        if (lastData) {
                            const data = lastData.CraneRequestId;
                            lastData.CraneRequestId = 0;
                            lastData.CraneRequestId = data + 1;
                        } else {
                            lastData = {};
                            lastData.CraneRequestId = 1;
                        }
                        let craneId = 0;
                        const newId = JSON.parse(JSON.stringify(lastData));
                        if (newId && newId.CraneRequestId !== null && newId.CraneRequestId !== undefined) {
                            craneId = newId.CraneRequestId;
                        }
                        const InspectionParam = {
                            description: inspectionData.description,
                            escort: inspectionData.escort,
                            vehicleDetails: inspectionData.vehicleDetails,
                            notes: inspectionData.notes,
                            isAssociatedWithCraneRequest: inspectionData.isAssociatedWithCraneRequest,
                            requestType: inspectionData.requestType,
                            cranePickUpLocation: inspectionData.cranePickUpLocation,
                            craneDropOffLocation: inspectionData.craneDropOffLocation,
                            // CraneRequestId: inspectionData.CraneRequestId,
                            recurrenceId: inspectionData.seriesOption !== 1 ? newRecurrenceId : null,
                            LocationId: inspectionData.LocationId,
                            inspectionType: inspectionData.inspectionType,
                            OriginationAddress: inspectionData.originationAddress,
                            vehicleType: inspectionData.vehicleType
                        };
                        if (
                            !idDetails.CraneRequestId &&
                            inspectionData.requestType === 'InspectionRequestWithCrane'
                        ) {
                            InspectionParam.CraneRequestId = craneId;
                        }
                        if (inspectionData.seriesOption === 1) {
                            InspectionParam.inspectionStart = inspectionData.inspectionStart;
                            InspectionParam.inspectionEnd = inspectionData.inspectionEnd;
                        }
                        if (inspectionData.seriesOption === 2 || inspectionData.seriesOption === 3) {
                            const utcinspectionStartTimestamp = moment.utc(idDetails.inspectionStart);
                            const localStartTimestamp = utcinspectionStartTimestamp.tz(inspectionData.timezone);
                            const utcinspectionEndTimestamp = moment.utc(idDetails.inspectionEnd);
                            const localEndTimestamp = utcinspectionEndTimestamp.tz(inspectionData.timezone);
                            InspectionParam.inspectionStart = await this.convertTimezoneToUtc(
                                moment(localStartTimestamp).format('MM/DD/YYYY'),
                                inspectionData.timezone,
                                inspectionData.inspectionStartTime,
                            );
                            InspectionParam.inspectionEnd = await this.convertTimezoneToUtc(
                                moment(localEndTimestamp).format('MM/DD/YYYY'),
                                inspectionData.timezone,
                                inspectionData.inspectionEndTime,
                            );
                        }

                        if (
                            ((memberData.RoleId === 2 || memberData.RoleId === 1) &&
                                idDetails.status === 'Approved') ||
                            memberData.isAutoApproveEnabled ||
                            projectSettingDetails.ProjectSettings.isAutoApprovalEnabled
                        ) {
                            InspectionParam.status = 'Approved';
                            InspectionParam.approvedBy = memberData.id;
                            InspectionParam.approved_at = new Date();
                        }

                        console.log(InspectionParam, "InspectionParam=====")
                        await InspectionRequest.update(InspectionParam, {
                            where: { id: idDetails.id },
                        });
                        const { companies, persons, define } = inspectionData;
                        const gates = [inspectionData.GateId];
                        const equipments = inspectionData.EquipmentId;
                        const condition = Sequelize.and({
                            ProjectId: inspectionData.ProjectId,
                            InspectionId: idDetails.id,
                        });
                        const updateParam = {
                            InspectionId: idDetails.id,
                            InspectionCode: idDetails.InspectionId,
                            ProjectId: inspectionData.ProjectId,
                            isDeleted: false,
                            isActive: true,
                        };
                        const existCompanies = await InspectionCompany.findAll({ where: condition });
                        const existGate = await InspectionGate.findAll({ where: condition });
                        const existEquipment = await InspectionEquipment.findAll({ where: condition });
                        const existPerson = await InspectionPerson.findAll({ where: condition });
                        const existDefine = await DeliverDefine.findAll({ where: condition });
                        await this.updateValues(condition, async (response, error) => {
                            if (!error) {
                                const addedCompany = [];
                                companies.forEach(async (element, i) => {
                                    const index = existCompanies.findIndex((item) => item.CompanyId === element);
                                    const companyParam = updateParam;
                                    companyParam.CompanyId = element;
                                    if (index !== -1) {
                                        await InspectionCompany.update(companyParam, {
                                            where: { id: existCompanies[index].id },
                                        });
                                        if (existCompanies[index].isDeleted !== false) {
                                            addedCompany.push(existCompanies[index]);
                                        }
                                    } else {
                                        const newCompanyData = await InspectionCompany.createInstance(companyParam);
                                        addedCompany.push(newCompanyData);
                                    }
                                });
                                const addedGate = [];
                                gates.forEach(async (element, i) => {
                                    const index = existGate.findIndex((item) => item.GateId === element);
                                    const gateParam = updateParam;
                                    gateParam.GateId = element;
                                    if (index !== -1) {
                                        await InspectionGate.update(gateParam, { where: { id: existGate[index].id } });
                                        if (existGate[index].isDeleted !== false) {
                                            addedGate.push(existGate[index]);
                                        }
                                    } else {
                                        const newGateData = await InspectionGate.createInstance(gateParam);
                                        addedGate.push(newGateData);
                                    }
                                });
                                const addedEquipment = [];
                                equipments.forEach(async (element, i) => {
                                    const index = existEquipment.findIndex((item) => item.EquipmentId === element);
                                    const equipmentParam = updateParam;
                                    equipmentParam.EquipmentId = element;
                                    if (index !== -1) {
                                        await InspectionEquipment.update(equipmentParam, {
                                            where: { id: existEquipment[index].id },
                                        });
                                        if (existEquipment[index].isDeleted !== false) {
                                            addedEquipment.push(existEquipment[index]);
                                        }
                                    } else {
                                        const newEquipmentData = await InspectionEquipment.createInstance(equipmentParam);
                                        addedEquipment.push(newEquipmentData);
                                    }
                                });
                                const addedPerson = [];
                                persons.forEach(async (element, i) => {
                                    const index = existPerson.findIndex((item) => item.MemberId === element);
                                    const memberParam = updateParam;
                                    memberParam.MemberId = element;
                                    if (index !== -1) {
                                        await InspectionPerson.update(memberParam, {
                                            where: { id: existPerson[index].id },
                                        });
                                        if (existPerson[index].isDeleted !== false) {
                                            addedPerson.push(existPerson[index]);
                                        }
                                    } else {
                                        const newPersonData = await InspectionPerson.createInstance(memberParam);
                                        addedPerson.push(newPersonData);
                                    }
                                });
                                const addedDefineData = [];
                                define.forEach(async (element, i) => {
                                    const index = existDefine.findIndex(
                                        (item) => item.DeliverDefineWorkId === element,
                                    );
                                    const defineParam = updateParam;
                                    defineParam.DeliverDefineWorkId = element;
                                    if (index !== -1) {
                                        await DeliverDefine.update(defineParam, {
                                            where: { id: existDefine[index].id },
                                        });
                                        if (existDefine[index].isDeleted !== false) {
                                            addedDefineData.push(existDefine[index]);
                                        }
                                    } else {
                                        const newDefineData = await DeliverDefine.createInstance(defineParam);
                                        addedDefineData.push(newDefineData);
                                    }
                                });
                                const locationChosen = await Locations.findOne({
                                    where: {
                                        ProjectId: inspectionData.ProjectId,
                                        id: inspectionData.LocationId,
                                    },
                                });
                                history.description = `${loginUser.firstName} ${loginUser.lastName} Updated the Inspection Booking, ${inspectionData.description}`;
                                history.locationFollowDescription = `${loginUser.firstName} ${loginUser.lastName} Updated the Inspection Booking, ${inspectionData.description}. Location: ${locationChosen.locationPath}.`;
                                history.MemberId = memberData.id;
                                history.firstName = loginUser.firstName;
                                history.profilePic = loginUser.profilePic;
                                history.createdAt = new Date();
                                history.ProjectId = inspectionData.ProjectId;
                                const projectDetails = await Project.findByPk(inspectionData.ProjectId);
                                history.projectName = projectDetails.projectName;
                                notification.ProjectId = idDetails.ProjectId;
                                notification.title = `Inspection Booking Updated by ${loginUser.firstName} ${loginUser.lastName}`;
                                if (
                                    existsInspectionRequest &&
                                    existsInspectionRequest.recurrence &&
                                    existsInspectionRequest.recurrence.recurrence
                                ) {
                                    notification.recurrenceType = `${existsInspectionRequest.recurrence.recurrence
                                        } From ${moment(existsInspectionRequest.recurrence.recurrenceStartDate).format(
                                            'MM/DD/YYYY',
                                        )} to ${moment(existsInspectionRequest.recurrence.recurrenceEndDate).format(
                                            'MM/DD/YYYY',
                                        )}`;
                                }
                                notification.requestType = 'InspectionRequest';
                                const newNotification = await Notification.createInstance(notification);
                                const memberLocationPreference = await LocationNotificationPreferences.findAll({
                                    where: {
                                        ProjectId: inspectionData.ProjectId,
                                        LocationId: inspectionData.LocationId,
                                        follow: true,
                                    },
                                    include: [
                                        {
                                            association: 'Member',
                                            attributes: ['id', 'RoleId'],
                                            where: {
                                                [Op.and]: [
                                                    {
                                                        id: { [Op.ne]: memberData.id },
                                                    },
                                                ],
                                            },
                                            include: [
                                                {
                                                    association: 'User',
                                                    attributes: ['id', 'firstName', 'lastName', 'email'],
                                                },
                                            ],
                                        },
                                    ],
                                });
                                const locationFollowMembers = [];
                                memberLocationPreference.forEach(async (element) => {
                                    locationFollowMembers.push(element.Member.id);
                                });
                                const adminData = await Member.findAll({
                                    where: {
                                        [Op.and]: [
                                            { ProjectId: inspectionData.ProjectId },
                                            { isDeleted: false },
                                            { id: { [Op.in]: persons } },
                                            { id: { [Op.ne]: newNotification.MemberId } },
                                            { id: { [Op.notIn]: locationFollowMembers } },
                                        ],
                                    },
                                    include: [
                                        {
                                            association: 'User',
                                            attributes: ['id', 'firstName', 'lastName', 'email'],
                                        },
                                    ],
                                    attributes: ['id', 'RoleId'],
                                });
                                if (memberLocationPreference && memberLocationPreference.length > 0) {
                                    // here 5-(NotificationPreferenceItemId -When a inspection/crane/concrete request is edited)
                                    await pushNotification.sendMemberLocationPreferencePushNotification(
                                        memberLocationPreference,
                                        inspectionData.InspectionRequestId,
                                        history.locationFollowDescription,
                                        inspectionData.requestType,
                                        inspectionData.ProjectId,
                                        inspectionData.id,
                                        5,
                                    );
                                    // here 5-(NotificationPreferenceItemId -When a inspection/crane/concrete request is edited)
                                    await notificationHelper.createMemberDeliveryLocationInAppNotification(
                                        DeliveryPersonNotification,
                                        inspectionData.ProjectId,
                                        newNotification.id,
                                        memberLocationPreference,
                                        5,
                                    );
                                }
                                history.adminData = adminData;
                                if (memberLocationPreference && memberLocationPreference.length > 0) {
                                    history.memberData = [];
                                    history.memberData.push(...memberLocationPreference);
                                }
                                // here 5-(NotificationPreferenceItemId -When a inspection/crane/concrete request is edited)
                                await notificationHelper.createDeliveryPersonNotification(
                                    adminData,
                                    [],
                                    projectDetails,
                                    newNotification,
                                    DeliveryPersonNotification,
                                    memberData,
                                    loginUser,
                                    5,
                                    'updated a',
                                    'Inspection Request',
                                    `Inspection Booking (${idDetails.InspectionId} - ${idDetails.description})`,
                                    idDetails.id,
                                );
                                const checkMemberNotification = await NotificationPreference.findAll({
                                    where: {
                                        ProjectId: inspectionData.ProjectId,
                                        isDeleted: false,
                                    },
                                    attributes: [
                                        'id',
                                        'MemberId',
                                        'ProjectId',
                                        'ParentCompanyId',
                                        'NotificationPreferenceItemId',
                                        'instant',
                                        'dailyDigest',
                                    ],
                                    include: [
                                        {
                                            association: 'NotificationPreferenceItem',
                                            where: {
                                                id: 5,
                                                isDeleted: false,
                                            },
                                            attributes: ['id', 'description', 'inappNotification', 'emailNotification'],
                                        },
                                    ],
                                });
                                history.notificationPreference = checkMemberNotification;
                                const updatedInspectionRequest = await InspectionRequest.getSingleInspectionRequestData({
                                    id: +idDetails.id,
                                });
                                if (
                                    updatedInspectionRequest.status === 'Approved' &&
                                    idDetails.status !== 'Approved' &&
                                    idDetails.isQueued === false
                                ) {
                                    const object = {
                                        ProjectId: inspectionData.ProjectId,
                                        MemberId: memberData.id,
                                        InspectionRequestId: updatedInspectionRequest.id,
                                        isDeleted: false,
                                        type: 'approved',
                                        description: history.description,
                                    };
                                    await InspectionHistory.createInstance(object);
                                }
                                if (!inspectionData.ndrStatus) {
                                    await this.updateEditInspectionRequestHistory(
                                        inspectionData,
                                        existsInspectionRequest,
                                        updatedInspectionRequest,
                                        history,
                                        loginUser,
                                    );
                                    // here 5-(NotificationPreferenceItemId -When a inspection/crane/concrete request is edited)
                                    await pushNotification.sendDeviceTokenForInspection(history, 5, inspectionData.ProjectId);
                                }
                                const editedNDR = await InspectionRequest.getNDRData({
                                    id: idDetails.id,
                                });
                                if (editedNDR.isQueued === true) {
                                    if (
                                        editedNDR.description &&
                                        editedNDR.inspectionStart &&
                                        editedNDR.inspectionEnd &&
                                        editedNDR.memberDetails.length > 0 &&
                                        editedNDR.companyDetails.length > 0 &&
                                        // editedNDR.defineWorkDetails.length > 0 &&
                                        editedNDR.gateDetails.length > 0 &&
                                        editedNDR.equipmentDetails.length > 0 &&
                                        editedNDR.escort !== null
                                    ) {
                                        await InspectionRequest.update(
                                            { isAllDetailsFilled: true },
                                            {
                                                where: { id: editedNDR.id },
                                            },
                                        );
                                        if (inspectionData.updateQueuedRequest === 1) {
                                            const queuedRequestPayload = {
                                                isQueued: false,
                                            };
                                            if (
                                                editedNDR.isQueued === true &&
                                                (memberData.RoleId === roleDetails.id ||
                                                    memberData.RoleId === accountRoleDetails.id ||
                                                    memberData.isAutoApproveEnabled ||
                                                    projectSettingDetails.ProjectSettings.isAutoApprovalEnabled)
                                            ) {
                                                queuedRequestPayload.status = 'Approved';
                                                queuedRequestPayload.approvedBy = memberData.id;
                                                queuedRequestPayload.approved_at = new Date();
                                                const historyUpdateObject = {
                                                    ProjectId: +inspectionData.ProjectId,
                                                    MemberId: memberData.id,
                                                    InspectionRequestId: editedNDR.id,
                                                    isDeleted: false,
                                                    type: 'approved',
                                                    description: `${loginUser.firstName} ${loginUser.lastName} Approved Inspection Booking, ${editedNDR.description}.`,
                                                };
                                                await InspectionHistory.createInstance(historyUpdateObject);
                                            }
                                            await InspectionRequest.update(queuedRequestPayload, {
                                                where: { id: editedNDR.id },
                                            });
                                        }
                                    }
                                }
                                if (!inspectionData.ndrStatus) {
                                    let tagsUpdated = false;
                                    let fieldsChanged = false;
                                    if (
                                        editedNDR.defineWorkDetails?.length > 0 &&
                                        existsInspectionRequest.defineWorkDetails?.length > 0
                                    ) {
                                        const addedDfow1 = editedNDR.defineWorkDetails.filter((el) => {
                                            return !existsInspectionRequest.defineWorkDetails.find((element) => {
                                                return element.id === el.id;
                                            });
                                        });
                                        const deletedDfow1 = existsInspectionRequest.defineWorkDetails.filter((el) => {
                                            return !existsInspectionRequest.defineWorkDetails.find((element) => {
                                                return element.id === el.id;
                                            });
                                        });
                                        if (addedDfow1.length > 0) {
                                            tagsUpdated = true;
                                        }
                                        if (deletedDfow1.length > 0) {
                                            tagsUpdated = true;
                                        }
                                    }
                                    if (
                                        editedNDR.gateDetails.length > 0 &&
                                        existsInspectionRequest.gateDetails.length > 0
                                    ) {
                                        const addedGate1 = editedNDR.gateDetails.filter((el) => {
                                            return !existsInspectionRequest.gateDetails.find((element) => {
                                                return element.Gate.id === el.Gate.id;
                                            });
                                        });
                                        const deletedGate1 = existsInspectionRequest.gateDetails.filter((el) => {
                                            return !existsInspectionRequest.gateDetails.find((element) => {
                                                return element.Gate.id === el.Gate.id;
                                            });
                                        });
                                        if (addedGate1.length > 0) {
                                            tagsUpdated = true;
                                        }
                                        if (deletedGate1.length > 0) {
                                            tagsUpdated = true;
                                        }
                                    }
                                    if (
                                        editedNDR.equipmentDetails.length > 0 &&
                                        existsInspectionRequest.equipmentDetails.length > 0
                                    ) {
                                        const addedEquipment1 = editedNDR.equipmentDetails.filter((el) => {
                                            return !existsInspectionRequest.equipmentDetails.find((element) => {
                                                return element.Equipment.id === el.Equipment.id;
                                            });
                                        });
                                        const deletedEquipment1 = existsInspectionRequest.equipmentDetails.filter(
                                            (el) => {
                                                return !existsInspectionRequest.equipmentDetails.find((element) => {
                                                    return element.Equipment.id === el.Equipment.id;
                                                });
                                            },
                                        );
                                        if (addedEquipment1.length > 0) {
                                            tagsUpdated = true;
                                        }
                                        if (deletedEquipment1.length > 0) {
                                            tagsUpdated = true;
                                        }
                                    }
                                    if (
                                        editedNDR.companyDetails.length > 0 &&
                                        existsInspectionRequest.companyDetails.length > 0
                                    ) {
                                        const addedCompany1 = editedNDR.companyDetails.filter((el) => {
                                            return !existsInspectionRequest.companyDetails.find((element) => {
                                                return element.Company.id === el.Company.id;
                                            });
                                        });
                                        const deletedCompany1 = existsInspectionRequest.companyDetails.filter((el) => {
                                            return !existsInspectionRequest.companyDetails.find((element) => {
                                                return element.Company.id === el.Company.id;
                                            });
                                        });
                                        if (addedCompany1.length > 0) {
                                            tagsUpdated = true;
                                        }
                                        if (deletedCompany1.length > 0) {
                                            tagsUpdated = true;
                                        }
                                    }
                                    if (
                                        editedNDR.memberDetails.length > 0 &&
                                        existsInspectionRequest.memberDetails.length > 0
                                    ) {
                                        const addedMember1 = editedNDR.memberDetails.filter((el) => {
                                            return !existsInspectionRequest.memberDetails.find((element) => {
                                                return element.Member.id === el.Member.id;
                                            });
                                        });
                                        const deletedMember1 = existsInspectionRequest.memberDetails.filter((el) => {
                                            return !editedNDR.memberDetails.find((element) => {
                                                return element.Member.id === el.Member.id;
                                            });
                                        });
                                        if (addedMember1.length > 0) {
                                            tagsUpdated = true;
                                        }
                                        if (deletedMember1.length > 0) {
                                            tagsUpdated = true;
                                        }
                                    }
                                    if (
                                        existsInspectionRequest.description !== editedNDR.description ||
                                        existsInspectionRequest.CraneRequestId !== editedNDR.CraneRequestId ||
                                        existsInspectionRequest.LocationId !== editedNDR.LocationId ||
                                        existsInspectionRequest.requestType !== editedNDR.requestType ||
                                        existsInspectionRequest.vehicleDetails !== editedNDR.vehicleDetails ||
                                        existsInspectionRequest.notes !== editedNDR.notes ||
                                        existsInspectionRequest.isAssociatedWithCraneRequest !==
                                        editedNDR.isAssociatedWithCraneRequest ||
                                        existsInspectionRequest.escort !== editedNDR.escort ||
                                        existsInspectionRequest.craneDropOffLocation !== editedNDR.craneDropOffLocation ||
                                        existsInspectionRequest.cranePickUpLocation !== editedNDR.cranePickUpLocation ||
                                        tagsUpdated ||
                                        existsInspectionRequest.recurrence !== editedNDR.recurrence ||
                                        existsInspectionRequest.chosenDateOfMonth !== editedNDR.chosenDateOfMonth ||
                                        existsInspectionRequest.dateOfMonth !== editedNDR.dateOfMonth ||
                                        existsInspectionRequest.monthlyRepeatType !== editedNDR.monthlyRepeatType ||
                                        existsInspectionRequest.days !== editedNDR.days ||
                                        existsInspectionRequest.repeatEveryType !== editedNDR.repeatEveryType ||
                                        existsInspectionRequest.repeatEveryCount !== editedNDR.repeatEveryCount
                                    ) {
                                        fieldsChanged = true;
                                    }
                                    let inspectionDateTimeChanged = false;
                                    if (
                                        moment(existsInspectionRequest.inspectionStart).format('h:mm a') !==
                                        moment(editedNDR.inspectionStart).format('h:mm a') ||
                                        moment(existsInspectionRequest.inspectionEnd).format('h:mm a') !==
                                        moment(editedNDR.inspectionEnd).format('h:mm a')
                                    ) {
                                        inspectionDateTimeChanged = true;
                                    }
                                    if (existsInspectionRequest.status === 'Inspectioned') {
                                        if (fieldsChanged && memberData.RoleId === 2) {
                                            await InspectionRequest.update(
                                                { status: 'Approved' },
                                                {
                                                    where: { id: editedNDR.id },
                                                },
                                            );
                                        }
                                        if (
                                            (fieldsChanged || inspectionDateTimeChanged) &&
                                            memberData.RoleId !== 2 &&
                                            !memberData.isAutoApproveEnabled &&
                                            !projectSettingDetails.ProjectSettings.isAutoApprovalEnabled
                                        ) {
                                            await InspectionRequest.update(
                                                { status: 'Pending', inspectionStatus: '' },
                                                {
                                                    where: { id: editedNDR.id },
                                                },
                                            );
                                        }
                                    }
                                    if (existsInspectionRequest.status === 'Approved') {
                                        if (
                                            (fieldsChanged || inspectionDateTimeChanged) &&
                                            memberData.RoleId !== 2 &&
                                            !memberData.isAutoApproveEnabled &&
                                            !projectSettingDetails.ProjectSettings.isAutoApprovalEnabled
                                        ) {
                                            await InspectionRequest.update(
                                                { status: 'Pending', inspectionStatus: '' },
                                                {
                                                    where: { id: editedNDR.id },
                                                },
                                            );
                                        }
                                        if (
                                            ((fieldsChanged || inspectionDateTimeChanged) && memberData.RoleId === 2) ||
                                            memberData.isAutoApproveEnabled ||
                                            projectSettingDetails.ProjectSettings.isAutoApprovalEnabled
                                        ) {
                                            await InspectionRequest.update(
                                                { status: 'Approved' },
                                                {
                                                    where: { id: editedNDR.id },
                                                },
                                            );
                                        }
                                    }
                                    if (
                                        existsInspectionRequest.status === 'Expired' ||
                                        existsInspectionRequest.status === 'Declined'
                                    ) {
                                        if (
                                            (fieldsChanged || inspectionDateTimeChanged) &&
                                            memberData.RoleId !== 2 &&
                                            !memberData.isAutoApproveEnabled &&
                                            !projectSettingDetails.ProjectSettings.isAutoApprovalEnabled
                                        ) {
                                            await InspectionRequest.update(
                                                { status: 'Pending', inspectionStatus: '' },
                                                {
                                                    where: { id: editedNDR.id },
                                                },
                                            );
                                        }
                                        if (
                                            ((fieldsChanged || inspectionDateTimeChanged) && memberData.RoleId === 2) ||
                                            memberData.isAutoApproveEnabled ||
                                            projectSettingDetails.ProjectSettings.isAutoApprovalEnabled
                                        ) {
                                            const isStatusDataUpdated = await InspectionRequest.update(
                                                { status: 'Approved', approvedBy: memberData.id, approved_at: new Date() },
                                                {
                                                    where: { id: editedNDR.id },
                                                },
                                            );
                                            if (isStatusDataUpdated) {
                                                const object = {
                                                    ProjectId: inspectionData.ProjectId,
                                                    MemberId: memberData.id,
                                                    InspectionRequestId: updatedInspectionRequest.id,
                                                    isDeleted: false,
                                                    type: 'approved',
                                                    description: `${loginUser.firstName} ${loginUser.lastName} Approved the Inspection Booking, ${inspectionData.description}`,
                                                };
                                                await InspectionHistory.createInstance(object);
                                            }
                                        }
                                    }
                                    if (existsInspectionRequest.status === 'Pending') {
                                        if (
                                            (fieldsChanged || inspectionDateTimeChanged) &&
                                            memberData.RoleId !== 2 &&
                                            (memberData.isAutoApproveEnabled ||
                                                projectSettingDetails.ProjectSettings.isAutoApprovalEnabled)
                                        ) {
                                            const isStatusDataUpdated = await InspectionRequest.update(
                                                { status: 'Approved' },
                                                {
                                                    where: { id: editedNDR.id },
                                                },
                                            );
                                            if (isStatusDataUpdated) {
                                                const object = {
                                                    ProjectId: inspectionData.ProjectId,
                                                    MemberId: memberData.id,
                                                    InspectionRequestId: updatedInspectionRequest.id,
                                                    isDeleted: false,
                                                    type: 'approved',
                                                    description: `${loginUser.firstName} ${loginUser.lastName} Approved the Inspection Request, ${inspectionData.description}`,
                                                };
                                                await InspectionHistory.createInstance(object);
                                            }
                                        }
                                        if (
                                            ((fieldsChanged || inspectionDateTimeChanged) && memberData.RoleId === 2) ||
                                            memberData.isAutoApproveEnabled ||
                                            projectSettingDetails.ProjectSettings.isAutoApprovalEnabled
                                        ) {
                                            const isStatusDataUpdated = await InspectionRequest.update(
                                                { status: 'Approved', approvedBy: memberData.id, approved_at: new Date() },
                                                {
                                                    where: { id: editedNDR.id },
                                                },
                                            );
                                            if (isStatusDataUpdated) {
                                                const object = {
                                                    ProjectId: inspectionData.ProjectId,
                                                    MemberId: memberData.id,
                                                    InspectionRequestId: updatedInspectionRequest.id,
                                                    isDeleted: false,
                                                    type: 'approved',
                                                    description: `${loginUser.firstName} ${loginUser.lastName} Approved the Inspection Request, ${inspectionData.description}`,
                                                };
                                                await InspectionHistory.createInstance(object);
                                            }
                                        }
                                    }
                                    if (+memberData.RoleId === 4 || +memberData.RoleId === 3) {
                                        const userEmails = await this.getMemberDetailData(
                                            history,
                                            memberLocationPreference,
                                        );
                                        if (userEmails.length > 0) {
                                            userEmails.forEach(async (element) => {
                                                if (element.RoleId === 2) {
                                                    let name;
                                                    if (!element.firstName) {
                                                        name = 'user';
                                                    } else {
                                                        name = `${element.firstName} ${element.lastName}`;
                                                    }
                                                    const memberRole = await Role.findOne({
                                                        where: {
                                                            id: memberData.RoleId,
                                                            isDeleted: false,
                                                        },
                                                    });
                                                    const mailPayload = {
                                                        name,
                                                        email: element.email,
                                                        content: `We would like to inform you that 
                          ${loginUser.firstName} ${loginUser.lastName} - ${memberRole.roleName} has updated a Inspection booking ${idDetails.InspectionId} and waiting for your approval.Kindly review the booking and update the status.`,
                                                    };
                                                    const isMemberFollowLocation =
                                                        await LocationNotificationPreferences.findOne({
                                                            where: {
                                                                MemberId: +element.MemberId,
                                                                ProjectId: +inspectionData.ProjectId,
                                                                LocationId: +inspectionData.LocationId,
                                                                isDeleted: false,
                                                                // follow: true,
                                                            },
                                                        });
                                                    if (isMemberFollowLocation) {
                                                        const memberNotification = await NotificationPreference.findOne({
                                                            where: {
                                                                MemberId: +element.MemberId,
                                                                ProjectId: +inspectionData.ProjectId,
                                                                isDeleted: false,
                                                            },
                                                            include: [
                                                                {
                                                                    association: 'NotificationPreferenceItem',
                                                                    where: {
                                                                        id: 9,
                                                                        isDeleted: false,
                                                                    },
                                                                },
                                                            ],
                                                        });
                                                        if (memberNotification && memberNotification.instant) {
                                                            await MAILER.sendMail(
                                                                mailPayload,
                                                                'notifyPAForReApproval',
                                                                `Inspection Booking updated by ${loginUser.firstName} ${loginUser.lastName} - ${memberRole.roleName}`,
                                                                `Inspection Booking updated by ${loginUser.firstName} ${loginUser.lastName} - ${memberRole.roleName}`,
                                                                async (info, err) => {
                                                                    console.log(info, err);
                                                                },
                                                            );
                                                        }
                                                        if (memberNotification && memberNotification.dailyDigest) {
                                                            await this.createDailyDigestDataApproval(
                                                                +memberData.RoleId,
                                                                +element.MemberId,
                                                                +inspectionData.ProjectId,
                                                                +inspectionData.ParentCompanyId,
                                                                loginUser,
                                                                'updated a',
                                                                'Inspection Request',
                                                                `Inspection Booking (${idDetails.InspectionId} - ${idDetails.description})`,
                                                                'and waiting for your approval',
                                                                idDetails.id,
                                                            );
                                                        }
                                                    }
                                                }
                                            });

                                            const exist2 = await InspectionRequest.findOne({
                                                include: [
                                                    {
                                                        association: 'memberDetails',
                                                        required: false,
                                                        where: { isDeleted: false, isActive: true },
                                                        attributes: ['id'],
                                                        include: [
                                                            {
                                                                association: 'Member',
                                                                attributes: ['id', 'isGuestUser'],
                                                                include: [
                                                                    {
                                                                        association: 'User',
                                                                        attributes: [
                                                                            'email',
                                                                            'phoneCode',
                                                                            'phoneNumber',
                                                                            'firstName',
                                                                            'lastName',
                                                                        ],
                                                                    },
                                                                ],
                                                            },
                                                        ],
                                                    },
                                                ],
                                                where: { isDeleted: false, id: +inspectionData.id },
                                            });
                                            if (exist2 && exist2.memberDetails) {
                                                const userDataMail = exist2.memberDetails;
                                                for (let i = 0; i < userDataMail.length; i++) {
                                                    const responsibleGuestUser = userDataMail[i].Member.isGuestUser;
                                                    if (responsibleGuestUser) {
                                                        const guestMailPayload = {
                                                            email: responsibleGuestUser.User.email,
                                                            guestName: responsibleGuestUser.User.firstName,
                                                            content: `We would like to inform you that 
                                  ${loginUser.firstName} ${loginUser.lastName} has updated a Inspection booking ${exist2.description}.`,
                                                        };
                                                        await MAILER.sendMail(
                                                            guestMailPayload,
                                                            'notifyGuestOnEdit',
                                                            `Inspection Booking updated by ${loginUser.firstName} ${loginUser.lastName}`,
                                                            `Inspection Booking updated by ${loginUser.firstName} ${loginUser.lastName}`,
                                                            async (info, err) => {
                                                                console.log(info, err);
                                                            },
                                                        );
                                                    }
                                                }
                                            }
                                            return done(history, false);
                                        }
                                    } else {
                                        return done(history, false);
                                    }
                                } else {
                                    return done(history, false);
                                }
                            } else {
                                return done(null, error);
                            }
                        });
                    });
                }
            }
        } catch (e) {
            return done(null, e);
        }
    },
    async updateCompanyHistory(addedCompany, deletedCompany, history, loginUser) {
        const newHistory = history;
        let addDesc = `${loginUser.firstName} ${loginUser.lastName} Added the Company`;
        let deleteDesc = `${loginUser.firstName} ${loginUser.lastName} Deleted the Company`;
        addedCompany.forEach(async (element, i) => {
            const newCompanyData = await Company.findOne({
                where: { id: element.CompanyId },
            });
            if (i === 0) {
                if (i === addedCompany.length - 1) {
                    addDesc += ` ${newCompanyData.companyName}`;
                    newHistory.description = addDesc;
                    InspectionHistory.createInstance(newHistory);
                } else {
                    addDesc += ` ${newCompanyData.companyName}`;
                }
            } else if (i === addedCompany.length - 1) {
                addDesc += `,${newCompanyData.companyName}`;
                newHistory.description = addDesc;
                InspectionHistory.createInstance(newHistory);
            } else {
                addDesc += `,${newCompanyData.companyName}`;
            }
        });
        deletedCompany.forEach(async (element, i) => {
            const newCompanyData = await Company.findOne({
                where: { id: element.CompanyId },
            });
            if (i === 0) {
                if (i === deletedCompany.length - 1) {
                    deleteDesc += ` ${newCompanyData.companyName}`;
                    newHistory.description = deleteDesc;
                    InspectionHistory.createInstance(newHistory);
                } else {
                    deleteDesc += ` ${newCompanyData.companyName}`;
                }
            } else if (i === deletedCompany.length - 1) {
                deleteDesc += `,${newCompanyData.companyName}`;
                newHistory.description = deleteDesc;
                InspectionHistory.createInstance(newHistory);
            } else {
                deleteDesc += `,${newCompanyData.companyName}`;
            }
        });
    },
    async updateGateHistory(addedGate, deletedGate, history, loginUser) {
        const newHistory = history;
        let addDesc = `${loginUser.firstName} ${loginUser.lastName} Added the Gate`;
        let deleteDesc = `${loginUser.firstName} ${loginUser.lastName} Deleted the Gate`;
        addedGate.forEach(async (element, i) => {
            const newGateData = await Gates.findOne({
                where: { id: element.GateId },
            });
            if (i === 0) {
                if (i === addedGate.length - 1) {
                    addDesc += ` ${newGateData.gateName}`;
                    newHistory.description = addDesc;
                    InspectionHistory.createInstance(newHistory);
                } else {
                    addDesc += ` ${newGateData.gateName}`;
                }
            } else if (i === addedGate.length - 1) {
                addDesc += `,${newGateData.gateName}`;
                newHistory.description = addDesc;
                InspectionHistory.createInstance(newHistory);
            } else {
                addDesc += `,${newGateData.gateName}`;
            }
        });
        deletedGate.forEach(async (element, i) => {
            const newGateData = await Gates.findOne({
                where: { id: element.GateId },
            });
            if (i === 0) {
                if (i === deletedGate.length - 1) {
                    deleteDesc += ` ${newGateData.gateName}`;
                    newHistory.description = deleteDesc;
                    InspectionHistory.createInstance(newHistory);
                } else {
                    deleteDesc += ` ${newGateData.gateName}`;
                }
            } else if (i === deletedGate.length - 1) {
                deleteDesc += `,${newGateData.gateName}`;
                newHistory.description = deleteDesc;
                InspectionHistory.createInstance(newHistory);
            } else {
                deleteDesc += `,${newGateData.gateName}`;
            }
        });
    },
    async updateEquipmentHistory(addedEquipment, deletedEquipment, history, loginUser) {
        const newHistory = history;
        let addDesc = `${loginUser.firstName} ${loginUser.lastName} Added the Equipment`;
        let deleteDesc = `${loginUser.firstName} ${loginUser.lastName} Deleted the Equipment`;
        addedEquipment.forEach(async (element, i) => {
            const newEquipmentData = await Equipments.findOne({
                where: { id: element.EquipmentId },
            });
            if (i === 0) {
                if (i === addedEquipment.length - 1) {
                    addDesc += ` ${newEquipmentData.equipmentName}`;
                    newHistory.description = addDesc;
                    InspectionHistory.createInstance(newHistory);
                } else {
                    addDesc += ` ${newEquipmentData.equipmentName}`;
                }
            } else if (i === addedEquipment.length - 1) {
                addDesc += `,${newEquipmentData.equipmentName}`;
                newHistory.description = addDesc;
                InspectionHistory.createInstance(newHistory);
            } else {
                addDesc += `,${newEquipmentData.equipmentName}`;
            }
        });
        deletedEquipment.forEach(async (element, i) => {
            const newEquipmentData = await Equipments.findOne({
                where: { id: element.EquipmentId },
            });
            if (i === 0) {
                if (i === deletedEquipment.length - 1) {
                    deleteDesc += ` ${newEquipmentData.equipmentName}`;
                    newHistory.description = deleteDesc;
                    InspectionHistory.createInstance(newHistory);
                } else {
                    deleteDesc += ` ${newEquipmentData.equipmentName}`;
                }
            } else if (i === deletedEquipment.length - 1) {
                deleteDesc += `,${newEquipmentData.equipmentName}`;
                newHistory.description = deleteDesc;
                InspectionHistory.createInstance(newHistory);
            } else {
                deleteDesc += `,${newEquipmentData.equipmentName}`;
            }
        });
    },
    async updatePersonHistory(addedPerson, deletedPerson, history, loginUser) {
        const newHistory = history;
        let addDesc = `${loginUser.firstName} ${loginUser.lastName} Added the Member`;
        let deleteDesc = `${loginUser.firstName} ${loginUser.lastName} Deleted the Member`;
        addedPerson.forEach(async (element, i) => {
            const newMemberData = await Member.findOne({
                where: { id: element.MemberId, isDeleted: false },
                include: [
                    {
                        association: 'User',
                        attributes: ['firstName', 'lastName'],
                    },
                ],
            });
            if (i === 0) {
                if (i === addedPerson.length - 1) {
                    if (newMemberData.User.firstName) {
                        addDesc += ` ${newMemberData.User.firstName} ${newMemberData.User.lastName}`;
                    }
                    newHistory.description = addDesc;
                    InspectionHistory.createInstance(newHistory);
                } else if (newMemberData.User.firstName) {
                    addDesc += ` ${newMemberData.User.firstName} ${newMemberData.User.lastName}`;
                }
            } else if (i === addedPerson.length - 1) {
                if (newMemberData.User.firstName) {
                    addDesc += `,${newMemberData.User.firstName} ${newMemberData.User.lastName}`;
                }
                newHistory.description = addDesc;
                InspectionHistory.createInstance(newHistory);
            } else if (newMemberData.User.firstName) {
                addDesc += `,${newMemberData.User.firstName} ${newMemberData.User.lastName}`;
            }
        });
        deletedPerson.forEach(async (element, i) => {
            const newMemberData = await Member.findOne({
                where: { id: element.MemberId, isDeleted: false },
                include: [
                    {
                        association: 'User',
                        attributes: ['firstName', 'lastName'],
                    },
                ],
            });
            if (i === 0) {
                if (i === deletedPerson.length - 1) {
                    if (newMemberData.User.firstName) {
                        deleteDesc += ` ${newMemberData.User.firstName} ${newMemberData.User.lastName}`;
                    }
                    newHistory.description = deleteDesc;
                    InspectionHistory.createInstance(newHistory);
                } else if (newMemberData.User.firstName) {
                    deleteDesc += ` ${newMemberData.User.firstName} ${newMemberData.User.lastName}`;
                }
            } else if (i === deletedPerson.length - 1) {
                if (newMemberData.User.firstName) {
                    deleteDesc += `,${newMemberData.User.firstName} ${newMemberData.User.lastName}`;
                }
                newHistory.description = deleteDesc;
                InspectionHistory.createInstance(newHistory);
            } else if (newMemberData.User.firstName) {
                deleteDesc += `,${newMemberData.User.firstName} ${newMemberData.User.lastName}`;
            }
        });
    },
    async updateDefineHistory(addedDefine, deletedDefine, history, loginUser) {
        const newHistory = history;
        let addDesc = `${loginUser.firstName} ${loginUser.lastName} Added the DFOW`;
        let deleteDesc = `${loginUser.firstName} ${loginUser.lastName} Deleted the DFOW`;
        addedDefine.forEach(async (element, i) => {
            const newDefineData = await DeliverDefineWork.findOne({
                where: { id: element.DeliverDefineWorkId },
            });
            if (i === 0) {
                if (i === addedDefine.length - 1) {
                    addDesc += ` ${newDefineData.DFOW}`;
                    newHistory.description = addDesc;
                    InspectionHistory.createInstance(newHistory);
                } else {
                    addDesc += ` ${newDefineData.DFOW}`;
                }
            } else if (i === addedDefine.length - 1) {
                addDesc += `,${newDefineData.DFOW}`;
                newHistory.description = addDesc;
                InspectionHistory.createInstance(newHistory);
            } else {
                addDesc += `,${newDefineData.DFOW}`;
            }
        });
        deletedDefine.forEach(async (element, i) => {
            const newDefineData = await DeliverDefineWork.findOne({
                where: { id: element.DeliverDefineWorkId },
            });
            if (i === 0) {
                if (i === deletedDefine.length - 1) {
                    deleteDesc += ` ${newDefineData.DFOW}`;
                    newHistory.description = deleteDesc;
                    InspectionHistory.createInstance(newHistory);
                } else {
                    deleteDesc += ` ${newDefineData.DFOW}`;
                }
            } else if (i === deletedDefine.length - 1) {
                deleteDesc += `,${newDefineData.DFOW}`;
                newHistory.description = deleteDesc;
                InspectionHistory.createInstance(newHistory);
            } else {
                deleteDesc += `,${newDefineData.DFOW}`;
            }
        });
    },
    async lastinspection(inputData, done) {
        try {
            const { params } = inputData;
            let data;
            const lastData = await InspectionRequest.findOne({
                where: { ProjectId: params.ProjectId, isDeleted: false },
                order: [['InspectionId', 'DESC']],
            });
            if (lastData) {
                data = lastData.InspectionId + 1;
            } else {
                data = 1;
            }
            done({ InspectionId: data }, false);
        } catch (e) {
            done(null, e);
        }
    },
    async updateValues(condition, done) {
        try {
            await InspectionCompany.update({ isDeleted: true }, { where: condition });
            await InspectionPerson.update({ isDeleted: true }, { where: condition });
            await InspectionGate.update({ isDeleted: true }, { where: condition });
            await InspectionEquipment.update({ isDeleted: true }, { where: condition });
            await DeliverDefine.update({ isDeleted: true }, { where: condition });
            done({ status: 'ok' }, false);
        } catch (e) {
            done(null, e);
        }
    },
    async listNDR(inputData, done) {
        try {
            await this.getDynamicModel(inputData);
            const { params } = inputData;
            const loginUser = inputData.user;
            const incomeData = inputData.body;
            let { sort } = inputData.body;
            let { sortByField } = inputData.body;
            let order;
            let searchCondition = {};
            if (params.void >= 1 && params.void <= 0) {
                done(null, { message: 'Please enter void as 1 or 0' });
            } else {
                const memberDetails = await Member.findOne({
                    where: Sequelize.and({
                        UserId: loginUser.id,
                        ProjectId: params.ProjectId,
                        isDeleted: false,
                        isActive: true,
                    }),
                });
                if (memberDetails) {
                    const voidInspection = [];
                    const voidList = await VoidList.findAll({
                        where: {
                            ProjectId: params.ProjectId,
                            // isInspectionRequest: true,
                            InspectionRequestId: { [Op.ne]: null },
                        },
                    });
                    voidList.forEach(async (element) => {
                        voidInspection.push(element.InspectionRequestId);
                    });
                    const offset = (+params.pageNo - 1) * +params.pageSize;
                    const condition = {
                        ProjectId: +params.ProjectId,
                        isQueued: incomeData.queuedNdr,
                    };
                    if (params.void === '0' || params.void === 0) {
                        condition['$InspectionRequest.id$'] = {
                            [Op.and]: [{ [Op.notIn]: voidInspection }],
                        };
                    } else {
                        condition['$InspectionRequest.id$'] = {
                            [Op.and]: [{ [Op.in]: voidInspection }],
                        };
                    }
                    if (incomeData.descriptionFilter) {
                        condition.description = {
                            [Sequelize.Op.iLike]: `%${incomeData.descriptionFilter}%`,
                        };
                    }
                    if (incomeData.pickFrom) {
                        condition.cranePickUpLocation = {
                            [Sequelize.Op.iLike]: `%${incomeData.pickFrom}%`,
                        };
                    }
                    if (incomeData.pickTo) {
                        condition.craneDropOffLocation = {
                            [Sequelize.Op.iLike]: `%${incomeData.pickTo}%`,
                        };
                    }
                    if (incomeData.inspectionStatusFilter) {
                        condition.inspectionStatus = {
                            [Sequelize.Op.iLike]: `%${incomeData.inspectionStatusFilter}%`,
                        };
                    }
                    if (incomeData.inspectionTypeFilter) {
                        condition.inspectionType = {
                            [Sequelize.Op.iLike]: `%${incomeData.inspectionTypeFilter}%`,
                        };
                    }
                    if (incomeData.equipmentFilter) {
                        condition['$equipmentDetails.Equipment.id$'] = incomeData.equipmentFilter;
                    }
                    if (incomeData.locationFilter) {
                        condition['$location.locationPath$'] = incomeData.locationFilter;
                    }
                    if (incomeData.statusFilter) {
                        if (incomeData.statusFilter === 'Completed') {
                            condition.inspectionStatus = {
                                [Sequelize.Op.or]: [
                                    {
                                        [Sequelize.Op.iLike]: 'Pass',
                                    },
                                    {
                                        [Sequelize.Op.iLike]: 'Fail',
                                    }
                                ]
                            };
                        } else if (incomeData.statusFilter === 'Approved') {
                            condition.status = incomeData.statusFilter;
                            condition.inspectionStatus = null
                        } else {
                            condition.status = incomeData.statusFilter;
                        }
                    }
                    if (incomeData.dateFilter) {
                        const startDateTime = moment(incomeData.dateFilter, 'YYYY-MM-DD')
                            .startOf('day')
                            .utcOffset(Number(inputData.headers.timezoneoffset), true);
                        const endDateTime = moment(incomeData.dateFilter, 'YYYY-MM-DD')
                            .endOf('day')
                            .utcOffset(Number(inputData.headers.timezoneoffset), true);
                        condition.inspectionStart = {
                            [Op.between]: [moment(startDateTime), moment(endDateTime)],
                        };
                    }
                    if (incomeData.upcoming) {
                        condition.inspectionStart = {
                            [Op.gt]: new Date(),
                        };
                        order = 'ASC';
                        sort = 'ASC';
                        sortByField = 'inspectionStart';
                    }
                    if (incomeData.search) {
                        const searchDefault = [
                            {
                                '$approverDetails.User.firstName$': {
                                    [Sequelize.Op.iLike]: `%${incomeData.search}%`,
                                },
                            },
                            {
                                '$equipmentDetails.Equipment.equipmentName$': {
                                    [Sequelize.Op.iLike]: `%${incomeData.search}%`,
                                },
                            },
                            {
                                '$location.locationPath$': {
                                    [Sequelize.Op.iLike]: `%${incomeData.search}%`,
                                },
                            },
                            {
                                description: {
                                    [Sequelize.Op.iLike]: `%${incomeData.search}%`,
                                },
                            },
                            {
                                cranePickUpLocation: {
                                    [Sequelize.Op.iLike]: `%${incomeData.search}%`,
                                },
                            },
                            {
                                craneDropOffLocation: {
                                    [Sequelize.Op.iLike]: `%${incomeData.search}%`,
                                },
                            },
                        ];
                        if (!Number.isNaN(+incomeData.search)) {
                            searchCondition = {
                                [Op.and]: [
                                    {
                                        [Op.or]: [
                                            searchDefault,
                                            {
                                                [Op.and]: [
                                                    {
                                                        InspectionId: +incomeData.search,
                                                        isDeleted: false,
                                                        ProjectId: +params.ProjectId,
                                                    },
                                                ],
                                            },
                                        ],
                                    },
                                ],
                            };
                        } else {
                            searchCondition = {
                                [Op.and]: [
                                    {
                                        [Op.or]: searchDefault,
                                    },
                                ],
                            };
                        }
                    }
                    const roleId = memberDetails.RoleId;
                    const memberId = memberDetails.id;
                    if (
                        incomeData.companyFilter ||
                        incomeData.gateFilter ||
                        incomeData.memberFilter ||
                        incomeData.assignedFilter ||
                        (memberDetails.RoleId === 4 &&
                            (params.void === '0' || params.void === 0) &&
                            !incomeData.upcoming) ||
                        (memberDetails.RoleId === 3 &&
                            (params.void === '0' || params.void === 0) &&
                            !incomeData.upcoming)
                    ) {
                        const result = { count: 0, rows: [] };
                        const inspectionList = await InspectionRequest.getCalendarData(
                            condition,
                            roleId,
                            memberId,
                            searchCondition,
                            order,
                            sort,
                            sortByField,
                        );
                        this.getSearchData(
                            incomeData,
                            inspectionList.rows,
                            [],
                            +params.pageSize,
                            0,
                            0,
                            memberDetails,
                            async (checkResponse, checkError) => {
                                if (!checkError) {
                                    this.getLimitData(
                                        checkResponse,
                                        0,
                                        +params.pageSize,
                                        [],
                                        incomeData,
                                        inputData.headers.timezoneoffset,
                                        async (newResponse, newError) => {
                                            if (!newError) {
                                                if (sort === 'ASC') {
                                                    newResponse.sort(function (a, b) {
                                                        // slint-disable-next-line no-nested-ternary
                                                        return a[sortByField] > b[sortByField]
                                                            ? 1
                                                            : b[sortByField] > a[sortByField]
                                                                ? -1
                                                                : 0;
                                                    });
                                                } else {
                                                    newResponse.sort(function (a, b) {
                                                        // slint-disable-next-line no-nested-ternary
                                                        return b[sortByField] > a[sortByField]
                                                            ? 1
                                                            : a[sortByField] > b[sortByField]
                                                                ? -1
                                                                : 0;
                                                    });
                                                }
                                                result.rows = newResponse.slice(offset, offset + +params.pageSize);
                                                result.count = checkResponse.length;
                                                done(result, false);
                                            } else {
                                                done(null, { message: 'Something went wrong' });
                                            }
                                        },
                                    );
                                } else {
                                    done(null, { message: 'Something went wrong' });
                                }
                            },
                        );
                    } else {
                        const newResult = { count: 0, rows: [] };
                        const inspectionList = await InspectionRequest.getAll(
                            condition,
                            roleId,
                            memberId,
                            +params.pageSize,
                            offset,
                            searchCondition,
                            order,
                            sort,
                            sortByField,
                        );
                        this.getLimitData(
                            inspectionList,
                            0,
                            +params.pageSize,
                            [],
                            incomeData,
                            inputData.headers.timezoneoffset,
                            async (newResponse, newError) => {
                                if (!newError) {
                                    if (sort === 'ASC') {
                                        newResponse.sort(function (a, b) {
                                            // slint-disable-next-line no-nested-ternary
                                            return a[sortByField] > b[sortByField]
                                                ? 1
                                                : b[sortByField] > a[sortByField]
                                                    ? -1
                                                    : 0;
                                        });
                                    } else {
                                        newResponse.sort(function (a, b) {
                                            // slint-disable-next-line no-nested-ternary
                                            return b[sortByField] > a[sortByField]
                                                ? 1
                                                : a[sortByField] > b[sortByField]
                                                    ? -1
                                                    : 0;
                                        });
                                    }
                                    newResult.rows = newResponse.slice(offset, offset + +params.pageSize);
                                    newResult.count = inspectionList.length;
                                    done(newResult, false);
                                } else {
                                    done(null, { message: 'Something went wrong' });
                                }
                            },
                        );
                    }
                } else {
                    done(null, { message: 'Project Id/Member does not exist' });
                }
            }
        } catch (e) {
            done(null, e);
        }
    },
    async getLimitData(result, index, limit, finalResult, incomeData, timezoneoffset, done) {
        if (index < limit) {
            finalResult.push(result);
            this.getLimitData(
                result,
                index + 1,
                limit,
                finalResult,
                incomeData,
                timezoneoffset,
                (response, err) => {
                    if (!err) {
                        done(response, false);
                    } else {
                        done(null, err);
                    }
                },
            );
        } else {
            done(result, false);
        }
    },
    async getSearchData(incomeData, inspectionList, result, limit, index, count, memberDetails, done) {
        const elementValue = inspectionList[index];
        if (elementValue) {
            const element = JSON.parse(JSON.stringify(elementValue));
            const status = { companyCondition: true, gateCondition: true, memberCondition: true };
            if (incomeData.companyFilter) {
                const data = await InspectionCompany.findOne({
                    where: {
                        InspectionId: element.id,
                        CompanyId: incomeData.companyFilter,
                        isDeleted: false,
                    },
                });
                if (!data) {
                    status.companyCondition = false;
                }
            }
            if (incomeData.gateFilter) {
                const data = await InspectionGate.findOne({
                    where: {
                        InspectionId: element.id,
                        GateId: incomeData.gateFilter,
                        isDeleted: false,
                    },
                });
                if (!data) {
                    status.gateCondition = false;
                }
            }
            if (incomeData.memberFilter) {
                const data = await InspectionPerson.findOne({
                    where: {
                        InspectionId: element.id,
                        MemberId: incomeData.memberFilter,
                        isDeleted: false,
                    },
                });
                if (!data) {
                    status.memberCondition = false;
                }
            }
            // if (memberDetails.RoleId === 4 || memberDetails.RoleId === 3) {
            //   const data = await InspectionPerson.findOne({
            //     where: {
            //       InspectionId: element.id,
            //       MemberId: memberDetails.id,
            //       isDeleted: false,
            //       isActive: true,
            //     },
            //   });
            //   if (!data) {
            //     status.memberCondition = false;
            //   }
            // }

            if (status.companyCondition && status.gateCondition && status.memberCondition) {
                result.push(element);
            }
            if (index < inspectionList.length - 1) {
                this.getSearchData(
                    incomeData,
                    inspectionList,
                    result,
                    limit,
                    index + 1,
                    count + 1,
                    memberDetails,
                    (response, err) => {
                        if (!err) {
                            done(response, false);
                        } else {
                            done(null, err);
                        }
                    },
                );
            } else {
                done(result, false);
            }
        } else {
            done(result, false);
        }
    },
    async getNDRData(inputData, done) {
        try {
            await this.getDynamicModel(inputData);
            const { params } = inputData;
            const condition = {
                id: params.inspectionRequestId,
            };
            const inspectionList = await InspectionRequest.getNDRData(condition);
            done(inspectionList, false);
        } catch (e) {
            done(null, e);
        }
    },
    async getMemberData(inputData, done) {
        try {
            await this.getDynamicModel(inputData);
            const { params } = inputData;
            const condition = {
                UserId: inputData.user.id,
                ProjectId: params.ProjectId,
            };
            const memberData = await Member.getBy(condition);
            done(memberData, false);
        } catch (e) {
            done(null, e);
        }
    },
    async updateNDRStatus(inputData, done) {
        try {
            await this.getDynamicModel(inputData);
            const updateData = inputData.body;
            const loginUser = inputData.user;
            const statusValue = await InspectionRequest.findOne({
                where: { id: updateData.id },
                include: [
                    {
                        association: 'recurrence',
                        required: false,
                        attributes: [
                            'id',
                            'recurrence',
                            'recurrenceStartDate',
                            'recurrenceEndDate',
                            'dateOfMonth',
                            'monthlyRepeatType',
                            'repeatEveryCount',
                            'days',
                            'requestType',
                            'repeatEveryType',
                            'chosenDateOfMonth',
                            'createdBy',
                        ],
                    },
                ],
            });
            const NDRData = await InspectionRequest.getNDRData({ id: updateData.id });
            if (!statusValue) {
                done(null, { message: 'Id does not exist.' });
            } else {
                const memberValue = await Member.findOne({
                    where: Sequelize.and({
                        UserId: inputData.user.id,
                        ProjectId: statusValue.ProjectId,
                        isDeleted: false,
                    }),
                });
                if (memberValue) {
                    if (
                        memberValue.RoleId === 2 ||
                        memberValue.RoleId === 3 ||
                        memberValue.RoleId === 1 ||
                        memberValue.RoleId === 4
                    ) {
                        if (memberValue.RoleId === 4) {
                            if (+loginUser.id !== +NDRData.createdUserDetails.User.id) {
                                return done(null, {
                                    message: 'SC can able to Inspection the NDR which was created by him only.',
                                });
                            }
                        }
                        const locationChosen = await Locations.findOne({
                            where: {
                                ProjectId: statusValue.ProjectId,
                                id: statusValue.LocationId,
                            },
                        });
                        const memberLocationPreference = await LocationNotificationPreferences.findAll({
                            where: {
                                ProjectId: statusValue.ProjectId,
                                LocationId: statusValue.LocationId,
                                follow: true,
                            },
                            include: [
                                {
                                    association: 'Member',
                                    attributes: ['id', 'RoleId'],
                                    where: {
                                        [Op.and]: [
                                            {
                                                id: { [Op.ne]: memberValue.id },
                                            },
                                        ],
                                    },
                                    include: [
                                        {
                                            association: 'User',
                                            attributes: ['id', 'firstName', 'lastName', 'email'],
                                        },
                                    ],
                                },
                            ],
                        });
                        const locationFollowMembers = [];
                        memberLocationPreference.forEach(async (element) => {
                            locationFollowMembers.push(element.Member.id);
                        });
                        const bookingMemberDetails = [];
                        NDRData.memberDetails.forEach(async (element) => {
                            bookingMemberDetails.push(element.Member.id);
                        });
                        const history = {
                            InspectionRequestId: statusValue.id,
                            MemberId: memberValue.id,
                            InspectionId: statusValue.InspectionId,
                        };
                        const exist = await InspectionRequest.findOne({
                            include: [
                                {
                                    association: 'memberDetails',
                                    required: false,
                                    where: { isDeleted: false, isActive: true },
                                    attributes: ['id'],
                                    include: [
                                        {
                                            association: 'Member',
                                            attributes: ['id', 'isGuestUser'],
                                            include: [
                                                {
                                                    association: 'User',
                                                    attributes: [
                                                        'email',
                                                        'phoneCode',
                                                        'phoneNumber',
                                                        'firstName',
                                                        'lastName',
                                                    ],
                                                },
                                            ],
                                        },
                                    ],
                                },
                            ],
                            where: { id: updateData.id },
                        });
                        if (updateData.status === 'Approved') {
                            if (updateData.statuschange && updateData.statuschange === 'Reverted') {
                                history.type = 'approved';
                                history.description = `${loginUser.firstName} ${loginUser.lastName} Reverted the status from Inspection to approved for Inspection Booking , ${statusValue.description}`;
                                history.locationFollowDescription = `${loginUser.firstName} ${loginUser.lastName} Reverted the status from Inspection to approved for Inspection Booking, ${statusValue.description}. Location: ${locationChosen.locationPath}.`;
                            } else {
                                history.type = 'approved';
                                history.description = `${loginUser.firstName} ${loginUser.lastName} Approved the Inspection Booking`;
                                history.locationFollowDescription = `${loginUser.firstName} ${loginUser.lastName} Approved the Inspection Booking, ${statusValue.description}. Location: ${locationChosen.locationPath}.`;
                            }

                            const notification = history;
                            notification.ProjectId = statusValue.ProjectId;
                            if (statusValue && statusValue.recurrence && statusValue.recurrence.recurrence) {
                                notification.recurrenceType = `${statusValue.recurrence.recurrence} From ${moment(
                                    statusValue.recurrence.recurrenceStartDate,
                                ).format('MM/DD/YYYY')} to ${moment(
                                    statusValue.recurrence.recurrenceEndDate,
                                ).format('MM/DD/YYYY')}`;
                            }
                            if (updateData.inspectionStatus) {
                                console.log("Getting into status update=======")
                                notification.title = `Inspection Booking is Passed by ${loginUser.firstName} ${loginUser.lastName}`;
                                await InspectionRequest.update(
                                    { inspectionStatus: updateData.inspectionStatus, approvedBy: memberValue.id, approved_at: new Date() },
                                    { where: { id: updateData.id } },
                                );
                            } else {
                                notification.title = `Inspection Booking Approved by ${loginUser.firstName} ${loginUser.lastName}`;
                                await InspectionRequest.update(
                                    { status: updateData.status, approvedBy: memberValue.id, approved_at: new Date() },
                                    { where: { id: updateData.id } },
                                );
                            }
                            const object = {
                                ProjectId: statusValue.ProjectId,
                                MemberId: memberValue.id,
                                InspectionRequestId: statusValue.id,
                                isDeleted: false,
                                type: updateData.status.toLowerCase(),
                                description: history.description,
                            };
                            await InspectionHistory.createInstance(object);
                            history.firstName = loginUser.firstName;
                            history.profilePic = loginUser.profilePic;
                            history.createdAt = new Date();
                            history.ProjectId = statusValue.ProjectId;
                            const projectDetails = await Project.findByPk(statusValue.ProjectId);
                            history.projectName = projectDetails.projectName;
                            notification.requestType = 'InspectionRequest';
                            const newNotification = await Notification.createInstance(notification);
                            const personData = await InspectionPerson.findAll({
                                where: { InspectionId: statusValue.id, isDeleted: false },
                                include: [
                                    {
                                        association: 'Member',
                                        include: [
                                            {
                                                association: 'User',
                                                attributes: ['id', 'firstName', 'lastName', 'email'],
                                            },
                                        ],
                                        where: {
                                            [Op.and]: {
                                                RoleId: {
                                                    [Op.notIn]: [1, 2],
                                                },
                                                id: { [Op.notIn]: locationFollowMembers },
                                            },
                                        },
                                        attributes: ['id', 'RoleId'],
                                    },
                                ],
                                attributes: ['id'],
                            });
                            const adminData = await Member.findAll({
                                where: {
                                    [Op.and]: [
                                        { ProjectId: statusValue.ProjectId },
                                        { isDeleted: false },
                                        { id: { [Op.in]: bookingMemberDetails } },
                                        { id: { [Op.ne]: newNotification.MemberId } },
                                        { id: { [Op.notIn]: locationFollowMembers } },
                                    ],
                                },
                                include: [
                                    {
                                        association: 'User',
                                        attributes: ['id', 'firstName', 'lastName', 'email'],
                                    },
                                ],
                                attributes: ['id'],
                            });
                            if (memberLocationPreference && memberLocationPreference.length > 0) {
                                // here 6-(NotificationPreferenceItemId -When a inspection/crane/concrete request status updated)
                                await pushNotification.sendMemberLocationPreferencePushNotification(
                                    memberLocationPreference,
                                    statusValue.InspectionRequestId,
                                    history.locationFollowDescription,
                                    statusValue.requestType,
                                    statusValue.ProjectId,
                                    statusValue.id,
                                    6,
                                );
                                // here 6-(NotificationPreferenceItemId -When a inspection/crane/concrete request status updated)
                                await notificationHelper.createMemberDeliveryLocationInAppNotification(
                                    DeliveryPersonNotification,
                                    statusValue.ProjectId,
                                    newNotification.id,
                                    memberLocationPreference,
                                    6,
                                );
                            }
                            history.memberData = personData;
                            history.adminData = adminData;
                            // here 6-(NotificationPreferenceItemId -When a inspection/crane/concrete request status updated)
                            await notificationHelper.createDeliveryPersonNotification(
                                adminData,
                                personData,
                                projectDetails,
                                newNotification,
                                DeliveryPersonNotification,
                                memberValue,
                                loginUser,
                                6,
                                'approved a',
                                'Inspection Request',
                                `Inspection Booking (${statusValue.InspectionId} - ${statusValue.description})`,
                                statusValue.id,
                            );
                            const checkMemberNotification = await NotificationPreference.findAll({
                                where: {
                                    ProjectId: statusValue.ProjectId,
                                    isDeleted: false,
                                },
                                attributes: [
                                    'id',
                                    'MemberId',
                                    'ProjectId',
                                    'ParentCompanyId',
                                    'NotificationPreferenceItemId',
                                    'instant',
                                    'dailyDigest',
                                ],
                                include: [
                                    {
                                        association: 'NotificationPreferenceItem',
                                        where: {
                                            id: 6,
                                            isDeleted: false,
                                        },
                                        attributes: ['id', 'description', 'inappNotification', 'emailNotification'],
                                    },
                                ],
                            });
                            history.notificationPreference = checkMemberNotification;
                            // here 6-(NotificationPreferenceItemId -When a inspection/crane/concrete request status updated)
                            await pushNotification.sendDeviceTokenForInspection(history, 6, statusValue.ProjectId);
                            if (memberLocationPreference && memberLocationPreference.length > 0) {
                                history.memberData.push(...memberLocationPreference);
                            }
                            const userDataMail = exist.memberDetails;
                            for (let i = 0; i < userDataMail.length; i++) {
                                const responsibleGuestUser = userDataMail[i].Member.isGuestUser;
                                if (responsibleGuestUser) {
                                    const guestMailPayload = {
                                        email: userDataMail[i].Member.User.email,
                                        guestName: userDataMail[i].Member.User.firstName,
                                        content: `We would like to inform you that 
                    ${loginUser.firstName} ${loginUser.lastName} Approved the Inspection Booking - ${statusValue.description}.`,
                                    };
                                    await MAILER.sendMail(
                                        guestMailPayload,
                                        'notifyGuestOnEdit',
                                        `Inspection Booking Approved by ${loginUser.firstName} `,
                                        'Inspection Booking Approved',
                                        async (info, err) => {
                                            console.log(info, err);
                                        },
                                    );
                                }
                            }
                            return done(history, false);
                        }
                        if (updateData.status === 'Declined' || updateData.status === 'Delivered') {
                            history.type = updateData.status.toLowerCase();
                            history.description = `${loginUser.firstName} ${loginUser.lastName} ${updateData.status} the Inspection Booking`;
                            history.locationFollowDescription = `${loginUser.firstName} ${loginUser.lastName} ${updateData.status} the Inspection Booking, ${statusValue.description}. Location: ${locationChosen.locationPath}.`;
                            const notification = history;
                            notification.ProjectId = statusValue.ProjectId;
                            if (statusValue && statusValue.recurrence && statusValue.recurrence.recurrence) {
                                notification.recurrenceType = `${statusValue.recurrence.recurrence} From ${moment(
                                    statusValue.recurrence.recurrenceStartDate,
                                ).format('MM/DD/YYYY')} to ${moment(
                                    statusValue.recurrence.recurrenceEndDate,
                                ).format('MM/DD/YYYY')}`;
                            }
                            notification.title = `Inspection Booking ${updateData.status} by ${loginUser.firstName} ${loginUser.lastName}`;
                            await InspectionRequest.update(
                                { status: updateData.status },
                                { where: { id: updateData.id } },
                            );
                            const object1 = {
                                ProjectId: statusValue.ProjectId,
                                MemberId: memberValue.id,
                                InspectionRequestId: statusValue.id,
                                isDeleted: false,
                                type: updateData.status.toLowerCase(),
                                description: history.description,
                            };
                            await InspectionHistory.createInstance(object1);
                            history.firstName = loginUser.firstName;
                            history.profilePic = loginUser.profilePic;
                            history.createdAt = new Date();
                            history.ProjectId = statusValue.ProjectId;
                            const projectDetails = await Project.findByPk(statusValue.ProjectId);
                            history.projectName = projectDetails.projectName;
                            notification.requestType = 'InspectionRequest';
                            const newNotification = await Notification.createInstance(notification);
                            const personData = await InspectionPerson.findAll({
                                where: { InspectionId: statusValue.id, isDeleted: false },
                                include: [
                                    {
                                        association: 'Member',
                                        include: [
                                            {
                                                association: 'User',
                                                attributes: ['id', 'firstName', 'lastName', 'email'],
                                            },
                                        ],
                                        where: {
                                            [Op.and]: {
                                                RoleId: {
                                                    [Op.notIn]: [1, 2],
                                                },
                                                id: { [Op.notIn]: locationFollowMembers },
                                            },
                                        },
                                        attributes: ['id', 'RoleId'],
                                    },
                                ],
                                attributes: ['id'],
                            });
                            const adminData = await Member.findAll({
                                where: {
                                    [Op.and]: [
                                        { ProjectId: statusValue.ProjectId },
                                        { isDeleted: false },
                                        { id: { [Op.in]: bookingMemberDetails } },
                                        { id: { [Op.ne]: newNotification.MemberId } },
                                        { id: { [Op.notIn]: locationFollowMembers } },
                                    ],
                                },
                                include: [
                                    {
                                        association: 'User',
                                        attributes: ['id', 'firstName', 'lastName', 'email'],
                                    },
                                ],
                                attributes: ['id'],
                            });
                            if (memberLocationPreference && memberLocationPreference.length > 0) {
                                // here 6-(NotificationPreferenceItemId -When a inspection/crane/concrete request status updated)
                                await pushNotification.sendMemberLocationPreferencePushNotification(
                                    memberLocationPreference,
                                    statusValue.InspectionRequestId,
                                    history.locationFollowDescription,
                                    statusValue.requestType,
                                    statusValue.ProjectId,
                                    statusValue.id,
                                    6,
                                );
                                // here 6-(NotificationPreferenceItemId -When a inspection/crane/concrete request status updated)
                                await notificationHelper.createMemberDeliveryLocationInAppNotification(
                                    DeliveryPersonNotification,
                                    statusValue.ProjectId,
                                    newNotification.id,
                                    memberLocationPreference,
                                    6,
                                );
                            }
                            history.memberData = personData;
                            history.adminData = adminData;
                            if (memberLocationPreference && memberLocationPreference.length > 0) {
                                history.memberData.push(...memberLocationPreference);
                            }
                            const checkMemberNotification = await NotificationPreference.findAll({
                                where: {
                                    ProjectId: statusValue.ProjectId,
                                    isDeleted: false,
                                },
                                attributes: [
                                    'id',
                                    'MemberId',
                                    'ProjectId',
                                    'ParentCompanyId',
                                    'NotificationPreferenceItemId',
                                    'instant',
                                    'dailyDigest',
                                ],
                                include: [
                                    {
                                        association: 'NotificationPreferenceItem',
                                        where: {
                                            id: 6,
                                            isDeleted: false,
                                        },
                                        attributes: ['id', 'description', 'inappNotification', 'emailNotification'],
                                    },
                                ],
                            });
                            history.notificationPreference = checkMemberNotification;
                            // here 6-(NotificationPreferenceItemId -When a inspection/crane/concrete request status updated)
                            await notificationHelper.createDeliveryPersonNotification(
                                adminData,
                                personData,
                                projectDetails,
                                newNotification,
                                DeliveryPersonNotification,
                                memberValue,
                                loginUser,
                                6,
                                `${updateData.status.toLowerCase()}`,
                                'Inspection Request',
                                `Inspection Booking (${statusValue.InspectionId} - ${statusValue.description})`,
                                statusValue.id,
                            );
                            // here 6-(NotificationPreferenceItemId -When a inspection/crane/concrete request status updated)
                            await pushNotification.sendDeviceTokenForInspection(history, 6, statusValue.ProjectId);
                            const userDataMail = exist.memberDetails;
                            for (let i = 0; i < userDataMail.length; i++) {
                                const responsibleGuestUser = userDataMail[i].Member.isGuestUser;
                                if (responsibleGuestUser) {
                                    const guestMailPayload = {
                                        email: userDataMail[i].Member.User.email,
                                        guestName: userDataMail[i].Member.User.firstName,
                                        content: `We would like to inform you that 
                    Inspection Booking - ${statusValue.description} was ${updateData.status} by ${loginUser.firstName} ${loginUser.lastName}.`,
                                    };
                                    await MAILER.sendMail(
                                        guestMailPayload,
                                        'notifyGuestOnEdit',
                                        `Inspection Booking ${updateData.status} by ${loginUser.firstName} `,
                                        'Inspection Booking Approved',
                                        async (info, err) => {
                                            console.log(info, err);
                                        },
                                    );
                                }
                            }
                            if (updateData.status === 'Inspectioned') {
                                const userEmails = await this.getMemberDetailData(
                                    history,
                                    memberLocationPreference,
                                );
                                if (userEmails.length > 0) {
                                    userEmails.forEach(async (element) => {
                                        let name;
                                        if (!element.firstName) {
                                            name = 'user';
                                        } else {
                                            name = `${element.firstName} ${element.lastName}`;
                                        }
                                        const time = moment(statusValue.inspectionStart).format('MM-DD-YYYY');
                                        const mailPayload = {
                                            userName: name,
                                            email: element.email,
                                            description: statusValue.description,
                                            userName1: `${loginUser.firstName} ${loginUser.lastName}`,
                                            inspectionID: statusValue.InspectionId,
                                            InspectionId: statusValue.InspectionId,
                                            status_timestamp: moment().utc().format('MM-DD-YYYY hh:mm:ss a zz'),
                                            timestamp: time,
                                        };
                                        const isMemberFollowLocation = await LocationNotificationPreferences.findOne({
                                            where: {
                                                MemberId: +element.MemberId,
                                                ProjectId: +statusValue.ProjectId,
                                                LocationId: +statusValue.LocationId,
                                                isDeleted: false,
                                                // follow: true,
                                            },
                                        });
                                        if (isMemberFollowLocation) {
                                            const memberNotification = await NotificationPreference.findOne({
                                                where: {
                                                    MemberId: +element.MemberId,
                                                    ProjectId: +statusValue.ProjectId,
                                                    isDeleted: false,
                                                },
                                                include: [
                                                    {
                                                        association: 'NotificationPreferenceItem',
                                                        where: {
                                                            id: 10,
                                                            isDeleted: false,
                                                        },
                                                    },
                                                ],
                                            });
                                            if (memberNotification && memberNotification.instant) {
                                                await MAILER.sendMail(
                                                    mailPayload,
                                                    'deliveredDR',
                                                    'Inspection Booking status updated',
                                                    'Inspection Booking status updated',
                                                    async (info, err) => {
                                                        console.log(info, err);
                                                    },
                                                );
                                            }
                                            if (memberNotification && memberNotification.dailyDigest) {
                                                await this.createDailyDigestData(
                                                    +memberValue.RoleId,
                                                    +element.MemberId,
                                                    +statusValue.ProjectId,
                                                    +statusValue.ParentCompanyId,
                                                    loginUser,
                                                    'delivered a',
                                                    'Inspection Request',
                                                    `Inspection Booking (${statusValue.InspectionId} - ${statusValue.description})`,
                                                    statusValue.id,
                                                );
                                            }
                                        }
                                    });
                                    return done(history, false);
                                }
                                return done(history, false);
                            }
                            return done(history, false);
                        }
                        return done(null, { message: 'Invalid Status' });
                    }
                    return done(null, {
                        message: 'Only Project Admin and General Contracter allowed to update the status',
                    });
                }
                return done(null, {
                    message: 'Not a Valid Member for this Inspection Booking',
                });
            }
        } catch (e) {
            return done(null, e);
        }
    },
    async bulkInspectionRequestUpload(inputData, done) {
        try {
            await this.getDynamicModel(inputData);
            const { file } = inputData;
            const ProjectId = +inputData.params.ProjectId;
            const memberDetail = await Member.findOne({
                where: [
                    Sequelize.and(
                        {
                            UserId: inputData.user.id,
                            ProjectId,
                            isDeleted: false,
                        },
                        Sequelize.or({ RoleId: [1, 2, 3, 4] }),
                    ),
                ],
            });
            if (memberDetail) {
                if (file && file.originalname) {
                    const splitValue = file.originalname.split('.');
                    const extension = splitValue[splitValue.length - 1];
                    const fileName = splitValue[0];
                    const firstSplitFileName = fileName.split('_');
                    if (firstSplitFileName.length === 3) {
                        const projectFileName = firstSplitFileName[0];
                        const projectId = firstSplitFileName[1];
                        const projectDetails = await Project.findByPk(ProjectId);
                        if (
                            projectDetails.projectName.toLowerCase() === projectFileName.toLowerCase() &&
                            +ProjectId === +projectId
                        ) {
                            if (extension === 'xlsx') {
                                const newWorkbook = new ExcelJS.Workbook();
                                await newWorkbook.xlsx.readFile(file.path);
                                const worksheet = newWorkbook.getWorksheet('Inspection Booking');
                                this.createInspectionRequestFile(worksheet, inputData, (resValue, error) => {
                                    if (!error) {
                                        return done(resValue, false);
                                    }
                                    return done(null, error);
                                });
                            } else {
                                return done(null, { message: 'Please choose valid file' });
                            }
                        } else {
                            return done(null, { message: 'Invalid file' });
                        }
                    } else {
                        return done(null, { message: 'Invalid file' });
                    }
                } else {
                    return done(null, { message: 'Please select a file.' });
                }
            } else {
                return done(null, { message: 'Project Does not exist or you are not a valid member.' });
            }
        } catch (e) {
            done(null, e);
        }
    },
    async createInspectionRequestFile(InspectionRequestWorksheet, inputData, done) {
        try {
            await this.getDynamicModel(inputData);
            const existProjectId = inputData.params.ProjectId;
            const ProjectId = +existProjectId;
            const loginUser = inputData.user;
            const projectDetails = await Project.findByPk(ProjectId);
            let fileFormat = true;
            const worksheet = InspectionRequestWorksheet;
            const ndrRecords = [];
            let headers;
            if (worksheet) {
                worksheet.eachRow(async (rowData, rowNumber) => {
                    const singleRowData = rowData.values;
                    singleRowData.shift();
                    if (rowNumber === 2) {
                        headers = rowData.values;
                    } else if (singleRowData.length > 0 && rowNumber >= 2) {
                        const getRow = rowData.values;
                        const description = getRow[2];
                        if (description) {
                            ndrRecords.push(rowData.values);
                        }
                    }
                });
                if (ndrRecords !== undefined && ndrRecords.length === 0) {
                    return done(null, {
                        message: 'Please upload proper document / Please fill Mandatory columns',
                    });
                }
                if (inputData.file) {
                    if (+headers.length === 15) {
                        fileFormat = false;
                    }
                }
                if (fileFormat) {
                    const worker = new Worker(bulkNdrProcess);
                    const object = stringify({
                        projectDetails,
                        loginUser,
                        ndrRecords,
                        ProjectId,
                        inputData,
                    });
                    worker.postMessage(object);
                    worker.on('message', (data) => {
                        if (data === 'success') {
                            const socketObject = {
                                message: data,
                                loginUserId: loginUser.id,
                            };
                            global.io.emit('bulkNdrNotification', socketObject);
                            worker.terminate();
                        }
                    });
                    worker.on('exit', (data) => {
                        console.log('worker thread exit ', data);
                    });
                    done({ message: 'success' }, false);
                } else {
                    done(null, { message: 'Invalid File Format' });
                }
            } else {
                done(null, { message: 'Invalid File' });
            }
        } catch (e) {
            done(null, e);
        }
    },
    async deleteQueuedNdr(input, done) {
        try {
            await this.getDynamicModel(input);
            const reqData = input.body;
            const inputData = {
                isDeleted: true,
            };
            if (reqData.queuedInspectionRequestSelectAll) {
                const deleteValue = await InspectionRequest.update(inputData, {
                    where: {
                        ProjectId: reqData.ProjectId,
                        isQueued: true,
                    },
                });
                done(deleteValue, false);
            } else {
                const { id } = input.body;
                const deleteValue = await InspectionRequest.update(inputData, {
                    where: { id: { [Op.in]: id } },
                });
                done(deleteValue, false);
            }
        } catch (e) {
            done(null, e);
        }
    },
    async editMultipleInspectionRequest(req) {
        try {
            const payload = req.body;
            if (payload.InspectionRequestIds && payload.InspectionRequestIds.length > 0) {
                await this.getDynamicModel(req);
                const loginUser = req.user;
                const { editedFields } = payload;
                let responsiblePersonsEdited = false;
                let escortEdited = false;
                const fieldsArray = editedFields.split(',');
                fieldsArray.forEach((element) => {
                    if (element === 'Responsible Person') {
                        responsiblePersonsEdited = true;
                    }
                    if (element === 'Escort') {
                        escortEdited = true;
                    }
                });
                const projectSettingDetails = await Project.getProjectAndSettings({
                    isDeleted: false,
                    id: +payload.ProjectId,
                });
                for (let mainIndex = 1; mainIndex <= payload.InspectionRequestIds.length; mainIndex += 1) {
                    let getInspectionRequestDetail = {};
                    getInspectionRequestDetail = await InspectionRequest.findOne({
                        where: [
                            Sequelize.and({
                                id: payload.InspectionRequestIds[mainIndex - 1],
                            }),
                        ],
                    });
                    const getExistsSingleInspectionRequest = await InspectionRequest.getSingleInspectionRequestData(
                        {
                            id: +payload.InspectionRequestIds[mainIndex - 1],
                        },
                    );
                    const InspectionRequestId = payload.InspectionRequestIds[mainIndex - 1];
                    const requestArray = [];
                    // checking the booking overlapping is allowed or not
                    if (payload.inspectionStart && payload.inspectionEnd) {
                        requestArray.push({
                            ProjectId: payload.ProjectId,
                            inspectionStart: payload.inspectionStart,
                            inspectionEnd: payload.inspectionEnd,
                            id: InspectionRequestId,
                        });
                        const isOverlapping = await this.checkDoubleBookingAllowedOrNot(
                            requestArray,
                            projectSettingDetails,
                            'edit',
                            getExistsSingleInspectionRequest.gateDetails[0].Gate.id,
                        );
                        if (isOverlapping && isOverlapping.error) {
                            return { success: false, message: isOverlapping.message };
                        }
                    }
                    if (
                        (payload.inspectionStart && !payload.inspectionEnd) ||
                        (!payload.inspectionStart && payload.inspectionEnd)
                    ) {
                        return { success: false, message: 'Booking start or end time is missing' };
                    }
                    const InspectionParam = {};
                    let lastData = {};
                    let craneId = 0;
                    if (payload.EquipmentId && payload.EquipmentId.length > 0) {
                        if (
                            payload.isAssociatedWithCraneRequest &&
                            !getInspectionRequestDetail.isAssociatedWithCraneRequest
                        ) {
                            lastData = await CraneRequest.findOne({
                                where: { ProjectId: +payload.ProjectId, isDeleted: false },
                                order: [['CraneRequestId', 'DESC']],
                            });
                            const InspectionRequestList = await InspectionRequest.findOne({
                                where: {
                                    ProjectId: +payload.ProjectId,
                                    isDeleted: false,
                                    isAssociatedWithCraneRequest: true,
                                },
                                order: [['CraneRequestId', 'DESC']],
                            });
                            if (InspectionRequestList) {
                                if (lastData) {
                                    if (InspectionRequestList.CraneRequestId > lastData.CraneRequestId) {
                                        lastData.CraneRequestId = InspectionRequestList.CraneRequestId;
                                    }
                                } else {
                                    lastData = {};
                                    lastData.CraneRequestId = InspectionRequestList.CraneRequestId;
                                }
                            }
                            if (lastData) {
                                const data = lastData.CraneRequestId;
                                lastData.CraneRequestId = 0;
                                lastData.CraneRequestId = data + 1;
                            } else {
                                lastData = {};
                                lastData.CraneRequestId = 1;
                            }
                            const newId = JSON.parse(JSON.stringify(lastData));
                            if (newId && newId.CraneRequestId !== null && newId.CraneRequestId !== undefined) {
                                craneId = newId.CraneRequestId;
                            }
                            InspectionParam.CraneRequestId = craneId;
                            InspectionParam.requestType = 'InspectionRequestWithCrane';
                            InspectionParam.cranePickUpLocation = payload.cranePickUpLocation;
                            InspectionParam.craneDropOffLocation = payload.craneDropOffLocation;
                            InspectionParam.isAssociatedWithCraneRequest = payload.isAssociatedWithCraneRequest;
                        } else if (
                            payload.isAssociatedWithCraneRequest &&
                            getInspectionRequestDetail.isAssociatedWithCraneRequest
                        ) {
                            InspectionParam.requestType = 'InspectionRequestWithCrane';
                            InspectionParam.cranePickUpLocation = payload.cranePickUpLocation;
                            InspectionParam.craneDropOffLocation = payload.craneDropOffLocation;
                            InspectionParam.isAssociatedWithCraneRequest = payload.isAssociatedWithCraneRequest;
                        } else if (!payload.isAssociatedWithCraneRequest) {
                            InspectionParam.requestType = 'InspectionRequest';
                            InspectionParam.cranePickUpLocation = null;
                            InspectionParam.craneDropOffLocation = null;
                            InspectionParam.isAssociatedWithCraneRequest = payload.isAssociatedWithCraneRequest;
                        }
                    }

                    if (getInspectionRequestDetail && getInspectionRequestDetail.recurrenceId) {
                        const { recurrenceId } = getInspectionRequestDetail;
                        const recurrenceSeriesStartDate = moment(getInspectionRequestDetail.inspectionStart).format(
                            'YYYY-MM-DD',
                        );
                        const recurrenceSeriesEndDate =
                            getExistsSingleInspectionRequest.recurrence.recurrenceEndDate;
                        const previousSeriesRecurrenceEndDate = moment(getInspectionRequestDetail.inspectionStart)
                            .add(-1, 'days')
                            .format('YYYY-MM-DD');
                        const chosenTimezonePreviousSeriesRecurrenceEndDate = moment.tz(
                            `${previousSeriesRecurrenceEndDate}  '00:00'`,
                            'YYYY-MM-DD HH:mm',
                            payload.timezone,
                        );
                        const utcPreviousSeriesRecurrenceEndDate = chosenTimezonePreviousSeriesRecurrenceEndDate
                            .clone()
                            .tz('UTC')
                            .format('YYYY-MM-DD HH:mm:ssZ');
                        const nextSeriesRecurrenceStartDate = moment(getInspectionRequestDetail.inspectionStart)
                            .add(1, 'days')
                            .format('YYYY-MM-DD');
                        const chosenTimezoneNextSeriesRecurrenceStartDate = moment.tz(
                            `${nextSeriesRecurrenceStartDate}  '00:00'`,
                            'YYYY-MM-DD HH:mm',
                            payload.timezone,
                        );
                        const utcNextSeriesRecurrenceStartDate = chosenTimezoneNextSeriesRecurrenceStartDate
                            .clone()
                            .tz('UTC')
                            .format('YYYY-MM-DD HH:mm:ssZ');

                        const previousRecordInThisEventSeries = await InspectionRequest.findAll({
                            where: [
                                Sequelize.and({
                                    recurrenceId,
                                    id: {
                                        [Op.lt]: getInspectionRequestDetail.id,
                                    },
                                }),
                            ],
                            order: [['id', 'DESC']],
                        });
                        const NextSeriesLastRecord = await InspectionRequest.findAll({
                            where: [
                                Sequelize.and({
                                    recurrenceId,
                                    id: {
                                        [Op.gt]: getInspectionRequestDetail.id,
                                    },
                                }),
                            ],
                            order: [['id', 'DESC']],
                        });
                        if (
                            (NextSeriesLastRecord && NextSeriesLastRecord.length > 0) ||
                            (previousRecordInThisEventSeries && previousRecordInThisEventSeries.length > 0)
                        ) {
                            if (previousRecordInThisEventSeries && previousRecordInThisEventSeries.length === 1) {
                                await InspectionRequest.update(
                                    { recurrenceId: null },
                                    {
                                        where: { id: previousRecordInThisEventSeries[0].id },
                                    },
                                );
                            }
                            if (NextSeriesLastRecord && NextSeriesLastRecord.length === 1) {
                                await InspectionRequest.update(
                                    { recurrenceId: null },
                                    {
                                        where: { id: NextSeriesLastRecord[0].id },
                                    },
                                );
                            }
                            if (previousRecordInThisEventSeries && previousRecordInThisEventSeries.length > 1) {
                                await RequestRecurrenceSeries.update(
                                    {
                                        recurrenceEndDate: utcPreviousSeriesRecurrenceEndDate,
                                    },
                                    {
                                        where: {
                                            id: previousRecordInThisEventSeries[0].recurrenceId,
                                        },
                                    },
                                );
                            }

                            if (
                                (previousRecordInThisEventSeries && previousRecordInThisEventSeries.length === 1) ||
                                (previousRecordInThisEventSeries && previousRecordInThisEventSeries.length === 0)
                            ) {
                                if (NextSeriesLastRecord && NextSeriesLastRecord.length > 1) {
                                    await RequestRecurrenceSeries.update(
                                        {
                                            recurrenceStartDate: utcNextSeriesRecurrenceStartDate,
                                        },
                                        {
                                            where: {
                                                id: NextSeriesLastRecord[0].recurrenceId,
                                            },
                                        },
                                    );
                                }
                            } else if (
                                previousRecordInThisEventSeries &&
                                previousRecordInThisEventSeries.length > 1
                            ) {
                                if (NextSeriesLastRecord && NextSeriesLastRecord.length > 1) {
                                    const recurrenceObject = {
                                        ProjectId: payload.ProjectId,
                                        ParentCompanyId: payload.ParentCompanyId,
                                        recurrence: getExistsSingleInspectionRequest.recurrence.recurrence,
                                        repeatEveryCount: getExistsSingleInspectionRequest.recurrence.repeatEveryCount,
                                        repeatEveryType: getExistsSingleInspectionRequest.recurrence.repeatEveryType,
                                        days: getExistsSingleInspectionRequest.recurrence.days,
                                        dateOfMonth: getExistsSingleInspectionRequest.recurrence.dateOfMonth,
                                        chosenDateOfMonth: getExistsSingleInspectionRequest.recurrence.chosenDateOfMonth,
                                        monthlyRepeatType: getExistsSingleInspectionRequest.recurrence.monthlyRepeatType,
                                        requestType: getExistsSingleInspectionRequest.requestType,
                                        createdBy: loginUser.id,
                                        recurrenceStartDate: utcNextSeriesRecurrenceStartDate,
                                        recurrenceEndDate: recurrenceSeriesEndDate,
                                    };
                                    const recurrenceSeries =
                                        await RequestRecurrenceSeries.createInstance(recurrenceObject);
                                    const newRecurrenceId = recurrenceSeries.id;
                                    for (let index = 0; index < NextSeriesLastRecord.length; index++) {
                                        const element = NextSeriesLastRecord[index];
                                        await InspectionRequest.update(
                                            { recurrenceId: newRecurrenceId },
                                            {
                                                where: { id: element.id },
                                            },
                                        );
                                    }
                                }
                            }
                        }

                        // creating a copy of request was not handled(refer edit if need to handle)
                        if (getInspectionRequestDetail) {
                            const requestData = await InspectionRequest.getSingleInspectionRequestData({
                                id: +getInspectionRequestDetail.id,
                            });
                            // inspectionResponse.push(requestData);
                            if (requestData && requestData.recurrence) {
                                requestData.recurrence.ParentCompanyId = payload.ParentCompanyId;
                                requestData.recurrence.ProjectId = payload.ProjectId;
                                requestData.recurrence.inspectionStart = recurrenceSeriesStartDate;
                                requestData.recurrence.inspectionEnd = recurrenceSeriesEndDate;
                            }
                        }
                    }

                    const seriesData = getInspectionRequestDetail;
                    const idDetails = await InspectionRequest.findOne({
                        where: [
                            Sequelize.and({
                                id: seriesData.id,
                            }),
                        ],
                    });
                    if (!idDetails) {
                        return { success: false, message: 'something Went wrong!!!' };
                    }
                    const existsInspectionRequest = await InspectionRequest.getSingleInspectionRequestData({
                        id: +idDetails.id,
                    });

                    if (existsInspectionRequest) {
                        // note: checkInput was need to handle

                        // await this.checkInputDatas(req, async (checkResponse, checkError) => {
                        // if (checkError) {
                        //   return done( { success: false, data: checkError });
                        // }
                        const memberData = await Member.getBy({
                            UserId: loginUser.id,
                            ProjectId: +payload.ProjectId,
                        });
                        const history = {
                            InspectionRequestId: idDetails.id,
                            InspectionId: idDetails.InspectionId,
                            MemberId: memberData.id,
                            type: 'edit',
                            description: `${loginUser.firstName} ${loginUser.lastName} Edited this Inspection Booking.`,
                        };
                        const notification = history;
                        if (
                            ((memberData.RoleId === 2 || memberData.RoleId === 1) &&
                                idDetails.status === 'Approved') ||
                            memberData.isAutoApproveEnabled ||
                            projectSettingDetails.ProjectSettings.isAutoApprovalEnabled
                        ) {
                            InspectionParam.status = 'Approved';
                            InspectionParam.approvedBy = memberData.id;
                            InspectionParam.approved_at = new Date();
                        }
                        InspectionParam.recurrenceId = null;
                        await InspectionRequest.update(InspectionParam, {
                            where: { id: idDetails.id },
                        });

                        const condition = Sequelize.and({
                            ProjectId: +payload.ProjectId,
                            InspectionId: idDetails.id,
                        });
                        const updateParam = {
                            InspectionId: getInspectionRequestDetail.id,
                            inspectionCode: getInspectionRequestDetail.InspectionId,
                            ProjectId: getInspectionRequestDetail.ProjectId,
                            isDeleted: false,
                        };

                        const addedCompany = [];
                        const addedPerson = [];
                        const addedDefineData = [];
                        const addedGate = [];
                        const addedEquipment = [];
                        let existCompanies;
                        let existPerson;
                        let existDefine;
                        let existGate;
                        let existEquipment;
                        if (payload.companies && payload.companies.length > 0) {
                            existCompanies = await InspectionCompany.findAll({ where: condition });
                            const deletedCompany = existCompanies.filter((e) => {
                                return payload.companies.indexOf(e.CompanyId) === -1 && e.isDeleted === false;
                            });
                            await InspectionCompany.update({ isDeleted: true }, { where: condition });
                            payload.companies.forEach(async (element, companyIndexValue) => {
                                const index = existCompanies.findIndex((item) => item.CompanyId === element);
                                const companyParam = updateParam;
                                companyParam.CompanyId = element;
                                if (index !== -1) {
                                    await InspectionCompany.update(companyParam, {
                                        where: { id: existCompanies[index].id },
                                    });
                                    if (existCompanies[index].isDeleted !== false) {
                                        addedCompany.push(existCompanies[index]);
                                    }
                                } else {
                                    const newCompanyData = await InspectionCompany.createInstance(companyParam);
                                    addedCompany.push(newCompanyData);
                                }
                                if (companyIndexValue === payload.companies.length - 1) {
                                    this.updateCompanyHistory(addedCompany, deletedCompany, history, loginUser);
                                }
                            });
                        }
                        if (responsiblePersonsEdited) {
                            if (payload.persons && payload.persons.length > 0) {
                                existPerson = await InspectionPerson.findAll({ where: condition });
                                const deletedPerson = existPerson.filter((e) => {
                                    return payload.persons.indexOf(e.MemberId) === -1 && e.isDeleted === false;
                                });
                                await InspectionPerson.update({ isDeleted: true }, { where: condition });
                                payload.persons.forEach(async (element, personIndexValue) => {
                                    const index = existPerson.findIndex((item) => item.MemberId === element);
                                    const memberParam = updateParam;
                                    memberParam.MemberId = element;
                                    if (index !== -1) {
                                        await InspectionPerson.update(memberParam, {
                                            where: { id: existPerson[index].id },
                                        });
                                        if (existPerson[index].isDeleted !== false) {
                                            addedPerson.push(existPerson[index]);
                                        }
                                    } else {
                                        const newPersonData = await InspectionPerson.createInstance(memberParam);
                                        addedPerson.push(newPersonData);
                                    }
                                    if (personIndexValue === payload.persons.length - 1) {
                                        this.updatePersonHistory(addedPerson, deletedPerson, history, loginUser);
                                    }
                                });
                            }
                        }

                        if (payload.define && payload.define.length > 0) {
                            existDefine = await DeliverDefine.findAll({ where: condition });
                            const deletedDefine = existDefine.filter((e) => {
                                return (
                                    payload.define.indexOf(e.DeliverDefineWorkId) === -1 && e.isDeleted === false
                                );
                            });
                            await DeliverDefine.update({ isDeleted: true }, { where: condition });
                            payload.define.forEach(async (element, defineIndexValue) => {
                                const index = existDefine.findIndex((item) => item.DeliverDefineWorkId === element);
                                const defineParam = updateParam;
                                defineParam.DeliverDefineWorkId = element;
                                if (index !== -1) {
                                    await DeliverDefine.update(defineParam, {
                                        where: { id: existDefine[index].id },
                                    });
                                    if (existDefine[index].isDeleted !== false) {
                                        addedDefineData.push(existDefine[index]);
                                    }
                                } else {
                                    const newDefineData = await DeliverDefine.createInstance(defineParam);
                                    addedDefineData.push(newDefineData);
                                }
                                if (defineIndexValue === payload.define.length - 1) {
                                    this.updateDefineHistory(addedDefineData, deletedDefine, history, loginUser);
                                }
                            });
                        }
                        if (escortEdited) {
                            if (payload.escort) {
                                await InspectionRequest.update(
                                    {
                                        escort: payload.escort,
                                        // status: payload.status ? payload.status : 'Pending'
                                    },
                                    {
                                        where: { id: InspectionRequestId },
                                    },
                                );
                            }
                        }

                        if (payload.GateId) {
                            const gates = [payload.GateId];
                            existGate = await InspectionGate.findAll({ where: condition });
                            const deletedGate = existGate.filter((e) => {
                                return gates.indexOf(e.GateId) === -1 && e.isDeleted === false;
                            });
                            await InspectionGate.update({ isDeleted: true }, { where: condition });
                            gates.forEach(async (element, gateIndexValue) => {
                                const index = existGate.findIndex((item) => item.GateId === element);
                                const gateParam = updateParam;
                                gateParam.GateId = element;
                                if (index !== -1) {
                                    await InspectionGate.update(gateParam, { where: { id: existGate[index].id } });
                                    if (existGate[index].isDeleted !== false) {
                                        addedGate.push(existGate[index]);
                                    }
                                } else {
                                    const newGateData = await InspectionGate.createInstance(gateParam);
                                    addedGate.push(newGateData);
                                }
                                if (gateIndexValue === gates.length - 1) {
                                    this.updateGateHistory(addedGate, deletedGate, history, loginUser);
                                }
                            });
                        }
                        if (payload.EquipmentId && payload.EquipmentId.length > 0) {
                            const equipments = payload.EquipmentId;
                            await InspectionRequest.update(
                                {
                                    escort: payload.escort,
                                    // status: payload.status ? payload.status : 'Pending'
                                },
                                {
                                    where: { id: InspectionRequestId },
                                },
                            );
                            existEquipment = await InspectionEquipment.findAll({ where: condition });
                            const deletedEquipment = existEquipment.filter((e) => {
                                return equipments.indexOf(e.EquipmentId) === -1 && e.isDeleted === false;
                            });
                            await InspectionEquipment.update({ isDeleted: true }, { where: condition });
                            equipments.forEach(async (element, equipmentIndexValue) => {
                                const index = existEquipment.findIndex((item) => item.EquipmentId === element);
                                const equipmentParam = updateParam;
                                equipmentParam.EquipmentId = element;
                                if (index !== -1) {
                                    await InspectionEquipment.update(equipmentParam, {
                                        where: { id: existEquipment[index].id },
                                    });
                                    if (existEquipment[index].isDeleted !== false) {
                                        addedEquipment.push(existEquipment[index]);
                                    }
                                } else {
                                    const newEquipmentData = await InspectionEquipment.createInstance(equipmentParam);
                                    addedEquipment.push(newEquipmentData);
                                }
                                if (equipmentIndexValue === equipments.length - 1) {
                                    this.updateEquipmentHistory(addedEquipment, deletedEquipment, history, loginUser);
                                }
                            });
                        }
                        if (payload.inspectionStart && payload.inspectionEnd) {
                            const startDate = new Date(payload.inspectionStart).getTime();
                            const currentDate = new Date().getTime();
                            const endDate = new Date(payload.inspectionEnd).getTime();
                            if (startDate > currentDate && endDate > currentDate) {
                                const InspectionParam = {
                                    // status: payload.status,
                                    inspectionStart: payload.inspectionStart,
                                    inspectionEnd: payload.inspectionEnd,
                                };
                                await InspectionRequest.update(InspectionParam, {
                                    where: { id: InspectionRequestId },
                                });
                            }
                        }
                        if (payload.void === true) {
                            const existVoid = await VoidList.findOne({
                                where: Sequelize.and({
                                    InspectionRequestId: InspectionRequestId,
                                }),
                            });
                            if (!existVoid) {
                                const voidcreate = {
                                    InspectionRequestId: InspectionRequestId,
                                    ProjectId: payload.ProjectId,
                                    ParentCompanyId: payload.ParentCompanyId,
                                };
                                const newVoidData = await VoidList.createInstance(voidcreate);
                                if (newVoidData) {
                                    const object = {
                                        ProjectId: payload.ProjectId,
                                        MemberId: memberData.id,
                                        InspectionRequestId: InspectionRequestId,
                                        isDeleted: false,
                                        type: 'void',
                                        description: `${loginUser.firstName} ${loginUser.lastName} Voided the Inspection Booking, ${getInspectionRequestDetail.description}`,
                                    };
                                    await InspectionHistory.createInstance(object);
                                }
                            } else {
                                return { success: false, message: 'Inspection Booking already is in void list.' };
                            }
                        }
                        if (payload.status) {
                            if (memberData.RoleId === 2 || memberData.RoleId === 1) {
                                const InspectionParam2 = {};
                                InspectionParam2.status = payload.status;
                                InspectionParam2.approvedBy = memberData.id;
                                InspectionParam2.approved_at = new Date();
                                await InspectionRequest.update(InspectionParam2, {
                                    where: { id: InspectionRequestId },
                                });
                            }
                        }

                        let persons;
                        if (responsiblePersonsEdited) {
                            persons = payload.persons;
                        } else {
                            const responsiblePersonsList = [];
                            getExistsSingleInspectionRequest.memberDetails.forEach((element) => {
                                responsiblePersonsList.push(element.Member.id);
                            });
                            persons = responsiblePersonsList;
                        }
                        const locationChosen = await Locations.findOne({
                            where: {
                                ProjectId: +payload.ProjectId,
                                id: existsInspectionRequest.LocationId,
                            },
                        });
                        history.description = `${loginUser.firstName} ${loginUser.lastName} Updated the Inspection Booking, ${getInspectionRequestDetail.description}`;
                        history.locationFollowDescription = `${loginUser.firstName} ${loginUser.lastName} Updated the Inspection Booking, ${getInspectionRequestDetail.description}. Location: ${locationChosen.locationPath}.`;
                        history.MemberId = memberData.id;
                        history.firstName = loginUser.firstName;
                        history.profilePic = loginUser.profilePic;
                        history.createdAt = new Date();
                        history.ProjectId = payload.ProjectId;
                        const projectDetails = await Project.findByPk(+payload.ProjectId);
                        history.projectName = projectDetails.projectName;
                        notification.ProjectId = idDetails.ProjectId;
                        notification.title = `Inspection Booking Updated by ${loginUser.firstName} ${loginUser.lastName}`;
                        if (
                            existsInspectionRequest &&
                            existsInspectionRequest.recurrence &&
                            existsInspectionRequest.recurrence.recurrence
                        ) {
                            notification.recurrenceType = `${existsInspectionRequest.recurrence.recurrence
                                } From ${moment(existsInspectionRequest.recurrence.recurrenceStartDate).format(
                                    'MM/DD/YYYY',
                                )} to ${moment(existsInspectionRequest.recurrence.recurrenceEndDate).format(
                                    'MM/DD/YYYY',
                                )}`;
                        }
                        notification.requestType = 'InspectionRequest';
                        const newNotification = await Notification.createInstance(notification);
                        const memberLocationPreference = await LocationNotificationPreferences.findAll({
                            where: {
                                ProjectId: payload.ProjectId,
                                LocationId: +existsInspectionRequest.LocationId,
                                follow: true,
                            },
                            include: [
                                {
                                    association: 'Member',
                                    attributes: ['id', 'RoleId'],
                                    where: {
                                        [Op.and]: [
                                            {
                                                id: { [Op.ne]: memberData.id },
                                            },
                                        ],
                                    },
                                    include: [
                                        {
                                            association: 'User',
                                            attributes: ['id', 'firstName', 'lastName', 'email'],
                                        },
                                    ],
                                },
                            ],
                        });

                        const locationFollowMembers = [];
                        memberLocationPreference.forEach(async (element) => {
                            locationFollowMembers.push(element.Member.id);
                        });
                        const adminData = await Member.findAll({
                            where: {
                                [Op.and]: [
                                    { ProjectId: payload.ProjectId },
                                    { isDeleted: false },
                                    { id: { [Op.in]: persons } },
                                    { id: { [Op.ne]: newNotification.MemberId } },
                                    { id: { [Op.notIn]: locationFollowMembers } },
                                ],
                            },
                            include: [
                                {
                                    association: 'User',
                                    attributes: ['id', 'firstName', 'lastName', 'email'],
                                },
                            ],
                            attributes: ['id', 'RoleId'],
                        });
                        if (memberLocationPreference && memberLocationPreference.length > 0) {
                            // here 5-(NotificationPreferenceItemId -When a inspection/crane/concrete request is edited)
                            await pushNotification.sendMemberLocationPreferencePushNotification(
                                memberLocationPreference,
                                getInspectionRequestDetail.InspectionRequestId,
                                history.locationFollowDescription,
                                InspectionParam.requestType,
                                payload.ProjectId,
                                getInspectionRequestDetail.id,
                                5,
                            );
                            // here 5-(NotificationPreferenceItemId -When a inspection/crane/concrete request is edited)
                            await notificationHelper.createMemberDeliveryLocationInAppNotification(
                                DeliveryPersonNotification,
                                payload.ProjectId,
                                newNotification.id,
                                memberLocationPreference,
                                5,
                            );
                        }
                        history.adminData = adminData;
                        if (memberLocationPreference && memberLocationPreference.length > 0) {
                            history.memberData = [];
                            history.memberData.push(...memberLocationPreference);
                        }
                        // here 5-(NotificationPreferenceItemId -When a inspection/crane/concrete request is edited)
                        await notificationHelper.createDeliveryPersonNotification(
                            adminData,
                            [],
                            projectDetails,
                            newNotification,
                            DeliveryPersonNotification,
                            memberData,
                            loginUser,
                            5,
                            'updated a',
                            'Inspection Request',
                            `Inspection Booking (${idDetails.InspectionId} - ${idDetails.description})`,
                            idDetails.id,
                        );
                        const checkMemberNotification = await NotificationPreference.findAll({
                            where: {
                                ProjectId: payload.ProjectId,
                                isDeleted: false,
                            },
                            attributes: [
                                'id',
                                'MemberId',
                                'ProjectId',
                                'ParentCompanyId',
                                'NotificationPreferenceItemId',
                                'instant',
                                'dailyDigest',
                            ],
                            include: [
                                {
                                    association: 'NotificationPreferenceItem',
                                    where: {
                                        id: 5,
                                        isDeleted: false,
                                    },
                                    attributes: ['id', 'description', 'inappNotification', 'emailNotification'],
                                },
                            ],
                        });
                        history.notificationPreference = checkMemberNotification;
                        const updatedInspectionRequest = await InspectionRequest.getSingleInspectionRequestData({
                            id: +idDetails.id,
                        });
                        if (
                            updatedInspectionRequest.status === 'Approved' &&
                            idDetails.status !== 'Approved' &&
                            idDetails.isQueued === false
                        ) {
                            const object = {
                                ProjectId: payload.ProjectId,
                                MemberId: memberData.id,
                                InspectionRequestId: updatedInspectionRequest.id,
                                isDeleted: false,
                                type: 'approved',
                                description: history.description,
                            };
                            await InspectionHistory.createInstance(object);
                        }
                        await this.updateEditInspectionRequestHistory(
                            updatedInspectionRequest,
                            existsInspectionRequest,
                            updatedInspectionRequest,
                            history,
                            loginUser,
                        );
                        // here 5-(NotificationPreferenceItemId -When a inspection/crane/concrete request is edited)
                        await pushNotification.sendDeviceTokenForInspection(history, 5, payload.ProjectId);
                        const editedNDR = await InspectionRequest.getNDRData({
                            id: idDetails.id,
                        });
                        // if (!inspectionData.ndrStatus) {
                        let tagsUpdated = false;
                        let fieldsChanged = false;
                        if (
                            editedNDR.defineWorkDetails?.length > 0 &&
                            existsInspectionRequest.defineWorkDetails?.length > 0
                        ) {
                            const addedDfow1 = editedNDR.defineWorkDetails.filter((el) => {
                                return !existsInspectionRequest.defineWorkDetails.find((element) => {
                                    return element.id === el.id;
                                });
                            });
                            const deletedDfow1 = existsInspectionRequest.defineWorkDetails.filter((el) => {
                                return !existsInspectionRequest.defineWorkDetails.find((element) => {
                                    return element.id === el.id;
                                });
                            });
                            if (addedDfow1.length > 0) {
                                tagsUpdated = true;
                            }
                            if (deletedDfow1.length > 0) {
                                tagsUpdated = true;
                            }
                        }
                        if (editedNDR.gateDetails.length > 0 && existsInspectionRequest.gateDetails.length > 0) {
                            const addedGate1 = editedNDR.gateDetails.filter((el) => {
                                return !existsInspectionRequest.gateDetails.find((element) => {
                                    return element.Gate.id === el.Gate.id;
                                });
                            });
                            const deletedGate1 = existsInspectionRequest.gateDetails.filter((el) => {
                                return !existsInspectionRequest.gateDetails.find((element) => {
                                    return element.Gate.id === el.Gate.id;
                                });
                            });
                            if (addedGate1.length > 0) {
                                tagsUpdated = true;
                            }
                            if (deletedGate1.length > 0) {
                                tagsUpdated = true;
                            }
                        }
                        if (
                            editedNDR.equipmentDetails.length > 0 &&
                            existsInspectionRequest.equipmentDetails.length > 0
                        ) {
                            const addedEquipment1 = editedNDR.equipmentDetails.filter((el) => {
                                return !existsInspectionRequest.equipmentDetails.find((element) => {
                                    return element.Equipment.id === el.Equipment.id;
                                });
                            });
                            const deletedEquipment1 = existsInspectionRequest.equipmentDetails.filter((el) => {
                                return !existsInspectionRequest.equipmentDetails.find((element) => {
                                    return element.Equipment.id === el.Equipment.id;
                                });
                            });
                            if (addedEquipment1.length > 0) {
                                tagsUpdated = true;
                            }
                            if (deletedEquipment1.length > 0) {
                                tagsUpdated = true;
                            }
                        }
                        if (
                            editedNDR.companyDetails.length > 0 &&
                            existsInspectionRequest.companyDetails.length > 0
                        ) {
                            const addedCompany1 = editedNDR.companyDetails.filter((el) => {
                                return !existsInspectionRequest.companyDetails.find((element) => {
                                    return element.Company.id === el.Company.id;
                                });
                            });
                            const deletedCompany1 = existsInspectionRequest.companyDetails.filter((el) => {
                                return !existsInspectionRequest.companyDetails.find((element) => {
                                    return element.Company.id === el.Company.id;
                                });
                            });
                            if (addedCompany1.length > 0) {
                                tagsUpdated = true;
                            }
                            if (deletedCompany1.length > 0) {
                                tagsUpdated = true;
                            }
                        }
                        if (
                            editedNDR.memberDetails.length > 0 &&
                            existsInspectionRequest.memberDetails.length > 0
                        ) {
                            const addedMember1 = editedNDR.memberDetails.filter((el) => {
                                return !existsInspectionRequest.memberDetails.find((element) => {
                                    return element.Member.id === el.Member.id;
                                });
                            });
                            const deletedMember1 = existsInspectionRequest.memberDetails.filter((el) => {
                                return !editedNDR.memberDetails.find((element) => {
                                    return element.Member.id === el.Member.id;
                                });
                            });
                            if (addedMember1.length > 0) {
                                tagsUpdated = true;
                            }
                            if (deletedMember1.length > 0) {
                                tagsUpdated = true;
                            }
                        }
                        if (
                            existsInspectionRequest.description !== editedNDR.description ||
                            existsInspectionRequest.CraneRequestId !== editedNDR.CraneRequestId ||
                            existsInspectionRequest.LocationId !== editedNDR.LocationId ||
                            existsInspectionRequest.requestType !== editedNDR.requestType ||
                            existsInspectionRequest.vehicleDetails !== editedNDR.vehicleDetails ||
                            existsInspectionRequest.notes !== editedNDR.notes ||
                            existsInspectionRequest.isAssociatedWithCraneRequest !==
                            editedNDR.isAssociatedWithCraneRequest ||
                            existsInspectionRequest.escort !== editedNDR.escort ||
                            existsInspectionRequest.craneDropOffLocation !== editedNDR.craneDropOffLocation ||
                            existsInspectionRequest.cranePickUpLocation !== editedNDR.cranePickUpLocation ||
                            tagsUpdated ||
                            existsInspectionRequest.recurrence !== editedNDR.recurrence ||
                            existsInspectionRequest.chosenDateOfMonth !== editedNDR.chosenDateOfMonth ||
                            existsInspectionRequest.dateOfMonth !== editedNDR.dateOfMonth ||
                            existsInspectionRequest.monthlyRepeatType !== editedNDR.monthlyRepeatType ||
                            existsInspectionRequest.days !== editedNDR.days ||
                            existsInspectionRequest.repeatEveryType !== editedNDR.repeatEveryType ||
                            existsInspectionRequest.repeatEveryCount !== editedNDR.repeatEveryCount
                        ) {
                            fieldsChanged = true;
                        }
                        let inspectionDateTimeChanged = false;
                        // if (
                        //   moment(existsInspectionRequest.inspectionStart).format('h:mm a') !==
                        //   moment(editedNDR.inspectionStart).format('h:mm a') ||
                        //   moment(existsInspectionRequest.inspectionEnd).format('h:mm a') !==
                        //   moment(editedNDR.inspectionEnd).format('h:mm a')
                        // ) {
                        //   inspectionDateTimeChanged = true;
                        // }
                        if (
                            new Date(existsInspectionRequest.inspectionStart) !== new Date(editedNDR.inspectionStart) ||
                            new Date(existsInspectionRequest.inspectionEnd) !== new Date(editedNDR.inspectionEnd)
                        ) {
                            inspectionDateTimeChanged = true;
                        }
                        if (!payload.status) {
                            if (existsInspectionRequest.status === 'Inspectioned') {
                                if (
                                    ((fieldsChanged || inspectionDateTimeChanged) && memberData.RoleId === 2) ||
                                    memberData.isAutoApproveEnabled ||
                                    projectSettingDetails.ProjectSettings.isAutoApprovalEnabled
                                ) {
                                    await InspectionRequest.update(
                                        { status: 'Approved', approvedBy: memberData.id, approved_at: new Date() },
                                        {
                                            where: { id: editedNDR.id },
                                        },
                                    );
                                }
                                if (
                                    (fieldsChanged || inspectionDateTimeChanged) &&
                                    memberData.RoleId !== 2 &&
                                    !memberData.isAutoApproveEnabled &&
                                    !projectSettingDetails.ProjectSettings.isAutoApprovalEnabled
                                ) {
                                    await InspectionRequest.update(
                                        { status: 'Pending' },
                                        {
                                            where: { id: editedNDR.id },
                                        },
                                    );
                                }
                            }
                            if (existsInspectionRequest.status === 'Approved') {
                                if (
                                    (fieldsChanged || inspectionDateTimeChanged) &&
                                    memberData.RoleId !== 2 &&
                                    !memberData.isAutoApproveEnabled &&
                                    !projectSettingDetails.ProjectSettings.isAutoApprovalEnabled
                                ) {
                                    await InspectionRequest.update(
                                        { status: 'Pending' },
                                        {
                                            where: { id: editedNDR.id },
                                        },
                                    );
                                }
                                if (
                                    ((fieldsChanged || inspectionDateTimeChanged) && memberData.RoleId === 2) ||
                                    memberData.isAutoApproveEnabled ||
                                    projectSettingDetails.ProjectSettings.isAutoApprovalEnabled
                                ) {
                                    await InspectionRequest.update(
                                        { status: 'Approved', approvedBy: memberData.id, approved_at: new Date() },
                                        {
                                            where: { id: editedNDR.id },
                                        },
                                    );
                                }
                            }
                            if (
                                existsInspectionRequest.status === 'Expired' ||
                                existsInspectionRequest.status === 'Declined'
                            ) {
                                if (
                                    (fieldsChanged || inspectionDateTimeChanged) &&
                                    memberData.RoleId !== 2 &&
                                    !memberData.isAutoApproveEnabled &&
                                    !projectSettingDetails.ProjectSettings.isAutoApprovalEnabled
                                ) {
                                    await InspectionRequest.update(
                                        { status: 'Pending' },
                                        {
                                            where: { id: editedNDR.id },
                                        },
                                    );
                                }
                                if (
                                    ((fieldsChanged || inspectionDateTimeChanged) && memberData.RoleId === 2) ||
                                    memberData.isAutoApproveEnabled ||
                                    projectSettingDetails.ProjectSettings.isAutoApprovalEnabled
                                ) {
                                    const isStatusDataUpdated = await InspectionRequest.update(
                                        { status: 'Approved', approvedBy: memberData.id, approved_at: new Date() },
                                        {
                                            where: { id: editedNDR.id },
                                        },
                                    );
                                    if (isStatusDataUpdated) {
                                        const object = {
                                            ProjectId: payload.ProjectId,
                                            MemberId: memberData.id,
                                            InspectionRequestId: updatedInspectionRequest.id,
                                            isDeleted: false,
                                            type: 'approved',
                                            description: `${loginUser.firstName} ${loginUser.lastName} Approved the Inspection Booking, ${getInspectionRequestDetail.description}`,
                                        };
                                        await InspectionHistory.createInstance(object);
                                    }
                                }
                            }
                            if (existsInspectionRequest.status === 'Pending') {
                                if (
                                    (fieldsChanged || inspectionDateTimeChanged) &&
                                    memberData.RoleId !== 2 &&
                                    !memberData.isAutoApproveEnabled &&
                                    !projectSettingDetails.ProjectSettings.isAutoApprovalEnabled
                                ) {
                                    await InspectionRequest.update(
                                        { status: 'Pending' },
                                        {
                                            where: { id: editedNDR.id },
                                        },
                                    );
                                }
                                if (
                                    ((fieldsChanged || inspectionDateTimeChanged) && memberData.RoleId === 2) ||
                                    memberData.isAutoApproveEnabled ||
                                    projectSettingDetails.ProjectSettings.isAutoApprovalEnabled
                                ) {
                                    const isStatusDataUpdated = await InspectionRequest.update(
                                        { status: 'Approved', approvedBy: memberData.id, approved_at: new Date() },
                                        {
                                            where: { id: editedNDR.id },
                                        },
                                    );
                                    if (isStatusDataUpdated) {
                                        const object = {
                                            ProjectId: payload.ProjectId,
                                            MemberId: memberData.id,
                                            InspectionRequestId: updatedInspectionRequest.id,
                                            isDeleted: false,
                                            type: 'approved',
                                            description: `${loginUser.firstName} ${loginUser.lastName} Approved the Inspection Request, ${getInspectionRequestDetail.description}`,
                                        };
                                        await InspectionHistory.createInstance(object);
                                    }
                                }
                            }
                        } else if (memberData.RoleId === 2 || memberData.RoleId === 1) {
                            const InspectionParam2 = {};
                            InspectionParam2.status = payload.status;
                            InspectionParam2.approvedBy = memberData.id;
                            InspectionParam2.approved_at = new Date();
                            await InspectionRequest.update(InspectionParam2, {
                                where: { id: InspectionRequestId },
                            });
                        }

                        if (+memberData.RoleId === 4 || +memberData.RoleId === 3) {
                            const userEmails = await this.getMemberDetailData(history, memberLocationPreference);
                            if (userEmails.length > 0) {
                                userEmails.forEach(async (element) => {
                                    if (element.RoleId === 2) {
                                        let name;
                                        if (!element.firstName) {
                                            name = 'user';
                                        } else {
                                            name = `${element.firstName} ${element.lastName}`;
                                        }
                                        const memberRole = await Role.findOne({
                                            where: {
                                                id: memberData.RoleId,
                                                isDeleted: false,
                                            },
                                        });
                                        const mailPayload = {
                                            name,
                                            email: element.email,
                                            content: `We would like to inform you that 
                          ${loginUser.firstName} ${loginUser.lastName} - ${memberRole.roleName} has updated a Inspection booking ${idDetails.InspectionId} and waiting for your approval.Kindly review the booking and update the status.`,
                                        };
                                        const isMemberFollowLocation = await LocationNotificationPreferences.findOne({
                                            where: {
                                                MemberId: +element.MemberId,
                                                ProjectId: +payload.ProjectId,
                                                LocationId: +existsInspectionRequest.LocationId,
                                                isDeleted: false,
                                                // follow: true,
                                            },
                                        });
                                        if (isMemberFollowLocation) {
                                            const memberNotification = await NotificationPreference.findOne({
                                                where: {
                                                    MemberId: +element.MemberId,
                                                    ProjectId: +payload.ProjectId,
                                                    isDeleted: false,
                                                },
                                                include: [
                                                    {
                                                        association: 'NotificationPreferenceItem',
                                                        where: {
                                                            id: 9,
                                                            isDeleted: false,
                                                        },
                                                    },
                                                ],
                                            });
                                            if (memberNotification && memberNotification.instant) {
                                                await MAILER.sendMail(
                                                    mailPayload,
                                                    'notifyPAForReApproval',
                                                    `Inspection Booking updated by ${loginUser.firstName} ${loginUser.lastName} - ${memberRole.roleName}`,
                                                    `Inspection Booking updated by ${loginUser.firstName} ${loginUser.lastName} - ${memberRole.roleName}`,
                                                    async (info, err) => {
                                                        console.log(info, err);
                                                    },
                                                );
                                            }
                                            if (memberNotification && memberNotification.dailyDigest) {
                                                await this.createDailyDigestDataApproval(
                                                    +memberData.RoleId,
                                                    +element.MemberId,
                                                    +payload.ProjectId,
                                                    +payload.ParentCompanyId,
                                                    loginUser,
                                                    'updated a',
                                                    'Inspection Request',
                                                    `Inspection Booking (${idDetails.InspectionId} - ${idDetails.description})`,
                                                    'and waiting for your approval',
                                                    idDetails.id,
                                                );
                                            }
                                        }
                                    }
                                });

                                const exist2 = await InspectionRequest.findOne({
                                    include: [
                                        {
                                            association: 'memberDetails',
                                            required: false,
                                            where: { isDeleted: false, isActive: true },
                                            attributes: ['id'],
                                            include: [
                                                {
                                                    association: 'Member',
                                                    attributes: ['id', 'isGuestUser'],
                                                    include: [
                                                        {
                                                            association: 'User',
                                                            attributes: [
                                                                'email',
                                                                'phoneCode',
                                                                'phoneNumber',
                                                                'firstName',
                                                                'lastName',
                                                            ],
                                                        },
                                                    ],
                                                },
                                            ],
                                        },
                                    ],
                                    where: { isDeleted: false, id: +getInspectionRequestDetail.id },
                                });
                                if (exist2 && exist2.memberDetails) {
                                    const userDataMail = exist2.memberDetails;
                                    if (userDataMail) {
                                        for (let i = 0; i < userDataMail.length; i++) {
                                            const responsibleGuestUser = userDataMail[i].Member.isGuestUser;
                                            if (responsibleGuestUser) {
                                                const guestMailPayload = {
                                                    email: responsibleGuestUser.User.email,
                                                    guestName: responsibleGuestUser.User.firstName,
                                                    content: `We would like to inform you that 
                                    ${loginUser.firstName} ${loginUser.lastName} has updated a Inspection booking ${exist2.description}.`,
                                                };
                                                await MAILER.sendMail(
                                                    guestMailPayload,
                                                    'notifyGuestOnEdit',
                                                    `Inspection Booking updated by ${loginUser.firstName} ${loginUser.lastName}`,
                                                    `Inspection Booking updated by ${loginUser.firstName} ${loginUser.lastName}`,
                                                    async (info, err) => {
                                                        console.log(info, err);
                                                    },
                                                );
                                            }
                                        }
                                    }
                                }
                                if (mainIndex === payload.InspectionRequestIds.length) {
                                    return { success: true, data: history };
                                    // return { success: true, data: inspectionResponse };
                                }
                            }
                        } else if (mainIndex === payload.InspectionRequestIds.length) {
                            return { success: true, data: history };
                        }
                        // } else {
                        //   return { success: true, data: history };
                        // }

                        // });
                    }

                    if (mainIndex === payload.InspectionRequestIds.length) {
                        return { success: true, data: history };
                    }
                }
            } else {
                return { message: 'Please select Inspection Booking to update.!' };
            }
        } catch (e) {
            return e;
        }
    },
    async updateEditInspectionRequestHistory(
        userEditedInspectionRequestData,
        existsInspectionRequestData,
        updatedInspectionRequest,
        history,
        loginUser,
    ) {
        const historyObject = history;
        if (
            (userEditedInspectionRequestData.description &&
                userEditedInspectionRequestData.description.toLowerCase()) !==
            (existsInspectionRequestData.description && existsInspectionRequestData.description.toLowerCase())
        ) {
            historyObject.description = `${loginUser.firstName} ${loginUser.lastName} Updated the Description ${userEditedInspectionRequestData.description}`;
            InspectionHistory.createInstance(historyObject);
        }
        if (
            userEditedInspectionRequestData.LocationId !== existsInspectionRequestData.LocationId &&
            updatedInspectionRequest &&
            updatedInspectionRequest.location &&
            updatedInspectionRequest.location.locationPath
        ) {
            historyObject.description = `${loginUser.firstName} ${loginUser.lastName} Updated the Location, ${updatedInspectionRequest.location.locationPath} `;
            InspectionHistory.createInstance(historyObject);
        }
        if (
            new Date(userEditedInspectionRequestData.inspectionStart).getTime() !==
            new Date(existsInspectionRequestData.inspectionStart).getTime()
        ) {
            historyObject.description = `${loginUser.firstName} 
      ${loginUser.lastName} Updated the Inspection Start Date ${userEditedInspectionRequestData.inspectionStart}`;
            InspectionHistory.createInstance(historyObject);
        }
        if (
            new Date(userEditedInspectionRequestData.inspectionEnd).getTime() !==
            new Date(existsInspectionRequestData.inspectionEnd).getTime()
        ) {
            historyObject.description = `${loginUser.firstName} 
      ${loginUser.lastName} Updated the Inspection End Date  ${userEditedInspectionRequestData.inspectionEnd}`;
            InspectionHistory.createInstance(historyObject);
        }
        if (userEditedInspectionRequestData.notes) {
            if (existsInspectionRequestData.notes) {
                if (
                    existsInspectionRequestData.notes.toLowerCase() !==
                    userEditedInspectionRequestData.notes.toLowerCase()
                ) {
                    historyObject.description = `${loginUser.firstName} ${loginUser.lastName} Updated the Notes ${existsInspectionRequestData.notes}`;
                    InspectionHistory.createInstance(historyObject);
                }
            } else {
                historyObject.description = `${loginUser.firstName} ${loginUser.lastName} Updated the Notes ${existsInspectionRequestData.notes}`;
                InspectionHistory.createInstance(historyObject);
            }
        }
        if (!userEditedInspectionRequestData.notes) {
            if (existsInspectionRequestData.notes) {
                historyObject.description = `${loginUser.firstName} ${loginUser.lastName} Removed the Notes ${existsInspectionRequestData.notes}`;
                InspectionHistory.createInstance(historyObject);
            }
        }
        if (userEditedInspectionRequestData.cranePickUpLocation) {
            if (
                userEditedInspectionRequestData.cranePickUpLocation !==
                existsInspectionRequestData.cranePickUpLocation
            ) {
                historyObject.description = `${loginUser.firstName} ${loginUser.lastName} Updated the Picking From ${userEditedInspectionRequestData.cranePickUpLocation}`;
                InspectionHistory.createInstance(historyObject);
            }
        }
        if (
            !userEditedInspectionRequestData.cranePickUpLocation &&
            existsInspectionRequestData.cranePickUpLocation
        ) {
            if (
                userEditedInspectionRequestData.cranePickUpLocation !==
                existsInspectionRequestData.cranePickUpLocation
            ) {
                historyObject.description = `${loginUser.firstName} ${loginUser.lastName} Removed the Picking From ${existsInspectionRequestData.cranePickUpLocation}`;
                InspectionHistory.createInstance(historyObject);
            }
        }
        if (userEditedInspectionRequestData.craneDropOffLocation) {
            if (
                userEditedInspectionRequestData.craneDropOffLocation !==
                existsInspectionRequestData.craneDropOffLocation
            ) {
                historyObject.description = `${loginUser.firstName} ${loginUser.lastName} Updated the Picking To ${userEditedInspectionRequestData.craneDropOffLocation}`;
                InspectionHistory.createInstance(historyObject);
            }
        }
        if (
            !userEditedInspectionRequestData.craneDropOffLocation &&
            existsInspectionRequestData.craneDropOffLocation
        ) {
            if (
                userEditedInspectionRequestData.craneDropOffLocation !==
                existsInspectionRequestData.craneDropOffLocation
            ) {
                historyObject.description = `${loginUser.firstName} ${loginUser.lastName} Removed the Picking To ${existsInspectionRequestData.craneDropOffLocation}`;
                InspectionHistory.createInstance(historyObject);
            }
        }
        if (userEditedInspectionRequestData.vehicleDetails) {
            if (existsInspectionRequestData.vehicleDetails) {
                if (
                    existsInspectionRequestData.vehicleDetails !== userEditedInspectionRequestData.vehicleDetails
                ) {
                    historyObject.description = `${loginUser.firstName} ${loginUser.lastName} Updated the Vechicle Details ${existsInspectionRequestData.vehicleDetails}`;
                    InspectionHistory.createInstance(historyObject);
                }
            } else {
                historyObject.description = `${loginUser.firstName} ${loginUser.lastName} Updated the Vechicle Details ${existsInspectionRequestData.vehicleDetails}`;
                InspectionHistory.createInstance(historyObject);
            }
        }
        if (!userEditedInspectionRequestData.vehicleDetails) {
            if (existsInspectionRequestData.vehicleDetails) {
                historyObject.description = `${loginUser.firstName} ${loginUser.lastName} Removed the Vechicle Details ${existsInspectionRequestData.vehicleDetails}`;
                InspectionHistory.createInstance(historyObject);
            }
        }
        if (userEditedInspectionRequestData.escort !== existsInspectionRequestData.escort) {
            if (userEditedInspectionRequestData.escort === true) {
                historyObject.description = `${loginUser.firstName} ${loginUser.lastName} enabled the Escort`;
            } else {
                historyObject.description = `${loginUser.firstName} ${loginUser.lastName} disabled the Escort`;
            }
            InspectionHistory.createInstance(historyObject);
        }
        if (
            updatedInspectionRequest.memberDetails.length > 0 &&
            existsInspectionRequestData.memberDetails.length > 0
        ) {
            const addedMember = updatedInspectionRequest.memberDetails.filter((el) => {
                return !existsInspectionRequestData.memberDetails.find((element) => {
                    return element.id === el.id;
                });
            });
            const deletedMember = existsInspectionRequestData.memberDetails.filter((el) => {
                return !updatedInspectionRequest.memberDetails.find((element) => {
                    return element.id === el.id;
                });
            });
            if (addedMember.length > 0) {
                addedMember.forEach(async (element) => {
                    historyObject.description = `${loginUser.firstName} ${loginUser.lastName} added the member ${element.Member.User.firstName} ${element.Member.User.lastName}`;
                    await InspectionHistory.createInstance(historyObject);
                });
            }
            if (deletedMember.length > 0) {
                deletedMember.forEach(async (element) => {
                    if (element.Member.User.firstName) {
                        historyObject.description = `${loginUser.firstName} ${loginUser.lastName} updated the member ${element.Member.User.firstName} ${element.Member.User.lastName}`;
                    } else {
                        historyObject.description = `${loginUser.firstName} ${loginUser.lastName} updated the member`;
                    }
                    await InspectionHistory.createInstance(historyObject);
                });
            }
        }
        if (
            updatedInspectionRequest.companyDetails.length > 0 &&
            existsInspectionRequestData.companyDetails.length > 0
        ) {
            const addedCompany = updatedInspectionRequest.companyDetails.filter((el) => {
                return !existsInspectionRequestData.companyDetails.find((element) => {
                    return element.id === el.id;
                });
            });
            const deletedCompany = existsInspectionRequestData.companyDetails.filter((el) => {
                return !updatedInspectionRequest.companyDetails.find((element) => {
                    return element.id === el.id;
                });
            });
            if (addedCompany.length > 0) {
                addedCompany.forEach(async (element) => {
                    historyObject.description = `${loginUser.firstName} ${loginUser.lastName} added the company ${element.Company.companyName}`;
                    await InspectionHistory.createInstance(historyObject);
                });
            }
            if (deletedCompany.length > 0) {
                deletedCompany.forEach(async (element) => {
                    historyObject.description = `${loginUser.firstName} ${loginUser.lastName} updated the company ${element.Company.companyName}`;
                    await InspectionHistory.createInstance(historyObject);
                });
            }
        }
        if (
            updatedInspectionRequest.defineWorkDetails?.length > 0 &&
            existsInspectionRequestData.defineWorkDetails?.length > 0
        ) {
            const addedDfow = updatedInspectionRequest.defineWorkDetails.filter((el) => {
                return !existsInspectionRequestData.defineWorkDetails.find((element) => {
                    return element.id === el.id;
                });
            });
            const deletedDfow = existsInspectionRequestData.defineWorkDetails.filter((el) => {
                return !updatedInspectionRequest.defineWorkDetails.find((element) => {
                    return element.id === el.id;
                });
            });
            if (addedDfow.length > 0) {
                addedDfow.forEach(async (element) => {
                    historyObject.description = `${loginUser.firstName} ${loginUser.lastName} added the Definable feature of work ${element.DeliverDefineWork.DFOW}`;
                    await InspectionHistory.createInstance(historyObject);
                });
            }
            if (deletedDfow.length > 0) {
                deletedDfow.forEach(async (element) => {
                    historyObject.description = `${loginUser.firstName} ${loginUser.lastName} updated the Definable feature of work ${element.DeliverDefineWork.DFOW}`;
                    await InspectionHistory.createInstance(historyObject);
                });
            }
        }
        if (
            updatedInspectionRequest.equipmentDetails.length > 0 &&
            existsInspectionRequestData.equipmentDetails.length > 0
        ) {
            const addedEquipment = updatedInspectionRequest.equipmentDetails.filter((el) => {
                return !existsInspectionRequestData.equipmentDetails.find((element) => {
                    return element.Equipment.id === el.Equipment.id;
                });
            });
            const deletedEquipment = existsInspectionRequestData.equipmentDetails.filter((el) => {
                return !updatedInspectionRequest.equipmentDetails.find((element) => {
                    return element.Equipment.id === el.Equipment.id;
                });
            });
            if (addedEquipment.length > 0) {
                addedEquipment.forEach(async (element) => {
                    historyObject.description = `${loginUser.firstName} ${loginUser.lastName} added the Equipment ${element.Equipment.equipmentName}`;
                    await InspectionHistory.createInstance(historyObject);
                });
            }
            if (deletedEquipment.length > 0) {
                deletedEquipment.forEach(async (element) => {
                    historyObject.description = `${loginUser.firstName} ${loginUser.lastName} updated the Equipment ${element.Equipment.equipmentName}`;
                    await InspectionHistory.createInstance(historyObject);
                });
            }
        }
        if (
            updatedInspectionRequest.gateDetails.length > 0 &&
            existsInspectionRequestData.gateDetails.length > 0
        ) {
            const addedGate = updatedInspectionRequest.gateDetails.filter((el) => {
                return !existsInspectionRequestData.gateDetails.find((element) => {
                    return element.Gate.id === el.Gate.id;
                });
            });
            const deletedGate = existsInspectionRequestData.gateDetails.filter((el) => {
                return !updatedInspectionRequest.gateDetails.find((element) => {
                    return element.Gate.id === el.Gate.id;
                });
            });
            if (addedGate.length > 0) {
                addedGate.forEach(async (element) => {
                    historyObject.description = `${loginUser.firstName} ${loginUser.lastName} added the Gate ${element.Gate.gateName}`;
                    await InspectionHistory.createInstance(historyObject);
                });
            }
            if (deletedGate.length > 0) {
                deletedGate.forEach(async (element) => {
                    historyObject.description = `${loginUser.firstName} ${loginUser.lastName} updated the Gate ${element.Gate.gateName}`;
                    await InspectionHistory.createInstance(historyObject);
                });
            }
        }
    },
    async getMemberDetailData(data, memberLocationPreference) {
        const emailArray = [];
        const existAdminData = [];
        if (data.memberData !== undefined) {
            data.memberData.forEach((element) => {
                const index = existAdminData.findIndex(
                    (adminNew) => adminNew.email === element.Member.User.email,
                );
                if (index === -1) {
                    existAdminData.push({ email: element.Member.User.email });
                    emailArray.push({
                        email: element.Member.User.email,
                        firstName: element.Member.User.firstName,
                        lastName: element.Member.User.lastName,
                        UserId: element.Member.User.id,
                        MemberId: element.Member.id,
                        RoleId: element.Member.RoleId,
                    });
                }
            });
        }
        if (data.adminData !== undefined) {
            data.adminData.forEach((element) => {
                const index = existAdminData.findIndex((adminNew) => adminNew.email === element.User.email);
                if (index === -1) {
                    existAdminData.push({ email: element.User.email });
                    emailArray.push({
                        email: element.User.email,
                        firstName: element.User.firstName,
                        lastName: element.User.lastName,
                        UserId: element.User.id,
                        MemberId: element.id,
                        RoleId: element.RoleId,
                    });
                }
            });
        }
        if (memberLocationPreference !== undefined && memberLocationPreference.length > 0) {
            memberLocationPreference.forEach((element) => {
                const index = existAdminData.findIndex(
                    (adminNew) => adminNew.email === element.Member.User.email,
                );
                if (index === -1) {
                    existAdminData.push({ email: element.Member.User.email });
                    emailArray.push({
                        email: element.Member.User.email,
                        firstName: element.Member.User.firstName,
                        lastName: element.Member.User.lastName,
                        UserId: element.Member.User.id,
                        MemberId: element.Member.id,
                        RoleId: element.Member.RoleId,
                    });
                }
            });
        }
        return emailArray;
    },
    // prettier-ignore
    async createDailyDigestData(
        RoleId,
        MemberId,
        ProjectId,
        ParentCompanyId,
        loginUser,
        dailyDigestMessage,
        requestType,
        messages,
        requestId,
    ) {
        const cryptr = new Cryptr('a0b1c2d3e4f5g6h7i8j9k10');
        const encryptedRequestId = cryptr.encrypt(requestId);
        const encryptedMemberId = cryptr.encrypt(MemberId);
        let imageUrl;
        let link;
        let height;
        if (requestType === 'Inspection Request') {
            imageUrl = 'https://d36hblf01wyurt.cloudfront.net/87758081-8ccd-4e4d-a4eb-6257466876da.png';
            link = 'inspection-request';
            height = 'height:18px;';
        }
        if (requestType === 'Crane Request') {
            imageUrl = 'https://d36hblf01wyurt.cloudfront.net/ab400721-520c-4f6f-941c-ac07acc28809.png';
            link = 'crane-request';
            height = 'height:32px;';
        }
        if (requestType === 'Concrete Request') {
            imageUrl = 'https://d36hblf01wyurt.cloudfront.net/c43e4c58-4c4d-44ff-b78c-fe9948ea53eb.png';
            link = 'concrete-request';
            height = 'height:18px;';
        }
        const object = {
            description: `<div>
	<ul style="list-style-type:none;padding:0px;border-bottom:1px dashed #E3E3E3;">
		<li style="display:flex;">
			<img src="${imageUrl}" alt="message-icon" style="${height}">
				<p style="margin:0px;font-size:12px;padding-left:10px;">
					<a href="#" ta
        rget="" style="text-decoration: none;color:#4470FF;">
			${loginUser.firstName}  ${loginUser.lastName}
					</a>
					${dailyDigestMessage}
      <a href = "${process.env.BASE_URL}/${link}?requestId=${encryptedRequestId}&memberId=${encryptedMemberId} " style="text - decoration: none; color:#4470FF; " >${messages}</a>
  <span style="color:#707070;">on ${moment().utc().format('MMMM DD')} at ${moment()
                    .utc()
                    .format('hh:mm A zz')}</span>
				</p>
		</li>
	</ul>
</div> `,
            MemberId,
            ProjectId,
            isSent: false,
            isDeleted: false,
            ParentCompanyId,
        };
        await DigestNotification.create(object);
    },
    async sendEmailNotificationToUser(
        history,
        memberDetails,
        loginUser,
        newInspectionData,
        inspectionData,
        memberLocationPreference,
    ) {
        const userEmails = await this.getMemberDetailData(history, memberLocationPreference);
        if (userEmails.length > 0) {
            userEmails.forEach(async (element) => {
                let name;
                if (!element.firstName) {
                    name = 'user';
                } else {
                    name = `${element.firstName} ${element.lastName} `;
                }
                if (+element.MemberId !== +memberDetails.id) {
                    const memberRole = await Role.findOne({
                        where: {
                            id: memberDetails.RoleId,
                            isDeleted: false,
                        },
                    });
                    const time = moment(newInspectionData.inspectionStart).format('MM-DD-YYYY');
                    const mailPayload = {
                        userName: name,
                        email: element.email,
                        InspectionId: newInspectionData.InspectionId,
                        description: newInspectionData.description,
                        timestamp: time,
                        createdTimestamp: moment().utc().format('MM-DD-YYYY hh:mm:ss a zz'),
                        content: ` ${loginUser.firstName} ${loginUser.lastName} - ${memberRole.roleName} has created the Inspection booking ${newInspectionData.InspectionId}.Please see below for more details`,
                    };
                    const memberNotification = await NotificationPreference.findOne({
                        where: {
                            MemberId: +element.MemberId,
                            ProjectId: +inspectionData.ProjectId,
                            isDeleted: false,
                        },
                        include: [
                            {
                                association: 'NotificationPreferenceItem',
                                where: {
                                    id: 12,
                                    isDeleted: false,
                                },
                            },
                        ],
                    });
                    if (memberNotification && memberNotification.instant) {
                        await MAILER.sendMail(
                            mailPayload,
                            'InspectionRequestCreated',
                            `Inspection Booking created by ${loginUser.firstName} ${loginUser.lastName} - ${memberRole.roleName} `,
                            `Inspection Booking created by ${loginUser.firstName} ${loginUser.lastName} - ${memberRole.roleName} `,
                            async (info, err) => {
                                console.log(info, err);
                            },
                        );
                    }
                    if (memberNotification && memberNotification.dailyDigest) {
                        await this.createDailyDigestData(
                            +memberDetails.RoleId,
                            +element.MemberId,
                            +inspectionData.ProjectId,
                            +inspectionData.ParentCompanyId,
                            loginUser,
                            'created a',
                            'Inspection Request',
                            `Inspection Booking(${newInspectionData.InspectionId} - ${newInspectionData.description})`,
                            newInspectionData.id,
                        );
                    }
                }
            });
        }
        return true;
    },
    async createDailyDigestDataApproval(
        RoleId,
        MemberId,
        ProjectId,
        ParentCompanyId,
        loginUser,
        dailyDigestMessage,
        requestType,
        messages,
        messages2,
        requestId,
    ) {
        const cryptr = new Cryptr('a0b1c2d3e4f5g6h7i8j9k10');
        const encryptedRequestId = cryptr.encrypt(requestId);
        const encryptedMemberId = cryptr.encrypt(MemberId);
        let imageUrl;
        let link;
        let height;
        if (requestType === 'inspection Request') {
            imageUrl = 'https://d36hblf01wyurt.cloudfront.net/87758081-8ccd-4e4d-a4eb-6257466876da.png';
            link = 'inspection-request';
            height = 'height:18px;';
        }
        if (requestType === 'Crane Request') {
            imageUrl = 'https://d36hblf01wyurt.cloudfront.net/ab400721-520c-4f6f-941c-ac07acc28809.png';
            link = 'crane-request';
            height = 'height:32px;';
        }
        if (requestType === 'Concrete Request') {
            imageUrl = 'https://d36hblf01wyurt.cloudfront.net/c43e4c58-4c4d-44ff-b78c-fe9948ea53eb.png';
            link = 'concrete-request';
            height = 'height:18px;';
        }
        const object = {
            description: `<div>
  <ul style="list-style-type:none;padding:0px;border-bottom:1px dashed #E3E3E3;">
    <li style="display:flex;">
      <img src="${imageUrl}" alt="message-icon" style="${height}">
        <p style="margin:0px;font-size:12px;padding-left:10px;">
          <a href="
        #" target="" style="text-decoration: none;color:#4470FF;">
          ${loginUser.firstName}  ${loginUser.lastName}
          </a>
          ${dailyDigestMessage}
      <a href = "${process.env.BASE_URL
                }/ ${link}?requestId = ${encryptedRequestId}& memberId=${encryptedMemberId} " style="text - decoration: none; color:#4470FF; " >
          ${messages}
        </a>
  ${messages2}
<span style="color:#707070;">on ${moment().utc().format('MMMM DD')} at ${moment()
                    .utc()
                    .format('hh:mm A zz')}</span>
        </p>
    </li>
  </ul>
</div> `,
            MemberId,
            ProjectId,
            isSent: false,
            isDeleted: false,
            ParentCompanyId,
        };
        await DigestNotification.create(object);
    },
    async convertTimezoneToUtc(date, timezone, time) {
        const chosenTimezoneinspectionStart = moment.tz(`${date} ${time}`, 'MM/DD/YYYY HH:mm', timezone);
        const utcDate = chosenTimezoneinspectionStart.clone().tz('UTC').format('YYYY-MM-DD HH:mm:ssZ');
        return utcDate;
    },
    async createCopyofInspectionRequest(dataInSeries, payload, dates, loginUser, newRecurrenceId) {
        const eventsArray = [];
        const memberDetails = await Member.getBy({
            UserId: loginUser.id,
            ProjectId: payload.ProjectId,
            isActive: true,
            isDeleted: false,
        });
        const projectDetails = await Project.getProjectAndSettings({
            isDeleted: false,
            id: +payload.ProjectId,
        });
        if (dataInSeries && dataInSeries.recurrence && dataInSeries.recurrence.recurrence) {
            let startDate;
            let endDate;
            if (payload.recurrence && dates && dates.length > 0) {
                startDate = await this.compareinspectionDateWithinspectionWindowDate(
                    dates[0],
                    payload.inspectionStartTime,
                    payload.timezone,
                    projectDetails.ProjectSettings.inspectionWindowTime,
                    projectDetails.ProjectSettings.inspectionWindowTimeUnit,
                );
                endDate = await this.compareinspectionDateWithinspectionWindowDate(
                    dates[0],
                    payload.inspectionEndTime,
                    payload.timezone,
                    projectDetails.ProjectSettings.inspectionWindowTime,
                    projectDetails.ProjectSettings.inspectionWindowTimeUnit,
                );
            }
            if (startDate || endDate) {
                throw new Error(
                    `Bookings can not be submitted within ${projectDetails.ProjectSettings.inspectionWindowTime} ${projectDetails.ProjectSettings.inspectionWindowTimeUnit} prior to the event`,
                );
            }
            let InspectionParam = {};
            const lastIdValue = await InspectionRequest.findOne({
                where: { ProjectId: memberDetails.ProjectId, isDeleted: false },
                order: [['InspectionId', 'DESC']],
            });
            let id = 0;
            const newValue = JSON.parse(JSON.stringify(lastIdValue));
            if (newValue && newValue.InspectionId !== null && newValue.InspectionId !== undefined) {
                id = newValue.InspectionId;
            }
            let lastData = {};
            lastData = await CraneRequest.findOne({
                where: { ProjectId: +memberDetails.ProjectId, isDeleted: false },
                order: [['CraneRequestId', 'DESC']],
            });
            const InspectionRequestList = await InspectionRequest.findOne({
                where: {
                    ProjectId: +memberDetails.ProjectId,
                    isDeleted: false,
                    isAssociatedWithCraneRequest: true,
                },
                order: [['CraneRequestId', 'DESC']],
            });
            if (InspectionRequestList) {
                if (lastData) {
                    if (InspectionRequestList.CraneRequestId > lastData.CraneRequestId) {
                        lastData.CraneRequestId = InspectionRequestList.CraneRequestId;
                    }
                } else {
                    lastData = {};
                    lastData.CraneRequestId = InspectionRequestList.CraneRequestId;
                }
            }
            if (lastData) {
                const data = lastData.CraneRequestId;
                lastData.CraneRequestId = 0;
                lastData.CraneRequestId = data + 1;
            } else {
                lastData = {};
                lastData.CraneRequestId = 1;
            }
            let craneId = 0;
            const newId = JSON.parse(JSON.stringify(lastData));
            if (newId && newId.CraneRequestId !== null && newId.CraneRequestId !== undefined) {
                craneId = newId.CraneRequestId;
            }
            const roleDetails = await Role.getBy('Project Admin');
            const accountRoleDetails = await Role.getBy('Account Admin');
            if (dataInSeries.recurrence.recurrence === 'Daily') {
                let dailyIndex = 0;
                while (dailyIndex < dates.length) {
                    const data = dates[dailyIndex];
                    if (
                        moment(data).isBetween(moment(dates[0]), moment(dates[dates.length - 1]), null, '[]') ||
                        moment(data).isSame(dates[0]) ||
                        moment(data).isSame(dates[dates.length - 1])
                    ) {
                        id += 1;
                        craneId += 1;
                        InspectionParam = {
                            description: payload.description,
                            escort: payload.escort,
                            vehicleDetails: payload.vehicleDetails,
                            notes: payload.notes,
                            InspectionId: id,
                            inspectionStart: await this.convertTimezoneToUtc(
                                data,
                                payload.timezone,
                                payload.inspectionStartTime,
                            ),
                            inspectionEnd: await this.convertTimezoneToUtc(
                                data,
                                payload.timezone,
                                payload.inspectionEndTime,
                            ),
                            ProjectId: payload.ProjectId,
                            createdBy: memberDetails.id,
                            isAssociatedWithCraneRequest: payload.isAssociatedWithCraneRequest,
                            requestType: payload.requestType,
                            cranePickUpLocation: payload.cranePickUpLocation,
                            craneDropOffLocation: payload.craneDropOffLocation,
                            recurrenceId: newRecurrenceId,
                            inspectionType: payload.inspectionType,
                            LocationId: payload.LocationId,
                            OriginationAddress: payload.originationAddress,
                            vehicleType: payload.vehicleType
                        };
                        if (payload.requestType === 'InspectionRequestWithCrane') {
                            InspectionParam.CraneRequestId = craneId;
                        }
                        if (
                            memberDetails.RoleId === roleDetails.id ||
                            memberDetails.RoleId === accountRoleDetails.id ||
                            memberDetails.isAutoApproveEnabled ||
                            projectDetails.ProjectSettings.isAutoApprovalEnabled
                        ) {
                            InspectionParam.status = 'Approved';
                            InspectionParam.approvedBy = memberDetails.id;
                            InspectionParam.approved_at = new Date();
                        }
                        eventsArray.push(InspectionParam);
                        // eslint-disable-next-line no-const-assign
                        dailyIndex += +dataInSeries.recurrence.repeatEveryCount;
                    }
                }
            }
            if (dataInSeries.recurrence.recurrence === 'Weekly') {
                const startDayWeek = moment(dates[0]).startOf('week');
                const endDayWeek = moment(dates[dates.length - 1]).endOf('week');
                const range1 = momentRange.range(moment(startDayWeek), moment(endDayWeek));
                const totalDaysOfRecurrence = Array.from(range1.by('day'));
                let count;
                let weekIncrement;
                if (+dataInSeries.recurrence.repeatEveryCount > 1) {
                    count = +dataInSeries.recurrence.repeatEveryCount - 1;
                    weekIncrement = 7;
                } else {
                    count = 1;
                    weekIncrement = 0;
                }
                for (
                    let indexba = 0;
                    indexba < totalDaysOfRecurrence.length;
                    indexba += weekIncrement * count
                ) {
                    const totalLength = indexba + 6;
                    for (let indexb = indexba; indexb <= totalLength; indexb += 1) {
                        const data = totalDaysOfRecurrence[indexb];
                        indexba += 1;
                        if (
                            data &&
                            !moment(data).isBefore(dates[0]) &&
                            !moment(data).isAfter(dates[dates.length - 1])
                        ) {
                            const day = moment(data).format('dddd');
                            const indexVal = dataInSeries.recurrence.days.includes(day);
                            if (indexVal) {
                                id += 1;
                                craneId += 1;
                                const date = moment(`${data}`).format('MM/DD/YYYY');
                                InspectionParam = {
                                    description: payload.description,
                                    escort: payload.escort,
                                    vehicleDetails: payload.vehicleDetails,
                                    notes: payload.notes,
                                    InspectionId: id,
                                    inspectionStart: await this.convertTimezoneToUtc(
                                        date,
                                        payload.timezone,
                                        payload.inspectionStartTime,
                                    ),
                                    inspectionEnd: await this.convertTimezoneToUtc(
                                        date,
                                        payload.timezone,
                                        payload.inspectionEndTime,
                                    ),
                                    ProjectId: payload.ProjectId,
                                    createdBy: memberDetails.id,
                                    isAssociatedWithCraneRequest: payload.isAssociatedWithCraneRequest,
                                    requestType: payload.requestType,
                                    cranePickUpLocation: payload.cranePickUpLocation,
                                    craneDropOffLocation: payload.craneDropOffLocation,
                                    recurrenceId: newRecurrenceId,
                                };
                                if (payload.requestType === 'InspectionRequestWithCrane') {
                                    InspectionParam.CraneRequestId = craneId;
                                }
                                if (
                                    memberDetails.RoleId === roleDetails.id ||
                                    memberDetails.RoleId === accountRoleDetails.id ||
                                    memberDetails.isAutoApproveEnabled ||
                                    projectDetails.ProjectSettings.isAutoApprovalEnabled
                                ) {
                                    InspectionParam.status = 'Approved';
                                    InspectionParam.approvedBy = memberDetails.id;
                                    InspectionParam.approved_at = new Date();
                                }
                                eventsArray.push(InspectionParam);
                            }
                        }
                    }
                }
            }
            if (dataInSeries.recurrence.recurrence === 'Monthly') {
                const startMonth = moment(dates[0]).startOf('month');
                const startMonthNumber = moment(startMonth).format('MM');
                const endMonth = moment(dates[dates.length - 1]).endOf('month');
                const endMonthNumber = moment(endMonth).format('MM');
                let startDate1 = moment(dates[0]);
                const endDate1 = moment(dates[dates.length - 1]).endOf('month');
                const allMonthsInPeriod = [];
                while (startDate1.isBefore(endDate1)) {
                    allMonthsInPeriod.push(startDate1.format('YYYY-MM'));
                    startDate1 = startDate1.add(1, 'month');
                }
                let currentMonthDates = [];
                let totalNumberOfMonths = endMonthNumber - startMonthNumber;
                if (totalNumberOfMonths < 0) {
                    totalNumberOfMonths *= -1;
                }
                let k = 0;
                while (k < allMonthsInPeriod.length + 1) {
                    currentMonthDates = Array.from(
                        { length: moment(allMonthsInPeriod[k], 'YYYY-MM').daysInMonth() },
                        (x, j) => moment(allMonthsInPeriod[k], 'YYYY-MM').startOf('month').add(j, 'days'),
                    );

                    if (dataInSeries.recurrence.chosenDateOfMonth) {
                        const getDate = currentMonthDates.filter(
                            (value) => moment(value).format('DD') === dataInSeries.recurrence.dateOfMonth,
                        );
                        if (getDate.length === 1) {
                            if (
                                moment(getDate[0]).isBetween(
                                    moment(dates[0]),
                                    moment(dates[dates.length - 1]),
                                    null,
                                    '[]',
                                ) ||
                                moment(getDate[0]).isSame(dates[0]) ||
                                moment(getDate[0]).isSame(dates[dates.length - 1])
                            ) {
                                id += 1;
                                craneId += 1;
                                const date = moment(getDate[0].toDate()).format('MM/DD/YYYY');
                                InspectionParam = {
                                    description: payload.description,
                                    escort: payload.escort,
                                    vehicleDetails: payload.vehicleDetails,
                                    notes: payload.notes,
                                    InspectionId: id,
                                    inspectionStart: await this.convertTimezoneToUtc(
                                        date,
                                        payload.timezone,
                                        payload.inspectionStartTime,
                                    ),
                                    inspectionEnd: await this.convertTimezoneToUtc(
                                        date,
                                        payload.timezone,
                                        payload.inspectionEndTime,
                                    ),
                                    ProjectId: payload.ProjectId,
                                    createdBy: memberDetails.id,
                                    isAssociatedWithCraneRequest: payload.isAssociatedWithCraneRequest,
                                    requestType: payload.requestType,
                                    cranePickUpLocation: payload.cranePickUpLocation,
                                    craneDropOffLocation: payload.craneDropOffLocation,
                                    recurrenceId: newRecurrenceId,
                                };
                                if (payload.requestType === 'InspectionRequestWithCrane') {
                                    InspectionParam.CraneRequestId = craneId;
                                }
                                if (
                                    memberDetails.RoleId === roleDetails.id ||
                                    memberDetails.RoleId === accountRoleDetails.id ||
                                    memberDetails.isAutoApproveEnabled ||
                                    projectDetails.ProjectSettings.isAutoApprovalEnabled
                                ) {
                                    InspectionParam.status = 'Approved';
                                    InspectionParam.approvedBy = memberDetails.id;
                                    InspectionParam.approved_at = new Date();
                                }
                                eventsArray.push(InspectionParam);
                            }
                        }
                    } else if (allMonthsInPeriod[k]) {
                        const dayOfMonth = dataInSeries.recurrence.monthlyRepeatType;
                        const week = dayOfMonth.split(' ')[0].toLowerCase();
                        const day = dayOfMonth.split(' ')[1].toLowerCase();
                        const chosenDay = moment(allMonthsInPeriod[k], 'YYYY-MM').startOf('month').day(day);
                        const getAllDays = [];
                        if (chosenDay.date() > 7) chosenDay.add(7, 'd');
                        const month = chosenDay.month();
                        while (month === chosenDay.month()) {
                            getAllDays.push(chosenDay.toString());
                            chosenDay.add(7, 'd');
                        }
                        let i = 0;
                        if (week === 'second') {
                            i += 1;
                        } else if (week === 'third') {
                            i += 2;
                        } else if (week === 'fourth') {
                            i += 3;
                        } else if (week === 'last') {
                            i = getAllDays.length - 1;
                        }
                        const finalDay = getAllDays[i];
                        if (
                            moment(finalDay).isBetween(
                                moment(dates[0]),
                                moment(dates[dates.length - 1]),
                                null,
                                '[]',
                            ) ||
                            moment(finalDay).isSame(dates[0]) ||
                            moment(finalDay).isSame(dates[dates.length - 1])
                        ) {
                            id += 1;
                            craneId += 1;
                            const date = moment(finalDay).format('MM/DD/YYYY');
                            InspectionParam = {
                                description: payload.description,
                                escort: payload.escort,
                                vehicleDetails: payload.vehicleDetails,
                                notes: payload.notes,
                                InspectionId: id,
                                inspectionStart: await this.convertTimezoneToUtc(
                                    date,
                                    payload.timezone,
                                    payload.inspectionStartTime,
                                ),
                                inspectionEnd: await this.convertTimezoneToUtc(
                                    date,
                                    payload.timezone,
                                    payload.inspectionEndTime,
                                ),
                                ProjectId: payload.ProjectId,
                                createdBy: memberDetails.id,
                                isAssociatedWithCraneRequest: payload.isAssociatedWithCraneRequest,
                                requestType: payload.requestType,
                                cranePickUpLocation: payload.cranePickUpLocation,
                                craneDropOffLocation: payload.craneDropOffLocation,
                                recurrenceId: newRecurrenceId,
                            };
                            if (payload.requestType === 'InspectionRequestWithCrane') {
                                InspectionParam.CraneRequestId = craneId;
                            }
                            if (
                                memberDetails.RoleId === roleDetails.id ||
                                memberDetails.RoleId === accountRoleDetails.id ||
                                memberDetails.isAutoApproveEnabled ||
                                projectDetails.ProjectSettings.isAutoApprovalEnabled
                            ) {
                                InspectionParam.status = 'Approved';
                                InspectionParam.approvedBy = memberDetails.id;
                                InspectionParam.approved_at = new Date();
                            }
                            eventsArray.push(InspectionParam);
                        }
                    }
                    k += +dataInSeries.recurrence.repeatEveryCount;
                }
            }
            if (dataInSeries.recurrence.recurrence === 'Yearly') {
                const startMonth = moment(dates[0]).startOf('month');
                const startMonthNumber = moment(startMonth).format('MM');
                const endMonth = moment(dates[dates.length - 1]).endOf('month');
                const endMonthNumber = moment(endMonth).format('MM');
                let startDate1 = moment(dates[0]);
                const endDate1 = moment(dates[dates.length - 1]).endOf('month');
                const allMonthsInPeriod = [];
                while (startDate1.isBefore(endDate1)) {
                    allMonthsInPeriod.push(startDate1.format('YYYY-MM'));
                    startDate1 = startDate1.add(12, 'month');
                }
                let currentMonthDates = [];
                let totalNumberOfMonths = endMonthNumber - startMonthNumber;
                if (totalNumberOfMonths < 0) {
                    totalNumberOfMonths *= -1;
                }
                for (let k = 0; k < allMonthsInPeriod.length + 1; k += 1) {
                    currentMonthDates = Array.from(
                        { length: moment(allMonthsInPeriod[k], 'YYYY-MM').daysInMonth() },
                        (x, j) => moment(allMonthsInPeriod[k], 'YYYY-MM').startOf('month').add(j, 'days'),
                    );
                    if (dataInSeries.recurrence.chosenDateOfMonth) {
                        const getDate = currentMonthDates.filter(
                            (value) => moment(value).format('DD') === dataInSeries.recurrence.dateOfMonth,
                        );
                        if (getDate.length === 1) {
                            if (
                                moment(getDate[0]).isBetween(
                                    moment(dates[0]),
                                    moment(dates[dates.length - 1]),
                                    null,
                                    '[]',
                                ) ||
                                moment(getDate[0]).isSame(dates[0]) ||
                                moment(getDate[0]).isSame(dates[dates.length - 1])
                            ) {
                                id += 1;
                                craneId += 1;
                                const date = moment(getDate[0].toDate()).format('MM/DD/YYYY');
                                InspectionParam = {
                                    description: payload.description,
                                    escort: payload.escort,
                                    vehicleDetails: payload.vehicleDetails,
                                    notes: payload.notes,
                                    InspectionId: id,
                                    inspectionStart: await this.convertTimezoneToUtc(
                                        date,
                                        payload.timezone,
                                        payload.inspectionStartTime,
                                    ),
                                    inspectionEnd: await this.convertTimezoneToUtc(
                                        date,
                                        payload.timezone,
                                        payload.inspectionEndTime,
                                    ),
                                    ProjectId: payload.ProjectId,
                                    createdBy: memberDetails.id,
                                    isAssociatedWithCraneRequest: payload.isAssociatedWithCraneRequest,
                                    requestType: payload.requestType,
                                    cranePickUpLocation: payload.cranePickUpLocation,
                                    craneDropOffLocation: payload.craneDropOffLocation,
                                    recurrenceId: newRecurrenceId,
                                };
                                if (payload.requestType === 'InspectionRequestWithCrane') {
                                    InspectionParam.CraneRequestId = craneId;
                                }
                                if (
                                    memberDetails.RoleId === roleDetails.id ||
                                    memberDetails.RoleId === accountRoleDetails.id ||
                                    memberDetails.isAutoApproveEnabled ||
                                    projectDetails.ProjectSettings.isAutoApprovalEnabled
                                ) {
                                    InspectionParam.status = 'Approved';
                                    InspectionParam.approvedBy = memberDetails.id;
                                    InspectionParam.approved_at = new Date();
                                }
                                eventsArray.push(InspectionParam);
                            }
                        }
                    } else if (allMonthsInPeriod[k]) {
                        const dayOfMonth = dataInSeries.recurrence.monthlyRepeatType;
                        const week = dayOfMonth.split(' ')[0].toLowerCase();
                        const day = dayOfMonth.split(' ')[1].toLowerCase();
                        const chosenDay = moment(allMonthsInPeriod[k], 'YYYY-MM').startOf('month').day(day);
                        const getAllDays = [];
                        if (chosenDay.date() > 7) chosenDay.add(7, 'd');
                        const month = chosenDay.month();
                        while (month === chosenDay.month()) {
                            getAllDays.push(chosenDay.toString());
                            chosenDay.add(7, 'd');
                        }
                        let i = 0;
                        if (week === 'second') {
                            i += 1;
                        } else if (week === 'third') {
                            i += 2;
                        } else if (week === 'fourth') {
                            i += 3;
                        } else if (week === 'last') {
                            i = getAllDays.length - 1;
                        }
                        const finalDay = getAllDays[i];
                        if (
                            moment(finalDay).isBetween(
                                moment(dates[0]),
                                moment(dates[dates.length - 1]),
                                null,
                                '[]',
                            ) ||
                            moment(finalDay).isSame(dates[0]) ||
                            moment(finalDay).isSame(dates[dates.length - 1])
                        ) {
                            id += 1;
                            craneId += 1;
                            const date = moment(finalDay).format('MM/DD/YYYY');
                            InspectionParam = {
                                description: payload.description,
                                escort: payload.escort,
                                vehicleDetails: payload.vehicleDetails,
                                notes: payload.notes,
                                InspectionId: id,
                                inspectionStart: await this.convertTimezoneToUtc(
                                    date,
                                    payload.timezone,
                                    payload.inspectionStartTime,
                                ),
                                inspectionEnd: await this.convertTimezoneToUtc(
                                    date,
                                    payload.timezone,
                                    payload.inspectionEndTime,
                                ),
                                ProjectId: payload.ProjectId,
                                createdBy: memberDetails.id,
                                isAssociatedWithCraneRequest: payload.isAssociatedWithCraneRequest,
                                requestType: payload.requestType,
                                cranePickUpLocation: payload.cranePickUpLocation,
                                craneDropOffLocation: payload.craneDropOffLocation,
                                recurrenceId: newRecurrenceId,
                            };
                            if (payload.requestType === 'InspectionRequestWithCrane') {
                                InspectionParam.CraneRequestId = craneId;
                            }
                            if (
                                memberDetails.RoleId === roleDetails.id ||
                                memberDetails.RoleId === accountRoleDetails.id ||
                                memberDetails.isAutoApproveEnabled ||
                                projectDetails.ProjectSettings.isAutoApprovalEnabled
                            ) {
                                InspectionParam.status = 'Approved';
                                InspectionParam.approvedBy = memberDetails.id;
                                InspectionParam.approved_at = new Date();
                            }
                            eventsArray.push(InspectionParam);
                        }
                    }
                }
            }
        }

        if (eventsArray.length > 0) {
            for (let i = 0; i < eventsArray.length; i += 1) {
                const newInspectionData = await InspectionRequest.createInstance(eventsArray[i]);
                const { companies, persons, define } = payload;
                const gates = [payload.GateId];
                const equipments = payload.EquipmentId;
                const updateParam = {
                    InspectionId: newInspectionData.id,
                    inspectionCode: newInspectionData.InspectionId,
                    ProjectId: payload.ProjectId,
                };
                companies.forEach(async (element) => {
                    const companyParam = updateParam;
                    companyParam.CompanyId = element;
                    await InspectionCompany.createInstance(companyParam);
                });
                gates.forEach(async (element) => {
                    const gateParam = updateParam;
                    gateParam.GateId = element;
                    await InspectionGate.createInstance(gateParam);
                });
                equipments.forEach(async (element) => {
                    const equipmentParam = updateParam;
                    equipmentParam.EquipmentId = element;
                    await InspectionEquipment.createInstance(equipmentParam);
                });
                persons.forEach(async (element) => {
                    const memberParam = updateParam;
                    memberParam.MemberId = element;
                    await InspectionPerson.createInstance(memberParam);
                });
                define.forEach(async (element) => {
                    const defineParam = updateParam;
                    defineParam.DeliverDefineWorkId = element;
                    await DeliverDefine.createInstance(defineParam);
                });
                const history = {
                    InspectionRequestId: newInspectionData.id,
                    InspectionId: newInspectionData.InspectionId,
                    MemberId: memberDetails.id,
                    isDeleted: false,
                    ProjectId: payload.ProjectId,
                    type: 'create',
                    description: `${loginUser.firstName} ${loginUser.lastName} Created Inspection Booking, ${payload.description}.`,
                };
                await InspectionHistory.createInstance(history);
                if (newInspectionData.status === 'Approved') {
                    const object = {
                        ProjectId: payload.ProjectId,
                        MemberId: memberDetails.id,
                        InspectionRequestId: newInspectionData.id,
                        isDeleted: false,
                        type: 'approved',
                        description: `${loginUser.firstName} ${loginUser.lastName} Approved Inspection Booking, ${payload.description}.`,
                    };
                    await InspectionHistory.createInstance(object);
                }
            }
        }
    },
    async checkinspectionConflictsWithAlreadyScheduled(requestsArray, type, gateId) {
        if (requestsArray && requestsArray.length > 0) {
            const inspectionStartDateArr = [];
            const inspectionEndDateArr = [];
            const requestIds = [];
            requestsArray.forEach((data) => {
                inspectionStartDateArr.push(new Date(data.inspectionStart));
                inspectionEndDateArr.push(new Date(data.inspectionEnd));
                if (type === 'edit' && data.id) {
                    requestIds.push(data.id);
                }
            });
            let condition = {
                ProjectId: requestsArray[0].ProjectId,
                status: {
                    [Op.notIn]: ['Delivered', 'Expired'],
                },
            };
            if (type === 'edit') {
                condition = {
                    ...condition,
                    id: {
                        [Op.notIn]: requestIds,
                    },
                };
            }
            // const isinspectionBookingOverlapping = await InspectionRequest.findAll({
            //     where: {
            //         ...condition,
            //         [Op.or]: [
            //             {
            //                 [Op.or]: inspectionStartDateArr.map((date) => ({
            //                     inspectionStart: { [Op.lte]: date },
            //                     inspectionEnd: { [Op.gte]: date },
            //                 })),
            //             },
            //             {
            //                 [Op.or]: inspectionEndDateArr.map((date) => ({
            //                     inspectionStart: { [Op.lte]: date },
            //                     inspectionEnd: { [Op.gte]: date },
            //                 })),
            //             },
            //         ],
            //     },
            //     include: [
            //         {
            //             association: 'gateDetails',
            //             where: {
            //                 isDeleted: false,
            //                 isActive: true,
            //                 GateId: { [Op.eq]: +gateId },
            //             },
            //         },
            //     ],
            // });
            // if (isinspectionBookingOverlapping && isinspectionBookingOverlapping.length > 0) {
            //     return true;
            // }
            return false;
        }
    },
    async checkDoubleBookingAllowedOrNot(eventsArray, projectDetails, type, gateId) {
        if (!projectDetails.ProjectSettings.inspectionAllowOverlappingBooking) {
            const checkBookingOverlapping = await this.checkinspectionConflictsWithAlreadyScheduled(
                eventsArray,
                type,
                gateId,
            );
            if (checkBookingOverlapping) {
                return {
                    error: true,
                    message:
                        'This booking clashes with another booking. Overlapping is disabled by the administrator.',
                };
            }
        }
        if (
            projectDetails.ProjectSettings &&
            !projectDetails.ProjectSettings.inspectionAllowOverlappingCalenderEvents
        ) {
            const checkCalenderEventsOverlapping =
                await concreteRequestService.checkCalenderEventsOverlappingWithBooking(
                    eventsArray,
                    'inspection',
                    type,
                );
            if (checkCalenderEventsOverlapping) {
                return {
                    error: true,
                    message:
                        'This booking clashes with a scheduled calendar event. Overlapping is disabled by the administrator',
                };
            }
        }
        return {
            error: false,
            message: '',
        };
    },
};
module.exports = inspectionService;