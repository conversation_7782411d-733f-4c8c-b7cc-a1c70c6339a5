const { Router } = require('express');
const { validate } = require('express-validation');
const { memberValidation } = require('../middlewares/validations');
const { MemberController } = require('../controllers');
const passportConfig = require('../config/passport');
const checkAdmin = require('../middlewares/checkAdmin');

const memberRoute = {
  get router() {
    const router = Router();
    router.post(
      '/invite_member',
      validate(memberValidation.inviteMembers, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      checkAdmin.isProjectAdmin,
      MemberController.inviteMembers,
    );
    router.post(
      '/resend_invite_link',
      passportConfig.isAuthenticated,
      MemberController.resendInviteLink,
    );
    router.post(
      '/get_user_detail',
      validate(memberValidation.getUserDetail, { keyByField: true }, { abortEarly: false }),
      MemberController.getUserDetail,
    );
    router.post(
      '/edit_member',
      validate(memberValidation.editMember, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      checkAdmin.isProjectAdmin,
      MemberController.editMember,
    );
    router.post('/update_member', MemberController.updateInviteMember);
    router.post(
      '/delete_member',
      validate(memberValidation.deleteMember, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      checkAdmin.isProjectAdmin,
      MemberController.deleteMember,
    );
    router.post('/check_user', passportConfig.isAuthenticated, MemberController.checkExistMember);
    router.post(
      '/list_member/:ProjectId/:pageSize/:pageNo/?:ParentCompanyId',
      validate(memberValidation.listMember, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      MemberController.listMember,
    );
    router.get(
      '/search_member/:ProjectId/:search/?:ParentCompanyId',
      validate(memberValidation.searchMember, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      MemberController.searchMember,
    );
    router.get(
      '/search_all_member/:ProjectId/:search/?:ParentCompanyId',
      passportConfig.isAuthenticated,
      MemberController.searchAllMember,
    );
    router.get(
      '/list_all_member/:ProjectId/?:ParentCompanyId',
      validate(memberValidation.listAllMember, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      MemberController.listAllMember,
    );
    router.get(
      '/get_overview_detail/:ProjectId/?:ParentCompanyId',
      validate(memberValidation.getOverViewDetail, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      MemberController.getOverViewDetail,
    );
    router.post(
      '/update_profile/',
      validate(memberValidation.updateUserProfile, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      MemberController.updateUserProfile,
    );
    router.get('/get_roles', passportConfig.isAuthenticated, MemberController.getRoles);
    router
      .get(
        '/members_list',
        passportConfig.isAuthenticated,
        checkAdmin.isAdmin,
        MemberController.getAllMemberLists,
      )
      .get(
        '/get_member_detail/:id',
        passportConfig.isAuthenticated,
        checkAdmin.isAdmin,
        MemberController.getMemberDetail,
      )
      .get(
        '/get_member_projects/:id',
        passportConfig.isAuthenticated,
        checkAdmin.isAdmin,
        MemberController.getMemberProjects,
      )
      .get(
        '/get_all_members',
        passportConfig.isAuthenticated,
        checkAdmin.isAdmin,
        MemberController.getAllMemberListsForAssignProject,
      );
    router
      .put(
        '/change_member_password/:id',
        passportConfig.isAuthenticated,
        checkAdmin.isAdmin,
        MemberController.changeMemberPassword,
      )
      .put(
        '/update_member_detail/:id',
        passportConfig.isAuthenticated,
        checkAdmin.isAdmin,
        MemberController.updateMemberProfile,
      )
      .put(
        '/update_member_project_status/:id',
        passportConfig.isAuthenticated,
        checkAdmin.isAdmin,
        MemberController.updateMemberProjectStatus,
      );
    router.post(
      '/activate_member',
      passportConfig.isAuthenticated,
      MemberController.activateMember,
    );
    router.post(
      '/deactivate_member',
      passportConfig.isAuthenticated,
      MemberController.deactivateMember,
    );
    router.post(
      '/get_mapped_requests',
      passportConfig.isAuthenticated,
      MemberController.getMappedRequests,
    );
    router.post(
      '/get_onboarding_invite_link',
      passportConfig.isAuthenticated,
      MemberController.getOnboardingInviteLink,
    );
    router.post('/get_member_data', passportConfig.isAuthenticated, MemberController.getMemberData);
    router.get(
      '/search_member_auto_approve/:ProjectId/:search/?:ParentCompanyId',
      validate(memberValidation.searchMember, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      MemberController.searchAutoApproveMember,
    );
    router.post(
      '/list_retool_members',
      MemberController.listRetoolMembers,
    );
    router.get(
      '/list_registered_members/:ProjectId/?:ParentCompanyId',
      validate(memberValidation.listAllMember, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      MemberController.listRegisteredMembers,
    );
    router.post(
      '/list_guest_member/:ProjectId/:pageSize/:pageNo/?:ParentCompanyId',
      validate(memberValidation.listGuestMember, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      MemberController.listGuestMembers,
    );
    router.put(
      '/add_guest_as_member',
      passportConfig.isAuthenticated,
      validate(memberValidation.addGuestAsMember, { keyByField: true }, { abortEarly: false }),
      MemberController.addGuestAsMember,
    );
    router.put(
      '/reject_guest_request',
      passportConfig.isAuthenticated,
      validate(memberValidation.rejectGuestRequest, { keyByField: true }, { abortEarly: false }),
      MemberController.rejectGuestRequest,
    );
    return router;
  },
};
module.exports = memberRoute;
