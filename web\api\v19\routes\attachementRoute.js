const { Router } = require('express');
const { validate } = require('express-validation');
const multer = require('multer');
const passportConfig = require('../config/passport');
const { AttachementController } = require('../controllers');
const { attachementValidation } = require('../middlewares/validations');

let upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 8000000 // Compliant: 8MB
  }
});

const attachementRoute = {
  get router() {
    const router = Router();
    router.post(
      '/add_attachement/:DeliveryRequestId/?:ParentCompanyId',
      upload.array('attachement', 12),
      validate(
        attachementValidation.createAttachement,
        { keyByField: true },
        { abortEarly: false },
      ),
      passportConfig.isAuthenticated,
      AttachementController.createAttachement,
    );
    router.post(
      '/add_inspection_attachement/:InspectionRequestId/?:ParentCompanyId',
      upload.array('attachement', 12),
      validate(
        attachementValidation.createInspectionAttachement,
        { keyByField: true },
        { abortEarly: false },
      ),
      passportConfig.isAuthenticated,
      AttachementController.createInspectionAttachement,
    );

    router.get(
      '/remove_attachement/:id/?:ParentCompanyId',
      validate(
        attachementValidation.deleteAttachement,
        { keyByField: true },
        { abortEarly: false },
      ),
      passportConfig.isAuthenticated,
      AttachementController.deleteAttachement,
    );

    router.get(
      '/remove_inspection_attachement/:id/?:ParentCompanyId',
      validate(
        attachementValidation.deleteAttachement,
        { keyByField: true },
        { abortEarly: false },
      ),
      passportConfig.isAuthenticated,
      AttachementController.deleteInspectionAttachement,
    );
    router.get(
      '/get_attachement/:DeliveryRequestId/?:ParentCompanyId',
      validate(attachementValidation.getAttachement, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      AttachementController.getAttachement,
    );

    router.get(
      '/get_inspection_attachement/:InspectionRequestId/?:ParentCompanyId',
      // validate(attachementValidation.getAttachement, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      AttachementController.getInspectionAttachement,
    );

    return router;
  },
};
module.exports = attachementRoute;
