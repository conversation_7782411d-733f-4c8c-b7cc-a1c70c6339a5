const fs = require('fs');
const formData = require('form-data');
const Mailgun = require('mailgun.js');

const mailgun = new Mailgun(formData);
const puppeteer = require('puppeteer');
const moment = require('moment');
const deepLinkService = require('../services/deepLinkService');

let invoiceAttachment;
const mg = mailgun.client({ username: 'api', key: process.env.MAILGUN_API_KEY });
async function getHtml(templateName, userData) {
  let mailTemplate;
  if (templateName === 'projectSubscriptionInvoice') {
    invoiceAttachment = '';
    mailTemplate = 'projectSubscriptionInvoiceNotification.html';
    invoiceAttachment = fs.readFileSync(
      '/usr/src/web/api/v19/views/mail-templates/projectSubscriptionInvoiceDocument.html',
      {
        encoding: 'utf-8',
      },
    );
    invoiceAttachment = invoiceAttachment
      .replace('$name', `${userData.firstName}`)
      .replace('$surName', `${userData.lastName}`)
      .replace('$firstName', `${userData.firstName}`)
      .replace('$lastName', `${userData.lastName}`)
      .replace('$email', `${userData.email}`)
      .replace('$customerEmail', `${userData.email}`)
      .replace('$invoiceNo', `${userData.invoiceNo}`)
      .replace('$footerInvoiceNo', `${userData.footerInvoiceNo}`)
      .replace('$invoiceDate', `${userData.invoiceDate}`)
      .replace('$paymentDate', `${userData.paymentDate}`)
      .replace('$address', `${userData.address}`)
      .replace('$city', `${userData.city}`)
      .replace('$state', `${userData.state}`)
      .replace('$zipcode', `${userData.zipcode}`)
      .replace('$country', `${userData.country}`)
      .replace('$projectName', `${userData.projectName}`)
      .replace('$projectStartDate', `${userData.projectStartDate}`)
      .replace('$projectEndDate', `${userData.projectEndDate}`)
      .replace('$amount', `${userData.amount}`)
      .replace('$quantity', `${userData.quantity}`)
      .replace('$subTotal', `${userData.subTotal}`)
      .replace('$total', `${userData.total}`)
      .replace('$overAllTotal', `${userData.total}`)
      .replace('$invoiceSubTotal', `${userData.invoiceSubTotal}`);
  }
  if (templateName === 'upgradeplan' || templateName === 'signUpSubscription') {
    invoiceAttachment = '';
    if (templateName === 'upgradeplan') {
      mailTemplate = 'upgradeProjectPlanSubscription.html';
    } else {
      mailTemplate = 'sendReceiptAfterSignUp.html';
    }
    invoiceAttachment = fs.readFileSync(
      '/usr/src/web/api/v19/views/mail-templates/projectSubscriptionReceiptDocument.html',
      {
        encoding: 'utf-8',
      },
    );
    invoiceAttachment = invoiceAttachment
      .replace('$name', `${userData.firstName}`)
      .replace('$surName', `${userData.lastName}`)
      .replace('$firstName', `${userData.firstName}`)
      .replace('$lastName', `${userData.lastName}`)
      .replace('$email', `${userData.email}`)
      .replace('$customerEmail', `${userData.email}`)
      .replace('$invoiceNo', `${userData.invoiceNo}`)
      .replace('$invoiceDate', `${userData.invoiceDate}`)
      .replace('$paymentDate', `${userData.paymentDate}`)
      .replace('$address', `${userData.address}`)
      .replace('$city', `${userData.city}`)
      .replace('$state', `${userData.state}`)
      .replace('$zipcode', `${userData.zipcode}`)
      .replace('$country', `${userData.country}`)
      .replace('$projectName', `${userData.projectName}`)
      .replace('$projectStartDate', `${userData.projectStartDate}`)
      .replace('$projectEndDate', `${userData.projectEndDate}`)
      .replace('$amount', `${userData.amount}`)
      .replace('$quantity', `${userData.quantity}`)
      .replace('$subTotal', `${userData.subTotal}`)
      .replace('$total', `${userData.total}`)
      .replace('$overAllTotal', `${userData.total}`)
      .replace('$invoiceSubTotal', `${userData.invoiceSubTotal}`)
      .replace('$receiptNo', `${userData.receiptNo}`)
      .replace('$cardNo', `${userData.cardNo}`)
      .replace('$interval', `${userData.interval}`)
      .replace('$footerInvoiceNo', `${userData.footerInvoiceNo}`)
      .replace('$datePaid', `${userData.datePaid}`);
    if (userData.cardType === 'american express') {
      invoiceAttachment = invoiceAttachment.replace(
        '$cardImageType',
        'https://d1k9lc9xzt55uy.cloudfront.net/b060fb7b-ef9b-4f93-8e28-d885181c5e4b.png',
      );
    }
    if (userData.cardType === 'master card') {
      invoiceAttachment = invoiceAttachment.replace(
        '$cardImageType',
        'https://d1k9lc9xzt55uy.cloudfront.net/8cf8bb34-53c8-4988-8e9e-910ae297c5bc.png',
      );
    }
    if (userData.cardType === 'visa') {
      invoiceAttachment = invoiceAttachment.replace(
        '$cardImageType',
        'https://d1k9lc9xzt55uy.cloudfront.net/f12a8d0e-0d55-471e-941d-93791d87c538.png',
      );
    }
  }
  if (templateName === 'forgotPassword') mailTemplate = 'forgot-password.html';
  if (templateName === 'register') mailTemplate = 'signupTemplate.html';
  if (templateName === 'addproject') mailTemplate = 'memberAddedInNewproject.html';
  if (templateName === 'trailPlanEndingRemainder') mailTemplate = 'trialPlanEndingRemainder.html';
  if (templateName === 'trailPlanExpired') mailTemplate = 'trialPlanOverNotification.html';
  if (templateName === 'trialPlanBegins') mailTemplate = 'trialPlanBeginsNotification.html';
  if (templateName === 'Account Creation') mailTemplate = 'accountAdminCredentials.html';
  if (templateName === 'Account Member') mailTemplate = 'accountmember.html';
  if (templateName === 'accountadminpay') mailTemplate = 'accountadminpay.html';
  if (templateName === 'invite_member') mailTemplate = 'memberOnboarding.html';
  if (templateName === 'commentadded') mailTemplate = 'commentAddedAgainstNDR.html';
  if (templateName === 'cranecommentadded') mailTemplate = 'commentAddedAgainstCraneRequest.html';
  if (templateName === 'concretecommentadded')
    mailTemplate = 'commentAddedAgainstConcreteRequest.html';
  if (templateName === 'adminoverride') mailTemplate = 'adminOverRideStatus.html';
  if (templateName === 'assignPAtoNewProjectByAccountadmin')
    mailTemplate = 'assignProjectToExistingPA_by_AccountAdmin.html';
  if (templateName === 'userPasswordChangeBySuperAdmin')
    mailTemplate = 'notifyUserPasswordChangedBySuperAdmin.html';
  if (templateName === 'editProjectBySA')
    mailTemplate = 'notifyMembers_If_SuperAdmin_Edited_Project.html';
  if (templateName === 'dailyDigestNotification')
    mailTemplate = 'dailyDigestEmailNotification.html';
  if (templateName === 'deliveredDR') mailTemplate = 'notifyUsersIfDeliveryRequestDelivered.html';
  if (templateName === 'completedCraneRequest')
    mailTemplate = 'notifyUserIfCraneRequestCompleted.html';
  if (templateName === 'completedConcreteRequest')
    mailTemplate = 'notifyUserIfConcreteRequestCompleted.html';
  if (templateName === 'notifyPAForApproval') mailTemplate = 'Notify_PA_forApproval.html';
  if (templateName === 'notifyPAForReApproval') mailTemplate = 'Notify_PA_forReApproval.html';
  if (templateName === 'deliveryRequestCreated')
    mailTemplate = 'NotifyUsersIfDeliveryRequestCreated.html';
  if (templateName === 'craneRequestCreated')
    mailTemplate = 'NotifyUsersIfCraneRequestCreated.html';
  if (templateName === 'concreteRequestCreated')
    mailTemplate = 'NotifyUserIfConcreteRequestCreated.html';
  if (templateName === 'schedulerReport') mailTemplate = 'schedulerReportModel.html';
  if (templateName === 'schedulerReportNoData') mailTemplate = 'schedulerReportModelNoData.html';
  if (templateName === 'guestApproved') mailTemplate = 'guestRequestApprovedByAdmin.html';
  if (templateName === 'guestRejected') mailTemplate = 'guestRequestRejected.html';
  if (templateName === 'guestRequested') mailTemplate = 'guestRequestToMember.html';
  if (templateName === 'notifyGuestOnEdit') mailTemplate = 'notifyGuestOnEdit.html';
  const emailTemplate = fs.readFileSync(
    `/usr/src/web/api/v19/views/mail-templates/${mailTemplate}`,
    {
      encoding: 'utf-8',
    },
  );

  if (process.env.NODE_ENV === 'test') return 'success';
  let user = userData;
  if (user.origin === '' || user.origin === null || user.origin === undefined) {
    user.link = process.env.BASE_URL;
  }
  if (templateName === 'invite_member') {
    user = await deepLinkService.getInviteMemberDeepLink(user);
  }
  if (templateName === 'forgotPassword') {
    user = await deepLinkService.getForgotPasswordDeepLink(user);
  }
  if (templateName === 'register') {
    user = await deepLinkService.getRegistrationDeepLink(user);
  }
  switch (templateName) {
    case 'register':
      return emailTemplate
        .replace('$firstName', `${user.firstName}`)
        .replace('$email', `${user.email}`)
        .replace('$link', `${user.link}`)
        .replace('$password', `${user.generatedPassword}`);
    case 'forgotPassword':
      return emailTemplate.replace('$name', `${user.firstName}`).replace('$link', `${user.link}`);
    case 'schedulerReport':
      return emailTemplate
        .replace('$Subject', `${user.subject}`)
        .replace('$Report_Name', `${user.reportName}`)
        .replace('$S3URL', `${user.s3_url}`)
        .replace('$Message', `${user.message}`);
    case 'schedulerReportNoData':
      return emailTemplate
        .replace('$StartDate', `${moment(user.customStartDate).format('ddd, MMM DD YYYY')}`)
        .replace('$EndDate', `${moment(user.customEndDate).format('ddd, MMM DD YYYY')}`);
    case 'addproject':
      return emailTemplate
        .replace('$name', `${user.firstName}`)
        .replace('$projectName', `${user.projectName}`)
        .replace('$type', `${user.type}`);
    case 'trailPlanEndingRemainder':
      return emailTemplate
        .replace('$name', `${user.firstName}`)
        .replace('$projectName', `${user.projectName}`)
        .replace('$plan', `${user.planName}`)
        .replace('$link', `${user.link}/login`)
        .replace('$expiryDays', `${user.expiryDays}`);
    case 'Account Creation':
      return emailTemplate
        .replace('$firstName', `${user.firstName}`)
        .replace('$email', `${user.email}`)
        .replace('$password', `${user.password}`)
        .replace('domainURL', `${user.domainURL}`)
        .replace('$link', `${user.domainURL}/login`)
        .replace('$amount', `${user.amount}`);
    case 'Account Member':
      return emailTemplate
        .replace('$firstName', `${user.firstName}`)
        .replace('$email', `${user.email}`)
        .replace('$password', `${user.password}`)
        .replace('domainURL', `${user.domainURL}`)
        .replace('$link', `${user.domainURL}/login`);
    case 'accountadminpay':
      return emailTemplate
        .replace('$firstName', `${user.firstName}`)
        .replace('$email', `${user.email}`)
        .replace('$status', `${user.status}`)
        .replace('amount', `${user.amount}`)
        .replace('currency', `${user.currency}`);
    case 'invite_member':
      return emailTemplate.replace('$type', `${user.type}`).replace('$link', `${user.link}`);
    case 'commentadded':
      return emailTemplate
        .replace('$NDR_AuthorName', `${user.toEmailUserName}`)
        .replace('$deliveryID', `${user.deliveryId}`)
        .replace('$commentedUserName', `${user.commentedPersonname}`)
        .replace('$deliveryId', `${user.deliveryId}`)
        .replace('$description', `${user.deliveryDescription}`)
        .replace('$timestamp', `${user.deliveryStart}`)
        .replace('$comment_timestamp', `${user.commentTimeStamp}`)
        .replace('$newComment', `${user.newComment}`)
        .replace('$previousComments', `${user.previousComments}`);
    case 'cranecommentadded':
      return emailTemplate
        .replace('$NDR_AuthorName', `${user.toEmailUserName}`)
        .replace('$craneID', `${user.craneId}`)
        .replace('$commentedUserName', `${user.commentedPersonname}`)
        .replace('$craneId', `${user.craneId}`)
        .replace('$description', `${user.craneDescription}`)
        .replace('$timestamp', `${user.craneDeliveryStart}`)
        .replace('$comment_timestamp', `${user.commentTimeStamp}`)
        .replace('$newComment', `${user.newComment}`)
        .replace('$previousComments', `${user.previousComments}`);
    case 'concretecommentadded':
      return emailTemplate
        .replace('$NDR_AuthorName', `${user.toEmailUserName}`)
        .replace('$concreteID', `${user.concreteId}`)
        .replace('$commentedUserName', `${user.commentedPersonname}`)
        .replace('$concreteId', `${user.concreteId}`)
        .replace('$description', `${user.concreteDescription}`)
        .replace('$timestamp', `${user.concreteStart}`)
        .replace('$comment_timestamp', `${user.commentTimeStamp}`)
        .replace('$newComment', `${user.newComment}`)
        .replace('$previousComments', `${user.previousComments}`);
    case 'adminoverride':
      return emailTemplate
        .replace('$name', `${user.firstName}`)
        .replace('$content', `Your Override Booking status"${user.status}"`);
    case 'upgradeplan':
      return emailTemplate
        .replace('$userName', `${user.firstName}`)
        .replace('$projectInterval', user.interval)
        .replace('$projectSubscribedName', user.projectName)
        .replace('$projectSubscribedStartDate ', user.projectStartDate)
        .replace('$projectSubscribedEndDate', user.projectEndDate);
    case 'signUpSubscription':
      return emailTemplate
        .replace('$name', `${user.firstName}`)
        .replace('$interval', user.interval)
        .replace('$projectName', user.projectName)
        .replace('$projectStartDate ', user.projectStartDate)
        .replace('$projectEndDate', user.projectEndDate);
    case 'trailPlanExpired':
      return emailTemplate
        .replace('$name', `${user.firstName}`)
        .replace('$projectName', `${user.projectName}`)
        .replace('$plan', `${user.planName}`)
        .replace('$link', `${user.link}/login`);
    case 'trialPlanBegins':
      return emailTemplate.replace('$name', `${user.firstName}`);
    case 'assignPAtoNewProjectByAccountadmin':
      return emailTemplate
        .replace('$projectAdminName', `${user.projectAdminName}`)
        .replace('$accountAdminName', `${user.accountAdminName}`)
        .replace('$projectName', `${user.projectName}`)
        .replace('$link', `${user.link}/login`);
    case 'userPasswordChangeBySuperAdmin':
      return emailTemplate
        .replace('$firstName', `${user.firstName}`)
        .replace('$email', `${user.email}`)
        .replace('$password', `${user.password}`);
    case 'projectSubscriptionInvoice':
      return emailTemplate
        .replace('$name', `${user.firstName}`)
        .replace('$date', `${user.projectEndDate}`);
    case 'editProjectBySA':
      return emailTemplate
        .replace('$name', `${user.name}`)
        .replace('$projectName', `${user.projectName}`);
    case 'dailyDigestNotification':
      return emailTemplate
        .replace('$mailContent', `${user.mailContent}`)
        .replace('$mailSentTime', `${user.mailSentTime}`);
    case 'notifyPAForApproval':
      return emailTemplate
        .replace('$content', `${user.content}`)
        .replace('$userName', `${user.name}`);
    case 'notifyPAForReApproval':
      return emailTemplate
        .replace('$content', `${user.content}`)
        .replace('$userName', `${user.name}`);
    case 'completedConcreteRequest':
      return emailTemplate
        .replace('$userName', `${user.userName}`)
        .replace('$userName1', `${user.userName1}`)
        .replace('$concreteID', `${user.concreteID}`)
        .replace('$concreteId', `${user.concreteId}`)
        .replace('$description', `${user.description}`)
        .replace('$status_timestamp', `${user.status_timestamp}`)
        .replace('$timestamp', `${user.timestamp}`);
    case 'deliveredDR':
      return emailTemplate
        .replace('$userName', `${user.userName}`)
        .replace('$userName1', `${user.userName1}`)
        .replace('$deliveryID', `${user.deliveryID}`)
        .replace('$deliveryId', `${user.deliveryId}`)
        .replace('$description', `${user.description}`)
        .replace('$status_timestamp', `${user.status_timestamp}`)
        .replace('$timestamp', `${user.timestamp}`);
    case 'completedCraneRequest':
      return emailTemplate
        .replace('$userName', `${user.userName}`)
        .replace('$userName1', `${user.userName1}`)
        .replace('$craneID', `${user.craneID}`)
        .replace('$craneId', `${user.craneId}`)
        .replace('$description', `${user.description}`)
        .replace('$status_timestamp', `${user.status_timestamp}`)
        .replace('$timestamp', `${user.timestamp}`);
    case 'concreteRequestCreated':
      return emailTemplate
        .replace('$userName', `${user.userName}`)
        .replace('$concreteId', `${user.concreteId}`)
        .replace('$description', `${user.description}`)
        .replace('$created_timestamp', `${user.createdTimestamp}`)
        .replace('$timestamp', `${user.timestamp}`)
        .replace('$content', `${user.content}`);
    case 'deliveryRequestCreated':
      return emailTemplate
        .replace('$userName', `${user.userName}`)
        .replace('$deliveryId', `${user.deliveryId}`)
        .replace('$description', `${user.description}`)
        .replace('$created_timestamp', `${user.createdTimestamp}`)
        .replace('$timestamp', `${user.timestamp}`)
        .replace('$content', `${user.content}`);
    case 'craneRequestCreated':
      return emailTemplate
        .replace('$userName', `${user.userName}`)
        .replace('$craneId', `${user.craneId}`)
        .replace('$description', `${user.description}`)
        .replace('$created_timestamp', `${user.createdTimestamp}`)
        .replace('$timestamp', `${user.timestamp}`)
        .replace('$content', `${user.content}`);
    case 'guestApproved':
      return emailTemplate
        .replace('$firstName', `${user.guestFirstName}`)
        .replace('$lastName', `${user.guestLastName}`)
        .replace('$email', `${user.email}`)
        .replace('$projectName', `${user.projectName}`)
        .replace('$link', `${user.link}/login`)
        .replace('$password', `${user.password}`);
    case 'guestRejected':
      return emailTemplate
        .replace('$firstName', `${user.guestFirstName}`)
        .replace('$lastName', `${user.guestLastName}`)
        .replace('$projectName', `${user.projectName}`);
    case 'guestRequested':
      return emailTemplate
        .replace('$firstName', `${user.adminFirstName}`)
        .replace('$guestFirstName', `${user.guestFirstName}`)
        .replace('$projectName', `${user.projectName}`);
    case 'notifyGuestOnEdit':
      return emailTemplate
        .replace('$content', `${user.content}`)
        .replace('$userName', `${user.guestName}`);
    default:
      return emailTemplate;
  }
}

async function sendReportMail(user) {
  return new Promise(async (res, rej) => {
    try {
      const html = await getHtml(user.emailTemplate, user);
      const mailOptions = {
        'h:sender': 'Follo <<EMAIL>>',
        from: 'Follo <<EMAIL>>',
        to: user.sendTo,
        subject: user.subject,
        html,
      };
      await mg.messages
        .create(process.env.MAILGUN_DOMAIN_URL, mailOptions)
        .then((msg) => {
          console.log(msg);
          res(msg);
        })
        .catch((err) => rej(err));
    } catch (e) {
      console.log('*******sendReportMail******8', e);
      rej(e);
    }
  });
}
async function sendMail(user, templateName, subject, tagName, callback) {
  const html = await getHtml(templateName, user);
  const mailOptions = {
    'h:sender': 'Follo <<EMAIL>>',
    from: 'Follo <<EMAIL>>',
    to: user.email,
    subject,
    html,
    'o:tag': [tagName],
  };
  if (
    templateName === 'projectSubscriptionInvoice' ||
    templateName === 'upgradeplan' ||
    templateName === 'signUpSubscription'
  ) {
    const browser = await puppeteer.launch({
      headless: true,
      args: [
        '--disable-gpu',
        '--disable-dev-shm-usage',
        '--disable-setuid-sandbox',
        '--no-sandbox',
      ],
    });
    const page = await browser.newPage();
    await page.setContent(invoiceAttachment, { waitUntil: 'networkidle0' });
    const pdf = await page.pdf({
      format: 'A4',
      margin: {
        top: '15px',
        bottom: '15px',
        left: '15px',
        right: '15px',
      },
      pageRanges: '',
    });
    await browser.close();
    mailOptions.attachment = pdf;
    console.log('File created by puppeteer');
    await mg.messages
      .create(process.env.MAILGUN_DOMAIN_URL, mailOptions)
      .then((msg) => {
        console.log(msg);
        callback(msg);
      })
      .catch((err) => console.log(err));
  } else {
    await mg.messages
      .create(process.env.MAILGUN_DOMAIN_URL, mailOptions)
      .then((msg) => {
        console.log(msg);
        callback(msg);
      })
      .catch((err) => console.log(err));
  }
}

module.exports = {
  sendMail,
  sendReportMail,
};
