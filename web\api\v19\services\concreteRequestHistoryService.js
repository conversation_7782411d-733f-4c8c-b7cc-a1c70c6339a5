const { Enterprise, Sequelize } = require('../models');
let { ConcreteRequestHistory, ConcreteRequest, User } = require('../models');
const helper = require('../helpers/domainHelper');

const { Op } = Sequelize;
let publicUser;
let publicMember;
const historyService = {
  async getConcreteRequestHistories(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const exist = await ConcreteRequest.findOne({
        where: {
          ConcreteRequestId: inputData.params.ConcreteRequestId,
          ProjectId: +inputData.params.ProjectId,
          isDeleted: false,
        },
      });
      if (exist) {
        const historyList = await ConcreteRequestHistory.findAll({
          include: [
            {
              association: 'Member',
              include: [
                { association: 'User', attributes: ['firstName', 'lastName', 'profilePic'] },
              ],
            },
          ],
          where: {
            ConcreteRequestId: exist.id,
            ProjectId: +inputData.params.ProjectId,
          },
          order: [['id', 'DESC']],
        });
        done({ historyList, exist }, false);
      } else {
        done(null, { message: 'Concrete Booking id does not exist' });
      }
    } catch (e) {
      done(null, e);
    }
  },
  async returnProjectModel() {
    const modelData = await helper.returnProjectModel();
    publicMember = modelData.Member;
    publicUser = modelData.User;
  },
  async getDynamicModel(inputData) {
    await this.returnProjectModel();
    let { domainName } = inputData.user;
    let enterpriseValue;
    const incomeData = inputData;
    const ParentCompanyId = inputData.body.ParentCompanyId
      ? inputData.body.ParentCompanyId
      : inputData.params.ParentCompanyId;
    let domainEnterpriseValue;
    if (domainName) {
      domainEnterpriseValue = await Enterprise.findOne({
        where: { name: domainName.toLowerCase() },
      });
      if (!domainEnterpriseValue) {
        domainName = '';
      }
    }
    if (!domainName && ParentCompanyId !== undefined && ParentCompanyId !== 'undefined') {
      const { email } = inputData.user;
      let userData;
      if (email) {
        userData = await publicUser.findOne({ where: { email } });
      }
      if (userData) {
        const memberData = await publicMember.findOne({
          where: { UserId: userData.id, RoleId: { [Op.ne]: 4 }, isDeleted: false },
        });
        if (memberData) {
          if (memberData.isAccount) {
            enterpriseValue = await Enterprise.findOne({
              where: { id: memberData.EnterpriseId, status: 'completed' },
            });
            if (enterpriseValue) {
              domainName = enterpriseValue.name.toLowerCase();
            }
          } else {
            enterpriseValue = await Enterprise.findOne({
              where: { ParentCompanyId, status: 'completed' },
            });
            if (enterpriseValue) {
              domainName = enterpriseValue.name.toLowerCase();
            }
          }
        } else {
          enterpriseValue = await Enterprise.findOne({
            where: { ParentCompanyId, status: 'completed' },
          });
          if (enterpriseValue) {
            domainName = enterpriseValue.name.toLowerCase();
          }
        }
      }
    }
    const modelObj = await helper.getDynamicModel(domainName);
    ConcreteRequestHistory = modelObj.ConcreteRequestHistory;
    ConcreteRequest = modelObj.ConcreteRequest;
    User = modelObj.User;
    if (enterpriseValue) {
      const newUser = await User.findOne({ where: { email: inputData.user.email } });
      incomeData.user = newUser;
    }
    return true;
  },
};

module.exports = historyService;
