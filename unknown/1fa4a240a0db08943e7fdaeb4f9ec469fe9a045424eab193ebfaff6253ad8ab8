const status = require('http-status');
const {
  Sequelize,
  Enterprise,
  DeliverEquipment,
  CraneRequestEquipment,
  DeliveryRequest,
  CraneRequest,
  DeliverHistory,
  CraneRequestHistory,
} = require('../models');
let { Equipments, Project, Member, User } = require('../models');
const { PresetEquipmentType } = require('../models');
const helper = require('../helpers/domainHelper');
// const mixpanelService = require('./mixpanelService');

const { Op } = Sequelize;
const ApiError = require('../helpers/apiError');

let publicUser;
let publicMember;
const equipmentService = {
  async addEquipment(equData, done) {
    try {
      await this.getDynamicModel(equData);
      const userDetails = equData.user;
      const inputData = equData.body;
      const createdByDetail = await Member.getBy({
        UserId: userDetails.id,
        ProjectId: inputData.ProjectId,
      });
      inputData.createdBy = createdByDetail.id;
      const projectDetails = await Project.getProject({ id: inputData.ProjectId });
      const controlluserDetails = await Member.getBy({
        id: inputData.controlledBy,
        ProjectId: inputData.ProjectId,
      });
      if (projectDetails) {
        if (controlluserDetails) {
          const nameExist = await Equipments.getEquipment({
            equipmentName: inputData.equipmentName,
            ProjectId: inputData.ProjectId,
            isDeleted: false,
          });
          if (nameExist) {
            const err = new ApiError('Equipment Name Already exist.', status.BAD_REQUEST);
            done(null, err);
          } else {
            const lastIdValue = await Equipments.findOne({
              where: { ProjectId: inputData.ProjectId, isDeleted: false },
              order: [['equipmentAutoId', 'DESC']],
            });
            let id = 0;
            const newValue = JSON.parse(JSON.stringify(lastIdValue));
            if (
              newValue &&
              newValue.equipmentAutoId !== null &&
              newValue.equipmentAutoId !== undefined
            ) {
              id = newValue.equipmentAutoId;
            }
            inputData.equipmentAutoId = id + 1;
            const newEquipment = await Equipments.createEquipment(inputData);
            done(newEquipment, false);
          }
        } else {
          const err = new ApiError('Controlled Member not in our Project.', status.BAD_REQUEST);
          done(null, err);
        }
      } else {
        const err = new ApiError('Project does not exist.', status.BAD_REQUEST);
        done(null, err);
      }
    } catch (e) {
      done(null, e);
    }
  },
  async returnProjectModel() {
    const modelData = await helper.returnProjectModel();
    // publicProject = modelData.Project;
    publicMember = modelData.Member;
    publicUser = modelData.User;
  },
  async getDynamicModel(inputData) {
    await this.returnProjectModel();
    let { domainName } = inputData.user;
    const incomeData = inputData;
    let enterpriseValue;
    let ProjectId;
    const ParentCompanyId = inputData.body.ParentCompanyId
      ? inputData.body.ParentCompanyId
      : inputData.params.ParentCompanyId;
    let domainEnterpriseValue;
    if (domainName) {
      domainEnterpriseValue = await Enterprise.findOne({
        where: { name: domainName.toLowerCase() },
      });
      if (!domainEnterpriseValue) {
        domainName = '';
      }
    }
    if (!domainName && ParentCompanyId !== undefined && ParentCompanyId !== 'undefined') {
      const { email } = inputData.user;
      let userData;
      if (email) {
        userData = await publicUser.findOne({ where: { email } });
      }
      if (userData) {
        const memberData = await publicMember.findOne({
          where: { UserId: userData.id, RoleId: { [Op.ne]: 4 }, isDeleted: false },
        });
        if (memberData) {
          if (memberData.isAccount) {
            enterpriseValue = await Enterprise.findOne({
              where: { id: memberData.EnterpriseId, status: 'completed' },
            });
            if (enterpriseValue) {
              domainName = enterpriseValue.name.toLowerCase();
            }
          } else {
            enterpriseValue = await Enterprise.findOne({
              where: { ParentCompanyId, status: 'completed' },
            });
            if (enterpriseValue) {
              domainName = enterpriseValue.name.toLowerCase();
            }
          }
        } else {
          enterpriseValue = await Enterprise.findOne({
            where: { ParentCompanyId, status: 'completed' },
          });
          if (enterpriseValue) {
            domainName = enterpriseValue.name.toLowerCase();
          }
        }
      }
    }
    const modelObj = await helper.getDynamicModel(domainName);
    Equipments = modelObj.Equipments;
    Project = modelObj.Project;
    Member = modelObj.Member;
    User = modelObj.User;
    if (enterpriseValue) {
      // const existProjectId = inputData.params.ProjectId
      //   ? inputData.params.ProjectId
      //   : inputData.body.ProjectId;
      // if (existProjectId) {
      //   const projectDet = await Project.findOne({
      //     where: { publicSchemaId: existProjectId },
      //   });
      //   ProjectId = projectDet.id;
      // }
      // if (incomeData.params.ProjectId) {
      //   incomeData.params.ProjectId = ProjectId;
      // }
      // if (incomeData.body.ProjectId) {
      //   incomeData.body.ProjectId = ProjectId;
      // }
      const newUser = await User.findOne({ where: { email: inputData.user.email } });
      incomeData.user = newUser;
    }
    return ProjectId;
  },
  async lastEquipment(inputData, done) {
    try {
      let data;
      const lastData = await Equipments.findOne({
        where: { ProjectId: inputData.params.ProjectId, isDeleted: false },
        order: [['equipmentAutoId', 'DESC']],
      });
      if (lastData) {
        data = lastData.equipmentAutoId + 1;
      } else {
        data = 1;
      }
      done({ id: data }, false);
    } catch (e) {
      done(null, e);
    }
  },
  async listEquipment(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { params } = inputData;
      const incomeData = inputData.body;
      let searchCondition = {};
      const pageNumber = +params.pageNo;
      const pageSize = +params.pageSize;
      const { sort } = inputData.body;
      const { sortByField } = inputData.body;
      const offset = (pageNumber - 1) * pageSize;
      const condition = {
        ProjectId: inputData.params.ProjectId,
        isDeleted: false,
      };
      if (inputData.body.showActivatedAlone) {
        condition.isActive = true;
      }
      if (incomeData.nameFilter) {
        condition.equipmentName = {
          [Sequelize.Op.iLike]: `%${incomeData.nameFilter}%`,
        };
      }
      if (incomeData.companyNameFilter) {
        condition['$controllUserDetails.Company.companyName$'] = {
          [Sequelize.Op.iLike]: `%${incomeData.companyNameFilter}%`,
        };
      }

      if (incomeData.idFilter) {
        condition.equipmentAutoId = incomeData.idFilter;
        condition.isDeleted = false;
        condition.ProjectId = inputData.params.ProjectId;
      }
      if (incomeData.memberFilter) {
        condition['$controllUserDetails.id$'] = incomeData.memberFilter;
      }
      if (incomeData.typeFilter) {
        condition['$PresetEquipmentType.equipmentType$'] = incomeData.typeFilter;
      }
      if (incomeData.search) {
        const searchDefault = [
          {
            equipmentName: {
              [Sequelize.Op.iLike]: `%${incomeData.search}%`,
            },
          },
          {
            '$controllUserDetails.User.email$': {
              [Sequelize.Op.iLike]: `%${incomeData.search}%`,
            },
          },
          {
            '$controllUserDetails.Company.companyName$': {
              [Sequelize.Op.iLike]: `%${incomeData.search}%`,
            },
          },
          {
            '$PresetEquipmentType.equipmentType$': {
              [Sequelize.Op.iLike]: `%${incomeData.search}%`,
            },
          },
          {
            '$controllUserDetails.User.firstName$': {
              [Sequelize.Op.iLike]: `%${incomeData.search}%`,
            },
          },
        ];
        if (!Number.isNaN(+incomeData.search)) {
          searchCondition = {
            [Op.and]: [
              {
                [Op.or]: [
                  searchDefault,
                  {
                    [Op.and]: [
                      {
                        equipmentAutoId: incomeData.search,
                        isDeleted: false,
                        ProjectId: inputData.params.ProjectId,
                      },
                    ],
                  },
                ],
              },
            ],
          };
        } else {
          searchCondition = {
            [Op.and]: [
              {
                [Op.or]: searchDefault,
              },
            ],
          };
        }
      }
      const equipmentData = await Equipments.getAll(
        condition,
        pageSize,
        offset,
        searchCondition,
        sort,
        sortByField,
      );
      if (incomeData && incomeData.isFilter) {
        if (equipmentData.rows) {
          if (equipmentData.rows.length > 0) {
            equipmentData.rows.sort((a, b) =>
              a.equipmentName.toLowerCase() > b.equipmentName.toLowerCase() ? 1 : -1,
            );
          }
        } else if (equipmentData.length > 0) {
          equipmentData.sort((a, b) =>
            a.equipmentName.toLowerCase() > b.equipmentName.toLowerCase() ? 1 : -1,
          );
        }
      }

      done(equipmentData, false);
    } catch (e) {
      done(null, e);
    }
  },
  async craneListEquipment(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const conditionData = {
        ProjectId: inputData.params.ProjectId,
        isDeleted: false,
      };
      if (inputData.body.showActivatedAlone) {
        conditionData.isActive = true;
      }
      const equipmentData = await Equipments.findAndCountAll({
        where: conditionData,
        include: [
          {
            required: true,
            where: { isDeleted: false, isActive: true, isCraneType: true },
            association: 'PresetEquipmentType',
            attributes: ['id', 'equipmentType', 'isCraneType'],
          },
        ],
      });
      if (equipmentData.rows) {
        if (equipmentData.rows.length > 0) {
          equipmentData.rows.sort((a, b) =>
            a.equipmentName.toLowerCase() > b.equipmentName.toLowerCase() ? 1 : -1,
          );
        }
      } else if (equipmentData.length > 0) {
        equipmentData.sort((a, b) =>
          a.equipmentName.toLowerCase() > b.equipmentName.toLowerCase() ? 1 : -1,
        );
      }

      done(equipmentData, false);
    } catch (e) {
      done(null, e);
    }
  },
  async getAllEquipmentType(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { params } = inputData;
      const equipmentData = await Equipments.findAll({
        where: {
          ProjectId: params.ProjectId,
          isDeleted: false,
        },
        include: [
          {
            required: false,
            where: { isDeleted: false, isActive: true },
            association: 'PresetEquipmentType',
            attributes: ['id', 'equipmentType', 'isCraneType'],
          },
        ],
      });
      done(equipmentData, false);
    } catch (e) {
      done(null, e);
    }
  },
  async updateEquipment(equData, done) {
    try {
      await this.getDynamicModel(equData);
      const inputData = equData.body;
      const equipmentDetails = await Equipments.findOne({
        where: Sequelize.and(
          {
            id: inputData.id,
          },
          { ProjectId: inputData.ProjectId },
        ),
      });
      if (equipmentDetails) {
        const projectDetails = await Project.getProject({ id: inputData.ProjectId });
        if (projectDetails) {
          const nameExist = await Equipments.findOne({
            where: Sequelize.and(
              {
                equipmentName: inputData.equipmentName,
              },
              { ProjectId: inputData.ProjectId },
            ),
          });
          if (nameExist && nameExist.id !== inputData.id) {
            const err = new ApiError('Equipment Name Already exist.', status.BAD_REQUEST);
            done(null, err);
          } else {
            delete inputData.ProjectId;
            const newEquipment = await Equipments.updateInstance(inputData.id, inputData);
            done(newEquipment, false);
          }
        } else {
          done(null, { message: 'Project does not exist.' });
        }
      } else {
        done(null, { message: 'Equipment id does not exist.' });
      }
    } catch (e) {
      done(null, e);
    }
  },
  async deleteEquipment(input, done) {
    try {
      await this.getDynamicModel(input);
      const reqData = input.body;
      const { id } = input.body;
      let getEquipments;
      if (reqData.isSelectAll) {
        getEquipments = await Equipments.findAll({
          where: { ProjectId: reqData.ProjectId, isDeleted: false },
        });
      } else {
        getEquipments = await Equipments.findAll({
          where: {
            ProjectId: reqData.ProjectId,
            isDeleted: false,
            id: { [Op.in]: id },
          },
        });
      }
      if (getEquipments && getEquipments.length > 0) {
        getEquipments.map(async (item, index) => {
          const isEquipmentMappedToDeliveryRequest = await DeliverEquipment.findOne({
            where: {
              EquipmentId: +item.id,
              isDeleted: false,
              ProjectId: reqData.ProjectId,
            },
          });
          const isEquipmentMappedToCraneRequest = await CraneRequestEquipment.findOne({
            where: {
              EquipmentId: +item.id,
              isDeleted: false,
              ProjectId: reqData.ProjectId,
            },
          });
          if (isEquipmentMappedToDeliveryRequest) {
            return done(null, {
              message: `${item.equipmentName} cannot be deleted. ${item.equipmentName} is mapped to submitted requests`,
            });
          }
          if (isEquipmentMappedToCraneRequest) {
            return done(null, {
              message: `${item.equipmentName} cannot be deleted. ${item.equipmentName} is mapped to submitted requests`,
            });
          }
          await Equipments.update(
            { isDeleted: true },
            {
              where: { id: +item.id, ProjectId: reqData.ProjectId, isDeleted: false },
            },
          );
          if (index === getEquipments.length - 1) {
            return done('success', false);
          }
        });
      }
    } catch (e) {
      done(null, e);
    }
  },
  async equipmentsForBulkUploadDeliveryRequest(req) {
    try {
      await this.getDynamicModel(req);
      const { params } = req;
      const condition = {
        ProjectId: +params.ProjectId,
        isDeleted: false,
        isActive: true,
      };
      const equipments = await Equipments.findAll({
        where: condition,
        attributes: ['id', 'equipmentName'],
      });
      return equipments;
    } catch (e) {
      console.log(e);
    }
  },
  async getPresetEquipmentTypeList(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const equipmentTypes = await PresetEquipmentType.findAll({
        where: {
          isDeleted: false,
          isActive: true,
        },
        attributes: ['id', 'equipmentType'],
      });
      equipmentTypes.sort((a, b) =>
        a.equipmentType.toLowerCase() > b.equipmentType.toLowerCase() ? 1 : -1,
      );

      done(equipmentTypes, false);
    } catch (e) {
      done(null, e);
    }
  },
  async getMappedRequests(req, done) {
    try {
      await this.getDynamicModel(req);
      const inputData = req.body;
      const equipmentDetails = await Equipments.findOne({
        where: {
          equipmentAutoId: inputData.id,
          ProjectId: inputData.ProjectId,
          isDeleted: false,
        },
      });
      if (equipmentDetails) {
        let deliveryRequestEquipment = [];
        deliveryRequestEquipment = await DeliveryRequest.findAll({
          where: {
            ProjectId: inputData.ProjectId,
            isDeleted: false,
            deliveryStart: {
              [Op.gte]: new Date(),
            },
          },
          include: [
            {
              association: 'equipmentDetails',
              where: { isDeleted: false, isActive: true, EquipmentId: equipmentDetails.id },
              attributes: ['id'],
              include: [
                {
                  include: [
                    {
                      required: false,
                      where: { isDeleted: false, isActive: true, isCraneType: true },
                      association: 'PresetEquipmentType',
                      attributes: ['id', 'equipmentType', 'isCraneType'],
                    },
                  ],
                  association: 'Equipment',
                  attributes: ['equipmentName', 'id'],
                },
              ],
            },
          ],
        });
        const craneRequestEquipment = await CraneRequest.findAll({
          where: {
            ProjectId: inputData.ProjectId,
            isDeleted: false,
            craneDeliveryStart: {
              [Op.gte]: new Date(),
            },
          },
          include: [
            {
              association: 'equipmentDetails',
              where: { isDeleted: false, isActive: true, EquipmentId: equipmentDetails.id },
              attributes: ['id'],
              include: [
                {
                  include: [
                    {
                      required: false,
                      where: { isDeleted: false, isActive: true, isCraneType: true },
                      association: 'PresetEquipmentType',
                      attributes: ['id', 'equipmentType', 'isCraneType'],
                    },
                  ],
                  association: 'Equipment',
                  attributes: ['equipmentName', 'id'],
                },
              ],
            },
          ],
        });
        if (craneRequestEquipment) {
          deliveryRequestEquipment.push(...craneRequestEquipment);
        }
        const craneEquipments = await Equipments.findAll({
          where: {
            ProjectId: inputData.ProjectId,
            isDeleted: false,
            isActive: true,
          },
          include: [
            {
              required: true,
              where: { isDeleted: false, isActive: true, isCraneType: true },
              association: 'PresetEquipmentType',
              attributes: ['id', 'equipmentType', 'isCraneType'],
            },
          ],
          attributes: ['equipmentName', 'id', 'PresetEquipmentTypeId'],
        });
        const nonCraneEquipments = await Equipments.findAll({
          where: Sequelize.and({
            ProjectId: inputData.ProjectId,
            isDeleted: false,
            isActive: true,
          }),
          include: [
            {
              required: true,
              where: { isDeleted: false, isActive: true, isCraneType: false },
              association: 'PresetEquipmentType',
              attributes: ['id', 'equipmentType', 'isCraneType'],
            },
          ],
          attributes: ['equipmentName', 'id', 'PresetEquipmentTypeId'],
        });
        return done(
          {
            mappedRequest: deliveryRequestEquipment,
            equipments: {
              craneEquipments,
              nonCraneEquipments,
            },
          },
          false,
        );
      }
      const err = new ApiError('Equipment not found.', status.BAD_REQUEST);
      return done(null, err);
    } catch (e) {
      console.log(e);
      return done(null, e);
    }
  },
  async deactivateEquipment(req, done) {
    try {
      await this.getDynamicModel(req);
      const inputData = req.body;
      const loginUser = req.user;
      const { equipmentSwitchedRequests } = req.body;
      const equipmentDetails = await Equipments.findOne({
        where: {
          id: inputData.id,
          ProjectId: inputData.ProjectId,
          isDeleted: false,
        },
      });
      const memberDetails = await Member.getBy({
        UserId: loginUser.id,
        ProjectId: inputData.ProjectId,
        isActive: true,
        isDeleted: false,
      });
      if (equipmentDetails) {
        let deliveryRequestEquipment = [];
        deliveryRequestEquipment = await DeliveryRequest.findAll({
          where: {
            ProjectId: inputData.ProjectId,
            isDeleted: false,
            deliveryStart: {
              [Op.gte]: new Date(),
            },
          },
          include: [
            {
              association: 'equipmentDetails',
              where: { isDeleted: false, isActive: true, EquipmentId: equipmentDetails.id },
              attributes: ['id'],
              include: [
                {
                  include: [
                    {
                      required: false,
                      where: { isDeleted: false, isActive: true, isCraneType: true },
                      association: 'PresetEquipmentType',
                      attributes: ['id', 'equipmentType', 'isCraneType'],
                    },
                  ],
                  association: 'Equipment',
                  attributes: ['equipmentName', 'id'],
                },
              ],
            },
          ],
        });
        if (deliveryRequestEquipment && deliveryRequestEquipment.length > 0) {
          deliveryRequestEquipment.map(async (object) => {
            const history = {
              DeliveryRequestId: object.id,
              MemberId: memberDetails.id,
              type: 'edit',
              ProjectId: inputData.ProjectId,
              description: `${loginUser.firstName} ${loginUser.lastName} deactivated the Equipment ${equipmentDetails.equipmentName}  `,
            };
            await DeliverHistory.createInstance(history);

            await DeliverEquipment.update(
              { isActive: false },
              {
                where: {
                  EquipmentId: equipmentDetails.id,
                  ProjectId: inputData.ProjectId,
                  isDeleted: false,
                  isActive: true,
                  DeliveryId: object.id,
                },
              },
            );
          });
        }
        const craneRequestEquipment = await CraneRequest.findAll({
          where: {
            ProjectId: inputData.ProjectId,
            isDeleted: false,
            craneDeliveryStart: {
              [Op.gte]: new Date(),
            },
          },
          include: [
            {
              association: 'equipmentDetails',
              where: { isDeleted: false, isActive: true, EquipmentId: equipmentDetails.id },
              attributes: ['id'],
              include: [
                {
                  include: [
                    {
                      required: false,
                      where: { isDeleted: false, isActive: true, isCraneType: true },
                      association: 'PresetEquipmentType',
                      attributes: ['id', 'equipmentType', 'isCraneType'],
                    },
                  ],
                  association: 'Equipment',
                  attributes: ['equipmentName', 'id'],
                },
              ],
            },
          ],
        });
        if (craneRequestEquipment && craneRequestEquipment.length > 0) {
          craneRequestEquipment.map(async (object) => {
            const history = {
              CraneRequestId: object.id,
              MemberId: memberDetails.id,
              type: 'edit',
              ProjectId: inputData.ProjectId,
              description: `${loginUser.firstName} ${loginUser.lastName} deactivated the Equipment ${equipmentDetails.equipmentName}  `,
            };
            await CraneRequestHistory.createInstance(history);

            await CraneRequestEquipment.update(
              { isActive: false },
              {
                where: {
                  EquipmentId: equipmentDetails.id,
                  ProjectId: inputData.ProjectId,
                  CraneRequestId: object.id,
                  isDeleted: false,
                  isActive: true,
                },
              },
            );
          });
        }

        if (equipmentSwitchedRequests && equipmentSwitchedRequests.length > 0) {
          equipmentSwitchedRequests.map(async (object) => {
            if (
              object.requestType === 'deliveryRequest' ||
              object.requestType === 'deliveryRequestWithCrane'
            ) {
              const updateDeliveryParam = {
                DeliveryId: object.id,
                DeliveryCode: object.DeliveryId,
                ProjectId: inputData.ProjectId,
                isDeleted: false,
                EquipmentId: +object.changedEquipmentId,
                isActive: true,
              };
              const existEquipment = await DeliverEquipment.findAll({
                where: {
                  ProjectId: inputData.ProjectId,
                  DeliveryId: object.id,
                },
              });
              const index = existEquipment.findIndex(
                (item) => item.EquipmentId === +object.changedEquipmentId,
              );
              const getEquipmentObject = await Equipments.findOne({
                where: {
                  id: +object.changedEquipmentId,
                },
              });
              if (deliveryRequestEquipment && deliveryRequestEquipment.length > 0) {
                const history = {
                  DeliveryRequestId: object.id,
                  MemberId: memberDetails.id,
                  type: 'edit',
                  ProjectId: inputData.ProjectId,
                  description: `${loginUser.firstName} ${loginUser.lastName} added the Equipment  ${getEquipmentObject.equipmentName}`,
                };
                await DeliverHistory.createInstance(history);
              }
              if (index !== -1) {
                await DeliverEquipment.update(updateDeliveryParam, {
                  where: {
                    id: existEquipment[index].id,
                    DeliveryId: object.id,
                    ProjectId: inputData.ProjectId,
                  },
                });
              } else {
                await DeliverEquipment.createInstance(updateDeliveryParam);
              }
            }
            if (object.requestType === 'craneRequest') {
              const updateCraneParam = {
                CraneRequestId: object.id,
                CraneRequestCode: object.CraneRequestId,
                ProjectId: inputData.ProjectId,
                isDeleted: false,
                EquipmentId: +object.changedEquipmentId,
                isActive: true,
              };
              const existEquipment = await CraneRequestEquipment.findAll({
                where: {
                  ProjectId: inputData.ProjectId,
                  CraneRequestId: object.id,
                },
              });
              const index = existEquipment.findIndex(
                (item) => item.EquipmentId === +object.changedEquipmentId,
              );
              const getEquipmentObject = Equipments.findOne({
                where: {
                  id: +object.changedEquipmentId,
                },
              });
              if (craneRequestEquipment && craneRequestEquipment.length > 0) {
                const history = {
                  CraneRequestId: object.id,
                  MemberId: memberDetails.id,
                  type: 'edit',
                  ProjectId: inputData.ProjectId,
                  description: `${loginUser.firstName} ${loginUser.lastName} added the Equipment, ${getEquipmentObject.equipmentName}.`,
                };
                await CraneRequestHistory.createInstance(history);
              }
              if (index !== -1) {
                await CraneRequestEquipment.update(updateCraneParam, {
                  where: {
                    id: existEquipment[index].id,
                    CraneRequestId: object.id,
                    ProjectId: inputData.ProjectId,
                  },
                });
              } else {
                await CraneRequestEquipment.createInstance(updateCraneParam);
              }
            }
          });
        }
        const equipmentDeactivated = await Equipments.update(
          { isActive: false },
          {
            where: {
              id: equipmentDetails.id,
              isActive: true,
            },
          },
        );
        return done(equipmentDeactivated, false);
      }
      const err = new ApiError('Equipment not found.', status.BAD_REQUEST);
      return done(null, err);
    } catch (e) {
      return done(null, e);
    }
  },
};
module.exports = equipmentService;
