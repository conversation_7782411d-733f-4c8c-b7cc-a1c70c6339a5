const status = require('http-status');
const { notificationService } = require('../services');

const NotificationController = {
  async listNotification(req, res, next) {
    try {
      await notificationService.listNotification(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.OK).json({
            message: 'Notification listed Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async setReadNotification(req, res, next) {
    try {
      await notificationService.setReadNotification(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.OK).json({
            message: 'Notification Read Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async setReadAllNotification(req, res, next) {
    try {
      await notificationService.setReadAllNotification(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.OK).json({
            message: 'Notification Read Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async getNotificationCount(req, res, next) {
    try {
      await notificationService.getNotificationCount(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.OK).json({
            message: 'Notification count.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async deleteNotification(req, res, next) {
    try {
      await notificationService.deleteNotification(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.OK).json({
            message: 'Notification Deleted Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async versionUpdate(req, res, next) {
    try {
      const versionupdate = await notificationService.versionUpdation(req);
      if (versionupdate) {
        res.status(status.OK).json({
          status: 200,
          message: 'Version updated Successfully',
        });
      }
    } catch (e) {
      next(e);
    }
  },
  async getVersion(req, res, next) {
    try {
      const versionupdate = await notificationService.getVersion(req);
      res.status(status.OK).json({
        status: 200,
        message: 'Version fetched Successfully',
        data: versionupdate.data,
      });
    } catch (e) {
      next(e);
    }
  },
};
module.exports = NotificationController;
