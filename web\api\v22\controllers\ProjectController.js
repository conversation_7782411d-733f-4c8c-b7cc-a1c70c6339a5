const status = require('http-status');
const { projectService } = require('../services');
const helper = require('../helpers/domainHelper');
const enterpriseCheck = require('../helpers/enterpriseCheckHelper');
const { Enterprise, Sequelize } = require('../models');
let { Project, User } = require('../models');

const { Op } = Sequelize;

const ProjectController = {
  async createProject(req, res, next) {
    try {
      await projectService.createProject(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.CREATED).json({
            message: 'Project Created Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async getProjects(req, res, next) {
    try {
      await projectService.getProjects(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.CREATED).json({
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async createAccountProject(req, res, next) {
    try {
      await projectService.createAccountProject(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.CREATED).json({
            message: 'Project Created Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async getDynamicModel(inputData) {
    try {
      const { domainName } = inputData.user;
      const modelObj = await helper.getDynamicModel(domainName);
      Project = modelObj.Project;
      return true;
    } catch (e) {
      return e;
    }
  },
  async existProject(req, res, next) {
    try {
      await projectService.existProject(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.CREATED).json({
            response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async upgradePlan(req, res, next) {
    try {
      await projectService.upgradePlan(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.CREATED).json({
            message: 'Plan Updated Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async getUserProjects(req, res, next) {
    try {
      const { domainName } = req.user;
      const modelObj = await helper.getDynamicModel(domainName);
      Project = modelObj.Project;
      const projectList = await Project.getUserProjects(req.user);
      if (projectList.rows) {
        if (projectList.rows.length > 0) {
          projectList.rows.sort((a, b) =>
            a.projectName.toLowerCase() > b.projectName.toLowerCase() ? 1 : -1,
          );
        }
      } else if (projectList.length > 0) {
        projectList.sort((a, b) =>
          a.projectName.toLowerCase() > b.projectName.toLowerCase() ? 1 : -1,
        );
      }
      res.status(status.OK).json({
        message: 'Project List.',
        data: projectList,
      });
    } catch (e) {
      next(e);
    }
  },
  async getAccountProjects(req, res, next) {
    try {
      const newDomainName = await enterpriseCheck.checkEnterPrise(req);
      let condition = {};
      if (!newDomainName) {
        const enterpriseValue = await Enterprise.findOne({
          where: { ParentCompanyId: req.params.companyId, status: 'completed' },
        });
        if (enterpriseValue) {
          const domainName = enterpriseValue.name.toLowerCase();
          const modelObj = await helper.getDynamicModel(domainName);
          Project = modelObj.Project;
          User = modelObj.User;
          req.user = await User.findOne({
            where: {
              [Op.and]: [
                {
                  isDeleted: false,
                  [Op.and]: Sequelize.and(
                    Sequelize.where(
                      Sequelize.fn('lower', Sequelize.col('email')),
                      Sequelize.fn('lower', req.user.email),
                    ),
                  ),
                },
              ],
            },
          });
          condition = {};
        } else {
          condition = { ParentCompanyId: req.params.companyId };
        }
      } else {
        const modelObj = await helper.getDynamicModel(newDomainName);
        Project = modelObj.Project;
        User = modelObj.User;
        req.user = await User.findOne({
          where: {
            [Op.and]: [
              {
                isDeleted: false,
                [Op.and]: Sequelize.and(
                  Sequelize.where(
                    Sequelize.fn('lower', Sequelize.col('email')),
                    Sequelize.fn('lower', req.user.email),
                  ),
                ),
              },
            ],
          },
        });
      }
      const projectList = await Project.getUserProjects(req.user, {}, condition);
      res.status(status.OK).json({
        message: 'Project List.',
        data: projectList,
      });
    } catch (e) {
      next(e);
    }
  },
  async getProjectsCompany(req, res, next) {
    try {
      let newDomainName = await projectService.getNewDynamicModel(req);
      if (!newDomainName) {
        newDomainName = req.user.domainName;
      }
      const modelObj = await helper.getDynamicModel(newDomainName);
      Project = modelObj.Project;
      const projectList = await Project.getUserProjects(req.user);
      projectService.getProjectsCompany(req, projectList, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.OK).json({
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },

  async getSingleProjectDetail(req, res, next) {
    try {
      const { domainName } = req.user;
      const modelObj = await helper.getDynamicModel(domainName);
      Project = modelObj.Project;
      const projectList = await Project.findOne({
        include: [{ association: 'stripePlan', attribute: ['stripePlanName'] }],
        where: { id: req.params.ProjectId },
        attribute: ['PlanId', 'projectName'],
      });
      res.status(status.OK).json({
        message: 'Project List.',
        data: projectList,
      });
    } catch (e) {
      next(e);
    }
  },

  async getSingleProjectProfileDetail(req, res, next) {
    try {
      const { domainName } = req.user;
      const modelObj = await helper.getDynamicModel(domainName);
      Project = modelObj.Project;
      const projectList = await Project.findOne({
        include: [
          { association: 'stripePlan', attribute: ['stripePlanName'] },
          { association: 'StripeSubscription' },
        ],
        where: { id: req.params.ProjectId },
        attribute: ['PlanId', 'projectName'],
      });
      const subDetail = await projectService.getProSubStripeDate(projectList.dataValues);
      res.status(status.OK).json({
        message: 'Project Lists.',
        data: subDetail,
      });
    } catch (e) {
      next(e);
    }
  },

  async getPlansAndProjects(req, res, next) {
    try {
      await projectService.getPlansAndProjects(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.CREATED).json({
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  /**
   *
   * @param {super admin token} req
   * @param {project array lists,status,message} res
   * @param {return if any exception} next
   */
  async getProjectList(req, res, next) {
    try {
      const projects = await projectService.getProjectList(req);
      if (projects) {
        res.status(status.OK).json({
          status: 200,
          message: 'Projects listed Successfully.',
          data: projects.projectList,
          count: projects.count,
        });
      } else {
        res.status(status.OK).json({
          status: 200,
          message: 'Projects listed successfully.',
          data: [],
          count: 0,
        });
      }
    } catch (e) {
      next(e);
    }
  },
  /**
   *
   * @param {super admin token} req
   * @param {project array lists,status,message} res
   * @param {return if any exception} next
   */
  async assignNewProjectToMember(req, res, next) {
    try {
      const newProject = await projectService.assignNewProjectToMember(req);
      if (newProject) {
        res.status(status.OK).json({
          status: 200,
          message: 'Project Created Successfully.',
        });
      } else {
        res.status(status.UNPROCESSABLE_ENTITY).json({
          status: 422,
          message: 'Cannot able to create project.',
        });
      }
    } catch (e) {
      next(e);
    }
  },
  /**
   *
   * @param {super admin token} req
   * @param {status,message} res
   * @param {return if any exception} next
   */
  async editMemberProject(req, res, next) {
    try {
      const editExistingProject = await projectService.editMemberProject(req);
      if (editExistingProject) {
        res.status(status.OK).json({
          status: 200,
          message: 'Project Updated Successfully.',
        });
      } else {
        res.status(status.UNPROCESSABLE_ENTITY).json({
          status: 422,
          message: 'Cannot able to update project.',
        });
      }
    } catch (e) {
      next(e);
    }
  },
  /**
   *
   * @param {super admin token} req
   * @param {memberProject,status,message} res
   * @param {return if any exception} next
   */
  async getMemberProject(req, res, next) {
    try {
      const memberProject = await projectService.getMemberProject(req);
      if (memberProject) {
        res.status(status.OK).json({
          status: 200,
          message: 'Project Viewed Successfully.',
          data: memberProject,
        });
      } else {
        res.status(status.OK).json({
          status: 200,
          message: 'Cannot able to view project.',
        });
      }
    } catch (e) {
      next(e);
    }
  },
  /**
   *
   * @param {super admin token} req
   * @param {projectsBillingHistories,status,message} res
   * @param {return if any exception} next
   */
  async projectsBillingHistories(req, res, next) {
    try {
      const projectsBillingHistoriesData = await projectService.projectsBillingHistories(req);
      if (projectsBillingHistoriesData) {
        res.status(status.OK).json({
          status: 200,
          message: 'Projects Billing Histories Listed Successfully.',
          data: projectsBillingHistoriesData,
        });
      } else {
        res.status(status.OK).json({
          status: 200,
          message: 'Cannot able to view Projects Billing Histories.',
        });
      }
    } catch (e) {
      next(e);
    }
  },
  /**
   *
   * @param {super admin token} req
   * @param {single project billing histories,status,message} res
   * @param {return if any exception} next
   */
  async getProjectBillingHistories(req, res, next) {
    try {
      const getProjectBillingHistoriesData = await projectService.getProjectBillingHistories(req);
      if (getProjectBillingHistoriesData) {
        res.status(status.OK).json({
          status: 200,
          message: 'Project Billing History Viewed Successfully.',
          data: getProjectBillingHistoriesData,
        });
      } else {
        res.status(status.OK).json({
          status: 200,
          message: 'Cannot able to view Project Billing History.',
        });
      }
    } catch (e) {
      next(e);
    }
  },
  /**
   *
   * @param {token,project id,payload} req
   * @param {status,message} res
   * @param {return if any exception} next
   */
  async editProject(req, res, next) {
    try {
      const updateProject = await Project.update(
        {
          projectName: req.body.projectName,
          projectLocation: req.body.projectLocation,
          projectLocationLatitude: req.body.projectLocationLatitude,
          projectLocationLongitude: req.body.projectLocationLongitude,
          TimeZoneId: req.body.TimeZoneId,
        },
        { where: { id: +req.body.ProjectId } },
      );
      if (updateProject) {
        res.status(status.OK).json({
          status: 200,
          message: 'Project Details updated successfully.',
        });
      } else {
        res.status(status.OK).json({
          status: 200,
          message: 'Cannot able to edit Project Detail.',
        });
      }
    } catch (e) {
      next(e);
    }
  },
  /**
   *
   * @param {project list} res
   * @param {return if any exception} next
   */
  async getTotalProjects(req, res, next) {
    try {
      const projects = await projectService.getTotalProjects();
      res.status(status.OK).json({
        status: 200,
        message: 'Project listed Successfully.',
        data: projects,
      });
    } catch (e) {
      next(e);
    }
  },
  /**
   *
   * @param {success} res
   * @param {return if any exception} next
   */
  async extendProjectDuration(req, res, next) {
    try {
      const projectExtended = await projectService.extendProjectDuration(req);
      if (projectExtended) {
        res.status(status.OK).json({
          status: 200,
          message: 'Project duration extended',
        });
      } else {
        res.status(status.INTERNAL_SERVER_ERROR).json({
          status: 500,
          message: 'Cannot able to extend the duration. Try again after sometime.!',
        });
      }
    } catch (e) {
      next(e);
    }
  },
  async updateProjectSharingSettings(req, res, next) {
    try {
      const projectSettings = await projectService.updateProjectSharingSettings(req.body);
      if (projectSettings) {
        res.status(status.OK).json({
          status: 200,
          message: 'Project Settings Updated Successfully',
        });
      } else {
        res.status(status.INTERNAL_SERVER_ERROR).json({
          status: 400,
          message: 'Cannot update project settings',
        });
      }
    } catch (e) {
      next(e);
    }
  },
  async uploadProjectLogisticPlanUrl(req, res, next) {
    try {
      await projectService.uploadLogisticPlan(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.CREATED).json({
            message:
              'Site Plan updated successfully. Please note that it may take a few minutes for the changes to reflect in the dashboard',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async generatePublicUrlForExistingProjects(req, res, next) {
    try {
      const isProjectUrlUpdated = await projectService.generatePublicUrlForExistingProjects();
      if (isProjectUrlUpdated) {
        res.status(status.OK).json({
          status: 200,
          message: 'Project`s Public URL Updated Successfully',
        });
      } else {
        res.status(status.INTERNAL_SERVER_ERROR).json({
          status: 400,
          message: 'Cannot Project`s Public URL',
        });
      }
    } catch (e) {
      next(e);
    }
  },
  async decodeProjectDetailUrl(req, res) {
    try {
      const response = await projectService.decodeProjectDetailUrl(req);
      if (response) {
        res.status(200).json({
          message: 'Decrypted URL Successfully.',
          data: response,
        });
      }
    } catch (e) {
      res.status(400).json({ error: e.message });
    }
  },

  async updateDashboardLogisticPlan(req, res, next) {
    try {
      const projectSettings = await projectService.updateDashboardLogisticPlan(req.body);
      if (projectSettings) {
        res.status(status.OK).json({
          status: 200,
          message: 'Logistic Plan Uploaded Successfully',
        });
      } else {
        res.status(status.INTERNAL_SERVER_ERROR).json({
          status: 400,
          message: 'Cannot upload Logistic Plan',
        });
      }
    } catch (e) {
      next(e);
    }
  },
  async retoolParentCompanyWithProjects(req, res) {
    try {
      const companies = await projectService.retoolParentCompanyWithProjects();
      res.status(200).json({ data: companies });
    } catch (error) {
      console.error('Error in getAllCompaniesController:', error.message);
      res.status(500).json({ error: error.message });
    }
  },
  async updatedSitePlanInAWS(req, res, next) {
    try {
      const projectSettings = await projectService.updatedSitePlanInAWS(req);
      if (projectSettings) {
        res.status(status.OK).json({
          status: 200,
          message: 'Project Settings Updated Successfully',
          data: projectSettings,
        });
      } else {
        res.status(status.INTERNAL_SERVER_ERROR).json({
          status: 400,
          message: 'Cannot update project settings',
        });
      }
    } catch (e) {
      next(e);
    }
  },
  // async uploadSiteLogisticPlan(req, res, next) {
  //   try {
  //     const updateSitePlan = await projectService.uploadSiteLogisticPlan(req);
  //     if (!updateSitePlan) {
  //       next(error);
  //     } else {
  //       res.status(status.CREATED).json({
  //         message: 'site plan uploaded successfully.',
  //       });
  //     }
  //   } catch (e) {
  //     next(e);
  //   }
  // },
};
module.exports = ProjectController;
