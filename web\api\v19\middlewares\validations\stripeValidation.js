const Joi = require('joi');

const stripeValidation = {
  createSubscription: {
    body: Joi.object({
      product: Joi.object({
        name: Joi.string().min(3).required(),
        type: Joi.string(),
      }),
      plan: Joi.object({
        nickName: Joi.string().min(3),
        amount: Joi.number().required(),
        currency: Joi.string().required(),
        interval: Joi.string(),
      }),
    }),
  },
  cancelSubscription: {
    params: Joi.object({
      ProjectId: Joi.number().required(),
    }),
  },
  holdSubscription: {
    params: Joi.object({
      ProjectId: Joi.number().required(),
    }),
  },
  editSubscription: {
    body: Joi.object({
      id: Joi.number().required(),
      planName: Joi.string()
        .messages({
          'any.required': 'planName is required',
          'string.pattern.base': 'Invalid planName',
        })
        .required(),
      monthlyPrice: Joi.string()
        .messages({
          'any.required': 'monthlyPrice is required',
          'string.pattern.base': 'Invalid monthlyPrice',
        })
        .required(),
      annualPrice: Joi.string()
        .messages({
          'any.required': 'annualPrice is required',
          'string.pattern.base': 'Invalid annualPrice',
        })
        .required(),
    }),
  },
};

module.exports = stripeValidation;
