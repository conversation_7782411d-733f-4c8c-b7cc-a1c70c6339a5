/* eslint-disable no-await-in-loop */
const moment = require('moment');
const crypto = require('crypto');
const status = require('http-status');
const bcrypt = require('bcrypt');
const stripe = require('stripe')(process.env.STRIPE_API_KEY, {
  maxNetworkRetries: 3,
});

const creditCardType = require('credit-card-type');
const helper = require('../helpers/domainHelper');
const ApiError = require('../helpers/apiError');
const { generatePassword } = require('../helpers/generatePassword');
const MAILER = require('../mailer');
const {
  StripeSubscription,
  Sequelize,
  RestrictEmail,
  Enterprise,
  StripePlan,
  TimeZone,
  NotificationPreference,
  NotificationPreferenceItem,
  ProjectSettings,
  Locations,
  LocationNotificationPreferences,
} = require('../models');
let { User, Role, Project, Member, ParentCompany, Company } = require('../models');
const jwtGenerator = require('../middlewares/jwtGenerator');
const { bcryptPassword } = require('./password');
const stripeService = require('./stripeService');
// const mixpanelService = require('./mixpanelService');
const db = require('../models');
const projectService = require('./projectService');

const { Op } = Sequelize;
let publicUser;
let domainName;
let publicMember;

const authService = {
  async getDynamicModel(inputData) {
    await this.returnProjectModel();
    domainName = inputData.domainName;
    if (!domainName) {
      const email = inputData.params.email ? inputData.params.email : inputData.body.email;
      let userData;
      if (email) {
        userData = await publicUser.findOne({
          where: {
            [Op.and]: [
              {
                isDeleted: false,
                [Op.and]: Sequelize.and(
                  Sequelize.where(
                    Sequelize.fn('lower', Sequelize.col('email')),
                    Sequelize.fn('lower', email),
                  ),
                ),
              },
            ],
          },
        });
      } else if (inputData.params.resetToken) {
        userData = await publicUser.findOne({
          where: { resetPasswordToken: inputData.params.resetToken },
        });
      } else {
        userData = await publicUser.findOne({
          where: { resetPasswordToken: inputData.params.reset_password_token },
        });
      }
      if (userData) {
        const memberData = await publicMember.findOne({
          where: { UserId: userData.id, RoleId: { [Op.ne]: 4 }, isDeleted: false },
        });
        if (memberData) {
          if (memberData.isAccount) {
            const enterpriseValue = await Enterprise.findOne({
              where: { id: memberData.EnterpriseId, status: 'completed' },
            });
            if (enterpriseValue) {
              domainName = enterpriseValue.name.toLowerCase();
            }
          }
        }
      }
    }
    const modelObj = await helper.getDynamicModel(domainName);
    Member = modelObj.Member;
    Company = modelObj.Company;
    Project = modelObj.Project;
    User = modelObj.User;
    Role = modelObj.Role;
    ParentCompany = modelObj.ParentCompany;
    return User;
  },
  async getPublicModel() {
    const newDomainName = '';
    const modelObj = await helper.getDynamicModel(newDomainName);
    Member = modelObj.Member;
    Company = modelObj.Company;
    Project = modelObj.Project;
    User = modelObj.User;
    Role = modelObj.Role;
    ParentCompany = modelObj.ParentCompany;
    return User;
  },
  async returnProjectModel() {
    const modelData = await helper.returnProjectModel();
    // publicProject = modelData.Project;
    publicUser = modelData.User;
    publicMember = modelData.Member;
  },
  async createUser(user, done) {
    try {
      await this.getPublicModel();
      const userData = user;
      const firstSplit = userData.basicDetails.email.split('@')[1];
      const secondSplit = firstSplit.split('.');
      let emailDomainName;
      if (secondSplit.length === 2) {
        emailDomainName = firstSplit;
      } else if (secondSplit.length > 2) {
        const str = firstSplit.substring(firstSplit.indexOf('.') + 1);
        emailDomainName = str;
      }
      const restrict = await RestrictEmail.getBy({ domainName: emailDomainName, isActive: true });
      if (restrict) {
        return done(null, { message: 'Please use your work email address to register' });
      }
      userData.emailDomainName = emailDomainName;
      if (userData.planData.Plan.planType.toLowerCase() === 'project plan') {
        this.registerProjectPlanUser(userData, (response, error) => {
          if (error) {
            done(null, error);
          } else {
            done(response, false);
          }
        });
      } else {
        this.registerUser(userData, (response, error) => {
          if (error) {
            done(null, error);
          } else {
            done(response, false);
          }
        });
      }
    } catch (e) {
      done(null, e);
    }
  },
  async registerProjectPlanUser(userInput, done) {
    let result = {};
    // stripeService.addNewCard(userInput, async (result, err) => {
    // if (err) {
    // done(null, err);
    // } else {

    const subscription = await stripe.subscriptions.list({
      customer: userInput.stripeCustomerId,
      limit: 1,
    });
    const latestSubscriptionObject = subscription.data[0];
    // const newSubscriptionCreated = await StripeSubscription.create({
    //   // UserId: user.id,
    //   status: 'active',
    //   subscriptionId: latestSubscriptionObject.id,
    // });
    // newInputData.stripeDetails = newSubscriptionCreated;
    result = latestSubscriptionObject;
    result.stripeCustomerId = userInput.stripeCustomerId;
    this.commonRegister(userInput, result, async (response, error) => {
      if (error) {
        done(null, error);
      } else {
        done(response, null);
      }
    });
    // }
    // });
  },
  async commonRegister(userInput, newResult, done) {
    try {
      const result = newResult;
      const roleDetails = await Role.getBy('Project Admin');
      const userData = userInput.basicDetails;
      const { emailDomainName } = userInput;
      userData.firstName = userInput.companyDetails.fullName;
      userData.lastName = userInput.companyDetails.lastName;
      const selectedPlanName = userInput.planData.Plan.planType.toLowerCase();
      const projectPlanPeriod = userInput.planData.stripePlanName.toLowerCase();
      const { projectName, projectLocation, projectLocationLatitude, projectLocationLongitude } =
        userInput.projectDetails;
      userData.password = generatePassword();
      userData.email = userData.email.toLowerCase();
      const generatedPassword = userData.password;
      const newUser = await User.createInstance(userData);
      let newStripeSubscription;
      if (result !== null && result !== undefined) {
        await newUser.updateInstance(newUser.id, {
          stripeCustomerId: result.stripeCustomerId,
        });
        delete result.stripeCustomerId;
        newStripeSubscription = await StripeSubscription.createInstance(newUser, result);
      } else {
        const customer = await stripe.customers.create({
          email: newUser.email,
          name: `${newUser.firstName} ${newUser.lastName}`,
          phone: `${userInput.basicDetails.phoneCode} ${userInput.basicDetails.phoneNumber}`,
          address: {
            city: userInput.companyDetails.city,
            country: userInput.companyDetails.country,
            line1: userInput.companyDetails.address,
            postal_code: userInput.companyDetails.zipCode,
            state: userInput.companyDetails.state,
          },
        });
        await newUser.updateInstance(newUser.id, {
          stripeCustomerId: customer.id,
        });
        const plan = await StripePlan.getBy({ stripePlanId: userInput.planData.stripePlanId });
        // You need to make sure that you always pass trial_end or trial_period_days
        // when you create the subscription instead.
        const subscribedToTrialPlan = await stripe.subscriptions.create({
          customer: customer.id,
          // Either charge_automatically, or send_invoice.When charging automatically, Stripe will attempt to pay this subscription at the end of the cycle using the default source attached to the customer.When sending an invoice, Stripe will email your customer an invoice with payment instructions.Defaults to charge_automatically.
          collection_method: 'send_invoice',
          trial_period_days: 14,
          days_until_due: 0,
          items: [{ plan: plan.stripePlanId }],
        });
        delete subscribedToTrialPlan.status;
        subscribedToTrialPlan.status = 'active';
        newStripeSubscription = await StripeSubscription.createInstance(
          newUser,
          subscribedToTrialPlan,
        );
      }
      let existParentCompany = await ParentCompany.getBy({ emailDomainName });
      let isParent = true;
      if (existParentCompany) {
        const checkIsParentCompanyExists = await Company.findOne({
          where: { ParentCompanyId: +existParentCompany.id, isParent: true },
        });
        if (checkIsParentCompanyExists) {
          isParent = false;
        }
      }
      if (!existParentCompany) {
        const parentParam = {
          UserId: newUser.id,
          emailDomainName,
        };
        existParentCompany = await ParentCompany.createInstance(parentParam);
      }
      const projectParam = {
        projectName,
        projectLocation,
        createdBy: newUser.id,
        StripeSubscriptionId: newStripeSubscription ? newStripeSubscription.id : null,
        ParentCompanyId: existParentCompany.id,
        subscribedOn: new Date(),
        PlanId: userInput.planData.id,
        projectLocationLatitude,
        projectLocationLongitude,
        TimeZoneId: null,
      };
      const timezoneList = await TimeZone.getAll();
      const getUserTimeZone = timezoneList.filter(
        (object) => object.timezone === userInput.timezone,
      );
      if (getUserTimeZone && getUserTimeZone.length > 0) {
        const userTimeZone = getUserTimeZone[0];
        projectParam.TimeZoneId = userTimeZone.id;
      }
      let startDate;
      let endDate;
      if (selectedPlanName === 'trial plan') {
        startDate = moment().format('YYYY-MM-DD');
        endDate = moment().add(14, 'days').format('YYYY-MM-DD');
      } else if (selectedPlanName === 'project plan' && projectPlanPeriod === 'monthly') {
        startDate = moment().format('YYYY-MM-DD');
        endDate = moment(startDate, 'YYYY-MM-DD').add(1, 'M').format('YYYY-MM-DD');
      } else if (selectedPlanName === 'project plan' && projectPlanPeriod === 'yearly') {
        startDate = moment().format('YYYY-MM-DD');
        endDate = moment(startDate, 'YYYY-MM-DD').add(12, 'M').format('YYYY-MM-DD');
      }
      projectParam.startDate = startDate;
      projectParam.endDate = endDate;
      const newProject = await Project.createInstance(projectParam);
      const createObject = {
        deliveryWindowTime: 0,
        deliveryWindowTimeUnit: 'minutes',
        ProjectId: +newProject.id,
      };
      await ProjectSettings.create(createObject);
      const projectObject = {
        ProjectId: +newProject.id,
        projectName,
      };
      await projectService.generatePublicUrlForCurrentProject(projectObject);
      const companyParam = userInput.companyDetails;
      companyParam.createdBy = newUser.id;
      companyParam.ProjectId = newProject.id;
      companyParam.ParentCompanyId = existParentCompany.id;
      companyParam.isParent = isParent;
      companyParam.companyAutoId = 1;
      delete companyParam.fullName;
      delete companyParam.lastName;
      // const existCompany = await Company.findOne({
      //   where: Sequelize.and({
      //     companyName: companyParam.companyName.toLowerCase(),
      //     website: companyParam.website,
      //   }),
      // });
      // if (existCompany) {
      //   newCompany = existCompany;
      // } else {
      // const newCompany = await Company.createInstance(companyParam);
      // }
      let newCompany = {};
      if (
        companyParam.companyId !== null &&
        companyParam.companyId !== '' &&
        companyParam.companyId !== undefined
      ) {
        newCompany.id = +companyParam.companyId;
      } else {
        newCompany = await Company.createInstance(companyParam);
      }
      const userDetail = newUser.dataValues ? newUser : newUser.attributes;
      userData.generatedPassword = generatedPassword;
      const memberParam = {
        UserId: newUser.id,
        firstName: newUser.firstName,
        password: generatedPassword,
        CompanyId: newCompany.id,
        phoneNumber: userData.phoneNumber,
        phoneCode: userData.phoneCode,
        ProjectId: newProject.id,
        memberId: 1,
        ParentCompanyId: existParentCompany.id,
        RoleId: roleDetails.id,
        createdBy: newUser.id,
        status: 'completed',
      };
      const addedMember = await Member.createInstance(memberParam);
      await this.createMemberNotificationPreference(addedMember);
      await this.createMemberLocationFollowPreference(
        +newProject.id,
        +existParentCompany.id,
        +addedMember.id,
        newProject.projectName,
        +newUser.id,
      );
      // if (selectedPlanName === 'project plan') {
      //   const invoices = await stripe.invoices.list({
      //     customer: result.stripeCustomerId,
      //     limit: 1,
      //   });
      //   const planDetails = await StripePlan.getBy({ id: userInput.planData.id });
      //   const getCardInfo = creditCardType(userInput.cardDetails.number);
      //   const cardType = getCardInfo[0].niceType.toLowerCase();
      //   let invoiceNo = invoices.data[0].number;
      //   let receiptNo = invoices.data[0].receipt_number;
      //   if (!invoiceNo) {
      //     invoiceNo = Math.floor(Math.random() * 324324 + 1);
      //   }
      //   if (!receiptNo) {
      //     receiptNo = Math.floor(Math.random() * 899776 + 1);
      //   }
      //   const mailData = {
      //     email: userData.email,
      //     firstName: userData.firstName,
      //     lastName: userData.lastName,
      //     invoiceNo,
      //     receiptNo,
      //     footerInvoiceNo: invoiceNo,
      //     invoiceDate: moment().format('DD-MM-YYYY'),
      //     paymentDate: moment().format('DD-MM-YYYY'),
      //     address: userInput.companyDetails.address,
      //     city: userInput.companyDetails.city,
      //     state: userInput.companyDetails.state,
      //     zipcode: userInput.companyDetails.zipCode,
      //     country: userInput.companyDetails.country,
      //     datePaid: moment().format('DD-MM-YYYY'),
      //     cardNo: userInput.cardDetails.number.substring(userInput.cardDetails.number.length - 4),
      //     amount: (planDetails.stripeAmount / 100).toFixed(2),
      //     quantity: 1,
      //     subTotal: (planDetails.stripeAmount / 100).toFixed(2),
      //     total: (planDetails.stripeAmount / 100).toFixed(2),
      //     invoiceSubTotal: (planDetails.stripeAmount / 100).toFixed(2),
      //     planName: 'Project Plan',
      //     cardType,
      //   };
      //   mailData.projectName = userInput.projectDetails.projectName;
      //   mailData.interval = userInput.cardDetails.interval;
      //   mailData.projectStartDate = moment(newProject.startDate).format('DD-MM-YYYY');
      //   mailData.projectEndDate = moment(newProject.endDate).format('DD-MM-YYYY');
      //   await MAILER.sendMail(
      //     mailData,
      //     'signUpSubscription',
      //     `Follo - Congratulations! ${mailData.projectName} has subscribed for -
      // ${mailData.interval}  from  ${mailData.projectStartDate} to ${mailData.projectEndDate}`,
      //     'SignUp Subscription',
      //     (info, err) => {
      //       console.log(info, err);
      //     },
      //   );
      // }
      this.sendMail(userData, (info, err) => {
        if (err) {
          const newError = new ApiError(err.message, status.BAD_REQUEST);
          done(null, newError);
        } else {
          done(userDetail, false);
        }
      });
      // if (selectedPlanName === 'trial plan') {
      //   const trialStartDate = moment().format('DD-MM-YYYY');
      //   const trialEndDate = moment().add(14, 'days').format('DD-MM-YYYY');
      //   userData.projectName = projectName;
      //   await MAILER.sendMail(
      //     userData,
      //     'trialPlanBegins',
      //     `Follo trial begins from ${trialStartDate} to ${trialEndDate} on
      //     ${userData.projectName} upto 14
      //     Days`,
      //    'Trail Plan Begins Notification',
      //     async (info, err) => {
      //       console.log(info, err);
      //     },
      //   );
      // }
    } catch (e) {
      done(null, e);
    }
  },
  async registerUser(userInput, done) {
    this.commonRegister(userInput, null, async (response, error) => {
      if (error) {
        done(null, error);
      } else {
        done(response, null);
      }
    });
  },
  async sendMail(userData, done) {
    await MAILER.sendMail(userData, 'register', 'Registration', 'Sign Up', (info, err) => {
      if (err) {
        const newError = new ApiError(err.message, status.BAD_REQUEST);
        done(null, newError);
      } else {
        done(userData, false);
      }
    });
  },
  async login(inputData, done) {
    try {
      const userData = inputData.body;
      await this.getDynamicModel(inputData);
      const { email, password } = userData;
      let condition = {
        isDeleted: false,
      };
      if (domainName === null || domainName === undefined) {
        condition = { ...condition, userType: 'user' };
      } else if (domainName !== 'public') {
        await db.syncToSchema(domainName);
      }
      const user = await User.findOne({
        where: Sequelize.and(
          Sequelize.where(
            Sequelize.fn('lower', Sequelize.col('email')),
            Sequelize.fn('lower', email),
          ),
          { ...condition },
        ),
      });
      if (!user) {
        const error = new ApiError('User not found', status.NOT_FOUND);
        done(null, error);
      } else if (user && user.isActive) {
        const onboardingusers = await Member.findOne({
          where: { UserId: user.id, isDeleted: false, status: 'completed' },
        });
        if (user && user.password && onboardingusers) {
          const verifyPassword = await bcrypt.compare(password, user.password);
          if (!verifyPassword) {
            const error = new ApiError('Incorrect Password.', status.UNAUTHORIZED);
            done(null, error);
          } else {
            const getUserProjects = await Member.findOne({
              where: { UserId: user.id, isDeleted: false, status: 'completed' },
            });
            if (getUserProjects) {
              const userParams = {
                loginDateTime: new Date(),
              };
              await user.updateInstance(user.id, userParams);
              user.domainName = domainName;
              const token = await jwtGenerator.token(user);
              user.token = token;
              done(user, false);
            } else {
              const error = new ApiError(
                'You need to be a part of a project to access Follo',
                status.NOT_FOUND,
              );
              done(null, error);
            }
          }
        } else {
          const error = new ApiError(
            'User not yet onboarded, kindly click the onboarding link sent to your email and complete the onboarding process',
            status.NOT_FOUND,
          );
          done(null, error);
        }
      } else {
        const error = new ApiError(
          'Your account was deactivated. Please contact Super Admin.!',
          status.NOT_FOUND,
        );
        done(null, error);
      }
    } catch (e) {
      done(null, e);
    }
  },
  async forgotPassword(inputData, userObj, done) {
    await this.returnProjectModel();
    const user = userObj;
    const params = {};
    if (!user.isActive || user.isActive === null || user.isActive === undefined) {
      const error = new ApiError('User is not verified', status.BAD_REQUEST);
      done(null, error);
    } else {
      params.resetPasswordToken = await crypto.randomBytes(64).toString('hex');
      const userData = await user.updateInstance(user.id, params);
      const publicData = await publicUser.findOne({
        where: Sequelize.and(
          Sequelize.where(
            Sequelize.fn('lower', Sequelize.col('email')),
            Sequelize.fn('lower', inputData.body.email),
          ),
          { isDeleted: false },
        ),
      });
      if (publicData.id !== userData.id) {
        publicUser.update(params, { where: { id: publicData.id } });
      }
      user.resetPasswordToken = params.resetPasswordToken;
      user.origin = inputData.get('origin');
      user.requestType = inputData.body.requestType;
      MAILER.sendMail(user, 'forgotPassword', 'Forgot Password', 'Forgot Password', (info, err) => {
        if (err) {
          const newError = new ApiError(err.message, status.BAD_REQUEST);
          done(null, newError);
        } else {
          done(userData, false);
        }
      });
    }
  },
  async resetPassword(inputData, user, params, done) {
    try {
      await this.getDynamicModel(inputData);
      let { password } = params;
      await bcryptPassword(password, (encPassword) => {
        password = encPassword;
      });
      const userParams = {
        password,
        resetPasswordToken: null,
        otpCode: null,
        secret: null,
      };
      await user.updateInstance(user.id, userParams);
      done(true, false);
    } catch (error) {
      done(true, error);
    }
  },
  async createMemberNotificationPreference(memberData) {
    const memberDetail = await Member.findOne({
      where: { id: memberData.id, isDeleted: false },
    });
    const getNotificationPreferenceItemsList = await NotificationPreferenceItem.findAll({
      where: { isDeleted: false },
    });
    const getProject = await Project.findOne({
      where: {
        isDeleted: false,
        id: +memberDetail.ProjectId,
      },
      include: [
        {
          where: { isDeleted: false },
          association: 'TimeZone',
          required: false,
          attributes: ['id', 'location', 'timeZoneOffsetInMinutes'],
        },
      ],
    });
    const attr = {
      time: '05:00',
      timeFormat: 'AM',
    };
    let projectObject;
    if (getProject) {
      projectObject = getProject.toJSON();
    }
    if (projectObject && projectObject.TimeZone) {
      attr.TimeZoneId = projectObject.TimeZone.id;
    } else {
      attr.TimeZoneId = 3;
    }
    await Member.update(attr, { where: { id: memberData.id } });

    for (let index2 = 0; index2 < getNotificationPreferenceItemsList.length; index2 += 1) {
      const item = getNotificationPreferenceItemsList[index2];
      if (
        (item.id === 7 &&
          item.description === 'When a comment is added to a delivery/crane/concrete request' &&
          item.itemId === 4 &&
          item.emailNotification === true &&
          item.inappNotification === false &&
          item.isDeleted === false) ||
        item.inappNotification === true
      ) {
        const object = {
          MemberId: memberData.id,
          ProjectId: memberDetail.ProjectId,
          ParentCompanyId: memberDetail.ParentCompanyId,
          NotificationPreferenceItemId: item.id,
          instant: true,
          dailyDigest: false,
          isDeleted: false,
        };
        await NotificationPreference.createInstance(object);
      } else {
        const object = {
          MemberId: memberData.id,
          ProjectId: memberDetail.ProjectId,
          ParentCompanyId: memberDetail.ParentCompanyId,
          NotificationPreferenceItemId: item.id,
          instant: false,
          dailyDigest: true,
          isDeleted: false,
        };
        await NotificationPreference.createInstance(object);
      }
    }
  },
  async createMemberLocationFollowPreference(
    ProjectId,
    ParentCompanyId,
    MemberId,
    projectName,
    createdBy,
  ) {
    const locationObject = {
      ProjectId,
      ParentCompanyId,
      notes: null,
      MemberId,
      createdBy,
      platform: 'web',
      locationName: projectName,
      locationPath: projectName,
      isDefault: true,
    };
    const location = await Locations.create(locationObject);
    const object = {
      MemberId,
      ProjectId,
      LocationId: location.id,
      follow: true,
      ParentCompanyId,
      isDeleted: false,
    };
    await LocationNotificationPreferences.createInstance(object);
  },
};

module.exports = authService;
