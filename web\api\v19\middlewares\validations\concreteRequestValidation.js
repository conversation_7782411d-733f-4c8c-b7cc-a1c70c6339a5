const Joi = require('joi');

const concreteRequestValidation = {
  concreteRequest: {
    body: Joi.object({
      location: Joi.string().optional().allow('', null),
      // location: Joi.array().items({
      //   id: Joi.number().required().allow(null, ''),
      //   location: Joi.string().required(),
      //   chosenFromDropdown: Joi.boolean().required(),
      // }),
      LocationId: Joi.number().required(),
      mixDesign: Joi.array().items({
        id: Joi.number().optional().allow(null, ''),
        mixDesign: Joi.string().optional(),
        chosenFromDropdown: Joi.boolean().optional(),
      }).optional(),
      pumpSize: Joi.array().items({
        id: Joi.number().required().allow(null, ''),
        pumpSize: Joi.string().required(),
        chosenFromDropdown: Joi.boolean().required(),
      }),
      description: Joi.string().required(),
      concreteSupplier: Joi.array().min(1).required(),
      concreteOrderNumber: Joi.string().optional().allow(null, ''),
      truckSpacingHours: Joi.string().optional().allow(null, ''),
      notes: Joi.string().optional().allow('', null),
      slump: Joi.string().optional().allow(null, ''),
      concreteQuantityOrdered: Joi.string().optional().allow(null, ''),
      concreteConfirmedOn: Joi.date().required().allow('', null),
      isConcreteConfirmed: Joi.boolean().required(),
      pumpLocation: Joi.string().allow('', null),
      pumpOrderedDate: Joi.string().allow('', null),
      pumpWorkStart: Joi.string().allow('', null),
      pumpWorkEnd: Joi.string().allow('', null),
      pumpConfirmedOn: Joi.date().allow('', null),
      isPumpConfirmed: Joi.boolean().required(),
      isPumpRequired: Joi.boolean().required(),
      ProjectId: Joi.number().required(),
      ParentCompanyId: Joi.number().required(),
      ConcreteRequestId: Joi.number().required(),
      requestType: Joi.string().required(),
      primerForPump: Joi.string().optional().allow(null, ''),
      concretePlacementStart: Joi.date().required(),
      concretePlacementEnd: Joi.date().required(),
      responsiblePersons: Joi.array().min(1).required(),
      recurrence: Joi.string().valid('Does Not Repeat', 'Daily', 'Weekly', 'Monthly', 'Yearly'),
      repeatEveryCount: Joi.when('recurrence', {
        is: Joi.string().valid('Daily', 'Weekly', 'Monthly'),
        then: Joi.string().min(1).required(),
        otherwise: Joi.string().allow('', null),
      }),
      repeatEveryType: Joi.when('recurrence', {
        is: Joi.string().valid('Daily', 'Weekly', 'Monthly'),
        then: Joi.string().min(1).required(),
        otherwise: Joi.string().allow('', null),
      }),
      days: Joi.when('recurrence', {
        is: Joi.string().valid('Daily', 'Weekly'),
        then: Joi.array().min(1).required(),
        otherwise: Joi.array().allow('', null),
      }),
      chosenDateOfMonth: Joi.when('recurrence', {
        is: Joi.string().valid('Monthly', 'Yearly'),
        then: Joi.boolean().required(),
        otherwise: Joi.boolean().allow('', null),
      }),
      dateOfMonth: Joi.when('recurrence', {
        is: Joi.string().valid('Monthly', 'Yearly'),
        then: Joi.when('chosenDateOfMonth', {
          is: Joi.boolean().valid(true),
          then: Joi.string().min(1).required(),
          otherwise: Joi.string().allow('', null),
        }),
        otherwise: Joi.string().allow('', null),
      }),
      monthlyRepeatType: Joi.when('recurrence', {
        is: Joi.string().valid('Monthly', 'Yearly'),
        then: Joi.when('chosenDateOfMonth', {
          is: Joi.string().valid(false),
          then: Joi.string().min(1).required(),
          otherwise: Joi.string().allow('', null),
        }),
        otherwise: Joi.string().allow('', null),
      }),
      TimeZoneId: Joi.number().required(),
      endPicker: Joi.string().allow('', null),
      startPicker: Joi.string().allow('', null),
      originationAddress: Joi.string().optional().allow('', null),
      vehicleType: Joi.string().optional().allow('', null),
      originationAddressPump: Joi.string().optional().allow('', null),
      vehicleTypePump: Joi.string().optional().allow('', null),
    }),
  },
  editConcreteRequest: {
    body: Joi.object({
      id: Joi.number().required(),
      // location: Joi.array().items({
      //   id: Joi.number().required().allow(null, ''),
      //   location: Joi.string().required(),
      //   chosenFromDropdown: Joi.boolean().required(),
      // }),
      location: Joi.string().optional().allow('', null),
      LocationDetailId: Joi.number().required(),
      LocationId: Joi.number().required(),
      mixDesign: Joi.array().items({
        id: Joi.number().optional().allow(null, ''),
        mixDesign: Joi.string().optional(),
        chosenFromDropdown: Joi.boolean().optional(),
      }).optional(),
      pumpSize: Joi.array().items({
        id: Joi.number().required().allow(null, ''),
        pumpSize: Joi.string().required(),
        chosenFromDropdown: Joi.boolean().required(),
      }),
      description: Joi.string().required(),
      concreteSupplier: Joi.array().min(1).required(),
      concreteOrderNumber: Joi.string().optional().allow(null, ''),
      truckSpacingHours: Joi.string().optional().allow(null, ''),
      notes: Joi.string().optional().allow('', null),
      slump: Joi.string().optional().allow(null, ''),
      concreteQuantityOrdered: Joi.string().optional().allow(null, ''),
      concreteConfirmedOn: Joi.date().required().allow('', null),
      isConcreteConfirmed: Joi.boolean().required(),
      pumpLocation: Joi.string().allow('', null),
      pumpOrderedDate: Joi.string().allow('', null),
      pumpWorkStart: Joi.string().allow('', null),
      pumpWorkEnd: Joi.string().allow('', null),
      pumpConfirmedOn: Joi.date().allow('', null),
      isPumpConfirmed: Joi.boolean().required(),
      isPumpRequired: Joi.boolean().required(),
      cubicYardsTotal: Joi.string().allow('', null),
      hoursToCompletePlacement: Joi.string().optional().allow(null, ''),
      minutesToCompletePlacement: Joi.string().optional().allow(null, ''),
      ProjectId: Joi.number().required(),
      ParentCompanyId: Joi.number().required(),
      ConcreteRequestId: Joi.number().required(),
      requestType: Joi.string().required(),
      primerForPump: Joi.string().optional().allow(null, ''),
      concretePlacementStart: Joi.date().required(),
      concretePlacementEnd: Joi.date().required(),
      responsiblePersons: Joi.array().min(1).required(),
      recurrenceId: Joi.number().optional().allow('', null),
      recurrenceEndDate: Joi.date().optional().allow('', null),
      previousSeriesRecurrenceEndDate: Joi.date().required().allow('', null),
      nextSeriesRecurrenceStartDate: Joi.date().required().allow('', null),
      recurrenceSeriesStartDate: Joi.date().required().allow('', null),
      recurrenceSeriesEndDate: Joi.date().required().allow('', null),
      seriesOption: Joi.number().required(),
      deliveryStartTime: Joi.string().required(),
      deliveryEndTime: Joi.string().required(),
      timezone: Joi.string().required(),
      originationAddress: Joi.string().optional().allow('', null),
      vehicleType: Joi.string().optional().allow('', null),
      originationAddressPump: Joi.string().optional().allow('', null),
      vehicleTypePump: Joi.string().optional().allow('', null),
    }),
  },
  getSingleConcreteRequest: {
    params: Joi.object({
      ConcreteRequestId: Joi.number().required(),
      ParentCompanyId: Joi.any(),
      ProjectId: Joi.any(),
    }),
  },
  updateConcreteRequestStatus: {
    body: Joi.object({
      id: Joi.number().required(),
      status: Joi.string().required(),
      hoursToCompletePlacement: Joi.string().optional().allow(null, ''),
      minutesToCompletePlacement: Joi.string().optional().allow(null, ''),
      cubicYardsTotal: Joi.string().optional().allow(null, ''),
      ParentCompanyId: Joi.any(),
      statuschange: Joi.string(),
    }),
  },
};
module.exports = concreteRequestValidation;
