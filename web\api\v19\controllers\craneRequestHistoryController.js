const status = require('http-status');
const { craneRequestHistoryService } = require('../services');

const craneRequestHistoryController = {
  async createCraneRequestHistory(req, res, next) {
    try {
      craneRequestHistoryService.createCraneRequestHistory(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.CREATED).json({
            message: 'Crane Booking History created successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async getCraneRequestHistories(req, res, next) {
    try {
      await craneRequestHistoryService.getCraneRequestHistories(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.OK).json({
            message: 'Crane Booking History Viewed Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
};
module.exports = craneRequestHistoryController;
