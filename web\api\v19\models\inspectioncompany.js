module.exports = (sequelize, DataTypes) => {
  const InspectionCompany = sequelize.define(
    'InspectionCompany',
    {
      InspectionId: DataTypes.INTEGER,
      isDeleted: DataTypes.BOOLEAN,
      InspectionCode: DataTypes.INTEGER,
      CompanyId: DataTypes.INTEGER,
      publicSchemaId: {
        type: DataTypes.INTEGER,
      },
      ProjectId: {
        type: DataTypes.INTEGER,
      },
    },
    {},
  );
  InspectionCompany.associate = (models) => {
    // associations can be defined here
    InspectionCompany.belongsTo(models.InspectionRequest, {
      as: 'inspectionrequest',
      foreignKey: 'InspectionId',
    });
    InspectionCompany.belongsTo(models.Company);
  };
  InspectionCompany.getAll = async (attr) => {
    const newInspectionCompany = await InspectionCompany.findAll({
      where: { ...attr },
      order: [['id', 'DESC']],
    });
    return newInspectionCompany;
  };
  InspectionCompany.createInstance = async (paramData) => {
    const newInspectionCompany = await InspectionCompany.create(paramData);
    return newInspectionCompany;
  };
  return InspectionCompany;
};
