const status = require('http-status');
const { Country, State, City } = require('../models');

const AddressController = {
  async getCountry(req, res, next) {
    try {
      const countryList = await Country.getAll({});
      res.status(status.OK).json({ countryList });
    } catch (e) {
      next(e);
    }
  },
  async getState(req, res, next) {
    try {
      const stateList = await State.getAll({ CountryId: req.params.CountryId });
      res.status(status.OK).json({ stateList });
    } catch (e) {
      next(e);
    }
  },
  async getCity(req, res, next) {
    try {
      const cityList = await City.getAll({ StateId: req.params.StateId });
      res.status(status.OK).json({ cityList });
    } catch (e) {
      next(e);
    }
  },
};
module.exports = AddressController;
