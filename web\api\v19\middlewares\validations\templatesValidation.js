const Joi = require('joi');

const templatesValidation = {
    createTemplate: {
        body: Joi.object({
            id: Joi.number().optional(),
            template_name: Joi.string().required(),
            description: Joi.string().required(),
            responsible_company: Joi.string().required(),
            responsible_person: Joi.string().required(),
            date: Joi.string().required(),
            from_time: Joi.string().required(),
            to_time: Joi.string().required(),
            time_zone: Joi.number().required(),
            location: Joi.number().required(),
            notes: Joi.string().required().allow('', null),
            picking_from: Joi.string().required().allow('', null),
            picking_to: Joi.string().required().allow('', null),
            is_escort_needed: Joi.boolean().required(),
            dfow: Joi.string().required().allow('', null),
            equipment: Joi.string().required(),
            gate: Joi.number().required(),
            recurrence: Joi.object({
                recurrence: Joi.string().valid('Does Not Repeat', 'Daily', 'Weekly', 'Monthly', 'Yearly'),
                repeatEveryCount: Joi.when('recurrence', {
                    is: Joi.string().valid('Daily', 'Weekly', 'Monthly'),
                    then: Joi.string().min(1).required(),
                    otherwise: Joi.string().allow('', null),
                }),
                repeatEveryType: Joi.when('recurrence', {
                    is: Joi.string().valid('Daily', 'Weekly', 'Monthly'),
                    then: Joi.string().min(1).required(),
                    otherwise: Joi.string().allow('', null),
                }),
                days: Joi.when('recurrence', {
                    is: Joi.string().valid('Weekly'),
                    then: Joi.array().min(1).required(),
                    otherwise: Joi.array().optional().allow('', null),
                }),
                chosenDateOfMonth: Joi.when('recurrence', {
                    is: Joi.string().valid('Monthly', 'Yearly'),
                    then: Joi.boolean().required(),
                    otherwise: Joi.boolean().allow('', null),
                }),
                dateOfMonth: Joi.when('recurrence', {
                    is: Joi.string().valid('Monthly', 'Yearly'),
                    then: Joi.when('chosenDateOfMonth', {
                        is: Joi.boolean().valid(true),
                        then: Joi.string().min(1).required(),
                        otherwise: Joi.string().allow('', null),
                    }),
                    otherwise: Joi.string().allow('', null),
                }),
                monthlyRepeatType: Joi.when('recurrence', {
                    is: Joi.string().valid('Monthly', 'Yearly'),
                    then: Joi.when('chosenDateOfMonth', {
                        is: Joi.string().valid(false),
                        then: Joi.string().min(1).required(),
                        otherwise: Joi.string().allow('', null),
                    }),
                    otherwise: Joi.string().allow('', null),
                }),
                requestType: Joi.string().required(),
                endDate: Joi.string().optional().allow('', null),
            }),
            template_type: Joi.string().required(),
            ProjectId: Joi.number().required(),
            ParentCompanyId: Joi.number().required(),
            isAssociatedWithCraneRequest: Joi.optional(),
            originationAddress: Joi.string().optional().allow('', null),
            vehicleType: Joi.string().optional().allow('', null),
        }),
    },
    getTemplates: {
        query: Joi.object({
            pageNo: Joi.number().optional(),
            pageSize: Joi.number().optional(),
            ParentCompanyId: Joi.number().required(),
            sort: Joi.string().optional(),
            sortColumn: Joi.string().optional(),
            isDropdown: Joi.boolean().optional(),
        }),
        params: Joi.object({
            ProjectId: Joi.number().required(),
        }),
    },
    getTemplate: {
        query: Joi.object({
            id: Joi.number().required(),
            ProjectId: Joi.number().required(),
            ParentCompanyId: Joi.number().required(),
        }),
    },
    deleteTemplate: {
        body: Joi.object({
            id: Joi.any().optional(),
            ParentCompanyId: Joi.number().required(),
            selectAll: Joi.boolean().optional(),
        }),
    },
};

module.exports = templatesValidation;
