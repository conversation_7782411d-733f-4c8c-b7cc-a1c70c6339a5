const { Router } = require('express');
const { validate } = require('express-validation');
const guestUserController = require('../controllers/guestUserController');
const guestUserValidation = require('../middlewares/validations/guestUserValidation');
const { MemberController,CalendarController,LocationController } = require('../controllers');
const { calendarValidation,locationValidation,memberValidation } = require('../middlewares/validations');
const { 
  equipmentValidation,
  companyValidation,
  gateValidation,
  projectValidation,
  deliveryValidation,
  craneRequestValidation,
  concreteRequestValidation,
  attachementValidation,
  craneRequestAttachmentValidation
 } = require('../middlewares/validations');
 const multer = require('multer');
 const storage = multer.memoryStorage();
 const upload = multer({ storage });
const guestUserRoute = {
  get router() {
    const router = Router();
    router.get(
      '/company_list/:ProjectId/:ParentCompanyId',
      validate(guestUserValidation.getCompanies, { keyByField: true }, { abortEarly: false }),
      guestUserController.getCompanyList,
    );
    router.post(
      '/create_guest_user',
      validate(guestUserValidation.createGuestUser, { keyByField: true }, { abortEarly: false }),
      guestUserController.createGuestUser,
    );
    router.post(
      '/alreadyVisited',
      validate(guestUserValidation.emailDetail, { keyByField: true }, { abortEarly: false }),
      guestUserController.alreadyVisited,
    );
    router.post(
      '/guestUser_detail',
      validate(guestUserValidation.emailDetail, { keyByField: true }, { abortEarly: false }),
      guestUserController.guestUserDetails,
    );
    router.post('/last_deliveryId', guestUserController.lastDeliveryId);
    router.get(
      '/list_members/:ProjectId/?:ParentCompanyId',
      validate(guestUserValidation.listAllMember, { keyByField: true }, { abortEarly: false }),
      guestUserController.listAllMember,
    );
    router.post(
      '/guest_event_NDR/:ProjectId/:void',
      guestUserController.getEventNDR,
    );
    router.post(
      '/guest_crane_associated_request/:ProjectId/:void',
      guestUserController.getDeliveryRequestWithCrane,
    );
    router.post(
      '/guest_concrete_request/:ProjectId/:void',
      guestUserController.getConcreteRequest,
    );
    router.get(
      '/guest_get_locations',
      LocationController.getLocations,
    );
    router.post(
      '/gate_list/:ProjectId/:pageSize/:pageNo/?:ParentCompanyId',
      validate(gateValidation.gateDetail, { keyByField: true }, { abortEarly: false }),
      guestUserController.listGates,
    );
    router.post(
      '/equipment_list/:ProjectId/:pageSize/:pageNo/?:ParentCompanyId',
      validate(equipmentValidation.equipmentDetail, { keyByField: true }, { abortEarly: false }),
      guestUserController.listEquipment,
    );
    router.get(
      '/get_newcompanies/:ProjectId/?:ParentCompanyId',
      guestUserController.getCompanies,
    );
    router.get(
      '/get_definable_work/:ProjectId/?:ParentCompanyId',
      validate(companyValidation.getDefinableWork, { keyByField: true }, { abortEarly: false }),
      guestUserController.getDefinableWork,
    );
    router.get(
      '/get_locations',
      validate(locationValidation.getLocations, { keyByField: true }, { abortEarly: false }),
      guestUserController.getLocations,
    );
    router.get(
      '/get_last_crane_request_id/:ProjectId/?:ParentCompanyId',
      guestUserController.getLastCraneRequestId,
    );
    router.get(
      '/get_timezone_list',
      guestUserController.getTimeZoneList,
    );
    router.get(
      '/get_single_project/:ProjectId',
      validate(
        projectValidation.getSingleProjectDetail,
        { keyByField: true },
        { abortEarly: false },
      ),
      guestUserController.getSingleProjectDetail,
    );
    router.post('/get_member_data', guestUserController.getMemberDataMixPanel);
    router.get(
      '/get_user_role/:ProjectId/?:ParentCompanyId/:id',
      validate(guestUserValidation.getMemberData, { keyByField: true }, { abortEarly: false }),
      guestUserController.getMemberData,
    );
    router.get(
      '/search_member/:ProjectId/:search/?:ParentCompanyId',
      validate(memberValidation.searchMember, { keyByField: true }, { abortEarly: false }),
      guestUserController.searchMember,
    );
    router.post(
      '/new_delivery_request',
      validate(guestUserValidation.newRequest, { keyByField: true }, { abortEarly: false }),
      guestUserController.newRequest,
    );
    router.post(
      '/crane_equipment_list/:ProjectId/:pageSize/:pageNo/?:ParentCompanyId',
      validate(
        equipmentValidation.craneEquipmentDetail,
        { keyByField: true },
        { abortEarly: false },
      ),
      guestUserController.craneListEquipment,
    );
    router.post(
      '/create_crane_request',
      validate(guestUserValidation.craneRequest, { keyByField: true }, { abortEarly: false }),
      guestUserController.createCraneRequest,
    );
    router.get(
      '/concrete_dropdown_detail',
      guestUserController.getConcreteDropdownDetail,
    );
    router.post(
      '/create_concrete_request',
      validate(
        guestUserValidation.concreteRequest,
        { keyByField: true },
        { abortEarly: false },
      ),
      guestUserController.createConcreteRequest,
    );
    router.get(
      '/guest_single_NDR/:DeliveryRequestId/?:ParentCompanyId',
      validate(deliveryValidation.getNDRData, { keyByField: true }, { abortEarly: false }),
      guestUserController.getNDRData,
    );
    router.get(
      '/guest_single_crane_request/:CraneRequestId/:ProjectId/?:ParentCompanyId',
      validate(
        craneRequestValidation.getSingleCraneRequest,
        { keyByField: true },
        { abortEarly: false },
      ),
      guestUserController.getSingleCraneRequest,
    );
    router.get(
      '/guest_single_Concrete_request/:ConcreteRequestId/:ProjectId/?:ParentCompanyId',
      validate(
        concreteRequestValidation.getSingleConcreteRequest,
        { keyByField: true },
        { abortEarly: false },
      ),
      guestUserController.getSingleConcreteRequest,
    );
    router.post(
      '/edit_request',
      validate(guestUserValidation.editDeliveryRequest, { keyByField: true }, { abortEarly: false }),
      guestUserController.editRequest,
    );
    router.post(
      '/edit_crane_request',
      validate(
        guestUserValidation.editCraneRequest,
        { keyByField: true },
        { abortEarly: false },
      ),
      guestUserController.editCraneRequest,
    );
    router.post(
      '/edit_concrete_request',
      validate(
        guestUserValidation.editConcreteRequest,
        { keyByField: true },
        { abortEarly: false },
      ),
      guestUserController.editConcreteRequest,
    );
    router.post(
      '/add_attachement/:DeliveryRequestId/?:ParentCompanyId/:userId',
      upload.array('attachement', 12),
      validate(
        guestUserValidation.createAttachement,
        { keyByField: true },
        { abortEarly: false },
      ),
      guestUserController.createAttachement,
    );
    router.post(
      '/create_comment',
      validate(guestUserValidation.addComment, { keyByField: true }, { abortEarly: false }),
      guestUserController.createComment,
    );
    router.post(
      '/add_crane_request_attachement/:CraneRequestId/?:ParentCompanyId/:ProjectId/:userId',
      upload.array('attachement', 12),
      validate(
        guestUserValidation.createCraneRequestAttachement,
        { keyByField: true },
        { abortEarly: false },
      ),
      guestUserController.createCraneRequestAttachement,
    );
    router.post(
      '/create_crane_request_comment',
      validate(
        guestUserValidation.createCraneRequestComment,
        { keyByField: true },
        { abortEarly: false },
      ),
      guestUserController.createCraneRequestComment,
    );
    router.post(
      '/add_concrete_request_attachment/:ConcreteRequestId/?:ParentCompanyId/:ProjectId/:userId',
      upload.array('attachment', 12),
      validate(
        guestUserValidation.createConcreteRequestAttachment,
        { keyByField: true },
        { abortEarly: false },
      ),
      guestUserController.createConcreteRequestAttachment,
    );
    router.post(
      '/create_concrete_request_comment',
      validate(
        guestUserValidation.createConcreteRequestComment,
        { keyByField: true },
        { abortEarly: false },
      ),
      guestUserController.createConcreteRequestComment,
    );
    router.post(
      '/is_request_to_member',
      validate(guestUserValidation.requestMember, { keyByField: true }, { abortEarly: false }),
      guestUserController.isRequestToMember,
    );
    router.put(
      '/update_guest_member',
      validate(guestUserValidation.requestMember, { keyByField: true }, { abortEarly: false }),
      guestUserController.updateGuestMember,
    );
    
    return router;
  },
};
module.exports = guestUserRoute;
