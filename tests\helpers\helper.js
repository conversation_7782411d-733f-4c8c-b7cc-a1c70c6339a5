const supertest = require('supertest');
const http = require('http');
const app = require('../../src/app');

function createHelper(token = '') {
  let server;

  const createServer = async (done) => {
    server = http.createServer(app);
    server.listen(done);
  };

  const close = (done) => {
    server.close(done);
  };

  const request = (method = 'post') => (args) =>
    supertest(server)[method](args).set('Authorization', `JWT ${token}`);

  return {
    apiServer: {
      post: request('post'),
      get: request('get'),
      put: request('put'),
      delete: request('delete'),
    },
    baseEmail: 'yopmail.com',
    close,
    server: createServer,
  };
}

module.exports = createHelper;
