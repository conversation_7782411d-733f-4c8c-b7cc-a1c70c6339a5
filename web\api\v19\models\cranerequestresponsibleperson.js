module.exports = (sequelize, DataTypes) => {
  const CraneRequestResponsiblePerson = sequelize.define(
    'CraneRequestResponsiblePerson',
    {
      CraneRequestId: DataTypes.INTEGER,
      isDeleted: DataTypes.BOOLEAN,
      CraneRequestCode: DataTypes.INTEGER,
      publicSchemaId: {
        type: DataTypes.INTEGER,
      },
      MemberId: DataTypes.INTEGER,
      ProjectId: DataTypes.INTEGER,
      isActive: DataTypes.BOOLEAN,
    },
    {},
  );
  CraneRequestResponsiblePerson.associate = (models) => {
    CraneRequestResponsiblePerson.belongsTo(models.CraneRequest, {
      as: 'craneRequest',
      foreignKey: 'CraneRequestId',
    });
    CraneRequestResponsiblePerson.belongsTo(models.Member, {
      as: 'Member',
      foreignKey: 'MemberId',
    });
    CraneRequestResponsiblePerson.belongsTo(models.Member);
  };
  CraneRequestResponsiblePerson.createInstance = async (paramData) => {
    const craneResponsiblePerson = await CraneRequestResponsiblePerson.create(paramData);
    return craneResponsiblePerson;
  };
  return CraneRequestResponsiblePerson;
};
