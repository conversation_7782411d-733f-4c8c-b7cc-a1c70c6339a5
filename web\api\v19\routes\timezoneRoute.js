const { Router } = require('express');
const passportConfig = require('../config/passport');
const { TimeZoneController } = require('../controllers');

const timezoneRoute = {
  get router() {
    const router = Router();
    router.get(
      '/get_timezone_list',
      passportConfig.isAuthenticated,
      TimeZoneController.getTimeZoneList,
    );
    return router;
  },
};
module.exports = timezoneRoute;
