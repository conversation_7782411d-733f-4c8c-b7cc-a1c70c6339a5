module.exports = (sequelize, DataTypes) => {
  const DeliverAttachement = sequelize.define(
    'DeliverAttachement',
    {
      ProjectId: DataTypes.INTEGER,
      MemberId: DataTypes.INTEGER,
      DeliveryRequestId: DataTypes.INTEGER,
      isDeleted: DataTypes.BOOLEAN,
      publicSchemaId: {
        type: DataTypes.INTEGER,
      },
      attachement: DataTypes.STRING,
      filename: DataTypes.STRING,
      extension: DataTypes.STRING,
    },
    {},
  );

  DeliverAttachement.associate = (models) => {
    // associations can be defined here
    DeliverAttachement.belongsTo(models.DeliveryRequest);
  };

  DeliverAttachement.getAll = async (attr) => {
    const newDeliverAttachement = await DeliverAttachement.findAll({
      where: { ...attr },
      order: [['id', 'DESC']],
    });
    return newDeliverAttachement;
  };
  DeliverAttachement.createMultipleInstance = async (paramData) => {
    const newDeliverAttachement = await DeliverAttachement.bulkCreate(paramData);
    return newDeliverAttachement;
  };
  DeliverAttachement.createInstance = async (paramData) => {
    const newDeliverAttachement = await DeliverAttachement.create(paramData);
    return newDeliverAttachement;
  };
  return DeliverAttachement;
};
