const status = require('http-status');
const { deviceTokenService } = require('../services');

const DeviceTokenController = {
  async setDeviceToken(req, res, next) {
    deviceTokenService.setDeviceToken(req, async (gateDetail, error) => {
      if (error) {
        next(error);
      } else {
        res.status(status.CREATED).json({
          message: 'Token added successfully.',
          data: gateDetail,
        });
      }
    });
  },
  async clearDeviceToken(req, res, next) {
    deviceTokenService.clearDeviceToken(req, async (gateDetail, error) => {
      if (error) {
        next(error);
      } else {
        res.status(status.CREATED).json({
          message: 'Token cleared successfully.',
          data: gateDetail,
        });
      }
    });
  },
};
module.exports = DeviceTokenController;
