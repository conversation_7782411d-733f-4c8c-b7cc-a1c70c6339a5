const status = require('http-status');
const { bookingTemplatesService } = require('../services');

const BookingTemplatesController = {
    async createTemplate(req, res, next) {
        try {
            const projectSettings = await bookingTemplatesService.createTemplate(req.user, req.body);
            if (projectSettings) {
                res.status(status.OK).json({
                    status: 200,
                    message: 'Booking Template Created Successfully!',
                });
            } else {
                res.status(status.INTERNAL_SERVER_ERROR).json({
                    status: 400,
                    message: 'Cannot create booking template',
                });
            }
        } catch (e) {
            next(e);
        }
    },
    async getTemplates(req, res, next) {
        try {
            const getTemplates = await bookingTemplatesService.getTemplates(req);
            res.status(status.OK).json({
                status: 200,
                message: 'Booking Templates listed Successfully',
                data: getTemplates,
            });
        } catch (error) {
            next(error);
        }
    },
    async updateTemplate(req, res, next) {
        try {
            const isTemplateUpdated = await bookingTemplatesService.updateTemplate(req.user, req.body);
            if (isTemplateUpdated) {
                res.status(status.OK).json({
                    status: 200,
                    message: 'Booking Template Updated Successfully!',
                });
            } else {
                res.status(status.INTERNAL_SERVER_ERROR).json({
                    status: 400,
                    message: 'Cannot update booking template',
                });
            }
        } catch (e) {
            next(e);
        }
    },
    async getTemplate(req, res, next) {
        try {
            const getTemplate = await bookingTemplatesService.getTemplate(req);
            res.status(status.OK).json({
                status: 200,
                message: 'Booking Template listed Successfully',
                data: getTemplate,
            });
        } catch (error) {
            next(error);
        }
    },
    async deleteTemplate(req, res, next) {
        try {
            const getTemplate = await bookingTemplatesService.deleteTemplate(req);
            res.status(status.OK).json({
                status: 200,
                message: 'Booking Template deleted Successfully',
                data: getTemplate,
            });
        } catch (error) {
            next(error);
        }
    },
};
module.exports = BookingTemplatesController;
