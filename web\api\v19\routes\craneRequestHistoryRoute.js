const { Router } = require('express');
const { validate } = require('express-validation');
const passportConfig = require('../config/passport');
const { craneRequestHistoryController } = require('../controllers');
const { craneRequestHistoryValidation } = require('../middlewares/validations');

const commentRoute = {
  get router() {
    const router = Router();
    router.get(
      '/get_crane_request_histories/:CraneRequestId/?:ParentCompanyId/:ProjectId',
      validate(
        craneRequestHistoryValidation.getCraneRequestHistories,
        { keyByField: true },
        { abortEarly: false },
      ),
      passportConfig.isAuthenticated,
      craneRequestHistoryController.getCraneRequestHistories,
    );
    return router;
  },
};
module.exports = commentRoute;
