const { Sequelize, Enterprise } = require('../models');
let { Del<PERSON>yRequest, Member, DeliverCompany, User } = require('../models');
const {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  VoidList,
  CraneRequestResponsible<PERSON>erson,
  CraneRequest,
  CraneRequestCompany,
} = require('../models');
const helper = require('../helpers/domainHelper');
const exportService = require('./exportService');
const pdfCraneReportService = require('./pdfCraneReportService');
const csvCraneReportService = require('./csvCraneReportService');
const excelCraneReportService = require('./excelCraneReportService');
const awsConfig = require('../middlewares/awsConfig');
const deliveryReportService = require('./deliveryreportService');

let publicUser;
let publicMember;
const { Op } = Sequelize;
const craneReportService = {
  async getDynamicModel(inputData) {
    await this.returnProjectModel();
    let { domainName } = inputData.user;
    const incomeData = inputData;
    let enterpriseValue;
    let ProjectId;
    const ParentCompanyId = inputData.body.ParentCompanyId
      ? inputData.body.ParentCompanyId
      : inputData.params.ParentCompanyId;
    let domainEnterpriseValue;
    if (domainName) {
      domainEnterpriseValue = await Enterprise.findOne({
        where: { name: domainName.toLowerCase() },
      });
      if (!domainEnterpriseValue) {
        domainName = '';
      }
    }
    if (!domainName && ParentCompanyId !== undefined && ParentCompanyId !== 'undefined') {
      const { email } = inputData.user;
      let userData;
      if (email) {
        userData = await publicUser.findOne({ where: { email } });
      }
      if (userData) {
        const memberData = await publicMember.findOne({
          where: { UserId: userData.id, RoleId: { [Op.ne]: 4 }, isDeleted: false },
        });
        if (memberData) {
          if (memberData.isAccount) {
            enterpriseValue = await Enterprise.findOne({
              where: { id: memberData.EnterpriseId, status: 'completed' },
            });
            if (enterpriseValue) {
              domainName = enterpriseValue.name.toLowerCase();
            }
          } else {
            enterpriseValue = await Enterprise.findOne({
              where: { ParentCompanyId, status: 'completed' },
            });
            if (enterpriseValue) {
              domainName = enterpriseValue.name.toLowerCase();
            }
          }
        } else {
          enterpriseValue = await Enterprise.findOne({
            where: { ParentCompanyId, status: 'completed' },
          });
          if (enterpriseValue) {
            domainName = enterpriseValue.name.toLowerCase();
          }
        }
      }
    }
    const modelObj = await helper.getDynamicModel(domainName);
    DeliveryRequest = modelObj.DeliveryRequest;
    Member = modelObj.Member;
    DeliverCompany = modelObj.DeliverCompany;
    User = modelObj.User;
    if (enterpriseValue) {
      const newUser = await User.findOne({ where: { email: inputData.user.email } });
      incomeData.user = newUser;
    }
    return ProjectId;
  },
  async listCraneRequest(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { params } = inputData;
      const loginUser = inputData.user;
      const incomeData = inputData.body;
      const { sort } = inputData.body;
      const { sortByField } = inputData.body;
      let order;
      if (params.void >= 1 && params.void <= 0) {
        done(null, { message: 'Please enter void as 1 or 0' });
      } else {
        const memberDetails = await Member.findOne({
          where: Sequelize.and({
            UserId: loginUser.id,
            ProjectId: params.ProjectId,
            isDeleted: false,
            isActive: true,
          }),
        });
        if (memberDetails) {
          const voidCraneDelivery = [];
          const voidDelivery = [];
          const voidDeliveryList = await VoidList.findAll({
            where: {
              ProjectId: params.ProjectId,
              isDeliveryRequest: true,
              DeliveryRequestId: { [Op.ne]: null },
            },
          });
          voidDeliveryList.forEach(async (element) => {
            voidDelivery.push(element.DeliveryRequestId);
          });
          const voidCraneRequestList = await VoidList.findAll({
            where: {
              ProjectId: params.ProjectId,
              isDeliveryRequest: false,
              CraneRequestId: { [Op.ne]: null },
            },
          });
          voidCraneRequestList.forEach(async (element) => {
            voidCraneDelivery.push(element.CraneRequestId);
          });
          const offset = (+params.pageNo - 1) * +params.pageSize;
          const craneCondition = {
            ProjectId: +params.ProjectId,
            isDeleted: false,
          };
          const condition = {
            ProjectId: +params.ProjectId,
            isDeleted: false,
          };
          if (params.void === '0' || params.void === 0) {
            condition['$DeliveryRequest.id$'] = {
              [Op.and]: [{ [Op.notIn]: voidDelivery }],
            };
          } else {
            condition['$DeliveryRequest.id$'] = {
              [Op.and]: [{ [Op.in]: voidDelivery }],
            };
          }
          if (params.void === '0' || params.void === 0) {
            craneCondition['$CraneRequest.id$'] = {
              [Op.and]: [{ [Op.notIn]: voidCraneDelivery }],
            };
          } else {
            craneCondition['$CraneRequest.id$'] = {
              [Op.and]: [{ [Op.in]: voidCraneDelivery }],
            };
          }
          if (incomeData.defineFilter && incomeData.defineFilter > 0) {
            condition['$defineWorkDetails.DeliverDefineWork.id$'] = incomeData.defineFilter;
            craneCondition['$defineWorkDetails.DeliverDefineWork.id$'] = incomeData.defineFilter;
          }
          const roleId = memberDetails.RoleId;
          const memberId = memberDetails.id;
          let craneRequestList;
          let deliveryRequest;
          if (
            (incomeData.gateFilter && incomeData.gateFilter > 0) ||
            (incomeData.statusFilter && incomeData.statusFilter === 'Delivered')
          ) {
            craneRequestList = [];
          } else {
            craneRequestList = await CraneRequest.getAll(
              inputData,
              roleId,
              memberId,
              craneCondition,
              incomeData.descriptionFilter,
              incomeData.startdate,
              incomeData.enddate,
              incomeData.companyFilter,
              incomeData.memberFilter,
              incomeData.equipmentFilter,
              incomeData.statusFilter,
              incomeData.idFilter,
              incomeData.pickFrom,
              incomeData.pickTo,
              incomeData.search,
              order,
              sort,
              sortByField,
              incomeData.dateFilter,
            );
          }
          if (incomeData.statusFilter && incomeData.statusFilter === 'Completed') {
            deliveryRequest = [];
          } else {
            deliveryRequest = await DeliveryRequest.getCraneAssociatedRequest(
              inputData,
              roleId,
              memberId,
              condition,
              incomeData.descriptionFilter,
              incomeData.startdate,
              incomeData.enddate,
              incomeData.companyFilter,
              incomeData.memberFilter,
              incomeData.equipmentFilter,
              incomeData.statusFilter,
              incomeData.idFilter,
              incomeData.pickFrom,
              incomeData.pickTo,
              incomeData.search,
              incomeData.gateFilter,
              order,
              sort,
              sortByField,
              params.void,
              incomeData.dateFilter,
            );
          }
          this.getSearchCraneData(
            inputData,
            incomeData,
            craneRequestList,
            [],
            +params.pageSize,
            0,
            0,
            memberDetails,
            async (checkResponse, checkError) => {
              if (!checkError) {
                craneRequestList = checkResponse;
                this.getSearchDeliveryData(
                  inputData,
                  incomeData,
                  deliveryRequest,
                  [],
                  +params.pageSize,
                  0,
                  0,
                  memberDetails,
                  async (checkResponse1, checkError1) => {
                    if (!checkError1) {
                      deliveryRequest = checkResponse1;
                      craneRequestList.push(...deliveryRequest);
                      this.getLimitData(
                        craneRequestList,
                        0,
                        +params.pageSize,
                        [],
                        incomeData,
                        inputData.headers.timezoneoffset,
                        async (newResponse, newError) => {
                          if (!newError) {
                            const newResult = { count: 0, rows: [] };
                            if (newResponse) {
                              if (sort === 'ASC') {
                                newResponse.sort(function (a, b) {
                                  return a[sortByField] > b[sortByField]
                                    ? 1
                                    : b[sortByField] > a[sortByField]
                                    ? -1
                                    : 0;
                                });
                              } else {
                                newResponse.sort(function (a, b) {
                                  return b[sortByField] > a[sortByField]
                                    ? 1
                                    : a[sortByField] > b[sortByField]
                                    ? -1
                                    : 0;
                                });
                              }
                            }
                            if (inputData.body.exportType) {
                              newResult.rows = newResponse;
                            } else {
                              newResult.rows = newResponse.slice(offset, offset + +params.pageSize);
                            }
                            newResult.count = craneRequestList.length;
                            done(newResult, false);
                          } else {
                            done(null, { message: 'Something went wrong' });
                          }
                        },
                      );
                    } else {
                      done(null, { message: 'Something went wrong' });
                    }
                  },
                );
              } else {
                done(null, { message: 'Something went wrong' });
              }
            },
          );
        } else {
          done(null, { message: 'Project Id/Member does not exist' });
        }
      }
    } catch (e) {
      done(null, e);
    }
  },
  async getSearchCraneData(
    req,
    incomeData,
    deliveryList,
    result,
    limit,
    index,
    count,
    memberDetails,
    done,
  ) {
    const elementValue = deliveryList[index];
    if (elementValue) {
      const element = JSON.parse(JSON.stringify(elementValue));
      const status = { companyCondition: true, memberCondition: true };
      if (Number(req.params.void) === 1) {
        if (incomeData.companyFilter > 0) {
          const data = element.companyDetails.find((ele) => {
            return ele.Company.companyName === incomeData.companyFilter;
          });
          if (!data) {
            status.companyCondition = false;
          }
        }
      } else if (Number(req.params.void) === 0) {
        if (incomeData.companyFilter > 0) {
          const data = await CraneRequestCompany.findOne({
            where: {
              CraneRequestId: element.id,
              CompanyId: incomeData.companyFilter,
              isDeleted: false,
            },
          });
          if (!data) {
            status.companyCondition = false;
          }
        }
      }
      // if (memberDetails.RoleId === 4 || memberDetails.RoleId === 3) {
      //   const data = await CraneRequestResponsiblePerson.findOne({
      //     where: {
      //       CraneRequestId: element.id,
      //       MemberId: memberDetails.id,
      //       isDeleted: false,
      //       isActive: true,
      //     },
      //   });
      //   if (!data) {
      //     status.memberCondition = false;
      //   }
      // }
      if (status.memberCondition && status.companyCondition) {
        result.push(element);
      }
      if (index < deliveryList.length - 1) {
        this.getSearchCraneData(
          req,
          incomeData,
          deliveryList,
          result,
          limit,
          index + 1,
          count + 1,
          memberDetails,
          (response, err) => {
            if (!err) {
              done(response, false);
            } else {
              done(null, err);
            }
          },
        );
      } else {
        done(result, false);
      }
    } else {
      done(result, false);
    }
  },
  async returnProjectModel() {
    const modelData = await helper.returnProjectModel();
    publicMember = modelData.Member;
    publicUser = modelData.User;
  },
  async getSearchDeliveryData(
    req,
    incomeData,
    deliveryList,
    result,
    limit,
    index,
    count,
    memberDetails,
    done,
  ) {
    const elementValue = deliveryList[index];
    if (elementValue) {
      const element = JSON.parse(JSON.stringify(elementValue));
      const status = { companyCondition: true, memberCondition: true };
      if (Number(req.params.void) === 1) {
        if (incomeData.companyFilter) {
          const data = element.companyDetails.find((ele) => {
            return ele.Company.companyName === incomeData.companyFilter;
          });
          if (!data) {
            status.companyCondition = false;
          }
        }
      } else if (req.params.void === 0) {
        if (incomeData.companyFilter > 0) {
          const data = await DeliverCompany.findOne({
            where: {
              DeliveryId: element.id,
              CompanyId: +incomeData.companyFilter,
              isDeleted: false,
            },
          });
          if (!data) {
            status.companyCondition = false;
          }
        }
      }

      // if (memberDetails.RoleId === 4 || memberDetails.RoleId === 3) {
      //   const data = await DeliveryPerson.findOne({
      //     where: {
      //       DeliveryId: element.id,
      //       MemberId: memberDetails.id,
      //       isDeleted: false,
      //       isActive: true,
      //     },
      //   });
      //   if (!data) {
      //     status.memberCondition = false;
      //   }
      // }
      if (status.memberCondition && status.companyCondition) {
        result.push(element);
      }
      if (index < deliveryList.length - 1) {
        this.getSearchDeliveryData(
          req,
          incomeData,
          deliveryList,
          result,
          limit,
          index + 1,
          count + 1,
          memberDetails,
          (response, err) => {
            if (!err) {
              done(response, false);
            } else {
              done(null, err);
            }
          },
        );
      } else {
        done(result, false);
      }
    } else {
      done(result, false);
    }
  },
  async getLimitData(result, index, limit, finalResult, incomeData, timezoneoffset, done) {
    if (index < limit) {
      finalResult.push(result);
      this.getLimitData(
        result,
        index + 1,
        limit,
        finalResult,
        incomeData,
        timezoneoffset,
        (response, err) => {
          if (!err) {
            done(result, false);
          } else {
            done(null, err);
          }
        },
      );
    } else {
      done(result, false);
    }
  },
  async exportReportForScheduler(req) {
    return new Promise(async (res, rej) => {
      try {
        await this.listCraneRequest(req, async (response, error) => {
          if (!error) {
            if (response.count == 0) {
              return res('No Data Found');
            }
            if (req.body.exportType === 'PDF') {
              const loginUser = req.user;
              await pdfCraneReportService.pdfFormatOfCraneRequest(
                req.params,
                loginUser,
                response.rows,
                req,
                async (pdfFile, err) => {
                  if (!err) {
                    res(pdfFile);
                  } else {
                    rej(err);
                  }
                },
              );
            }
            if (req.body.exportType === 'EXCEL') {
              const workbook = await exportService.createWorkbook();
              let reportWorkbook = await excelCraneReportService.craneReport(
                workbook,
                response.rows,
                req.body.selectedHeaders,
                req.headers.timezoneoffset,
              );
              if (reportWorkbook) {
                reportWorkbook = await reportWorkbook.xlsx.writeBuffer();
                // console.log("******Buffer.isBuffer(reportWorkbook)******", Buffer.isBuffer(reportWorkbook));
                if (Buffer.isBuffer(reportWorkbook)) {
                  await awsConfig.reportUpload(
                    reportWorkbook,
                    req.body.reportName,
                    req.body.exportType === 'EXCEL' ? 'xlsx' : req.body.exportType,
                    async (result, error1) => {
                      if (!error1) {
                        res(result);
                      } else {
                        rej(error1);
                      }
                    },
                  );
                } else {
                  res('No data found');
                }
              } else {
                res('No data found');
              }
            }
            if (req.body.exportType === 'CSV') {
              await csvCraneReportService.exportCraneReportInCsvFormat(
                response.rows,
                req.body.selectedHeaders,
                req.headers.timezoneoffset,
                req.body.reportName,
                req.body.exportType,
                async (csvFile, err) => {
                  if (!err) {
                    res(csvFile);
                  } else {
                    rej(err);
                  }
                },
              );
            }
          } else {
            rej(error);
          }
        });
      } catch (e) {
        rej(e);
      }
    });
  },
  async exportReport(req, done) {
    await this.listCraneRequest(req, async (response, error) => {
      if (!error) {
        if (req.body.exportType === 'PDF') {
          const loginUser = req.user;
          await pdfCraneReportService.pdfFormatOfCraneRequest(
            req.params,
            loginUser,
            response.rows,
            req,
            async (pdfFile, err) => {
              if (!err) {
                if (req.body.saved) {
                  req.body.reportType = 'Crane';
                  const savedData = await deliveryReportService.createSavedReports(req, pdfFile);
                  if (savedData) {
                    return done(pdfFile, false);
                  }
                  done(null, { message: 'cannot create reports' });
                } else {
                  return done(pdfFile, false);
                }
              }
            },
          );
        }
        if (req.body.exportType === 'EXCEL') {
          const workbook = await exportService.createWorkbook();
          let reportWorkbook = await excelCraneReportService.craneReport(
            workbook,
            response.rows,
            req.body.selectedHeaders,
            req.headers.timezoneoffset,
          );
          if (reportWorkbook) {
            // return done(reportWorkbook, false);
            if (req.body.saved) {
              reportWorkbook = await reportWorkbook.xlsx.writeBuffer();
              const excelFile = await deliveryReportService.saveExcelReport(
                reportWorkbook,
                req.body.reportName,
                req.body.exportType,
              );
              if (excelFile) {
                req.body.reportType = 'Crane';
                const savedData = await deliveryReportService.createSavedReports(req, excelFile);
                return done(excelFile, false);
              }
              done(null, { message: 'cannot create reports' });
            } else {
              return done(reportWorkbook, false);
            }
          }
          done(null, { message: 'cannot export document' });
        }
        if (req.body.exportType === 'CSV') {
          await csvCraneReportService.exportCraneReportInCsvFormat(
            response.rows,
            req.body.selectedHeaders,
            req.headers.timezoneoffset,
            req.body.reportName,
            req.body.exportType,
            async (csvFile, err) => {
              if (!err) {
                if (req.body.saved) {
                  req.body.reportType = 'Crane';
                  const savedData = await deliveryReportService.createSavedReports(req, csvFile);
                  if (savedData) {
                    return done(csvFile, false);
                  }
                  done(null, { message: 'cannot create reports' });
                } else {
                  return done(csvFile, false);
                }
              }
              return done(null, { message: 'cannot export document' });
            },
          );
        }
      }
    });
  },
};
module.exports = craneReportService;
