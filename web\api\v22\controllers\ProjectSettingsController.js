const status = require('http-status');
const { projectSettingsService } = require('../services');
const { Project, ProjectSettings } = require('../models');
const axios = require('axios');

const ProjectSettingsController = {
  /**
   *
   * @param {project settings list} res
   * @param {return if any exception} next
   */
  async getProjectSettings(req, res, next) {
    try {
      const { ProjectId } = req.query;
      const projectSettings = await projectSettingsService.getProjectSettings(ProjectId);
      res.status(status.OK).json({
        status: 200,
        message: 'Project Settings listed Successfully',
        data: projectSettings,
      });
    } catch (e) {
      next(e);
    }
  },
  /**
   *
   * @param {project settings list} res
   * @param {return if any exception} next
   */
  async updateProjectSettings(req, res, next) {
    try {
      const projectSettings = await projectSettingsService.updateProjectSettings(req.body);
      if (projectSettings) {
        res.status(status.OK).json({
          status: 200,
          message: 'Project Settings Updated Successfully!',
        });
      } else {
        res.status(status.INTERNAL_SERVER_ERROR).json({
          status: 400,
          message: 'Cannot update project settings',
        });
      }
    } catch (e) {
      next(e);
    }
  },
  /**
   *
   * @param {success} res
   * @param {return if any exception} next
   */
  async setDefaultDeliveryWindow(req, res, next) {
    try {
      const project = await Project.findAll();
      const createObject = {
        deliveryWindowTime: 0,
        deliveryWindowTimeUnit: 'minutes',
      };
      for (let i = 0; i < project.length; i += 1) {
        createObject.ProjectId = project[i].id;
        await ProjectSettings.create(createObject);
      }
      res.status(status.OK).json({
        status: 200,
        message: 'success',
      });
    } catch (e) {
      next(e);
    }
  },

  async proxyDownloadFile(req, res, next) {
    const fileUrl = req.query.fileUrl;  // Get the S3 URL from the frontend
    if (!fileUrl) {
      return res.status(400).send('No file URL provided');
    }
    try {
      // Fetch the file from S3 using the URL
      const response = await axios.get(fileUrl, { responseType: 'stream' });
      // Set the appropriate headers to trigger the download in the browser
      const filename = fileUrl.split('/').pop();  // Extract filename from URL
      res.setHeader('Content-Disposition', `attachment; filename=${filename}`);
      // Pipe the file stream to the response
      response.data.pipe(res);
    } catch (error) {
      console.error('Error fetching file:', error);
      res.status(500).send('Error downloading file.');
    }
  },

  async mapGateAndEquipmentInLocation(req, res, next) {
    try {
      const response = await projectSettingsService.mapGatesAndEquipmentToLocation();
      if (!response) {
        res.status(status.INTERNAL_SERVER_ERROR).json({
          status: 400,
          message: 'Cannot map gate and equipment',
        });
      } else {
        res.status(status.OK).json({
          status: 200,
          message: 'Gate and Equipment mapped Successfully',
        });
      }
    } catch (e) {
      next(e);
    }
  }
};
module.exports = ProjectSettingsController;
