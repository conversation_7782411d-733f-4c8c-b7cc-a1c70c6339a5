const moment = require('moment');
const Moment = require('moment');
const status = require('http-status');
const MomentRange = require('moment-range');
const ApiError = require('../helpers/apiError');

const momentRange = MomentRange.extendMoment(Moment);
const {
  Company,
  User,
  Sequelize,
  Member,
  Role,
  DeliveryRequest,
  VoidList,
  DeliverCompany,
  DeliverGate,
  CalendarSetting,
  DeliveryPerson,
  CraneRequest,
  CraneRequestResponsiblePerson,
  ConcreteRequest,
  TimeZone,
} = require('../models');
const {
  CraneRequestDefinableFeatureOfWork,
  CraneRequestCompany,
  CraneRequestEquipment,
  CraneRequestHistory,
} = require('../models');
const {
  Gates,
  Locations,
  Equipments,
  DeliverDefineWork,
  Project,
  DeliverEquipment,
  DeliverDefine,
  DeliverHistory,
  Notification,
  LocationNotificationPreferences,
  NotificationPreference,
  DeliveryPersonNotification,
  DeliverAttachement,
  DeliverComment,
  CraneRequestAttachment,
  CraneRequestComment,
  ConcreteRequestAttachment,
  ConcreteRequestComment,
} = require('../models');
const { ConcreteRequestResponsiblePerson } = require('../models');
const {
  ConcreteRequestHistory,
  ConcreteRequestCompany,
  ConcreteLocation,
  ConcreteMixDesign,
  ConcretePumpSize,
  ConcreteRequestLocation,
  ConcreteRequestMixDesign,
  ConcreteRequestPumpSize,
  RequestRecurrenceSeries,
} = require('../models');
const notificationHelper = require('../helpers/notificationHelper');
const pushNotification = require('../config/fcm');
const concreteRequestService = require('./concreteRequestService');
const MAILER = require('../mailer');
const deliveryService = require('./deliveryService');
const craneRequestService = require('./craneRequestService');
const commentService = require('./commentService');
const awsConfig = require('../middlewares/awsConfig');
const craneRequestCommentService = require('./craneRequestCommentService');
const concreteRequestCommentService = require('./concreteRequestCommentService');

const { Op } = Sequelize;
const guestUserService = {
  async getCompanies(req) {
    const { ProjectId } = req.params;
    const { ParentCompanyId } = req.params;
    const companyList = await Company.findAll({
      where: {
        isDeleted: false,
        [Op.or]: [
          { ParentCompanyId, ProjectId, isParent: { [Op.not]: true } },
          { ParentCompanyId, isParent: true },
        ],
      },
      attributes: ['id', 'companyName', 'ParentCompanyId', 'isParent'],
    });
    return { companyList };
  },
  async createGuestUser(req) {
    const userData = req.body;
    const existUser = await User.findOne({
      where: {
        isDeleted: false,
        email: userData.email,
      },
    });
    if (existUser) {
      const condition = {
        isDeleted: false,
        UserId: existUser.id,
        ProjectId: userData.ProjectId,
      };
      const condition1 = await Member.findOne({
        where: {
          ...condition,
          isActive: true,
          isGuestUser: false,
        },
      });
      if (condition1) {
        // error
        return { activeMember: 'You are already active Member in this project!' };
      }
      const condition2 = await Member.findOne({
        where: {
          isDeleted: false,
          UserId: existUser.id,
          ProjectId: userData.ProjectId,
          isActive: true,
          isGuestUser: true,
          ParentCompanyId: userData.ParentCompanyId,
        },
      });
      if (condition2) {
        const userUpdateData = {
          phoneCode: userData.phoneCode,
          phoneNumber: userData.phoneNumber,
          firstName: userData.firstName,
          lastName: userData.lastName,
          // isGuestUser:true,
          // isActive:true,
        };
        await User.update(userUpdateData, { where: { id: +existUser.id, email: existUser.email } });
        const memberUpdateData = {
          phoneCode: userData.phoneCode,
          phoneNumber: userData.phoneNumber,
          firstName: userData.firstName,
          CompanyId: userData.companyId,
          // isGuestUser:true,
          // isActive:true,
        };
        await Member.update(memberUpdateData, {
          where: { UserId: +existUser.id, ProjectId: +userData.ProjectId },
        });
        return { existUser };
      }
      // create
      const newUser = await this.createGuestMember(userData, existUser);
      if (newUser) {
        return { existUser };
      }

      const condition3 = await Member.findOne({
        where: {
          isDeleted: false,
          UserId: existUser.id,
          ProjectId: userData.ProjectId,
          isActive: false,
          isGuestUser: false,
        },
      });
      if (condition3) {
        // update
        const updateData = {
          phoneCode: userData.phoneCode,
          phoneNumber: userData.phoneNumber,
          firstName: userData.firstName,
          CompanyId: userData.companyId,
          isGuestUser: true,
          isActive: true,
          status: 'pending',
        };
        await Member.update(updateData, {
          where: { UserId: +existUser.id, ProjectId: userData.ProjectId },
        });
        return { existUser };
      }
      const condition4 = await Member.findOne({
        where: {
          isDeleted: true,
          UserId: existUser.id,
          ProjectId: userData.ProjectId,
          isGuestUser: false,
        },
      });
      if (condition4) {
        // create
        const newUser = await this.createGuestMember(userData, existUser);
        if (newUser) {
          return { existUser };
        }
      }
    } else {
      const newUser = await this.createNewGuestUser(userData);
      if (newUser) {
        return { newUser };
      }
    }
  },
  async alreadyVisited(req) {
    const { email, ProjectId } = req.body;

    const checkIsActive = await User.findOne({
      where: {
        isDeleted: false,
        email,
      },
      include: [
        {
          association: 'Members',
          where: {
            ProjectId,
            isActive: true,
            isDeleted: false,
            isGuestUser: false,
          },
        },
      ],
    });
    if (checkIsActive) {
      return { activeMember: 'You are already a Member in this project!' };
    }
    const oldUser = await User.findOne({
      where: {
        isDeleted: false,
        email,
      },
      include: [
        {
          association: 'Members',
          include: [
            {
              association: 'Company',
              attributes: ['companyName', 'id'],
            },
          ],
          where: {
            ProjectId: req.body.ProjectId,
            isGuestUser: true,
          },
        },
      ],
    });
    const data = oldUser;
    if (oldUser) {
      return { data };
    }
    return { newUser: 'newUser' };
  },
  async guestUserDetail(req) {
    const { email, ProjectId } = req.body;
    const oldUser = await User.findOne({
      where: {
        isDeleted: false,
        email,
      },
    });
    if (oldUser) {
      const memberDetail = await Member.findOne({
        where: {
          isDeleted: false,
          UserId: oldUser.id,
          ProjectId,
          isGuestUser: true,
        },
      });
      const data = memberDetail;
      return { data };
    }
    return {};
  },
  async lastDeliveryId(req) {
    let data;
    const lastData = await DeliveryRequest.findOne({
      where: { ProjectId: req.body.ProjectId, isDeleted: false },
      order: [['DeliveryId', 'DESC']],
    });
    if (lastData) {
      data = lastData.DeliveryId + 1;
    } else {
      data = 1;
    }
    return { DeliveryId: data };
  },
  async listAllMember(req) {
    const { ProjectId } = req.params;
    const { ParentCompanyId } = req.params;

    const data = await Member.findAll({
      include: [
        {
          association: 'User',
          attributes: ['email', 'firstName', 'lastName'],
        },
      ],
      where: { ProjectId, isDeleted: false, isActive: true },
      order: [['id', 'DESC']],
    });
    return data;
  },
  async createNewGuestUser(data) {
    const userData = data;
    const newUser = await User.createInstance(userData);
    if (newUser) {
      userData.isGuestUser = true;
      const createMember = await this.createGuestMember(userData, newUser);
      if (createMember) {
        return newUser;
      }
    }
  },
  async createGuestMember(userData, newUser) {
    const roleDetails = await Role.getBy('Guest User');
    const memberData = {
      UserId: +newUser.id,
      ParentCompanyId: +userData.ParentCompanyId,
      firstName: newUser.firstName,
      phoneNumber: newUser.phoneNumber,
      phoneCode: newUser.phoneCode,
      CompanyId: +userData.companyId,
      ProjectId: +userData.ProjectId,
      status: 'pending',
      isGuestUser: true,
      RoleId: +roleDetails.id,
    };
    const updatedMemberData = await Member.createInstance(memberData);
    if (!updatedMemberData) {
      throw new Error('Guest Member Creation failed!!');
    } else {
      return updatedMemberData;
    }
  },
  async getEventNDR(inputData) {
    try {
      const { timezoneoffset } = inputData.headers;
      const order = 'DESC';
      const { params } = inputData;
      const incomeData = inputData.body;
      let searchCondition = {};
      const memberDetails = await Member.findOne({
        where: {
          UserId: incomeData.id,
          ProjectId: params.ProjectId,
          isDeleted: false,
          isActive: true,
        },
      });
      const startDateTime = moment(incomeData.start, 'YYYY-MM-DD')
        .startOf('day')
        .utcOffset(Number(timezoneoffset), true);
      const endDateTime = moment(incomeData.end, 'YYYY-MM-DD')
        .endOf('day')
        .utcOffset(Number(timezoneoffset), true);
      const condition = {
        ProjectId: params.ProjectId,
        isQueued: false,
        deliveryStart: {
          [Op.between]: [moment(startDateTime), moment(endDateTime)],
        },
      };
      if (memberDetails) {
        const voidDelivery = [];
        const voidList = await VoidList.findAll({
          where: {
            ProjectId: params.ProjectId,
            isDeliveryRequest: true,
            DeliveryRequestId: { [Op.ne]: null },
          },
        });
        voidList.forEach(async (element) => {
          voidDelivery.push(element.DeliveryRequestId);
        });
        if (params.void === '0' || params.void === 0) {
          condition['$DeliveryRequest.id$'] = {
            [Op.and]: [{ [Op.notIn]: voidDelivery }],
          };
        } else {
          condition['$DeliveryRequest.id$'] = {
            [Op.and]: [{ [Op.in]: voidDelivery }],
          };
        }
      }
      if (incomeData.descriptionFilter) {
        condition.description = {
          [Sequelize.Op.iLike]: `%${incomeData.descriptionFilter}%`,
        };
      }
      if (incomeData.pickFrom) {
        condition.cranePickUpLocation = {
          [Sequelize.Op.iLike]: `%${incomeData.pickFrom}%`,
        };
      }
      if (incomeData.pickTo) {
        condition.craneDropOffLocation = {
          [Sequelize.Op.iLike]: `%${incomeData.pickTo}%`,
        };
      }
      if (incomeData.equipmentFilter) {
        condition['$equipmentDetails.Equipment.id$'] = incomeData.equipmentFilter;
      }
      if (incomeData.memberFilter > 0) {
        condition['$memberDetails.Member.id$'] = +incomeData.memberFilter;
      }
      if (incomeData.locationFilter) {
        condition['$location.locationPath$'] = incomeData.locationFilter;
      }
      if (incomeData.statusFilter) {
        condition.status = incomeData.statusFilter;
      }
      if (incomeData.search) {
        const searchDefault = [
          {
            '$approverDetails.User.firstName$': {
              [Sequelize.Op.iLike]: `%${incomeData.search}%`,
            },
          },
          {
            '$equipmentDetails.Equipment.equipmentName$': {
              [Sequelize.Op.iLike]: `%${incomeData.search}%`,
            },
          },
          {
            description: {
              [Sequelize.Op.iLike]: `%${incomeData.search}%`,
            },
          },
          {
            cranePickUpLocation: {
              [Sequelize.Op.iLike]: `%${incomeData.search}%`,
            },
          },
          {
            craneDropOffLocation: {
              [Sequelize.Op.iLike]: `%${incomeData.search}%`,
            },
          },
          {
            '$location.locationPath$': {
              [Sequelize.Op.iLike]: `%${incomeData.search}%`,
            },
          },
        ];
        if (!Number.isNaN(+incomeData.search)) {
          searchCondition = {
            [Op.and]: [
              {
                [Op.or]: [
                  searchDefault,
                  {
                    [Op.and]: [
                      {
                        DeliveryId: +incomeData.search,
                        isDeleted: false,
                        ProjectId: +params.ProjectId,
                      },
                    ],
                  },
                ],
              },
            ],
          };
        } else {
          searchCondition = {
            [Op.and]: [
              {
                [Op.or]: searchDefault,
              },
            ],
          };
        }
      }
      const roleId = memberDetails.RoleId;
      const memberId = memberDetails.id;
      const deliveryList = await DeliveryRequest.getCalendarData(
        condition,
        roleId,
        memberId,
        searchCondition,
        order,
      );
      const result = { count: 0, rows: [] };
      if (deliveryList && deliveryList.rows.length > 0) {
        if (
          (incomeData.companyFilter && incomeData.companyFilter > 0) ||
          (incomeData.gateFilter && incomeData.gateFilter > 0) ||
          (incomeData.memberFilter && incomeData.memberFilter > 0) ||
          incomeData.dateFilter
        ) {
          this.getSearchData(
            incomeData,
            deliveryList.rows,
            0,
            [],
            memberDetails,
            timezoneoffset,
            async (checkResponse, checkError) => {
              if (!checkError) {
                result.rows = checkResponse;
                result.count = checkResponse.length;
                done(result, false);
              }
            },
          );
        } else {
          return deliveryList;
        }
      } else {
        return deliveryList;
      }
    } catch (e) {
      throw new Error(e);
    }
  },
  async getSearchData(
    incomeData,
    deliveryList,
    index,
    result,
    memberDetails,
    timezoneoffset,
    done,
  ) {
    if (deliveryList.length > 0) {
      const elementValue = deliveryList[index];
      const element = JSON.parse(JSON.stringify(elementValue));
      const status = { companyCondition: true, gateCondition: true, memberCondition: true };
      if (incomeData.companyFilter > 0) {
        const data = await DeliverCompany.findOne({
          where: {
            DeliveryId: element.id,
            CompanyId: +incomeData.companyFilter,
            isDeleted: false,
          },
        });
        if (!data) {
          status.companyCondition = false;
        }
      }

      if (incomeData.gateFilter > 0) {
        const data = await DeliverGate.findOne({
          where: {
            DeliveryId: element.id,
            GateId: +incomeData.gateFilter,
            isDeleted: false,
          },
        });
        if (!data) {
          status.gateCondition = false;
        }
      }
      // if (incomeData.memberFilter > 0) {
      //   const data = await DeliveryPerson.findOne({
      //     where: {
      //       DeliveryId: element.id,
      //       MemberId: +incomeData.memberFilter,
      //       isDeleted: false,
      //     },
      //   });
      //   if (!data) {
      //     status.memberCondition = false;
      //   }
      // }
      if (status.companyCondition && status.gateCondition && status.memberCondition) {
        if (incomeData.dateFilter) {
          const startDateTime = moment(incomeData.dateFilter, 'YYYY-MM-DD')
            .startOf('day')
            .utcOffset(Number(timezoneoffset), true);
          const endDateTime = moment(incomeData.dateFilter, 'YYYY-MM-DD')
            .endOf('day')
            .utcOffset(Number(timezoneoffset), true);
          if (
            element &&
            moment(element.deliveryStart)
              .utcOffset(Number(timezoneoffset))
              .isBetween(startDateTime, endDateTime, undefined, '()')
          ) {
            result.push(element);
          }
        } else {
          result.push(element);
        }
      }
      if (index < deliveryList.length - 1) {
        this.getSearchData(
          incomeData,
          deliveryList,
          index + 1,
          result,
          memberDetails,
          timezoneoffset,
          (response, err) => {
            if (!err) {
              done(response, false);
            } else {
              done(null, err);
            }
          },
        );
      } else {
        done(result, false);
      }
    } else {
      done(deliveryList, false);
    }
  },
  async getAll(req, next) {
    try {
      const { ProjectId } = req.query;
      const { ParentCompanyId } = req.query;
      const { search } = req.query;
      const { isApplicableToDelivery } = req.body;
      const { isApplicableToCrane } = req.body;
      const { isApplicableToConcrete } = req.body;
      const loginUser = req.body;
      const isMemberExists = await Member.findOne({
        where: Sequelize.and({
          UserId: loginUser.id,
          ProjectId,
          isDeleted: false,
          ParentCompanyId,
        }),
      });
      if (isMemberExists) {
        const condition = {
          ProjectId,
          isDeleted: false,
          description: '',
          isApplicableToDelivery: '',
          isApplicableToCrane: '',
          isApplicableToConcrete: '',
        };
        if (search) {
          condition.description = {
            [Op.iLike]: `%${search}%`,
          };
        } else {
          delete condition.description;
        }
        if (isApplicableToDelivery) {
          condition.isApplicableToDelivery = true;
        } else {
          delete condition.isApplicableToDelivery;
        }
        if (isApplicableToCrane) {
          condition.isApplicableToCrane = true;
        } else {
          delete condition.isApplicableToCrane;
        }
        if (isApplicableToConcrete) {
          condition.isApplicableToConcrete = true;
        } else {
          delete condition.isApplicableToConcrete;
        }
        let events;
        if (req.query.weeklyReportTest === 'weeklyReport') {
          const newCondition = {
            ProjectId: +req.query.ProjectId,
            isDeleted: false,
          };
          events = await CalendarSetting.getAllWeeklyReport(newCondition);
        } else {
          events = await CalendarSetting.getAll(condition);
        }
        if (events.length > 0) {
          const eventsArray = [];
          let uniqueNumber = 0;
          for (let indexval = 0; indexval < events.length; indexval += 1) {
            const eventObject = events[indexval];
            const eventTimeZone = await TimeZone.findOne({
              where: {
                isDeleted: false,
                id: +eventObject.TimeZoneId,
              },
              attributes: [
                'id',
                'location',
                'isDayLightSavingEnabled',
                'timeZoneOffsetInMinutes',
                'dayLightSavingTimeInMinutes',
                'timezone',
              ],
            });
            if (eventObject) {
              const range = momentRange.range(
                moment(eventObject.fromDate),
                moment(eventObject.toDate),
              );
              let totalDays = Array.from(range.by('day'));
              if (eventObject.recurrence === 'Does Not Repeat') {
                const startDate = totalDays[0];
                const endDate = totalDays[totalDays.length - 1];
                totalDays.forEach(async (data) => {
                  uniqueNumber += 1;
                  const recurrenceObject = await this.createRecurrenceObject(
                    eventObject,
                    eventTimeZone,
                    data.toDate(),
                    startDate,
                    endDate,
                    uniqueNumber,
                    eventObject.recurrence,
                  );
                  eventsArray.push(recurrenceObject);
                });
              }
              if (eventObject.recurrence === 'Daily') {
                let dailyIndex = 0;
                const startDate = totalDays[0];
                const endDate = totalDays[totalDays.length - 1];
                while (dailyIndex < totalDays.length) {
                  const data = totalDays[dailyIndex];
                  uniqueNumber += 1;
                  const recurrenceObject = await this.createRecurrenceObject(
                    eventObject,
                    eventTimeZone,
                    data.toDate(),
                    startDate,
                    endDate,
                    uniqueNumber,
                    eventObject.recurrence,
                  );
                  eventsArray.push(recurrenceObject);
                  dailyIndex += +eventObject.repeatEveryCount;
                }
              }
              if (eventObject.recurrence === 'Weekly') {
                const startDayWeek = moment(eventObject.fromDate).startOf('week');
                const endDayWeek = moment(eventObject.endDate).endOf('week');
                const range1 = momentRange.range(moment(startDayWeek), moment(endDayWeek));
                const totalDaysOfRecurrence = Array.from(range1.by('day'));
                totalDays = totalDaysOfRecurrence;
                const startDate = moment(eventObject.fromDate);
                const endDate = moment(eventObject.endDate);
                let count;
                let weekIncrement;
                if (+eventObject.repeatEveryCount > 1) {
                  count = +eventObject.repeatEveryCount - 1;
                  weekIncrement = 7;
                } else {
                  count = 1;
                  weekIncrement = 0;
                }
                for (
                  let indexba = 0;
                  indexba < totalDays.length;
                  indexba += weekIncrement * count
                ) {
                  const totalLength = indexba + 6;
                  for (let indexb = indexba; indexb <= totalLength; indexb += 1) {
                    const data = totalDays[indexb];
                    indexba += 1;
                    if (
                      data &&
                      !moment(data).isBefore(eventObject.fromDate) &&
                      !moment(data).isAfter(eventObject.endDate)
                    ) {
                      const day = moment(data).format('dddd');
                      const indexVal = eventObject.days.includes(day);
                      if (indexVal) {
                        uniqueNumber += 1;
                        const recurrenceObject = await this.createRecurrenceObject(
                          eventObject,
                          eventTimeZone,
                          data.toDate(),
                          startDate,
                          endDate,
                          uniqueNumber,
                          eventObject.recurrence,
                        );
                        eventsArray.push(recurrenceObject);
                      }
                    }
                  }
                }
              }
              if (eventObject.recurrence === 'Monthly' || eventObject.recurrence === 'Yearly') {
                const startMonth = moment(eventObject.fromDate).startOf('month');
                const startMonthNumber = moment(startMonth).format('MM');
                const endMonth = moment(eventObject.endDate).endOf('month');
                const endMonthNumber = moment(endMonth).format('MM');
                let startDate = moment(eventObject.fromDate, 'YYYY-MM-DD');
                const endDate = moment(eventObject.endDate, 'YYYY-MM-DD').endOf('month');
                const allMonthsInPeriod = [];
                while (startDate.isBefore(endDate)) {
                  allMonthsInPeriod.push(startDate.format('YYYY-MM'));
                  startDate =
                    eventObject.recurrence === 'Monthly'
                      ? startDate.add(1, 'month')
                      : startDate.add(12, 'month');
                }
                let currentMonthDates = [];
                let totalNumberOfMonths = endMonthNumber - startMonthNumber;
                if (totalNumberOfMonths < 0) {
                  totalNumberOfMonths *= -1;
                }
                let k = 0;
                while (k < allMonthsInPeriod.length + 1) {
                  currentMonthDates = Array.from(
                    { length: moment(allMonthsInPeriod[k], 'YYYY-MM').daysInMonth() },
                    (x, j) =>
                      moment(allMonthsInPeriod[k], 'YYYY-MM').startOf('month').add(j, 'days'),
                  );
                  if (eventObject.chosenDateOfMonth) {
                    const getDate = currentMonthDates.filter(
                      (value) => moment(value).format('DD') === eventObject.dateOfMonth,
                    );
                    if (getDate.length === 1) {
                      if (
                        moment(getDate[0]).isBetween(
                          moment(eventObject.fromDate),
                          moment(eventObject.endDate),
                          null,
                          '[]',
                        ) ||
                        moment(getDate[0]).isSame(eventObject.fromDate) ||
                        moment(getDate[0]).isSame(eventObject.endDate)
                      ) {
                        const recurrenceObject = await this.createRecurrenceObject(
                          eventObject,
                          eventTimeZone,
                          getDate[0].toDate(),
                          startDate,
                          eventObject.endDate,
                          uniqueNumber,
                          eventObject.recurrence,
                        );
                        eventsArray.push(recurrenceObject);
                      }
                    }
                  } else if (allMonthsInPeriod[k]) {
                    const dayOfMonth = eventObject.monthlyRepeatType;
                    const week = dayOfMonth.split(' ')[0].toLowerCase();
                    const day = dayOfMonth.split(' ')[1].toLowerCase();
                    const chosenDay = moment(allMonthsInPeriod[k], 'YYYY-MM')
                      .startOf('month')
                      .day(day);
                    const getAllDays = [];
                    if (chosenDay.date() > 7) chosenDay.add(7, 'd');
                    const month = chosenDay.month();
                    while (month === chosenDay.month()) {
                      getAllDays.push(chosenDay.toString());
                      chosenDay.add(7, 'd');
                    }
                    let i = 0;
                    if (week === 'second') {
                      i += 1;
                    } else if (week === 'third') {
                      i += 2;
                    } else if (week === 'fourth') {
                      i += 3;
                    } else if (week === 'last') {
                      i = getAllDays.length - 1;
                    }
                    const finalDay = getAllDays[i];
                    if (
                      moment(finalDay).isBetween(
                        moment(eventObject.fromDate),
                        moment(eventObject.endDate),
                        null,
                        '[]',
                      ) ||
                      moment(finalDay).isSame(eventObject.fromDate) ||
                      moment(finalDay).isSame(eventObject.endDate)
                    ) {
                      const recurrenceObject = await this.createRecurrenceObject(
                        eventObject,
                        eventTimeZone,
                        finalDay,
                        startDate,
                        eventObject.endDate,
                        uniqueNumber,
                        eventObject.recurrence,
                      );
                      eventsArray.push(recurrenceObject);
                    }
                  }
                  k =
                    eventObject.recurrence === 'Monthly'
                      ? k + +eventObject.repeatEveryCount
                      : k + 1;
                }
              }
            }
          }
          if (eventsArray.length > 0) {
            const resultantArray = [];
            if (req.query.weeklyReportTest === 'weeklyReport') {
              let startingDate = moment(req.query.start).format('YYYY-MM-DD');
              let endingDate = moment(req.query.end).format('YYYY-MM-DD');
              if (req.body.startDate && req.body.endDate) {
                startingDate = moment(req.body.startDate).format('YYYY-MM-DD');
                endingDate = moment(req.body.endDate).format('YYYY-MM-DD');
              }
              const nextDay = moment(startingDate).add(1, 'days').format('YYYY-MM-DD');
              eventsArray.forEach(async (data) => {
                const target = momenttz
                  .utc(data.fromDate, 'YYYY-MM-DD HH:mm:ssZ')
                  .tz(req.body.timezone);
                const timeRangeCondition = target.format('HH:mm');
                const compare = moment(target.format('YYYY-MM-DD'));
                if (req.body.eventStartTime < req.body.eventEndTime) {
                  if (
                    compare.isBetween(startingDate, endingDate, null, '[]') &&
                    timeRangeCondition <= req.body.eventEndTime &&
                    timeRangeCondition >= req.body.eventStartTime
                  ) {
                    resultantArray.push(data);
                  }
                } else if (req.body.eventStartTime > req.body.eventEndTime) {
                  if (
                    (compare.isBetween(startingDate, endingDate, null, '[]') &&
                      timeRangeCondition >= req.body.eventStartTime &&
                      timeRangeCondition <= '23:59:59') ||
                    (compare.isBetween(nextDay, endingDate, null, '[]') &&
                      timeRangeCondition >= '00:00:00' &&
                      timeRangeCondition <= req.body.eventEndTime)
                  ) {
                    resultantArray.push(data);
                  }
                }
              });
            } else {
              const startingDate = moment(req.query.start).subtract(2, 'days').format('YYYY-MM-DD');
              const endingDate = moment(req.query.end).add(2, 'days').format('YYYY-MM-DD');
              eventsArray.forEach(async (data) => {
                if (
                  moment(moment(data.fromDate).format('YYYY-MM-DD')).isBetween(
                    startingDate,
                    endingDate,
                    null,
                    '[]',
                  )
                ) {
                  resultantArray.push(data);
                }
              });
            }
            return resultantArray;
          }
          return eventsArray;
        }
        return events;
      }
    } catch (e) {
      const newError = new ApiError(e, status.BAD_REQUEST);
      next(newError);
    }
  },
  async convertTimezoneToUtc(date, timezone, time) {
    const chosenTimezoneDeliveryStart = moment.tz(`${date} ${time}`, 'MM/DD/YYYY HH:mm', timezone);
    const utcDate = chosenTimezoneDeliveryStart.clone().tz('UTC').format('YYYY-MM-DD HH:mm:ssZ');
    return utcDate;
  },
  async createRecurrenceObject(
    eventObject,
    eventTimeZone,
    data,
    startDate,
    endDate,
    uniqueNumber,
    recurrence,
  ) {
    const objectToAddEvents = {
      id: eventObject.id,
      description: eventObject.description,
      timeZoneLocation: eventObject.TimeZone.location,
      durationInMinutes: '',
      fromDate: eventObject.isAllDay
        ? await this.convertTimezoneToUtc(
          moment(data).startOf('day').format('MM/DD/YYYY'),
          eventTimeZone.timezone,
          '00:00',
        )
        : await this.convertTimezoneToUtc(
          moment(data).format('MM/DD/YYYY'),
          eventTimeZone.timezone,
          eventObject.startTime,
        ),
      toDate: eventObject.isAllDay
        ? await this.convertTimezoneToUtc(
          moment(data).endOf('day').format('MM/DD/YYYY'),
          eventTimeZone.timezone,
          '23:59',
        )
        : await this.convertTimezoneToUtc(
          moment(data).format('MM/DD/YYYY'),
          eventTimeZone.timezone,
          eventObject.endTime,
        ),
      startTime: await this.convertTimezoneToUtc(
        moment(startDate).format('MM/DD/YYYY'),
        eventTimeZone.timezone,
        eventObject.startTime,
      ),
      endTime: eventObject.isAllDay
        ? await this.convertTimezoneToUtc(
          moment(data).startOf('day').format('MM/DD/YYYY'),
          eventTimeZone.timezone,
          '00:00',
        )
        : await this.convertTimezoneToUtc(
          moment(endDate).format('MM/DD/YYYY'),
          eventTimeZone.timezone,
          eventObject.endTime,
        ),
      repeatEveryType: eventObject.repeatEveryType,
      repeatEveryCount: eventObject.repeatEveryCount,
      days: eventObject.days,
      isAllDay: eventObject.isAllDay,
      uniqueNumber,
      requestType: 'calendarEvent',
    };
    if (recurrence === 'Monthly' || recurrence === 'Yearly') {
      objectToAddEvents.chosenDateOfMonth = eventObject.chosenDateOfMonth;
      objectToAddEvents.dateOfMonth = eventObject.dateOfMonth;
      objectToAddEvents.monthlyRepeatType = eventObject.monthlyRepeatType;
    }
    objectToAddEvents.durationInMinutes = moment(objectToAddEvents.toDate)
      .diff(moment(objectToAddEvents.fromDate), 'minutes')
      .toString();
    return objectToAddEvents;
  },
  async getDeliveryRequestWithCrane(inputData) {
    try {
      const { timezoneoffset } = inputData.headers;
      const order = 'DESC';
      const { params } = inputData;
      const incomeData = inputData.body;
      const { sort } = inputData.body;
      const { sortByField } = inputData.body;
      const memberDetails = await Member.findOne({
        where: Sequelize.and({
          UserId: incomeData.id,
          ProjectId: +params.ProjectId,
          isDeleted: false,
          isActive: true,
        }),
      });
      const startDateTime = moment(incomeData.start, 'YYYY-MM-DD')
        .startOf('day')
        .utcOffset(Number(timezoneoffset), true);
      const endDateTime = moment(incomeData.end, 'YYYY-MM-DD')
        .endOf('day')
        .utcOffset(Number(timezoneoffset), true);
      const DeliveryRequestCondition = {
        ProjectId: +params.ProjectId,
        isQueued: false,
        isAssociatedWithCraneRequest: true,
        deliveryStart: {
          [Op.between]: [moment(startDateTime), moment(endDateTime)],
        },
      };
      const craneDeliveryRequestCondition = {
        ProjectId: +params.ProjectId,
        craneDeliveryStart: {
          [Op.between]: [moment(startDateTime), moment(endDateTime)],
        },
      };
      if (memberDetails) {
        const voidDelivery = [];
        const voidCraneDelivery = [];
        const voidList = await VoidList.findAll({
          where: {
            ProjectId: +params.ProjectId,
            isDeliveryRequest: true,
            DeliveryRequestId: { [Op.ne]: null },
          },
        });
        voidList.forEach(async (element) => {
          voidDelivery.push(element.DeliveryRequestId);
        });
        const voidCraneRequestList = await VoidList.findAll({
          where: {
            ProjectId: +params.ProjectId,
            isDeliveryRequest: false,
            CraneRequestId: { [Op.ne]: null },
          },
        });
        voidCraneRequestList.forEach(async (element) => {
          voidCraneDelivery.push(element.CraneRequestId);
        });
        if (params.void === '0' || params.void === 0) {
          DeliveryRequestCondition['$DeliveryRequest.id$'] = {
            [Op.and]: [{ [Op.notIn]: voidDelivery }],
          };
        } else {
          DeliveryRequestCondition['$DeliveryRequest.id$'] = {
            [Op.and]: [{ [Op.in]: voidDelivery }],
          };
        }
        if (params.void === '0' || params.void === 0) {
          craneDeliveryRequestCondition['$CraneRequest.id$'] = {
            [Op.and]: [{ [Op.notIn]: voidCraneDelivery }],
          };
        } else {
          craneDeliveryRequestCondition['$CraneRequest.id$'] = {
            [Op.and]: [{ [Op.in]: voidCraneDelivery }],
          };
        }
      }
      const roleId = memberDetails.RoleId;
      const memberId = memberDetails.id;
      let craneRequestArray = [];
      let craneRequestList;
      if (
        (incomeData.gateFilter && incomeData.gateFilter > 0) ||
        (incomeData.statusFilter && incomeData.statusFilter === 'Delivered')
      ) {
        craneRequestList = [];
      } else {
        craneRequestList = await CraneRequest.getAll(
          inputData,
          roleId,
          memberId,
          craneDeliveryRequestCondition,
          incomeData.descriptionFilter,
          incomeData.startdate,
          incomeData.enddate,
          incomeData.companyFilter,
          incomeData.memberFilter,
          incomeData.equipmentFilter,
          incomeData.statusFilter,
          incomeData.idFilter,
          incomeData.pickFrom,
          incomeData.pickTo,
          incomeData.search,
          order,
          sort,
          sortByField,
          incomeData.dateFilter,
        );
      }
      if (craneRequestList.length > 0) {
        craneRequestArray = craneRequestList;
      }
      let deliveryList = [];
      if (incomeData.statusFilter && incomeData.statusFilter === 'Completed') {
        deliveryList = [];
      } else {
        deliveryList = await DeliveryRequest.getCraneAssociatedRequest(
          inputData,
          roleId,
          memberId,
          DeliveryRequestCondition,
          incomeData.descriptionFilter,
          incomeData.startdate,
          incomeData.enddate,
          incomeData.companyFilter,
          incomeData.memberFilter,
          incomeData.equipmentFilter,
          incomeData.statusFilter,
          incomeData.idFilter,
          incomeData.pickFrom,
          incomeData.pickTo,
          incomeData.search,
          incomeData.gateFilter,
          order,
          sort,
          sortByField,
          params.void,
          incomeData.dateFilter,
        );
      }
      if (
        (incomeData.companyFilter && incomeData.companyFilter > 0) ||
        (incomeData.gateFilter && incomeData.gateFilter > 0) ||
        (incomeData.memberFilter && incomeData.memberFilter > 0) ||
        incomeData.dateFilter
      ) {
        this.getSearchCraneRequestCalendarData(
          incomeData,
          craneRequestArray,
          0,
          [],
          memberDetails,
          timezoneoffset,
          async (checkResponse, checkError) => {
            craneRequestArray = checkResponse;
            if (!checkError) {
              this.getSearchData(
                incomeData,
                deliveryList,
                0,
                [],
                memberDetails,
                timezoneoffset,
                async (checkResponse1, checkError1) => {
                  if (!checkError1) {
                    if (checkResponse1.length > 0) {
                      craneRequestArray.push(...checkResponse1);
                      return craneRequestArray;
                    }
                    return craneRequestArray;
                  }
                },
              );
            } else {
              return { message: 'Something went wrong' };
            }
          },
        );
      } else {
        if (deliveryList.length > 0) {
          craneRequestArray.push(...deliveryList);
          return craneRequestArray;
        }
        return craneRequestArray;
      }
    } catch (e) {
      throw new Error(e);
    }
  },
  async getSearchCraneRequestCalendarData(
    incomeData,
    deliveryList,
    index,
    result,
    memberDetails,
    timezoneoffset,
    done,
  ) {
    if (deliveryList.length > 0) {
      const elementValue = deliveryList[index];
      const element = JSON.parse(JSON.stringify(elementValue));
      const status = { companyCondition: true, memberCondition: true };
      if (incomeData.companyFilter > 0) {
        const data = await CraneRequestCompany.findOne({
          where: {
            CraneRequestId: element.id,
            CompanyId: +incomeData.companyFilter,
            isDeleted: false,
          },
        });
        if (!data) {
          status.companyCondition = false;
        }
      }
      if (incomeData.memberFilter > 0) {
        const data = await CraneRequestResponsiblePerson.findOne({
          where: {
            CraneRequestId: element.id,
            MemberId: incomeData.memberFilter,
            isDeleted: false,
          },
        });
        if (!data) {
          status.memberCondition = false;
        }
      }
      if (status.companyCondition && status.memberCondition) {
        result.push(element);
      }
      if (index < deliveryList.length - 1) {
        this.getSearchCraneRequestCalendarData(
          incomeData,
          deliveryList,
          index + 1,
          result,
          memberDetails,
          timezoneoffset,
          (response, err) => {
            if (!err) {
              done(response, false);
            } else {
              done(null, err);
            }
          },
        );
      } else {
        done(result, false);
      }
    } else {
      done(deliveryList, false);
    }
  },
  async getConcreteRequest(inputData, done) {
    try {
      const { timezoneoffset } = inputData.body;
      const { params } = inputData;
      const incomeData = inputData.body;
      let order;
      const startDateTime = moment(incomeData.start, 'YYYY-MM-DD')
        .startOf('day')
        .utcOffset(Number(timezoneoffset), true);
      const endDateTime = moment(incomeData.end, 'YYYY-MM-DD')
        .endOf('day')
        .utcOffset(Number(timezoneoffset), true);
      if (params.void >= 1 && params.void <= 0) {
        done(null, { message: 'Please enter void as 1 or 0' });
      } else {
        const memberDetails = await Member.findOne({
          where: Sequelize.and({
            UserId: incomeData.id,
            ProjectId: params.ProjectId,
            isDeleted: false,
            isActive: true,
          }),
        });
        if (memberDetails) {
          const voidConcreteDelivery = [];
          const voidConcreteRequestList = await VoidList.findAll({
            where: {
              ProjectId: params.ProjectId,
              isDeliveryRequest: false,
              ConcreteRequestId: { [Op.ne]: null },
            },
          });
          voidConcreteRequestList.forEach(async (element) => {
            voidConcreteDelivery.push(element.ConcreteRequestId);
          });
          const concreteCondition = {
            ProjectId: +params.ProjectId,
            isDeleted: false,
            concretePlacementStart: {
              [Op.between]: [moment(startDateTime), moment(endDateTime)],
            },
          };
          if (params.void === '0' || params.void === 0) {
            concreteCondition['$ConcreteRequest.id$'] = {
              [Op.and]: [{ [Op.notIn]: voidConcreteDelivery }],
            };
          } else {
            concreteCondition['$ConcreteRequest.id$'] = {
              [Op.and]: [{ [Op.in]: voidConcreteDelivery }],
            };
          }
          const getConcreteRequest = await ConcreteRequest.getAll(
            inputData,
            concreteCondition,
            '',
            '',
            incomeData.descriptionFilter,
            incomeData.locationFilter,
            incomeData.concreteSupplierFilter,
            incomeData.orderNumberFilter,
            incomeData.statusFilter,
            incomeData.mixDesignFilter,
            '',
            '',
            incomeData.memberFilter,
            incomeData.search,
            order,
            incomeData.sort,
            incomeData.sortByField,
          );
          done(getConcreteRequest, false);
        } else {
          done(null, { message: 'Project Id/Member does not exist' });
        }
      }
    } catch (e) {
      done(null, e);
    }
  },
  async listGates(inputData) {
    try {
      // await this.getDynamicModel(inputData);
      const { params } = inputData;
      const incomeData = inputData.body;
      const { sort } = inputData.body;
      const { sortByField } = inputData.body;
      const pageNumber = +params.pageNo;
      const pageSize = +params.pageSize;
      const offset = (pageNumber - 1) * pageSize;
      const condition = {
        ProjectId: params.ProjectId,
        isDeleted: false,
      };
      if (inputData.body.showActivatedAlone) {
        condition.isActive = true;
      }
      let searchCondition = {};
      if (incomeData.search) {
        const searchDefault = [
          {
            gateName: {
              [Sequelize.Op.iLike]: `%${incomeData.search}%`,
            },
          },
        ];
        if (!Number.isNaN(+incomeData.search)) {
          searchCondition = {
            [Op.and]: [
              {
                [Op.or]: [
                  searchDefault,
                  {
                    [Op.and]: [
                      {
                        gateAutoId: incomeData.search,
                        isDeleted: false,
                        ProjectId: params.ProjectId,
                      },
                    ],
                  },
                ],
              },
            ],
          };
        } else {
          searchCondition = {
            [Op.and]: [
              {
                [Op.or]: searchDefault,
              },
            ],
          };
        }
      }
      const gateData = await Gates.getAll(
        condition,
        pageSize,
        offset,
        searchCondition,
        sort,
        sortByField,
      );
      if (incomeData.isFilter) {
        if (gateData.rows) {
          if (gateData.rows.length > 0) {
            gateData.rows.sort((a, b) =>
              a.gateName.toLowerCase() > b.gateName.toLowerCase() ? 1 : -1,
            );
          }
        } else if (gateData.length > 0) {
          gateData.sort((a, b) => (a.gateName.toLowerCase() > b.gateName.toLowerCase() ? 1 : -1));
        }
      }

      // done(gateData, false);
      return gateData;
    } catch (e) {
      throw new Error(e);
    }
  },
  async lastGate(inputData) {
    try {
      let data;
      const lastData = await Gates.findOne({
        where: { ProjectId: inputData.params.ProjectId, isDeleted: false },
        order: [['gateAutoId', 'DESC']],
      });
      if (lastData) {
        data = lastData.gateAutoId + 1;
      } else {
        data = 1;
      }
      // done({ id: data }, false);
      return { id: data };
    } catch (e) {
      throw new Error(e);
    }
  },
  async listEquipment(inputData) {
    try {
      // await this.getDynamicModel(inputData);
      const { params } = inputData;
      const incomeData = inputData.body;
      let searchCondition = {};
      const pageNumber = +params.pageNo;
      const pageSize = +params.pageSize;
      const { sort } = inputData.body;
      const { sortByField } = inputData.body;
      const offset = (pageNumber - 1) * pageSize;
      const condition = {
        ProjectId: inputData.params.ProjectId,
        isDeleted: false,
      };
      if (inputData.body.showActivatedAlone) {
        condition.isActive = true;
      }
      if (incomeData.nameFilter) {
        condition.equipmentName = {
          [Sequelize.Op.iLike]: `%${incomeData.nameFilter}%`,
        };
      }
      if (incomeData.companyNameFilter) {
        condition['$controllUserDetails.Company.companyName$'] = {
          [Sequelize.Op.iLike]: `%${incomeData.companyNameFilter}%`,
        };
      }

      if (incomeData.idFilter) {
        condition.equipmentAutoId = incomeData.idFilter;
        condition.isDeleted = false;
        condition.ProjectId = inputData.params.ProjectId;
      }
      if (incomeData.memberFilter) {
        condition['$controllUserDetails.id$'] = incomeData.memberFilter;
      }
      if (incomeData.typeFilter) {
        condition['$PresetEquipmentType.equipmentType$'] = incomeData.typeFilter;
      }
      if (incomeData.search) {
        const searchDefault = [
          {
            equipmentName: {
              [Sequelize.Op.iLike]: `%${incomeData.search}%`,
            },
          },
          {
            '$controllUserDetails.User.email$': {
              [Sequelize.Op.iLike]: `%${incomeData.search}%`,
            },
          },
          {
            '$controllUserDetails.Company.companyName$': {
              [Sequelize.Op.iLike]: `%${incomeData.search}%`,
            },
          },
          {
            '$PresetEquipmentType.equipmentType$': {
              [Sequelize.Op.iLike]: `%${incomeData.search}%`,
            },
          },
          {
            '$controllUserDetails.User.firstName$': {
              [Sequelize.Op.iLike]: `%${incomeData.search}%`,
            },
          },
        ];
        if (!Number.isNaN(+incomeData.search)) {
          searchCondition = {
            [Op.and]: [
              {
                [Op.or]: [
                  searchDefault,
                  {
                    [Op.and]: [
                      {
                        equipmentAutoId: incomeData.search,
                        isDeleted: false,
                        ProjectId: inputData.params.ProjectId,
                      },
                    ],
                  },
                ],
              },
            ],
          };
        } else {
          searchCondition = {
            [Op.and]: [
              {
                [Op.or]: searchDefault,
              },
            ],
          };
        }
      }
      const equipmentData = await Equipments.getAll(
        condition,
        pageSize,
        offset,
        searchCondition,
        sort,
        sortByField,
      );
      if (incomeData && incomeData.isFilter) {
        if (equipmentData.rows) {
          if (equipmentData.rows.length > 0) {
            equipmentData.rows.sort((a, b) =>
              a.equipmentName.toLowerCase() > b.equipmentName.toLowerCase() ? 1 : -1,
            );
          }
        } else if (equipmentData.length > 0) {
          equipmentData.sort((a, b) =>
            a.equipmentName.toLowerCase() > b.equipmentName.toLowerCase() ? 1 : -1,
          );
        }
      }
      return equipmentData;
    } catch (e) {
      throw new Error(e);
    }
  },

  async lastEquipment(inputData) {
    try {
      let data;
      const lastData = await Equipments.findOne({
        where: { ProjectId: inputData.params.ProjectId, isDeleted: false },
        order: [['equipmentAutoId', 'DESC']],
      });
      if (lastData) {
        data = lastData.equipmentAutoId + 1;
      } else {
        data = 1;
      }
      return { id: data };
    } catch (e) {
      throw new Error(e);
    }
  },
  async getAllCompany(inputData) {
    try {
      // await this.getDynamicModel(inputData);
      const { params } = inputData;
      const parentCompany = await Company.findOne({
        required: false,
        subQuery: false,
        attributes: [
          'id',
          'companyName',
          'website',
          'address',
          'secondAddress',
          'country',
          'city',
          'companyAutoId',
          'state',
          'zipCode',
          'scope',
          'logo',
        ],
        where: { isParent: true, ParentCompanyId: +params.ParentCompanyId, isDeleted: false },
      });
      const companyList = await Company.getAllCompany({
        ProjectId: params.ProjectId,
        isDeleted: false,
        isParent: { [Op.not]: true },
      });
      return { companyList, parentCompany };
    } catch (e) {
      throw new Error(e);
    }
  },
  async getDefinableWork(inputData) {
    try {
      // await this.getDynamicModel(inputData);
      const { params } = inputData;
      const defineRecord = await DeliverDefineWork.findAll({
        where: Sequelize.and({ ProjectId: params.ProjectId, isDeleted: false }),
      });
      defineRecord.sort((a, b) => (a.DFOW.toLowerCase() > b.DFOW.toLowerCase() ? 1 : -1));
      return { defineRecord };
    } catch (e) {
      throw new Error(e);
    }
  },
  async getLocations(req) {
    try {
      const { query } = req;
      const ProjectId = +query.ProjectId;
      const ParentCompanyId = +query.ParentCompanyId;
      const condition = {
        ProjectId,
        ParentCompanyId,
        isDeleted: false,
        isActive: true,
      };
      const locations = await Locations.getLocations(condition);
      return locations;
    } catch (e) {
      throw new Error(e);
    }
  },
  async lastCraneRequest(inputData) {
    try {
      const { params } = inputData;
      let data;
      let data2;
      let lastData = {};
      lastData = await CraneRequest.findOne({
        where: { ProjectId: params.ProjectId, isDeleted: false },
        order: [['CraneRequestId', 'DESC']],
      });
      const deliveryRequestList = await DeliveryRequest.findOne({
        where: {
          ProjectId: params.ProjectId,
          isDeleted: false,
          isAssociatedWithCraneRequest: true,
        },
        order: [['CraneRequestId', 'DESC']],
      });
      if (deliveryRequestList) {
        if (lastData) {
          if (deliveryRequestList.CraneRequestId > lastData.CraneRequestId) {
            lastData.CraneRequestId = deliveryRequestList.CraneRequestId;
          }
        } else {
          lastData = {};
          lastData.CraneRequestId = deliveryRequestList.CraneRequestId;
        }
      }
      if (lastData) {
        data = lastData.CraneRequestId + 1;
      } else {
        data = 1;
      }
      const lastDeliveryId = await DeliveryRequest.findOne({
        where: { ProjectId: params.ProjectId, isDeleted: false },
        order: [['DeliveryId', 'DESC']],
      });
      if (lastDeliveryId) {
        data2 = lastDeliveryId.DeliveryId + 1;
      } else {
        data2 = 1;
      }
      return { CraneRequestId: data, DeliveryId: data2 };
    } catch (e) {
      throw new Error(e);
    }
  },
  async getMemberDataMixPanel(req) {
    try {
      // await this.getDynamicModel(req);
      // const getUser = await User.findOne({
      //   where: {
      //     id: req.user.id,
      //   },
      // });
      const memberData = req.body;
      const memberDetails = await Member.findOne({
        where: {
          UserId: +memberData.id,
          ProjectId: +memberData.ProjectId,
          isDeleted: false,
          isActive: true,
        },
        include: [
          {
            association: 'Project',
            attributes: ['id', 'projectName'],
          },
          {
            association: 'Company',
            attributes: ['id', 'companyName'],
          },
          {
            association: 'User',
            attributes: ['id', 'firstName', 'lastName', 'email'],
          },
          {
            association: 'Role',
            attributes: ['roleName'],
          },
        ],
      });
      return memberDetails;
    } catch (e) {
      throw new Error(e);
    }
  },
  async getMemberData(inputData) {
    try {
      // await this.getDynamicModel(inputData);
      const { params } = inputData;
      const condition = {
        UserId: params.id,
        ProjectId: params.ProjectId,
      };
      const memberData = await Member.getBy(condition);
      return memberData;
    } catch (e) {
      throw new Error(e);
    }
  },
  async searchMember(inputData) {
    try {
      // await this.getDynamicModel(inputData);
      const { params } = inputData;
      const condition = {
        [Op.and]: [
          {
            ProjectId: +params.ProjectId,
            isDeleted: false,
            RoleId: { [Op.ne]: 1 },
            isActive: true,
            isGuestUser: false,
          },
        ],
      };
      const memberList = await Member.searchMemberNDR(condition, params);
      const finalList = [];
      let assignEmail;
      memberList.forEach((element) => {
        if (element.User.firstName && element.User.lastName) {
          assignEmail = `${element.User.firstName} ${element.User.lastName}(${element.User.email})`;
        } else {
          assignEmail = `(${element.User.email})`;
        }
        finalList.push({
          id: element.id,
          emails: element.User.email,
          firstName: element.firstName,
          lastName: element.User.lastName,
          email: assignEmail,
        });
      });
      return finalList;
    } catch (e) {
      throw new Error(e);
    }
  },
  async newRequest(inputData, done) {
    try {
      // await this.getDynamicModel(inputData);
      const eventTimeZone = await TimeZone.findOne({
        where: {
          isDeleted: false,
          id: +inputData.body.TimeZoneId,
        },
        attributes: [
          'id',
          'location',
          'isDayLightSavingEnabled',
          'timeZoneOffsetInMinutes',
          'dayLightSavingTimeInMinutes',
          'timezone',
        ],
      });
      if (!eventTimeZone) {
        return done(null, { message: 'Provide a valid timezone' });
      }
      const deliveryData = inputData.body;
      const userDetail = await User.findOne({
        where: { id: inputData.body.userId, isDeleted: false },
      });
      inputData.user = userDetail;
      const loginUser = inputData.user;
      const projectDetails = await Project.getProjectAndSettings({
        isDeleted: false,
        id: +deliveryData.ProjectId,
      });
      let startDate;
      let endDate;
      if (deliveryData.recurrence) {
        // startDate = await this.compareDeliveryDateWithDeliveryWindowDate(
        startDate = await deliveryService.compareDeliveryDateWithDeliveryWindowDate(
          deliveryData.deliveryStart,
          deliveryData.startPicker,
          eventTimeZone.timezone,
          projectDetails.ProjectSettings.deliveryWindowTime,
          projectDetails.ProjectSettings.deliveryWindowTimeUnit,
        );
        // endDate = await this.compareDeliveryDateWithDeliveryWindowDate(
        endDate = await deliveryService.compareDeliveryDateWithDeliveryWindowDate(
          deliveryData.deliveryEnd,
          deliveryData.endPicker,
          eventTimeZone.timezone,
          projectDetails.ProjectSettings.deliveryWindowTime,
          projectDetails.ProjectSettings.deliveryWindowTimeUnit,
        );
      }
      if (deliveryData.startPicker === deliveryData.endPicker) {
        return done(null, { message: 'Delivery Start time and End time should not be the same' });
      }
      if (deliveryData.startPicker > deliveryData.endPicker) {
        return done(null, { message: 'Please enter From Time lesser than To Time' });
      }
      if (startDate || endDate) {
        if (projectDetails.ProjectSettings.deliveryWindowTime === 0) {
          if (deliveryData.recurrence === 'Does Not Repeat') {
            return done(null, { message: 'Please enter Future Date/Time' });
          }
          return done(null, { message: 'Please enter Future Start or End Date/Time' });
        }
        return done(null, {
          message: `Bookings can not be submitted within ${projectDetails.ProjectSettings.deliveryWindowTime} ${projectDetails.ProjectSettings.deliveryWindowTimeUnit} prior to the event`,
        });
      }

      if (projectDetails && projectDetails.ProjectSettings) {
        this.checkInputDatas(inputData, async (checkResponse, checkError) => {
          if (checkError) {
            return done(null, checkError);
          }
          const memberDetails = await Member.getBy({
            UserId: loginUser.id,
            ProjectId: deliveryData.ProjectId,
            isActive: true,
            isDeleted: false,
          });
          if (memberDetails) {
            const range = momentRange.range(
              moment(deliveryData.deliveryStart),
              moment(deliveryData.deliveryEnd),
            );
            let totalDays = Array.from(range.by('day'));
            const eventsArray = [];
            let DeliverParam = {};
            const lastIdValue = await DeliveryRequest.findOne({
              where: { ProjectId: memberDetails.ProjectId, isDeleted: false },
              order: [['DeliveryId', 'DESC']],
            });
            let id = 0;
            const newValue = JSON.parse(JSON.stringify(lastIdValue));
            if (newValue && newValue.DeliveryId !== null && newValue.DeliveryId !== undefined) {
              id = newValue.DeliveryId;
            }
            let lastData = {};
            lastData = await CraneRequest.findOne({
              where: { ProjectId: +memberDetails.ProjectId, isDeleted: false },
              order: [['CraneRequestId', 'DESC']],
            });
            const deliveryRequestList = await DeliveryRequest.findOne({
              where: {
                ProjectId: +memberDetails.ProjectId,
                isDeleted: false,
                isAssociatedWithCraneRequest: true,
              },
              order: [['CraneRequestId', 'DESC']],
            });
            if (deliveryRequestList) {
              if (lastData) {
                if (deliveryRequestList.CraneRequestId > lastData.CraneRequestId) {
                  lastData.CraneRequestId = deliveryRequestList.CraneRequestId;
                }
              } else {
                lastData = {};
                lastData.CraneRequestId = deliveryRequestList.CraneRequestId;
              }
            }
            if (lastData) {
              const data = lastData.CraneRequestId;
              lastData.CraneRequestId = 0;
              lastData.CraneRequestId = data + 1;
            } else {
              lastData = {};
              lastData.CraneRequestId = 1;
            }
            let craneId = 0;
            const newId = JSON.parse(JSON.stringify(lastData));
            if (newId && newId.CraneRequestId !== null && newId.CraneRequestId !== undefined) {
              craneId = newId.CraneRequestId;
            }
            const roleDetails = await Role.getBy('Project Admin');
            const accountRoleDetails = await Role.getBy('Account Admin');
            if (deliveryData.recurrence === 'Daily') {
              const startTime = deliveryData.startPicker;
              const endTime = deliveryData.endPicker;
              let dailyIndex = 0;
              const recurrenceId = await concreteRequestService.insertRecurrenceSeries(
                deliveryData,
                inputData.user,
                deliveryData.requestType,
                eventTimeZone.timezone,
              );

              while (dailyIndex < totalDays.length) {
                const data = totalDays[dailyIndex];
                if (
                  moment(data).isBetween(
                    moment(deliveryData.deliveryStart),
                    moment(deliveryData.deliveryEnd),
                    null,
                    '[]',
                  ) ||
                  moment(data).isSame(deliveryData.deliveryStart) ||
                  moment(data).isSame(deliveryData.deliveryEnd)
                ) {
                  id += 1;
                  craneId += 1;
                  const date = moment(`${data}`).format('MM/DD/YYYY');
                  const chosenTimezoneDeliveryStart = moment.tz(
                    `${date} ${startTime}`,
                    'MM/DD/YYYY HH:mm',
                    eventTimeZone.timezone,
                  );
                  const chosenTimezoneDeliveryEnd = moment.tz(
                    `${date} ${endTime}`,
                    'MM/DD/YYYY HH:mm',
                    eventTimeZone.timezone,
                  );
                  const deliveryStart = chosenTimezoneDeliveryStart
                    .clone()
                    .tz('UTC')
                    .format('YYYY-MM-DD HH:mm:ssZ');
                  const deliveryEnd = chosenTimezoneDeliveryEnd
                    .clone()
                    .tz('UTC')
                    .format('YYYY-MM-DD HH:mm:ssZ');
                  DeliverParam = {
                    description: deliveryData.description,
                    escort: deliveryData.escort,
                    vehicleDetails: deliveryData.vehicleDetails,
                    notes: deliveryData.notes,
                    DeliveryId: id,
                    deliveryStart,
                    deliveryEnd,
                    ProjectId: deliveryData.ProjectId,
                    createdBy: memberDetails.id,
                    isAssociatedWithCraneRequest: deliveryData.isAssociatedWithCraneRequest,
                    requestType: deliveryData.requestType,
                    cranePickUpLocation: deliveryData.cranePickUpLocation,
                    craneDropOffLocation: deliveryData.craneDropOffLocation,
                    CraneRequestId:
                      deliveryData.requestType === 'deliveryRequestWithCrane' ? craneId : null,
                    recurrenceId,
                    LocationId: deliveryData.LocationId,
                    isCreatedByGuestUser: true,
                  };
                  if (
                    memberDetails.RoleId === roleDetails.id ||
                    memberDetails.RoleId === accountRoleDetails.id ||
                    memberDetails.isAutoApproveEnabled ||
                    projectDetails.ProjectSettings.isAutoApprovalEnabled
                  ) {
                    DeliverParam.status = 'Approved';
                    DeliverParam.approvedBy = memberDetails.id;
                    DeliverParam.approved_at = new Date();
                  }
                  eventsArray.push(DeliverParam);
                  // eslint-disable-next-line no-const-assign
                  dailyIndex += +deliveryData.repeatEveryCount;
                }
              }
              if (eventsArray && eventsArray.length > 0) {
                // const isOverlapping = await this.checkDoubleBookingAllowedOrNot(
                const isOverlapping = await deliveryService.checkDoubleBookingAllowedOrNot(
                  eventsArray,
                  projectDetails,
                  'add',
                  deliveryData.GateId,
                );
                if (isOverlapping && isOverlapping.error) {
                  return done(null, {
                    message: isOverlapping.message,
                  });
                }
              }
            }
            if (deliveryData.recurrence === 'Weekly') {
              const startTime = deliveryData.startPicker;
              const endTime = deliveryData.endPicker;
              const startDayWeek = moment(deliveryData.deliveryStart).startOf('week');
              const endDayWeek = moment(deliveryData.deliveryEnd).endOf('week');
              const range1 = momentRange.range(moment(startDayWeek), moment(endDayWeek));
              const totalDaysOfRecurrence = Array.from(range1.by('day'));
              totalDays = totalDaysOfRecurrence;
              const recurrenceId = await concreteRequestService.insertRecurrenceSeries(
                deliveryData,
                inputData.user,
                deliveryData.requestType,
                eventTimeZone.timezone,
              );
              let count;
              let weekIncrement;
              if (+deliveryData.repeatEveryCount > 1) {
                count = +deliveryData.repeatEveryCount - 1;
                weekIncrement = 7;
              } else {
                count = 1;
                weekIncrement = 0;
              }
              for (
                let indexba = 0;
                indexba < totalDaysOfRecurrence.length;
                indexba += weekIncrement * count
              ) {
                const totalLength = indexba + 6;
                for (let indexb = indexba; indexb <= totalLength; indexb += 1) {
                  const data = totalDaysOfRecurrence[indexb];
                  indexba += 1;
                  if (
                    data &&
                    !moment(data).isBefore(deliveryData.deliveryStart) &&
                    !moment(data).isAfter(deliveryData.deliveryEnd)
                  ) {
                    const day = moment(data).format('dddd');
                    const indexVal = deliveryData.days.includes(day);
                    if (indexVal) {
                      id += 1;
                      craneId += 1;
                      const date = moment(`${data}`).format('MM/DD/YYYY');
                      const chosenTimezoneDeliveryStart = moment.tz(
                        `${date} ${startTime}`,
                        'MM/DD/YYYY HH:mm',
                        eventTimeZone.timezone,
                      );
                      const chosenTimezoneDeliveryEnd = moment.tz(
                        `${date} ${endTime}`,
                        'MM/DD/YYYY HH:mm',
                        eventTimeZone.timezone,
                      );
                      const deliveryStart = chosenTimezoneDeliveryStart
                        .clone()
                        .tz('UTC')
                        .format('YYYY-MM-DD HH:mm:ssZ');
                      const deliveryEnd = chosenTimezoneDeliveryEnd
                        .clone()
                        .tz('UTC')
                        .format('YYYY-MM-DD HH:mm:ssZ');
                      DeliverParam = {
                        description: deliveryData.description,
                        escort: deliveryData.escort,
                        vehicleDetails: deliveryData.vehicleDetails,
                        notes: deliveryData.notes,
                        DeliveryId: id,
                        deliveryStart,
                        deliveryEnd,
                        ProjectId: deliveryData.ProjectId,
                        createdBy: memberDetails.id,
                        isAssociatedWithCraneRequest: deliveryData.isAssociatedWithCraneRequest,
                        requestType: deliveryData.requestType,
                        cranePickUpLocation: deliveryData.cranePickUpLocation,
                        craneDropOffLocation: deliveryData.craneDropOffLocation,
                        CraneRequestId:
                          deliveryData.requestType === 'deliveryRequestWithCrane' ? craneId : null,
                        recurrenceId,
                        LocationId: deliveryData.LocationId,
                        isCreatedByGuestUser: true,
                      };
                      if (
                        memberDetails.RoleId === roleDetails.id ||
                        memberDetails.RoleId === accountRoleDetails.id ||
                        memberDetails.isAutoApproveEnabled ||
                        projectDetails.ProjectSettings.isAutoApprovalEnabled
                      ) {
                        DeliverParam.status = 'Approved';
                        DeliverParam.approvedBy = memberDetails.id;
                        DeliverParam.approved_at = new Date();
                      }
                      eventsArray.push(DeliverParam);
                    }
                  }
                }
              }
              if (eventsArray && eventsArray.length > 0) {
                // const isOverlapping = await this.checkDoubleBookingAllowedOrNot(
                const isOverlapping = await deliveryService.checkDoubleBookingAllowedOrNot(
                  eventsArray,
                  projectDetails,
                  'add',
                  deliveryData.GateId,
                );
                if (isOverlapping && isOverlapping.error) {
                  return done(null, {
                    message: isOverlapping.message,
                  });
                }
              }
            }
            if (deliveryData.recurrence === 'Monthly') {
              const startTime = deliveryData.startPicker;
              const endTime = deliveryData.endPicker;
              const startMonth = moment(deliveryData.deliveryStart).startOf('month');
              const startMonthNumber = moment(startMonth).format('MM');
              const endMonth = moment(deliveryData.deliveryEnd).endOf('month');
              const endMonthNumber = moment(endMonth).format('MM');
              let startDate1 = moment(deliveryData.deliveryStart);
              const endDate1 = moment(deliveryData.deliveryEnd).endOf('month');
              const allMonthsInPeriod = [];
              const recurrenceId = await concreteRequestService.insertRecurrenceSeries(
                deliveryData,
                inputData.user,
                deliveryData.requestType,
                eventTimeZone.timezone,
              );
              while (startDate1.isBefore(endDate1)) {
                allMonthsInPeriod.push(startDate1.format('YYYY-MM'));
                startDate1 = startDate1.add(1, 'month');
              }
              let currentMonthDates = [];
              let totalNumberOfMonths = endMonthNumber - startMonthNumber;
              if (totalNumberOfMonths < 0) {
                totalNumberOfMonths *= -1;
              }
              let k = 0;
              while (k < allMonthsInPeriod.length + 1) {
                currentMonthDates = Array.from(
                  { length: moment(allMonthsInPeriod[k], 'YYYY-MM').daysInMonth() },
                  (x, j) => moment(allMonthsInPeriod[k], 'YYYY-MM').startOf('month').add(j, 'days'),
                );

                if (deliveryData.chosenDateOfMonth) {
                  const getDate = currentMonthDates.filter(
                    (value) => moment(value).format('DD') === deliveryData.dateOfMonth,
                  );
                  if (getDate.length === 1) {
                    if (
                      moment(getDate[0]).isBetween(
                        moment(deliveryData.deliveryStart),
                        moment(deliveryData.deliveryEnd),
                        null,
                        '[]',
                      ) ||
                      moment(getDate[0]).isSame(deliveryData.deliveryStart) ||
                      moment(getDate[0]).isSame(deliveryData.deliveryEnd)
                    ) {
                      id += 1;
                      craneId += 1;
                      const date = moment(getDate[0].toDate()).format('MM/DD/YYYY');
                      const chosenTimezoneDeliveryStart = moment.tz(
                        `${date} ${startTime}`,
                        'MM/DD/YYYY HH:mm',
                        eventTimeZone.timezone,
                      );
                      const chosenTimezoneDeliveryEnd = moment.tz(
                        `${date} ${endTime}`,
                        'MM/DD/YYYY HH:mm',
                        eventTimeZone.timezone,
                      );
                      const deliveryStart = chosenTimezoneDeliveryStart
                        .clone()
                        .tz('UTC')
                        .format('YYYY-MM-DD HH:mm:ssZ');
                      const deliveryEnd = chosenTimezoneDeliveryEnd
                        .clone()
                        .tz('UTC')
                        .format('YYYY-MM-DD HH:mm:ssZ');
                      DeliverParam = {
                        description: deliveryData.description,
                        escort: deliveryData.escort,
                        vehicleDetails: deliveryData.vehicleDetails,
                        notes: deliveryData.notes,
                        DeliveryId: id,
                        deliveryStart,
                        deliveryEnd,
                        ProjectId: deliveryData.ProjectId,
                        createdBy: memberDetails.id,
                        isAssociatedWithCraneRequest: deliveryData.isAssociatedWithCraneRequest,
                        requestType: deliveryData.requestType,
                        cranePickUpLocation: deliveryData.cranePickUpLocation,
                        craneDropOffLocation: deliveryData.craneDropOffLocation,
                        CraneRequestId:
                          deliveryData.requestType === 'deliveryRequestWithCrane' ? craneId : null,
                        recurrenceId,
                        LocationId: deliveryData.LocationId,
                        isCreatedByGuestUser: true,
                      };
                      if (
                        memberDetails.RoleId === roleDetails.id ||
                        memberDetails.RoleId === accountRoleDetails.id ||
                        memberDetails.isAutoApproveEnabled ||
                        projectDetails.ProjectSettings.isAutoApprovalEnabled
                      ) {
                        DeliverParam.status = 'Approved';
                        DeliverParam.approvedBy = memberDetails.id;
                        DeliverParam.approved_at = new Date();
                      }
                      eventsArray.push(DeliverParam);
                    }
                  }
                } else if (allMonthsInPeriod[k]) {
                  const dayOfMonth = deliveryData.monthlyRepeatType;
                  const week = dayOfMonth.split(' ')[0].toLowerCase();
                  const day = dayOfMonth.split(' ')[1].toLowerCase();
                  const chosenDay = moment(allMonthsInPeriod[k], 'YYYY-MM')
                    .startOf('month')
                    .day(day);
                  const getAllDays = [];
                  if (chosenDay.date() > 7) chosenDay.add(7, 'd');
                  const month = chosenDay.month();
                  while (month === chosenDay.month()) {
                    getAllDays.push(chosenDay.toString());
                    chosenDay.add(7, 'd');
                  }
                  let i = 0;
                  if (week === 'second') {
                    i += 1;
                  } else if (week === 'third') {
                    i += 2;
                  } else if (week === 'fourth') {
                    i += 3;
                  } else if (week === 'last') {
                    i = getAllDays.length - 1;
                  }
                  const finalDay = getAllDays[i];
                  if (
                    moment(finalDay).isBetween(
                      moment(deliveryData.deliveryStart),
                      moment(deliveryData.deliveryEnd),
                      null,
                      '[]',
                    ) ||
                    moment(finalDay).isSame(deliveryData.deliveryStart) ||
                    moment(finalDay).isSame(deliveryData.deliveryEnd)
                  ) {
                    id += 1;
                    craneId += 1;
                    const date = moment(finalDay).format('MM/DD/YYYY');
                    const chosenTimezoneDeliveryStart = moment.tz(
                      `${date} ${startTime}`,
                      'MM/DD/YYYY HH:mm',
                      eventTimeZone.timezone,
                    );
                    const chosenTimezoneDeliveryEnd = moment.tz(
                      `${date} ${endTime}`,
                      'MM/DD/YYYY HH:mm',
                      eventTimeZone.timezone,
                    );
                    const deliveryStart = chosenTimezoneDeliveryStart
                      .clone()
                      .tz('UTC')
                      .format('YYYY-MM-DD HH:mm:ssZ');
                    const deliveryEnd = chosenTimezoneDeliveryEnd
                      .clone()
                      .tz('UTC')
                      .format('YYYY-MM-DD HH:mm:ssZ');
                    DeliverParam = {
                      description: deliveryData.description,
                      escort: deliveryData.escort,
                      vehicleDetails: deliveryData.vehicleDetails,
                      notes: deliveryData.notes,
                      DeliveryId: id,
                      deliveryStart,
                      deliveryEnd,
                      ProjectId: deliveryData.ProjectId,
                      createdBy: memberDetails.id,
                      isAssociatedWithCraneRequest: deliveryData.isAssociatedWithCraneRequest,
                      requestType: deliveryData.requestType,
                      cranePickUpLocation: deliveryData.cranePickUpLocation,
                      craneDropOffLocation: deliveryData.craneDropOffLocation,
                      CraneRequestId:
                        deliveryData.requestType === 'deliveryRequestWithCrane' ? craneId : null,
                      recurrenceId,
                      LocationId: deliveryData.LocationId,
                      isCreatedByGuestUser: true,
                    };
                    if (
                      memberDetails.RoleId === roleDetails.id ||
                      memberDetails.RoleId === accountRoleDetails.id ||
                      memberDetails.isAutoApproveEnabled ||
                      projectDetails.ProjectSettings.isAutoApprovalEnabled
                    ) {
                      DeliverParam.status = 'Approved';
                      DeliverParam.approvedBy = memberDetails.id;
                      DeliverParam.approved_at = new Date();
                    }
                    eventsArray.push(DeliverParam);
                  }
                }
                k += +deliveryData.repeatEveryCount;
              }
              if (eventsArray && eventsArray.length > 0) {
                // const isOverlapping = await this.checkDoubleBookingAllowedOrNot(
                const isOverlapping = await deliveryService.checkDoubleBookingAllowedOrNot(
                  eventsArray,
                  projectDetails,
                  'add',
                  deliveryData.GateId,
                );
                if (isOverlapping && isOverlapping.error) {
                  return done(null, {
                    message: isOverlapping.message,
                  });
                }
              }
            }
            if (deliveryData.recurrence === 'Yearly') {
              const startTime = deliveryData.startPicker;
              const endTime = deliveryData.endPicker;
              const startMonth = moment(deliveryData.deliveryStart).startOf('month');
              const startMonthNumber = moment(startMonth).format('MM');
              const endMonth = moment(deliveryData.deliveryEnd).endOf('month');
              const endMonthNumber = moment(endMonth).format('MM');
              let startDate1 = moment(deliveryData.deliveryStart);
              const endDate1 = moment(deliveryData.deliveryEnd).endOf('month');
              const allMonthsInPeriod = [];
              const recurrenceId = await concreteRequestService.insertRecurrenceSeries(
                deliveryData,
                inputData.user,
                deliveryData.requestType,
                eventTimeZone.timezone,
              );
              while (startDate1.isBefore(endDate1)) {
                allMonthsInPeriod.push(startDate1.format('YYYY-MM'));
                startDate1 = startDate1.add(12, 'month');
              }
              let currentMonthDates = [];
              let totalNumberOfMonths = endMonthNumber - startMonthNumber;
              if (totalNumberOfMonths < 0) {
                totalNumberOfMonths *= -1;
              }
              for (let k = 0; k < allMonthsInPeriod.length + 1; k += 1) {
                currentMonthDates = Array.from(
                  { length: moment(allMonthsInPeriod[k], 'YYYY-MM').daysInMonth() },
                  (x, j) => moment(allMonthsInPeriod[k], 'YYYY-MM').startOf('month').add(j, 'days'),
                );
                if (deliveryData.chosenDateOfMonth) {
                  const getDate = currentMonthDates.filter(
                    (value) => moment(value).format('DD') === deliveryData.dateOfMonth,
                  );
                  if (getDate.length === 1) {
                    if (
                      moment(getDate[0]).isBetween(
                        moment(deliveryData.deliveryStart),
                        moment(deliveryData.deliveryEnd),
                        null,
                        '[]',
                      ) ||
                      moment(getDate[0]).isSame(deliveryData.deliveryStart) ||
                      moment(getDate[0]).isSame(deliveryData.deliveryEnd)
                    ) {
                      id += 1;
                      craneId += 1;
                      const date = moment(getDate[0].toDate()).format('MM/DD/YYYY');
                      const chosenTimezoneDeliveryStart = moment.tz(
                        `${date} ${startTime}`,
                        'MM/DD/YYYY HH:mm',
                        eventTimeZone.timezone,
                      );
                      const chosenTimezoneDeliveryEnd = moment.tz(
                        `${date} ${endTime}`,
                        'MM/DD/YYYY HH:mm',
                        eventTimeZone.timezone,
                      );
                      const deliveryStart = chosenTimezoneDeliveryStart
                        .clone()
                        .tz('UTC')
                        .format('YYYY-MM-DD HH:mm:ssZ');
                      const deliveryEnd = chosenTimezoneDeliveryEnd
                        .clone()
                        .tz('UTC')
                        .format('YYYY-MM-DD HH:mm:ssZ');
                      DeliverParam = {
                        description: deliveryData.description,
                        escort: deliveryData.escort,
                        vehicleDetails: deliveryData.vehicleDetails,
                        notes: deliveryData.notes,
                        DeliveryId: id,
                        deliveryStart,
                        deliveryEnd,
                        ProjectId: deliveryData.ProjectId,
                        createdBy: memberDetails.id,
                        isAssociatedWithCraneRequest: deliveryData.isAssociatedWithCraneRequest,
                        requestType: deliveryData.requestType,
                        cranePickUpLocation: deliveryData.cranePickUpLocation,
                        craneDropOffLocation: deliveryData.craneDropOffLocation,
                        CraneRequestId:
                          deliveryData.requestType === 'deliveryRequestWithCrane' ? craneId : null,
                        recurrenceId,
                        LocationId: deliveryData.LocationId,
                        isCreatedByGuestUser: true,
                      };
                      if (
                        memberDetails.RoleId === roleDetails.id ||
                        memberDetails.RoleId === accountRoleDetails.id ||
                        memberDetails.isAutoApproveEnabled ||
                        projectDetails.ProjectSettings.isAutoApprovalEnabled
                      ) {
                        DeliverParam.status = 'Approved';
                        DeliverParam.approvedBy = memberDetails.id;
                        DeliverParam.approved_at = new Date();
                      }
                      eventsArray.push(DeliverParam);
                    }
                  }
                } else if (allMonthsInPeriod[k]) {
                  const dayOfMonth = deliveryData.monthlyRepeatType;
                  const week = dayOfMonth.split(' ')[0].toLowerCase();
                  const day = dayOfMonth.split(' ')[1].toLowerCase();
                  const chosenDay = moment(allMonthsInPeriod[k], 'YYYY-MM')
                    .startOf('month')
                    .day(day);
                  const getAllDays = [];
                  if (chosenDay.date() > 7) chosenDay.add(7, 'd');
                  const month = chosenDay.month();
                  while (month === chosenDay.month()) {
                    getAllDays.push(chosenDay.toString());
                    chosenDay.add(7, 'd');
                  }
                  let i = 0;
                  if (week === 'second') {
                    i += 1;
                  } else if (week === 'third') {
                    i += 2;
                  } else if (week === 'fourth') {
                    i += 3;
                  } else if (week === 'last') {
                    i = getAllDays.length - 1;
                  }
                  const finalDay = getAllDays[i];
                  if (
                    moment(finalDay).isBetween(
                      moment(deliveryData.deliveryStart),
                      moment(deliveryData.deliveryEnd),
                      null,
                      '[]',
                    ) ||
                    moment(finalDay).isSame(deliveryData.deliveryStart) ||
                    moment(finalDay).isSame(deliveryData.deliveryEnd)
                  ) {
                    id += 1;
                    craneId += 1;
                    const date = moment(finalDay).format('MM/DD/YYYY');
                    const chosenTimezoneDeliveryStart = moment.tz(
                      `${date} ${startTime}`,
                      'MM/DD/YYYY HH:mm',
                      eventTimeZone.timezone,
                    );
                    const chosenTimezoneDeliveryEnd = moment.tz(
                      `${date} ${endTime}`,
                      'MM/DD/YYYY HH:mm',
                      eventTimeZone.timezone,
                    );
                    const deliveryStart = chosenTimezoneDeliveryStart
                      .clone()
                      .tz('UTC')
                      .format('YYYY-MM-DD HH:mm:ssZ');
                    const deliveryEnd = chosenTimezoneDeliveryEnd
                      .clone()
                      .tz('UTC')
                      .format('YYYY-MM-DD HH:mm:ssZ');
                    DeliverParam = {
                      description: deliveryData.description,
                      escort: deliveryData.escort,
                      vehicleDetails: deliveryData.vehicleDetails,
                      notes: deliveryData.notes,
                      DeliveryId: id,
                      deliveryStart,
                      deliveryEnd,
                      ProjectId: deliveryData.ProjectId,
                      createdBy: memberDetails.id,
                      isAssociatedWithCraneRequest: deliveryData.isAssociatedWithCraneRequest,
                      requestType: deliveryData.requestType,
                      cranePickUpLocation: deliveryData.cranePickUpLocation,
                      craneDropOffLocation: deliveryData.craneDropOffLocation,
                      CraneRequestId:
                        deliveryData.requestType === 'deliveryRequestWithCrane' ? craneId : null,
                      recurrenceId,
                      LocationId: deliveryData.LocationId,
                      isCreatedByGuestUser: true,
                    };
                    if (
                      memberDetails.RoleId === roleDetails.id ||
                      memberDetails.RoleId === accountRoleDetails.id ||
                      memberDetails.isAutoApproveEnabled ||
                      projectDetails.ProjectSettings.isAutoApprovalEnabled
                    ) {
                      DeliverParam.status = 'Approved';
                      DeliverParam.approvedBy = memberDetails.id;
                      DeliverParam.approved_at = new Date();
                    }
                    eventsArray.push(DeliverParam);
                  }
                }
              }
              if (eventsArray && eventsArray.length > 0) {
                // const isOverlapping = await this.checkDoubleBookingAllowedOrNot(
                const isOverlapping = await deliveryService.checkDoubleBookingAllowedOrNot(
                  eventsArray,
                  projectDetails,
                  'add',
                  deliveryData.GateId,
                );
                if (isOverlapping && isOverlapping.error) {
                  return done(null, {
                    message: isOverlapping.message,
                  });
                }
              }
            }
            if (deliveryData.recurrence === 'Does Not Repeat') {
              id += 1;
              craneId += 1;
              const chosenTimezoneDeliveryStart = moment.tz(
                `${deliveryData.deliveryStart} ${deliveryData.startPicker}`,
                'YYYY MM DD 00:00:00 HH:mm',
                eventTimeZone.timezone,
              );
              const chosenTimezoneDeliveryEnd = moment.tz(
                `${deliveryData.deliveryEnd} ${deliveryData.endPicker}`,
                'YYYY MM DD 00:00:00 HH:mm',
                eventTimeZone.timezone,
              );
              const deliveryStart = chosenTimezoneDeliveryStart
                .clone()
                .tz('UTC')
                .format('YYYY-MM-DD HH:mm:ssZ');
              const deliveryEnd = chosenTimezoneDeliveryEnd
                .clone()
                .tz('UTC')
                .format('YYYY-MM-DD HH:mm:ssZ');
              const recurrenceId = await concreteRequestService.insertRecurrenceSeries(
                deliveryData,
                inputData.user,
                deliveryData.requestType,
                eventTimeZone.timezone,
              );
              DeliverParam = {
                description: deliveryData.description,
                escort: deliveryData.escort,
                vehicleDetails: deliveryData.vehicleDetails,
                notes: deliveryData.notes,
                DeliveryId: id,
                deliveryStart,
                deliveryEnd,
                ProjectId: deliveryData.ProjectId,
                createdBy: memberDetails.id,
                isAssociatedWithCraneRequest: deliveryData.isAssociatedWithCraneRequest,
                requestType: deliveryData.requestType,
                cranePickUpLocation: deliveryData.cranePickUpLocation,
                craneDropOffLocation: deliveryData.craneDropOffLocation,
                CraneRequestId:
                  deliveryData.requestType === 'deliveryRequestWithCrane' ? craneId : null,
                recurrenceId,
                LocationId: deliveryData.LocationId,
                isCreatedByGuestUser: true,
              };
              if (
                memberDetails.RoleId === roleDetails.id ||
                memberDetails.RoleId === accountRoleDetails.id ||
                memberDetails.isAutoApproveEnabled ||
                projectDetails.ProjectSettings.isAutoApprovalEnabled
              ) {
                DeliverParam.status = 'Approved';
                DeliverParam.approvedBy = memberDetails.id;
                DeliverParam.approved_at = new Date();
              }
              eventsArray.push(DeliverParam);
              if (eventsArray && eventsArray.length > 0) {
                // const isOverlapping = await this.checkDoubleBookingAllowedOrNot(
                const isOverlapping = await deliveryService.checkDoubleBookingAllowedOrNot(
                  eventsArray,
                  projectDetails,
                  'add',
                  deliveryData.GateId,
                );
                if (isOverlapping && isOverlapping.error) {
                  return done(null, {
                    message: isOverlapping.message,
                  });
                }
              }
            }
            let newDeliverData = {};
            if (eventsArray.length > 0) {
              for (let i = 0; i < eventsArray.length; i += 1) {
                newDeliverData = await DeliveryRequest.createInstance(eventsArray[i]);
                const { companies, persons, define } = deliveryData;
                const gates = [deliveryData.GateId];
                const equipments = deliveryData.EquipmentId;
                const updateParam = {
                  DeliveryId: newDeliverData.id,
                  DeliveryCode: newDeliverData.DeliveryId,
                  ProjectId: deliveryData.ProjectId,
                };
                companies.forEach(async (element) => {
                  const companyParam = updateParam;
                  companyParam.CompanyId = element;
                  await DeliverCompany.createInstance(companyParam);
                });
                gates.forEach(async (element) => {
                  const gateParam = updateParam;
                  gateParam.GateId = element;
                  await DeliverGate.createInstance(gateParam);
                });
                equipments.forEach(async (element) => {
                  const equipmentParam = updateParam;
                  equipmentParam.EquipmentId = element;
                  await DeliverEquipment.createInstance(equipmentParam);
                });
                persons.forEach(async (element) => {
                  const memberParam = updateParam;
                  memberParam.MemberId = element;
                  await DeliveryPerson.createInstance(memberParam);
                });
                define.forEach(async (element) => {
                  const defineParam = updateParam;
                  defineParam.DeliverDefineWorkId = element;
                  await DeliverDefine.createInstance(defineParam);
                });
                const history = {
                  DeliveryRequestId: newDeliverData.id,
                  DeliveryId: newDeliverData.DeliveryId,
                  MemberId: memberDetails.id,
                  type: 'create',
                  description: `${loginUser.firstName} ${loginUser.lastName} Created Delivery Booking, ${deliveryData.description}.`,
                };
                const notification = history;
                notification.ProjectId = eventsArray[i].ProjectId;
                notification.title = 'Delivery Booking Creation';
                await DeliverHistory.createInstance(history);
                if (newDeliverData.status === 'Approved') {
                  const object = {
                    ProjectId: deliveryData.ProjectId,
                    MemberId: memberDetails.id,
                    DeliveryRequestId: newDeliverData.id,
                    isDeleted: false,
                    type: 'approved',
                    description: `${loginUser.firstName} ${loginUser.lastName} Approved Delivery Booking, ${deliveryData.description}.`,
                  };
                  await DeliverHistory.createInstance(object);
                }
              }
            }
            if (Object.keys(newDeliverData).length > 0 && typeof newDeliverData === 'object') {
              const { persons } = deliveryData;
              const locationChosen = await Locations.findOne({
                where: {
                  ProjectId: deliveryData.ProjectId,
                  id: deliveryData.LocationId,
                },
              });

              const history = {
                DeliveryRequestId: newDeliverData.id,
                DeliveryId: newDeliverData.DeliveryId,
                MemberId: memberDetails.id,
                type: 'create',
                description: `${loginUser.firstName} ${loginUser.lastName} Created Delivery Booking, ${deliveryData.description}.`,
                locationFollowDescription: `${loginUser.firstName} ${loginUser.lastName} Created Delivery Booking, ${deliveryData.description}. Location: ${locationChosen.locationPath}.`,
                memberData: [],
              };
              const notification = history;
              notification.ProjectId = deliveryData.ProjectId;
              notification.LocationId = deliveryData.LocationId;
              notification.title = 'Delivery Booking Creation';
              notification.recurrenceType = `${deliveryData.recurrence} From ${moment(
                deliveryData.deliveryStart,
              ).format('MM/DD/YYYY')} to ${moment(deliveryData.deliveryEnd).format('MM/DD/YYYY')}`;
              notification.requestType = 'deliveryRequest';
              const newNotification = await Notification.createInstance(notification);
              const memberLocationPreference = await LocationNotificationPreferences.findAll({
                where: {
                  ProjectId: deliveryData.ProjectId,
                  LocationId: deliveryData.LocationId,
                  follow: true,
                },
                include: [
                  {
                    association: 'Member',
                    attributes: ['id', 'RoleId'],
                    where: {
                      [Op.and]: [
                        {
                          id: { [Op.ne]: memberDetails.id },
                        },
                      ],
                    },
                    include: [
                      {
                        association: 'User',
                        attributes: ['id', 'firstName', 'lastName', 'email'],
                      },
                    ],
                  },
                ],
              });
              const locationFollowMembers = [];
              memberLocationPreference.forEach(async (element) => {
                locationFollowMembers.push(element.Member.id);
              });
              const adminData = await Member.findAll({
                where: {
                  [Op.and]: [
                    { ProjectId: deliveryData.ProjectId },
                    { isDeleted: false },
                    { id: { [Op.in]: persons } },
                    { id: { [Op.ne]: newNotification.MemberId } },
                    { id: { [Op.notIn]: locationFollowMembers } },
                  ],
                },
                include: [
                  {
                    association: 'User',
                    attributes: ['id', 'firstName', 'lastName', 'email'],
                  },
                ],
                attributes: ['id', 'RoleId'],
              });
              if (memberLocationPreference && memberLocationPreference.length > 0) {
                // send notification to responsible and followed members
                // here 3-(NotificationPreferenceItemId - When a new delivery/crane/concrete request added to the project)
                await pushNotification.sendMemberLocationPreferencePushNotification(
                  memberLocationPreference,
                  newDeliverData.DeliveryRequestId,
                  history.locationFollowDescription,
                  newDeliverData.requestType,
                  newDeliverData.ProjectId,
                  newDeliverData.id,
                  3,
                );
                // in App notification for location follow members
                // here 3-(NotificationPreferenceItemId - When a new delivery/crane/concrete request added to the project)
                await notificationHelper.createMemberDeliveryLocationInAppNotification(
                  DeliveryPersonNotification,
                  deliveryData.ProjectId,
                  newNotification.id,
                  memberLocationPreference,
                  3,
                );
              }
              history.adminData = adminData;
              history.firstName = loginUser.firstName;
              history.profilePic = loginUser.profilePic;
              history.createdAt = new Date();
              history.ProjectId = deliveryData.ProjectId;
              history.projectName = projectDetails.projectName;
              // here 3-(NotificationPreferenceItemId - When a new delivery/crane/concrete request added to the project)
              await notificationHelper.createDeliveryPersonNotification(
                adminData,
                [],
                projectDetails,
                newNotification,
                DeliveryPersonNotification,
                memberDetails,
                loginUser,
                3,
                'created a',
                'Delivery Request',
                `delivery Booking (${newDeliverData.DeliveryId} - ${newDeliverData.description})`,
                newDeliverData.id,
              );
              const checkMemberNotification = await NotificationPreference.findAll({
                where: {
                  ProjectId: deliveryData.ProjectId,
                  isDeleted: false,
                },
                attributes: [
                  'id',
                  'MemberId',
                  'ProjectId',
                  'ParentCompanyId',
                  'NotificationPreferenceItemId',
                  'instant',
                  'dailyDigest',
                ],
                include: [
                  {
                    association: 'NotificationPreferenceItem',
                    where: {
                      id: 3,
                      isDeleted: false,
                    },
                    attributes: ['id', 'description', 'inappNotification', 'emailNotification'],
                  },
                ],
              });
              history.notificationPreference = checkMemberNotification;
              // here 3-(NotificationPreferenceItemId - When a new delivery/crane/concrete request added to the project)
              await pushNotification.sendDeviceToken(history, 3, deliveryData.ProjectId);
              //
              // await this.sendEmailNotificationToUser(
              await deliveryService.sendEmailNotificationToUser(
                history,
                memberDetails,
                loginUser,
                newDeliverData,
                deliveryData,
                memberLocationPreference,
              );
              const memberLocationPreferenceNotify = await LocationNotificationPreferences.findAll({
                where: {
                  ProjectId: +deliveryData.ProjectId,
                  LocationId: +deliveryData.LocationId,
                  follow: true,
                },
                include: [
                  {
                    association: 'Member',
                    attributes: ['id', 'RoleId'],
                    where: {
                      [Op.and]: [
                        {
                          id: { [Op.ne]: memberDetails.id },
                        },
                      ],
                    },
                    include: [
                      {
                        association: 'User',
                        attributes: ['id', 'firstName', 'lastName', 'email'],
                      },
                    ],
                  },
                ],
              });
              if (
                +memberDetails.RoleId === 4 ||
                +memberDetails.RoleId === 3 ||
                +memberDetails.RoleId === 7
              ) {
                // const userEmails = await this.getMemberDetailData(
                const userEmails = await deliveryService.getMemberDetailData(
                  history,
                  memberLocationPreference,
                );
                if (userEmails.length > 0) {
                  userEmails.forEach(async (element) => {
                    if (+element.RoleId === 2 && +element.MemberId !== +memberDetails.id) {
                      let name;
                      if (!element.firstName) {
                        name = 'user';
                      } else {
                        name = `${element.firstName} ${element.lastName}`;
                      }
                      const memberRole = await Role.findOne({
                        where: {
                          id: memberDetails.RoleId,
                          isDeleted: false,
                        },
                      });
                      const mailPayload = {
                        name,
                        email: element.email,
                        content: `We would like to inform you that 
                          ${loginUser.firstName} ${loginUser.lastName} - ${memberRole.roleName} has created a delivery booking ${newDeliverData.DeliveryId} and waiting for your approval.Kindly review the booking and update the status.`,
                      };
                      const isMemberFollowLocation = await LocationNotificationPreferences.findOne({
                        where: {
                          MemberId: +element.MemberId,
                          ProjectId: +deliveryData.ProjectId,
                          LocationId: +deliveryData.LocationId,
                          isDeleted: false,
                          // follow: true,
                        },
                      });
                      if (isMemberFollowLocation) {
                        const memberNotification = await NotificationPreference.findOne({
                          where: {
                            MemberId: +element.MemberId,
                            ProjectId: +deliveryData.ProjectId,
                            isDeleted: false,
                          },
                          include: [
                            {
                              association: 'NotificationPreferenceItem',
                              where: {
                                id: 8,
                                isDeleted: false,
                              },
                            },
                          ],
                        });
                        if (memberNotification && memberNotification.instant) {
                          await MAILER.sendMail(
                            mailPayload,
                            'notifyPAForApproval',
                            `Delivery Booking created by ${loginUser.firstName} ${loginUser.lastName} - ${memberRole.roleName}`,
                            `Delivery Booking created by ${loginUser.firstName} ${loginUser.lastName} - ${memberRole.roleName}`,
                            async (info, err) => {
                              console.log(info, err);
                            },
                          );
                        }
                        if (memberNotification && memberNotification.dailyDigest) {
                          // await this.createDailyDigestDataApproval(
                          await deliveryService.createDailyDigestDataApproval(
                            +memberDetails.RoleId,
                            +element.MemberId,
                            +deliveryData.ProjectId,
                            +deliveryData.ParentCompanyId,
                            loginUser,
                            'created a',
                            'Delivery Request',
                            `delivery Booking (${newDeliverData.DeliveryId} - ${newDeliverData.description})`,
                            'and waiting for your approval',
                            newDeliverData.id,
                          );
                        }
                      }
                    }
                  });
                  if (memberLocationPreferenceNotify && memberLocationPreferenceNotify.length > 0) {
                    history.memberData = [];
                    history.memberData.push(...memberLocationPreferenceNotify);
                  }
                  return done(history, false);
                }
                if (memberLocationPreferenceNotify && memberLocationPreferenceNotify.length > 0) {
                  history.memberData = [];
                  history.memberData.push(...memberLocationPreferenceNotify);
                }
                return done(history, false);
              }
              if (memberLocationPreferenceNotify && memberLocationPreferenceNotify.length > 0) {
                history.memberData = [];
                history.memberData.push(...memberLocationPreferenceNotify);
              }
              return done(history, false);
            }
            return done(null, {
              message: 'Bookings will not be created for the scheduled date/time',
            });
          }
          return done(null, {
            message: 'You are not allowed create Delivery Booking for this project.',
          });
        });
      } else {
        return done(null, { message: 'Project does not exist.' });
      }
    } catch (e) {
      return done(null, e);
    }
  },
  async compareDeliveryDateWithDeliveryWindowDate(
    dateStr,
    timeStr,
    timezoneStr,
    deliveryWindowTime,
    deliveryWindowTimeUnit,
  ) {
    const datetimeStr = `${moment(dateStr).format('YYYY-MM-DD')}T${timeStr}`;
    const datetime = moment.tz(datetimeStr, timezoneStr);
    const currentDatetime = moment
      .tz(timezoneStr)
      .add(deliveryWindowTime, deliveryWindowTimeUnit)
      .startOf('minute');
    return datetime.isSameOrBefore(currentDatetime);
  },
  async checkInputDatas(inputData, done) {
    // await this.getDynamicModel(inputData);
    const deliveryData = inputData.body;
    const { companies, persons, define } = deliveryData;
    const gates = [deliveryData.GateId];
    const equipments = deliveryData.EquipmentId;
    const inputProjectId = deliveryData.ProjectId;
    const memberList = await Member.count({
      where: { id: { [Op.in]: persons }, ProjectId: inputProjectId, isDeleted: false },
    });
    const gateList = await Gates.count({
      where: { id: { [Op.in]: gates }, ProjectId: inputProjectId, isDeleted: false },
    });
    const equipmentList = await Equipments.count({
      where: { id: { [Op.in]: equipments }, ProjectId: inputProjectId, isDeleted: false },
    });
    const defineList = await DeliverDefineWork.count({
      where: { id: { [Op.in]: define }, ProjectId: inputProjectId, isDeleted: false },
    });
    const companyList = await Company.count({
      where: {
        [Op.or]: [
          {
            id: { [Op.in]: companies },
            ProjectId: +inputProjectId,
            isDeleted: false,
          },
          {
            id: {
              [Op.in]: companies,
            },
            isParent: true,
            ParentCompanyId: +deliveryData.ParentCompanyId,
            isDeleted: false,
          },
        ],
      },
    });
    if (deliveryData.persons && deliveryData.persons.length > 0 && memberList !== persons.length) {
      return done(null, { message: 'Some Member is not in the project' });
    }
    if (deliveryData.GateId && gateList !== gates.length) {
      return done(null, { message: 'Mentioned Gate is not in the project' });
    }
    if (deliveryData.EquipmentId && equipmentList !== equipments.length) {
      return done(null, { message: 'Mentioned Equipment is not in this project' });
    }
    if (
      deliveryData.companies &&
      deliveryData.companies.length > 0 &&
      companyList !== companies.length
    ) {
      return done(null, { message: 'Some Company is not in the project' });
    }
    if (deliveryData.define && deliveryData.define.length > 0 && defineList !== define.length) {
      return done(null, { message: 'Some Definable Feature of Work is not in the project' });
    }
    return done(true, false);
  },
  async checkDeliveryConflictsWithAlreadyScheduled(requestsArray, type, gateId) {
    if (requestsArray && requestsArray.length > 0) {
      const deliveryStartDateArr = [];
      const deliveryEndDateArr = [];
      const requestIds = [];
      requestsArray.forEach((data) => {
        deliveryStartDateArr.push(new Date(data.deliveryStart));
        deliveryEndDateArr.push(new Date(data.deliveryEnd));
        if (type === 'edit' && data.id) {
          requestIds.push(data.id);
        }
      });
      let condition = {
        ProjectId: requestsArray[0].ProjectId,
        status: {
          [Op.notIn]: ['Delivered', 'Expired'],
        },
      };
      if (type === 'edit') {
        condition = {
          ...condition,
          id: {
            [Op.notIn]: requestIds,
          },
        };
      }
      const isDeliveryBookingOverlapping = await DeliveryRequest.findAll({
        where: {
          ...condition,
          [Op.or]: [
            {
              [Op.or]: deliveryStartDateArr.map((date) => ({
                deliveryStart: { [Op.lte]: date },
                deliveryEnd: { [Op.gte]: date },
              })),
            },
            {
              [Op.or]: deliveryEndDateArr.map((date) => ({
                deliveryStart: { [Op.lte]: date },
                deliveryEnd: { [Op.gte]: date },
              })),
            },
          ],
        },
        include: [
          {
            association: 'gateDetails',
            where: {
              isDeleted: false,
              isActive: true,
              GateId: { [Op.eq]: +gateId },
            },
          },
        ],
      });
      if (isDeliveryBookingOverlapping && isDeliveryBookingOverlapping.length > 0) {
        return true;
      }
      return false;
    }
  },
  async craneListEquipment(inputData) {
    try {
      // await this.getDynamicModel(inputData);
      const conditionData = {
        ProjectId: inputData.params.ProjectId,
        isDeleted: false,
      };
      if (inputData.body.showActivatedAlone) {
        conditionData.isActive = true;
      }
      const equipmentData = await Equipments.findAndCountAll({
        where: conditionData,
        include: [
          {
            required: true,
            where: { isDeleted: false, isActive: true, isCraneType: true },
            association: 'PresetEquipmentType',
            attributes: ['id', 'equipmentType', 'isCraneType'],
          },
        ],
      });
      if (equipmentData.rows) {
        if (equipmentData.rows.length > 0) {
          equipmentData.rows.sort((a, b) =>
            a.equipmentName.toLowerCase() > b.equipmentName.toLowerCase() ? 1 : -1,
          );
        }
      } else if (equipmentData.length > 0) {
        equipmentData.sort((a, b) =>
          a.equipmentName.toLowerCase() > b.equipmentName.toLowerCase() ? 1 : -1,
        );
      }
      return equipmentData;
    } catch (e) {
      throw new Error(e);
    }
  },
  async newCraneRequest(inputData, done) {
    try {
      // await this.getDynamicModel(inputData);
      const eventTimeZone = await TimeZone.findOne({
        where: {
          isDeleted: false,
          id: +inputData.body.TimeZoneId,
        },
        attributes: [
          'id',
          'location',
          'isDayLightSavingEnabled',
          'timeZoneOffsetInMinutes',
          'dayLightSavingTimeInMinutes',
          'timezone',
        ],
      });
      if (!eventTimeZone) {
        return done(null, { message: 'Provide a valid timezone' });
      }

      const userDetail = await User.findOne({
        where: { id: inputData.body.userId, isDeleted: false },
      });
      inputData.user = userDetail;
      const craneRequestDetail = inputData.body;
      const loginUser = inputData.user;
      const projectDetails = await Project.getProjectAndSettings({
        isDeleted: false,
        id: +craneRequestDetail.ProjectId,
      });
      let startDate;
      let endDate;
      if (craneRequestDetail.recurrence) {
        startDate = await this.compareDeliveryDateWithDeliveryWindowDate(
          craneRequestDetail.craneDeliveryStart,
          craneRequestDetail.startPicker,
          eventTimeZone.timezone,
          projectDetails.ProjectSettings.deliveryWindowTime,
          projectDetails.ProjectSettings.deliveryWindowTimeUnit,
        );
        endDate = await this.compareDeliveryDateWithDeliveryWindowDate(
          craneRequestDetail.craneDeliveryEnd,
          craneRequestDetail.endPicker,
          eventTimeZone.timezone,
          projectDetails.ProjectSettings.deliveryWindowTime,
          projectDetails.ProjectSettings.deliveryWindowTimeUnit,
        );
      }
      if (craneRequestDetail.startPicker === craneRequestDetail.endPicker) {
        return done(null, { message: 'Delivery Start time and End time should not be the same' });
      }
      if (craneRequestDetail.startPicker > craneRequestDetail.endPicker) {
        return done(null, { message: 'Please enter From Time lesser than To Time' });
      }
      if (startDate || endDate) {
        if (projectDetails.ProjectSettings.deliveryWindowTime === 0) {
          if (craneRequestDetail.recurrence === 'Does Not Repeat') {
            return done(null, { message: 'Please enter Future Date/Time' });
          }
          return done(null, { message: 'Please enter Future Start or End Date/Time' });
        }
        return done(null, {
          message: `Bookings can not be submitted within ${projectDetails.ProjectSettings.deliveryWindowTime} ${projectDetails.ProjectSettings.deliveryWindowTimeUnit} prior to the event`,
        });
      }
      if (projectDetails && projectDetails.ProjectSettings) {
        this.checkCraneInputDatas(inputData, async (checkResponse, checkError) => {
          if (checkError) {
            return done(null, checkError);
          }
          const memberDetails = await Member.getBy({
            UserId: loginUser.id,
            ProjectId: craneRequestDetail.ProjectId,
            isActive: true,
            isDeleted: false,
          });
          if (memberDetails) {
            let lastData = {};
            lastData = await CraneRequest.findOne({
              where: { ProjectId: +memberDetails.ProjectId, isDeleted: false },
              order: [['CraneRequestId', 'DESC']],
            });
            const deliveryRequestList = await DeliveryRequest.findOne({
              where: {
                ProjectId: +memberDetails.ProjectId,
                isDeleted: false,
                isAssociatedWithCraneRequest: true,
              },
              order: [['CraneRequestId', 'DESC']],
            });
            if (deliveryRequestList) {
              if (lastData) {
                if (deliveryRequestList.CraneRequestId > lastData.CraneRequestId) {
                  lastData.CraneRequestId = deliveryRequestList.CraneRequestId;
                }
              } else {
                lastData = {};
                lastData.CraneRequestId = deliveryRequestList.CraneRequestId;
              }
            }
            if (lastData) {
              const data = lastData.CraneRequestId;
              lastData.CraneRequestId = 0;
              lastData.CraneRequestId = data + 1;
            } else {
              lastData = {};
              lastData.CraneRequestId = 1;
            }
            let id = 0;
            const newValue = JSON.parse(JSON.stringify(lastData));
            if (
              newValue &&
              newValue.CraneRequestId !== null &&
              newValue.CraneRequestId !== undefined
            ) {
              id = newValue.CraneRequestId;
            }
            let craneRequestParam = {};
            const roleDetails = await Role.getBy('Project Admin');
            const accountRoleDetails = await Role.getBy('Account Admin');
            const range = momentRange.range(
              moment(craneRequestDetail.craneDeliveryStart),
              moment(craneRequestDetail.craneDeliveryEnd),
            );
            let totalDays = Array.from(range.by('day'));
            const eventsArray = [];
            if (craneRequestDetail.recurrence === 'Daily') {
              const startTime = craneRequestDetail.startPicker;
              const endTime = craneRequestDetail.endPicker;
              let dailyIndex = 0;
              const recurrenceId = await concreteRequestService.insertRecurrenceSeries(
                craneRequestDetail,
                inputData.user,
                'craneRequest',
                eventTimeZone.timezone,
              );
              while (dailyIndex < totalDays.length) {
                const data = totalDays[dailyIndex];
                if (
                  moment(data).isBetween(
                    moment(craneRequestDetail.craneDeliveryStart),
                    moment(craneRequestDetail.craneDeliveryEnd),
                    null,
                    '[]',
                  ) ||
                  moment(data).isSame(craneRequestDetail.craneDeliveryStart) ||
                  moment(data).isSame(craneRequestDetail.craneDeliveryEnd)
                ) {
                  id += 1;
                  const date = moment(data).format('MM/DD/YYYY');
                  const chosenTimezoneCraneDeliveryStart = moment.tz(
                    `${date} ${startTime}`,
                    'MM/DD/YYYY HH:mm',
                    eventTimeZone.timezone,
                  );
                  const chosenTimezoneCraneDeliveryEnd = moment.tz(
                    `${date} ${endTime}`,
                    'MM/DD/YYYY HH:mm',
                    eventTimeZone.timezone,
                  );
                  const craneDeliveryStart = chosenTimezoneCraneDeliveryStart
                    .clone()
                    .tz('UTC')
                    .format('YYYY-MM-DD HH:mm:ssZ');
                  const craneDeliveryEnd = chosenTimezoneCraneDeliveryEnd
                    .clone()
                    .tz('UTC')
                    .format('YYYY-MM-DD HH:mm:ssZ');
                  craneRequestParam = {
                    description: craneRequestDetail.description,
                    isEscortNeeded: craneRequestDetail.isEscortNeeded,
                    LocationId: craneRequestDetail.LocationId,
                    additionalNotes: craneRequestDetail.additionalNotes,
                    CraneRequestId: id,
                    craneDeliveryStart,
                    craneDeliveryEnd,
                    ProjectId: craneRequestDetail.ProjectId,
                    createdBy: memberDetails.id,
                    isAssociatedWithDeliveryRequest:
                      craneRequestDetail.isAssociatedWithDeliveryRequest,
                    pickUpLocation: craneRequestDetail.pickUpLocation,
                    dropOffLocation: craneRequestDetail.dropOffLocation,
                    recurrenceId,
                    isCreatedByGuestUser: true,
                  };
                  if (
                    memberDetails.RoleId === roleDetails.id ||
                    memberDetails.RoleId === accountRoleDetails.id ||
                    memberDetails.isAutoApproveEnabled ||
                    projectDetails.ProjectSettings.isAutoApprovalEnabled
                  ) {
                    craneRequestParam.status = 'Approved';
                    craneRequestParam.approvedBy = memberDetails.id;
                    craneRequestParam.approved_at = new Date();
                  }
                  eventsArray.push(craneRequestParam);
                  // eslint-disable-next-line no-const-assign
                  dailyIndex += +craneRequestDetail.repeatEveryCount;
                }
              }
              if (eventsArray && eventsArray.length > 0) {
                const isOverlapping = await craneRequestService.checkDoubleBookingAllowedOrNot(
                  eventsArray,
                  projectDetails,
                  'add',
                );
                if (isOverlapping && isOverlapping.error) {
                  return done(null, {
                    message: isOverlapping.message,
                  });
                }
              }
            }
            if (craneRequestDetail.recurrence === 'Weekly') {
              const startTime = craneRequestDetail.startPicker;
              const endTime = craneRequestDetail.endPicker;
              const startDayWeek = moment(craneRequestDetail.craneDeliveryStart).startOf('week');
              const endDayWeek = moment(craneRequestDetail.craneDeliveryEnd).endOf('week');
              const range1 = momentRange.range(moment(startDayWeek), moment(endDayWeek));
              const totalDaysOfRecurrence = Array.from(range1.by('day'));
              const recurrenceId = await concreteRequestService.insertRecurrenceSeries(
                craneRequestDetail,
                inputData.user,
                'craneRequest',
                eventTimeZone.timezone,
              );
              totalDays = totalDaysOfRecurrence;
              let count;
              let weekIncrement;
              if (+craneRequestDetail.repeatEveryCount > 1) {
                count = +craneRequestDetail.repeatEveryCount - 1;
                weekIncrement = 7;
              } else {
                count = 1;
                weekIncrement = 0;
              }
              for (let indexba = 0; indexba < totalDays.length; indexba += weekIncrement * count) {
                const totalLength = indexba + 6;
                for (let indexb = indexba; indexb <= totalLength; indexb += 1) {
                  const data = totalDays[indexb];
                  indexba += 1;
                  if (
                    data &&
                    !moment(data).isBefore(craneRequestDetail.craneDeliveryStart) &&
                    !moment(data).isAfter(craneRequestDetail.craneDeliveryEnd)
                  ) {
                    const day = moment(data).format('dddd');
                    const indexVal = craneRequestDetail.days.includes(day);
                    if (indexVal) {
                      id += 1;
                      const date = moment(data).format('MM/DD/YYYY');
                      const chosenTimezoneCraneDeliveryStart = moment.tz(
                        `${date} ${startTime}`,
                        'MM/DD/YYYY HH:mm',
                        eventTimeZone.timezone,
                      );
                      const chosenTimezoneCraneDeliveryEnd = moment.tz(
                        `${date} ${endTime}`,
                        'MM/DD/YYYY HH:mm',
                        eventTimeZone.timezone,
                      );
                      const craneDeliveryStart = chosenTimezoneCraneDeliveryStart
                        .clone()
                        .tz('UTC')
                        .format('YYYY-MM-DD HH:mm:ssZ');
                      const craneDeliveryEnd = chosenTimezoneCraneDeliveryEnd
                        .clone()
                        .tz('UTC')
                        .format('YYYY-MM-DD HH:mm:ssZ');
                      craneRequestParam = {
                        description: craneRequestDetail.description,
                        isEscortNeeded: craneRequestDetail.isEscortNeeded,
                        additionalNotes: craneRequestDetail.additionalNotes,
                        CraneRequestId: id,
                        craneDeliveryStart,
                        craneDeliveryEnd,
                        ProjectId: craneRequestDetail.ProjectId,
                        createdBy: memberDetails.id,
                        isAssociatedWithDeliveryRequest:
                          craneRequestDetail.isAssociatedWithDeliveryRequest,
                        pickUpLocation: craneRequestDetail.pickUpLocation,
                        dropOffLocation: craneRequestDetail.dropOffLocation,
                        recurrenceId,
                        LocationId: craneRequestDetail.LocationId,
                        isCreatedByGuestUser: true,
                      };
                      if (
                        memberDetails.RoleId === roleDetails.id ||
                        memberDetails.RoleId === accountRoleDetails.id ||
                        memberDetails.isAutoApproveEnabled ||
                        projectDetails.ProjectSettings.isAutoApprovalEnabled
                      ) {
                        craneRequestParam.status = 'Approved';
                        craneRequestParam.approvedBy = memberDetails.id;
                        craneRequestParam.approved_at = new Date();
                      }
                      eventsArray.push(craneRequestParam);
                    }
                  }
                }
              }
              if (eventsArray && eventsArray.length > 0) {
                const isOverlapping = await craneRequestService.checkDoubleBookingAllowedOrNot(
                  eventsArray,
                  projectDetails,
                  'add',
                );
                if (isOverlapping && isOverlapping.error) {
                  return done(null, {
                    message: isOverlapping.message,
                  });
                }
              }
            }
            if (craneRequestDetail.recurrence === 'Monthly') {
              const startTime = craneRequestDetail.startPicker;
              const endTime = craneRequestDetail.endPicker;
              const startMonth = moment(craneRequestDetail.craneDeliveryStart).startOf('month');
              const startMonthNumber = moment(startMonth).format('MM');
              const endMonth = moment(craneRequestDetail.craneDeliveryEnd).endOf('month');
              const endMonthNumber = moment(endMonth).format('MM');
              let startDate1 = moment(craneRequestDetail.craneDeliveryStart);
              const endDate1 = moment(craneRequestDetail.craneDeliveryEnd).endOf('month');
              const allMonthsInPeriod = [];
              const recurrenceId = await concreteRequestService.insertRecurrenceSeries(
                craneRequestDetail,
                inputData.user,
                'craneRequest',
                eventTimeZone.timezone,
              );
              while (startDate1.isBefore(endDate1)) {
                allMonthsInPeriod.push(startDate1.format('YYYY-MM'));
                startDate1 = startDate1.add(1, 'month');
              }
              let currentMonthDates = [];
              let totalNumberOfMonths = endMonthNumber - startMonthNumber;
              if (totalNumberOfMonths < 0) {
                totalNumberOfMonths *= -1;
              }
              let k = 0;
              while (k < allMonthsInPeriod.length + 1) {
                currentMonthDates = Array.from(
                  { length: moment(allMonthsInPeriod[k], 'YYYY-MM').daysInMonth() },
                  (x, j) => moment(allMonthsInPeriod[k], 'YYYY-MM').startOf('month').add(j, 'days'),
                );
                if (craneRequestDetail.chosenDateOfMonth) {
                  const getDate = currentMonthDates.filter(
                    (value) => moment(value).format('DD') === craneRequestDetail.dateOfMonth,
                  );
                  if (getDate.length === 1) {
                    if (
                      moment(getDate[0]).isBetween(
                        moment(craneRequestDetail.craneDeliveryStart),
                        moment(craneRequestDetail.craneDeliveryEnd),
                        null,
                        '[]',
                      ) ||
                      moment(getDate[0]).isSame(craneRequestDetail.craneDeliveryStart) ||
                      moment(getDate[0]).isSame(craneRequestDetail.craneDeliveryEnd)
                    ) {
                      id += 1;
                      const date = moment(getDate[0].toDate()).format('MM/DD/YYYY');
                      const chosenTimezoneCraneDeliveryStart = moment.tz(
                        `${date} ${startTime}`,
                        'MM/DD/YYYY HH:mm',
                        eventTimeZone.timezone,
                      );
                      const chosenTimezoneCraneDeliveryEnd = moment.tz(
                        `${date} ${endTime}`,
                        'MM/DD/YYYY HH:mm',
                        eventTimeZone.timezone,
                      );
                      const craneDeliveryStart = chosenTimezoneCraneDeliveryStart
                        .clone()
                        .tz('UTC')
                        .format('YYYY-MM-DD HH:mm:ssZ');
                      const craneDeliveryEnd = chosenTimezoneCraneDeliveryEnd
                        .clone()
                        .tz('UTC')
                        .format('YYYY-MM-DD HH:mm:ssZ');
                      craneRequestParam = {
                        description: craneRequestDetail.description,
                        isEscortNeeded: craneRequestDetail.isEscortNeeded,
                        additionalNotes: craneRequestDetail.additionalNotes,
                        CraneRequestId: id,
                        craneDeliveryStart,
                        craneDeliveryEnd,
                        ProjectId: craneRequestDetail.ProjectId,
                        createdBy: memberDetails.id,
                        isAssociatedWithDeliveryRequest:
                          craneRequestDetail.isAssociatedWithDeliveryRequest,
                        pickUpLocation: craneRequestDetail.pickUpLocation,
                        dropOffLocation: craneRequestDetail.dropOffLocation,
                        recurrenceId,
                        LocationId: craneRequestDetail.LocationId,
                        isCreatedByGuestUser: true,
                      };
                      if (
                        memberDetails.RoleId === roleDetails.id ||
                        memberDetails.RoleId === accountRoleDetails.id ||
                        memberDetails.isAutoApproveEnabled ||
                        projectDetails.ProjectSettings.isAutoApprovalEnabled
                      ) {
                        craneRequestParam.status = 'Approved';
                        craneRequestParam.approvedBy = memberDetails.id;
                        craneRequestParam.approved_at = new Date();
                      }
                      eventsArray.push(craneRequestParam);
                    }
                  }
                } else if (allMonthsInPeriod[k]) {
                  const dayOfMonth = craneRequestDetail.monthlyRepeatType;
                  const week = dayOfMonth.split(' ')[0].toLowerCase();
                  const day = dayOfMonth.split(' ')[1].toLowerCase();
                  const chosenDay = moment(allMonthsInPeriod[k], 'YYYY-MM')
                    .startOf('month')
                    .day(day);
                  const getAllDays = [];
                  if (chosenDay.date() > 7) chosenDay.add(7, 'd');
                  const month = chosenDay.month();
                  while (month === chosenDay.month()) {
                    getAllDays.push(chosenDay.toString());
                    chosenDay.add(7, 'd');
                  }
                  let i = 0;
                  if (week === 'second') {
                    i += 1;
                  } else if (week === 'third') {
                    i += 2;
                  } else if (week === 'fourth') {
                    i += 3;
                  } else if (week === 'last') {
                    i = getAllDays.length - 1;
                  }
                  const finalDay = getAllDays[i];
                  if (
                    moment(finalDay).isBetween(
                      moment(craneRequestDetail.craneDeliveryStart),
                      moment(craneRequestDetail.craneDeliveryEnd),
                      null,
                      '[]',
                    ) ||
                    moment(finalDay).isSame(craneRequestDetail.craneDeliveryStart) ||
                    moment(finalDay).isSame(craneRequestDetail.craneDeliveryEnd)
                  ) {
                    id += 1;
                    const date = moment(finalDay).format('MM/DD/YYYY');
                    const chosenTimezoneCraneDeliveryStart = moment.tz(
                      `${date} ${startTime}`,
                      'MM/DD/YYYY HH:mm',
                      eventTimeZone.timezone,
                    );
                    const chosenTimezoneCraneDeliveryEnd = moment.tz(
                      `${date} ${endTime}`,
                      'MM/DD/YYYY HH:mm',
                      eventTimeZone.timezone,
                    );
                    const craneDeliveryStart = chosenTimezoneCraneDeliveryStart
                      .clone()
                      .tz('UTC')
                      .format('YYYY-MM-DD HH:mm:ssZ');
                    const craneDeliveryEnd = chosenTimezoneCraneDeliveryEnd
                      .clone()
                      .tz('UTC')
                      .format('YYYY-MM-DD HH:mm:ssZ');
                    craneRequestParam = {
                      description: craneRequestDetail.description,
                      isEscortNeeded: craneRequestDetail.isEscortNeeded,
                      additionalNotes: craneRequestDetail.additionalNotes,
                      CraneRequestId: id,
                      craneDeliveryStart,
                      craneDeliveryEnd,
                      ProjectId: craneRequestDetail.ProjectId,
                      createdBy: memberDetails.id,
                      isAssociatedWithDeliveryRequest:
                        craneRequestDetail.isAssociatedWithDeliveryRequest,
                      pickUpLocation: craneRequestDetail.pickUpLocation,
                      dropOffLocation: craneRequestDetail.dropOffLocation,
                      recurrenceId,
                      LocationId: craneRequestDetail.LocationId,
                      isCreatedByGuestUser: true,
                    };
                    if (
                      memberDetails.RoleId === roleDetails.id ||
                      memberDetails.RoleId === accountRoleDetails.id ||
                      memberDetails.isAutoApproveEnabled ||
                      projectDetails.ProjectSettings.isAutoApprovalEnabled
                    ) {
                      craneRequestParam.status = 'Approved';
                      craneRequestParam.approvedBy = memberDetails.id;
                      craneRequestParam.approved_at = new Date();
                    }
                    eventsArray.push(craneRequestParam);
                  }
                }
                k += +craneRequestDetail.repeatEveryCount;
              }
              if (eventsArray && eventsArray.length > 0) {
                const isOverlapping = await craneRequestService.checkDoubleBookingAllowedOrNot(
                  eventsArray,
                  projectDetails,
                  'add',
                );
                if (isOverlapping && isOverlapping.error) {
                  return done(null, {
                    message: isOverlapping.message,
                  });
                }
              }
            }
            if (craneRequestDetail.recurrence === 'Yearly') {
              const startTime = craneRequestDetail.startPicker;
              const endTime = craneRequestDetail.endPicker;
              const startMonth = moment(craneRequestDetail.craneDeliveryStart).startOf('month');
              const startMonthNumber = moment(startMonth).format('MM');
              const endMonth = moment(craneRequestDetail.craneDeliveryEnd).endOf('month');
              const endMonthNumber = moment(endMonth).format('MM');
              let startDate1 = moment(craneRequestDetail.craneDeliveryStart);
              const endDate1 = moment(craneRequestDetail.craneDeliveryEnd).endOf('month');
              const allMonthsInPeriod = [];
              const recurrenceId = await concreteRequestService.insertRecurrenceSeries(
                craneRequestDetail,
                inputData.user,
                'craneRequest',
                eventTimeZone.timezone,
              );
              while (startDate1.isBefore(endDate1)) {
                allMonthsInPeriod.push(startDate1.format('YYYY-MM'));
                startDate1 = startDate1.add(12, 'month');
              }
              let currentMonthDates = [];
              let totalNumberOfMonths = endMonthNumber - startMonthNumber;
              if (totalNumberOfMonths < 0) {
                totalNumberOfMonths *= -1;
              }
              for (let k = 0; k < allMonthsInPeriod.length + 1; k += 1) {
                currentMonthDates = Array.from(
                  { length: moment(allMonthsInPeriod[k], 'YYYY-MM').daysInMonth() },
                  (x, j) => moment(allMonthsInPeriod[k], 'YYYY-MM').startOf('month').add(j, 'days'),
                );
                if (craneRequestDetail.chosenDateOfMonth) {
                  const getDate = currentMonthDates.filter(
                    (value) => moment(value).format('DD') === craneRequestDetail.dateOfMonth,
                  );
                  if (getDate.length === 1) {
                    if (
                      moment(getDate[0]).isBetween(
                        moment(craneRequestDetail.craneDeliveryStart),
                        moment(craneRequestDetail.craneDeliveryEnd),
                        null,
                        '[]',
                      ) ||
                      moment(getDate[0]).isSame(craneRequestDetail.craneDeliveryStart) ||
                      moment(getDate[0]).isSame(craneRequestDetail.craneDeliveryEnd)
                    ) {
                      id += 1;
                      const date = moment(getDate[0].toDate()).format('MM/DD/YYYY');
                      const chosenTimezoneCraneDeliveryStart = moment.tz(
                        `${date} ${startTime}`,
                        'MM/DD/YYYY HH:mm',
                        eventTimeZone.timezone,
                      );
                      const chosenTimezoneCraneDeliveryEnd = moment.tz(
                        `${date} ${endTime}`,
                        'MM/DD/YYYY HH:mm',
                        eventTimeZone.timezone,
                      );
                      const craneDeliveryStart = chosenTimezoneCraneDeliveryStart
                        .clone()
                        .tz('UTC')
                        .format('YYYY-MM-DD HH:mm:ssZ');
                      const craneDeliveryEnd = chosenTimezoneCraneDeliveryEnd
                        .clone()
                        .tz('UTC')
                        .format('YYYY-MM-DD HH:mm:ssZ');
                      craneRequestParam = {
                        description: craneRequestDetail.description,
                        isEscortNeeded: craneRequestDetail.isEscortNeeded,
                        additionalNotes: craneRequestDetail.additionalNotes,
                        CraneRequestId: id,
                        craneDeliveryStart,
                        craneDeliveryEnd,
                        ProjectId: craneRequestDetail.ProjectId,
                        createdBy: memberDetails.id,
                        isAssociatedWithDeliveryRequest:
                          craneRequestDetail.isAssociatedWithDeliveryRequest,
                        pickUpLocation: craneRequestDetail.pickUpLocation,
                        dropOffLocation: craneRequestDetail.dropOffLocation,
                        recurrenceId,
                        LocationId: craneRequestDetail.LocationId,
                        isCreatedByGuestUser: true,
                      };
                      if (
                        memberDetails.RoleId === roleDetails.id ||
                        memberDetails.RoleId === accountRoleDetails.id ||
                        memberDetails.isAutoApproveEnabled ||
                        projectDetails.ProjectSettings.isAutoApprovalEnabled
                      ) {
                        craneRequestParam.status = 'Approved';
                        craneRequestParam.approvedBy = memberDetails.id;
                        craneRequestParam.approved_at = new Date();
                      }
                      eventsArray.push(craneRequestParam);
                    }
                  }
                } else if (allMonthsInPeriod[k]) {
                  const dayOfMonth = craneRequestDetail.monthlyRepeatType;
                  const week = dayOfMonth.split(' ')[0].toLowerCase();
                  const day = dayOfMonth.split(' ')[1].toLowerCase();
                  const chosenDay = moment(allMonthsInPeriod[k], 'YYYY-MM')
                    .startOf('month')
                    .day(day);
                  const getAllDays = [];
                  if (chosenDay.date() > 7) chosenDay.add(7, 'd');
                  const month = chosenDay.month();
                  while (month === chosenDay.month()) {
                    getAllDays.push(chosenDay.toString());
                    chosenDay.add(7, 'd');
                  }
                  let i = 0;
                  if (week === 'second') {
                    i += 1;
                  } else if (week === 'third') {
                    i += 2;
                  } else if (week === 'fourth') {
                    i += 3;
                  } else if (week === 'last') {
                    i = getAllDays.length - 1;
                  }
                  const finalDay = getAllDays[i];
                  if (
                    moment(finalDay).isBetween(
                      moment(craneRequestDetail.craneDeliveryStart),
                      moment(craneRequestDetail.craneDeliveryEnd),
                      null,
                      '[]',
                    ) ||
                    moment(finalDay).isSame(craneRequestDetail.craneDeliveryStart) ||
                    moment(finalDay).isSame(craneRequestDetail.craneDeliveryEnd)
                  ) {
                    id += 1;
                    const date = moment(finalDay).format('MM/DD/YYYY');
                    const chosenTimezoneCraneDeliveryStart = moment.tz(
                      `${date} ${startTime}`,
                      'MM/DD/YYYY HH:mm',
                      eventTimeZone.timezone,
                    );
                    const chosenTimezoneCraneDeliveryEnd = moment.tz(
                      `${date} ${endTime}`,
                      'MM/DD/YYYY HH:mm',
                      eventTimeZone.timezone,
                    );
                    const craneDeliveryStart = chosenTimezoneCraneDeliveryStart
                      .clone()
                      .tz('UTC')
                      .format('YYYY-MM-DD HH:mm:ssZ');
                    const craneDeliveryEnd = chosenTimezoneCraneDeliveryEnd
                      .clone()
                      .tz('UTC')
                      .format('YYYY-MM-DD HH:mm:ssZ');
                    craneRequestParam = {
                      description: craneRequestDetail.description,
                      isEscortNeeded: craneRequestDetail.isEscortNeeded,
                      additionalNotes: craneRequestDetail.additionalNotes,
                      CraneRequestId: id,
                      craneDeliveryStart,
                      craneDeliveryEnd,
                      ProjectId: craneRequestDetail.ProjectId,
                      createdBy: memberDetails.id,
                      isAssociatedWithDeliveryRequest:
                        craneRequestDetail.isAssociatedWithDeliveryRequest,
                      pickUpLocation: craneRequestDetail.pickUpLocation,
                      dropOffLocation: craneRequestDetail.dropOffLocation,
                      recurrenceId,
                      LocationId: craneRequestDetail.LocationId,
                      isCreatedByGuestUser: true,
                    };
                    if (
                      memberDetails.RoleId === roleDetails.id ||
                      memberDetails.RoleId === accountRoleDetails.id ||
                      memberDetails.isAutoApproveEnabled ||
                      projectDetails.ProjectSettings.isAutoApprovalEnabled
                    ) {
                      craneRequestParam.status = 'Approved';
                      craneRequestParam.approvedBy = memberDetails.id;
                      craneRequestParam.approved_at = new Date();
                    }
                    eventsArray.push(craneRequestParam);
                  }
                }
              }
              if (eventsArray && eventsArray.length > 0) {
                const isOverlapping = await craneRequestService.checkDoubleBookingAllowedOrNot(
                  eventsArray,
                  projectDetails,
                  'add',
                );
                if (isOverlapping && isOverlapping.error) {
                  return done(null, {
                    message: isOverlapping.message,
                  });
                }
              }
            }
            if (craneRequestDetail.recurrence === 'Does Not Repeat') {
              id += 1;
              const chosenTimezoneCraneDeliveryStart = moment.tz(
                `${craneRequestDetail.craneDeliveryStart} ${craneRequestDetail.startPicker}`,
                'YYYY MM DD 00:00:00 HH:mm',
                eventTimeZone.timezone,
              );
              const chosenTimezoneCraneDeliveryEnd = moment.tz(
                `${craneRequestDetail.craneDeliveryEnd} ${craneRequestDetail.endPicker}`,
                'YYYY MM DD 00:00:00 HH:mm',
                eventTimeZone.timezone,
              );
              const craneDeliveryStart = chosenTimezoneCraneDeliveryStart
                .clone()
                .tz('UTC')
                .format('YYYY-MM-DD HH:mm:ssZ');
              const craneDeliveryEnd = chosenTimezoneCraneDeliveryEnd
                .clone()
                .tz('UTC')
                .format('YYYY-MM-DD HH:mm:ssZ');
              const recurrenceId = await concreteRequestService.insertRecurrenceSeries(
                craneRequestDetail,
                inputData.user,
                'craneRequest',
                eventTimeZone.timezone,
              );
              craneRequestParam = {
                description: craneRequestDetail.description,
                isEscortNeeded: craneRequestDetail.isEscortNeeded,
                additionalNotes: craneRequestDetail.additionalNotes,
                CraneRequestId: id,
                craneDeliveryStart,
                craneDeliveryEnd,
                ProjectId: craneRequestDetail.ProjectId,
                createdBy: memberDetails.id,
                isAssociatedWithDeliveryRequest: craneRequestDetail.isAssociatedWithDeliveryRequest,
                pickUpLocation: craneRequestDetail.pickUpLocation,
                dropOffLocation: craneRequestDetail.dropOffLocation,
                recurrenceId,
                LocationId: craneRequestDetail.LocationId,
                isCreatedByGuestUser: true,
              };
              if (
                memberDetails.RoleId === roleDetails.id ||
                memberDetails.RoleId === accountRoleDetails.id ||
                memberDetails.isAutoApproveEnabled ||
                projectDetails.ProjectSettings.isAutoApprovalEnabled
              ) {
                craneRequestParam.status = 'Approved';
                craneRequestParam.approvedBy = memberDetails.id;
                craneRequestParam.approved_at = new Date();
              }
              eventsArray.push(craneRequestParam);
              if (eventsArray && eventsArray.length > 0) {
                const isOverlapping = await craneRequestService.checkDoubleBookingAllowedOrNot(
                  eventsArray,
                  projectDetails,
                  'add',
                );
                if (isOverlapping && isOverlapping.error) {
                  return done(null, {
                    message: isOverlapping.message,
                  });
                }
              }
            }
            let newCraneRequestData = {};
            if (eventsArray.length > 0) {
              for (let i = 0; i < eventsArray.length; i += 1) {
                newCraneRequestData = await CraneRequest.createInstance(eventsArray[i]);
                const { companies, responsiblePersons, definableFeatureOfWorks } =
                  craneRequestDetail;
                const equipments = craneRequestDetail.EquipmentId;
                const updateParam = {
                  CraneRequestId: newCraneRequestData.id,
                  CraneRequestCode: newCraneRequestData.CraneRequestId,
                  ProjectId: craneRequestDetail.ProjectId,
                };
                companies.forEach(async (element) => {
                  const companyParam = updateParam;
                  companyParam.CompanyId = element;
                  await CraneRequestCompany.createInstance(companyParam);
                });
                equipments.forEach(async (element) => {
                  const equipmentParam = updateParam;
                  equipmentParam.EquipmentId = element;
                  await CraneRequestEquipment.createInstance(equipmentParam);
                });
                responsiblePersons.forEach(async (element) => {
                  const memberParam = updateParam;
                  memberParam.MemberId = element;
                  await CraneRequestResponsiblePerson.createInstance(memberParam);
                });
                definableFeatureOfWorks.forEach(async (element) => {
                  const defineParam = updateParam;
                  defineParam.DeliverDefineWorkId = element;
                  await CraneRequestDefinableFeatureOfWork.createInstance(defineParam);
                });
                const history = {
                  CraneRequestId: newCraneRequestData.id,
                  MemberId: memberDetails.id,
                  type: 'create',
                  description: `${loginUser.firstName} ${loginUser.lastName} Created Crane Booking, ${craneRequestDetail.description}.`,
                };
                const notification = history;
                notification.ProjectId = eventsArray[i].ProjectId;
                notification.title = 'Crane Booking Creation';
                await CraneRequestHistory.createInstance(history);
                if (newCraneRequestData.status === 'Approved') {
                  const object = {
                    ProjectId: craneRequestDetail.ProjectId,
                    MemberId: memberDetails.id,
                    CraneRequestId: newCraneRequestData.id,
                    isDeleted: false,
                    type: 'approved',
                    description: `${loginUser.firstName} ${loginUser.lastName} Approved Crane Booking, ${craneRequestDetail.description}.`,
                  };
                  await CraneRequestHistory.createInstance(object);
                }
              }
            }
            if (
              Object.keys(newCraneRequestData).length > 0 &&
              typeof newCraneRequestData === 'object'
            ) {
              const { responsiblePersons } = craneRequestDetail;
              const locationChosen = await Locations.findOne({
                where: {
                  ProjectId: craneRequestDetail.ProjectId,
                  id: craneRequestDetail.LocationId,
                },
              });
              const history = {
                CraneRequestId: newCraneRequestData.id,
                MemberId: memberDetails.id,
                type: 'create',
                description: `${loginUser.firstName} ${loginUser.lastName} Created Crane Booking, ${craneRequestDetail.description}.`,
                locationFollowDescription: `${loginUser.firstName} ${loginUser.lastName} Created Crane Booking, ${craneRequestDetail.description}. Location: ${locationChosen.locationPath}.`,
                memberData: [],
              };
              const notification = history;
              notification.ProjectId = craneRequestDetail.ProjectId;
              notification.LocationId = craneRequestDetail.LocationId;
              notification.title = 'Crane Booking Creation';
              notification.isDeliveryRequest = false;
              notification.requestType = 'craneRequest';
              notification.recurrenceType = `${craneRequestDetail.recurrence} From ${moment(
                craneRequestDetail.craneDeliveryStart,
              ).format('MM/DD/YYYY')} to ${moment(craneRequestDetail.craneDeliveryEnd).format(
                'MM/DD/YYYY',
              )}`;
              const newNotification = await Notification.createInstance(notification);
              const memberLocationPreference = await LocationNotificationPreferences.findAll({
                where: {
                  ProjectId: craneRequestDetail.ProjectId,
                  LocationId: craneRequestDetail.LocationId,
                  follow: true,
                },
                include: [
                  {
                    association: 'Member',
                    attributes: ['id', 'RoleId'],
                    where: {
                      [Op.and]: [
                        {
                          id: { [Op.ne]: memberDetails.id },
                        },
                      ],
                    },
                    include: [
                      {
                        association: 'User',
                        attributes: ['id', 'firstName', 'lastName', 'email'],
                      },
                    ],
                  },
                ],
              });
              const locationFollowMembers = [];
              memberLocationPreference.forEach(async (element) => {
                locationFollowMembers.push(element.Member.id);
              });
              const personData = await CraneRequestResponsiblePerson.findAll({
                where: { CraneRequestId: newCraneRequestData.id, isDeleted: false },
                include: [
                  {
                    association: 'Member',
                    include: [
                      {
                        association: 'User',
                        attributes: ['id', 'firstName', 'lastName', 'email'],
                      },
                    ],
                    where: {
                      [Op.and]: {
                        RoleId: {
                          [Op.notIn]: [1, 2],
                        },
                        id: { [Op.notIn]: locationFollowMembers },
                      },
                    },
                    attributes: ['id', 'RoleId'],
                  },
                ],
                attributes: ['id'],
              });
              const adminData = await Member.findAll({
                where: {
                  [Op.and]: [
                    { ProjectId: craneRequestDetail.ProjectId },
                    { isDeleted: false },
                    { id: { [Op.in]: responsiblePersons } },
                    { id: { [Op.ne]: newNotification.MemberId } },
                    { id: { [Op.notIn]: locationFollowMembers } },
                  ],
                },
                include: [
                  {
                    association: 'User',
                    attributes: ['id', 'firstName', 'lastName', 'email'],
                  },
                ],
                attributes: ['id', 'RoleId'],
              });
              if (memberLocationPreference && memberLocationPreference.length > 0) {
                // here 3-(NotificationPreferenceItemId - When a new delivery/crane/concrete request added to the project)
                await pushNotification.sendMemberLocationPreferencePushNotificationForCrane(
                  memberLocationPreference,
                  newCraneRequestData.CraneRequestId,
                  history.locationFollowDescription,
                  newCraneRequestData.requestType,
                  newCraneRequestData.ProjectId,
                  newCraneRequestData.id,
                  3,
                );
                // here 3-(NotificationPreferenceItemId - When a new delivery/crane/concrete request added to the project)
                await notificationHelper.createMemberDeliveryLocationInAppNotification(
                  DeliveryPersonNotification,
                  craneRequestDetail.ProjectId,
                  newNotification.id,
                  memberLocationPreference,
                  3,
                );
              }
              history.memberData = personData;
              history.adminData = adminData;
              history.firstName = loginUser.firstName;
              history.profilePic = loginUser.profilePic;
              history.createdAt = new Date();
              history.ProjectId = craneRequestDetail.ProjectId;
              history.projectName = projectDetails.projectName;
              // here 3-(NotificationPreferenceItemId - When a new delivery/crane/concrete request added to the project)
              await notificationHelper.createDeliveryPersonNotification(
                adminData,
                [],
                projectDetails,
                newNotification,
                DeliveryPersonNotification,
                memberDetails,
                loginUser,
                3,
                'created a',
                'Crane Request',
                `crane Booking (${newCraneRequestData.CraneRequestId} - ${newCraneRequestData.description})`,
                newCraneRequestData.CraneRequestId,
              );
              const checkMemberNotification = await NotificationPreference.findAll({
                where: {
                  ProjectId: craneRequestDetail.ProjectId,
                  isDeleted: false,
                },
                attributes: [
                  'id',
                  'MemberId',
                  'ProjectId',
                  'ParentCompanyId',
                  'NotificationPreferenceItemId',
                  'instant',
                  'dailyDigest',
                ],
                include: [
                  {
                    association: 'NotificationPreferenceItem',
                    where: {
                      id: 3,
                      isDeleted: false,
                    },
                    attributes: ['id', 'description', 'inappNotification', 'emailNotification'],
                  },
                ],
              });
              history.notificationPreference = checkMemberNotification;
              // here 3-(NotificationPreferenceItemId - When a new delivery/crane/concrete request added to the project)
              await pushNotification.sendPushNotificationForCrane(
                history,
                3,
                craneRequestDetail.ProjectId,
              );
              history.CraneRequestId = newCraneRequestData.CraneRequestId;
              await craneRequestService.sendEmailNotificationToUser(
                history,
                memberDetails,
                loginUser,
                newCraneRequestData,
                craneRequestDetail,
                memberLocationPreference,
              );
              const memberLocationPreferenceNotify = await LocationNotificationPreferences.findAll({
                where: {
                  ProjectId: craneRequestDetail.ProjectId,
                  LocationId: craneRequestDetail.LocationId,
                  follow: true,
                },
                include: [
                  {
                    association: 'Member',
                    attributes: ['id', 'RoleId'],
                    where: {
                      [Op.and]: [
                        {
                          id: { [Op.ne]: memberDetails.id },
                        },
                      ],
                    },
                    include: [
                      {
                        association: 'User',
                        attributes: ['id', 'firstName', 'lastName', 'email'],
                      },
                    ],
                  },
                ],
              });
              if (
                +memberDetails.RoleId === 4 ||
                +memberDetails.RoleId === 3 ||
                +memberDetails.RoleId === 7
              ) {
                const userEmails = await craneRequestService.getMemberDetailData(
                  history,
                  memberLocationPreference,
                );
                if (userEmails.length > 0) {
                  userEmails.forEach(async (element) => {
                    if (+element.RoleId === 2) {
                      let name;
                      if (!element.firstName) {
                        name = 'user';
                      } else {
                        name = `${element.firstName} ${element.lastName}`;
                      }
                      const memberRole = await Role.findOne({
                        where: {
                          id: memberDetails.RoleId,
                          isDeleted: false,
                        },
                      });
                      const mailPayload = {
                        name,
                        email: element.email,
                        content: `We would like to inform you that 
                          ${loginUser.firstName} ${loginUser.lastName} - ${memberRole.roleName} has created a crane booking ${newCraneRequestData.CraneRequestId} and waiting for your approval.Kindly review the booking and update the status.`,
                      };
                      const isMemberFollowLocation = await LocationNotificationPreferences.findOne({
                        where: {
                          MemberId: +element.MemberId,
                          ProjectId: +craneRequestDetail.ProjectId,
                          LocationId: +craneRequestDetail.LocationId,
                          isDeleted: false,
                          // follow: true,
                        },
                      });
                      if (isMemberFollowLocation) {
                        const memberNotification = await NotificationPreference.findOne({
                          where: {
                            MemberId: +element.MemberId,
                            ProjectId: +craneRequestDetail.ProjectId,
                            isDeleted: false,
                          },
                          include: [
                            {
                              association: 'NotificationPreferenceItem',
                              where: {
                                id: 8,
                                isDeleted: false,
                              },
                            },
                          ],
                        });

                        if (memberNotification && memberNotification.instant) {
                          await MAILER.sendMail(
                            mailPayload,
                            'notifyPAForApproval',
                            `Crane Booking created by ${loginUser.firstName} ${loginUser.lastName} - ${memberRole.roleName}`,
                            `Crane Booking created by ${loginUser.firstName} ${loginUser.lastName} - ${memberRole.roleName}`,
                            async (info, err) => {
                              console.log(info, err);
                            },
                          );
                        }
                        if (memberNotification && memberNotification.dailyDigest) {
                          await craneRequestService.createDailyDigestDataApproval(
                            +memberDetails.RoleId,
                            +element.MemberId,
                            +craneRequestDetail.ProjectId,
                            +craneRequestDetail.ParentCompanyId,
                            loginUser,
                            'created a',
                            'Crane Request',
                            `crane Booking (${newCraneRequestData.CraneRequestId} - ${newCraneRequestData.description})`,
                            'and waiting for your approval',
                            newCraneRequestData.CraneRequestId,
                          );
                        }
                      }
                    }
                  });
                  if (memberLocationPreferenceNotify && memberLocationPreferenceNotify.length > 0) {
                    history.memberData.push(...memberLocationPreferenceNotify);
                  }
                  return done(history, false);
                }
                if (memberLocationPreferenceNotify && memberLocationPreferenceNotify.length > 0) {
                  history.memberData.push(...memberLocationPreferenceNotify);
                }
                return done(history, false);
              }
              if (memberLocationPreferenceNotify && memberLocationPreferenceNotify.length > 0) {
                history.memberData.push(...memberLocationPreferenceNotify);
              }
              return done(history, false);
            }
            return done(null, {
              message: 'Bookings will not be created for the scheduled date/time',
            });
          }
          return done(null, {
            message: 'You are not allowed create Crane Booking for this project.',
          });
        });
      } else {
        return done(null, { message: 'Project does not exist.' });
      }
    } catch (e) {
      done(null, e);
    }
  },
  async checkCraneInputDatas(inputData, done) {
    // await this.getDynamicModel(inputData);
    const craneRequestData = inputData.body;
    const { companies, responsiblePersons, definableFeatureOfWorks } = craneRequestData;
    const equipments = craneRequestData.EquipmentId;
    const inputProjectId = craneRequestData.ProjectId;
    const memberList = await Member.count({
      where: { id: { [Op.in]: responsiblePersons }, ProjectId: inputProjectId, isDeleted: false },
    });
    const equipmentList = await Equipments.count({
      where: { id: { [Op.in]: equipments }, ProjectId: inputProjectId, isDeleted: false },
    });
    const defineList = await DeliverDefineWork.count({
      where: {
        id: { [Op.in]: definableFeatureOfWorks },
        ProjectId: inputProjectId,
        isDeleted: false,
      },
    });
    const companyList = await Company.count({
      where: {
        [Op.or]: [
          {
            id: { [Op.in]: companies },
            ProjectId: +inputProjectId,
            isDeleted: false,
          },
          {
            id: {
              [Op.in]: companies,
            },
            isParent: true,
            ParentCompanyId: +craneRequestData.ParentCompanyId,
            isDeleted: false,
          },
        ],
      },
    });
    if (
      craneRequestData.responsiblePersons &&
      craneRequestData.responsiblePersons.length > 0 &&
      memberList !== responsiblePersons.length
    ) {
      return done(null, { message: 'Some Member is not in the project' });
    }
    if (craneRequestData.EquipmentId && equipmentList !== equipments.length) {
      return done(null, { message: 'Mentioned Equipment is not in the project' });
    }
    if (
      craneRequestData.companies &&
      craneRequestData.companies.length > 0 &&
      companyList !== companies.length
    ) {
      return done(null, { message: 'Some Company is not in the project' });
    }
    if (
      craneRequestData.definableFeatureOfWorks &&
      craneRequestData.definableFeatureOfWorks.length > 0 &&
      defineList !== definableFeatureOfWorks.length
    ) {
      return done(null, { message: 'Some Definable Feature of Work is not in the project' });
    }
    return done(true, false);
  },
  async getConcreteDropdownDetail(req) {
    const { ProjectId } = req.query;
    const locationDetailsDropdown = await ConcreteLocation.findAll({
      where: { ProjectId, isDeleted: false },
    });
    const mixDesignDropdown = await ConcreteMixDesign.findAll({
      where: { ProjectId, isDeleted: false },
    });
    const pumpSizeDropdown = await ConcretePumpSize.findAll({
      where: { ProjectId, isDeleted: false },
    });
    // await this.getDynamicModel(req);
    const parentCompany = await Company.findOne({
      required: false,
      subQuery: false,
      attributes: [
        'id',
        'companyName',
        'website',
        'address',
        'secondAddress',
        'country',
        'city',
        'companyAutoId',
        'state',
        'zipCode',
        'scope',
        'logo',
      ],
      where: { isParent: true, ParentCompanyId: +req.query.ParentCompanyId, isDeleted: false },
    });
    const companyList = await Company.getAllCompany({
      ProjectId,
      isDeleted: false,
      isParent: { [Op.not]: true },
    });
    const newCompanyList = [];
    await companyList.rows.forEach((element) => {
      newCompanyList.push({
        id: element.id,
        companyName: element.companyName,
      });
    });
    if (parentCompany) {
      const index = newCompanyList.findIndex(
        (item) =>
          item.id === parentCompany.id ||
          item.companyName.toLowerCase() === parentCompany.companyName.toLowerCase(),
      );
      if (index === -1) {
        newCompanyList.push({
          id: parentCompany.id,
          companyName: parentCompany.companyName,
        });
      }
    }
    newCompanyList.sort((a, b) =>
      a.companyName.toLowerCase() > b.companyName.toLowerCase() ? 1 : -1,
    );
    const condition = {
      ProjectId,
      isDeleted: false,
      isActive: true,
    };
    const locationDropdown = await Locations.getLocations(condition);

    let data;
    let lastData = {};
    lastData = await ConcreteRequest.findOne({
      where: { ProjectId, isDeleted: false },
      order: [['ConcreteRequestId', 'DESC']],
    });
    if (lastData) {
      data = lastData.ConcreteRequestId + 1;
    } else {
      data = 1;
    }
    const dropdownData = {
      locationDetailsDropdown,
      locationDropdown,
      concreteSupplierDropdown: newCompanyList,
      mixDesignDropdown,
      pumpSizeDropdown,
      ConcreteRequestId: data,
    };
    return { status: 200, data: dropdownData };
  },
  async newConcreteRequest(inputData, done) {
    try {
      // await this.getDynamicModel(inputData);
      const eventTimeZone = await TimeZone.findOne({
        where: {
          isDeleted: false,
          id: +inputData.body.TimeZoneId,
        },
        attributes: [
          'id',
          'location',
          'isDayLightSavingEnabled',
          'timeZoneOffsetInMinutes',
          'dayLightSavingTimeInMinutes',
          'timezone',
        ],
      });
      if (!eventTimeZone) {
        return done(null, { message: 'Provide a valid timezone' });
      }
      const concreteRequestDetail = inputData.body;
      const userDetail = await User.findOne({
        where: { id: inputData.body.userId, isDeleted: false },
      });
      inputData.user = userDetail;
      const loginUser = inputData.user;
      const projectDetails = await Project.getProjectAndSettings({
        isDeleted: false,
        id: +concreteRequestDetail.ProjectId,
      });
      let startDate;
      let endDate;
      let pumpStartDate;
      let pumpEndDate;
      const memberDetails = await Member.getBy({
        UserId: loginUser.id,
        ProjectId: concreteRequestDetail.ProjectId,
        isActive: true,
        isDeleted: false,
      });
      if (concreteRequestDetail.recurrence) {
        startDate = await this.compareDeliveryDateWithDeliveryWindowDate(
          concreteRequestDetail.concretePlacementStart,
          concreteRequestDetail.startPicker,
          eventTimeZone.timezone,
          projectDetails.ProjectSettings.deliveryWindowTime,
          projectDetails.ProjectSettings.deliveryWindowTimeUnit,
        );
        endDate = await this.compareDeliveryDateWithDeliveryWindowDate(
          concreteRequestDetail.concretePlacementEnd,
          concreteRequestDetail.endPicker,
          eventTimeZone.timezone,
          projectDetails.ProjectSettings.deliveryWindowTime,
          projectDetails.ProjectSettings.deliveryWindowTimeUnit,
        );
      }
      if (concreteRequestDetail.startPicker === concreteRequestDetail.endPicker) {
        return done(null, {
          message: 'Placement Start Time and Anticipated Completion Time should not be the same',
        });
      }
      if (concreteRequestDetail.startPicker > concreteRequestDetail.endPicker) {
        return done(null, {
          message: 'Please enter Placement Start Time lesser than Anticipated Completion Time',
        });
      }
      if (+memberDetails.RoleId === 4 && (startDate || endDate)) {
        if (startDate || endDate) {
          if (projectDetails.ProjectSettings.deliveryWindowTime === 0) {
            if (concreteRequestDetail.recurrence === 'Does Not Repeat') {
              return done(null, { message: 'Please enter Future Concrete Placement Date/Time' });
            }
            return done(null, {
              message: 'Please enter Future Concrete Placement or Recurrence End Date/Time',
            });
          }
          return done(null, {
            message: `Bookings can not be submitted within ${projectDetails.ProjectSettings.deliveryWindowTime} ${projectDetails.ProjectSettings.deliveryWindowTimeUnit} prior to the event`,
          });
        }
      }
      if (concreteRequestDetail.isPumpRequired) {
        if (concreteRequestDetail.pumpWorkStart === concreteRequestDetail.pumpWorkEnd) {
          return done(null, {
            message: 'Pump Show up Time and Completion Time should not be the same',
          });
        }
        if (concreteRequestDetail.pumpWorkStart > concreteRequestDetail.pumpWorkEnd) {
          return done(null, {
            message: 'Please enter Pump Show up Time lesser than Pump Completion Time',
          });
        }
        pumpStartDate = await this.compareDeliveryDateWithDeliveryWindowDate(
          concreteRequestDetail.pumpOrderedDate,
          concreteRequestDetail.pumpWorkStart,
          eventTimeZone.timezone,
          projectDetails.ProjectSettings.deliveryWindowTime,
          projectDetails.ProjectSettings.deliveryWindowTimeUnit,
        );
        pumpEndDate = await this.compareDeliveryDateWithDeliveryWindowDate(
          concreteRequestDetail.pumpOrderedDate,
          concreteRequestDetail.pumpWorkEnd,
          eventTimeZone.timezone,
          projectDetails.ProjectSettings.deliveryWindowTime,
          projectDetails.ProjectSettings.deliveryWindowTimeUnit,
        );
        if (+memberDetails.RoleId === 4 && (pumpStartDate || pumpEndDate)) {
          return done(null, { message: 'Please enter Future Pump Ordered Date/Time' });
        }
      }
      if (projectDetails && projectDetails.ProjectSettings) {
        this.concreteCheckInputDatas(inputData, async (checkResponse, checkError) => {
          if (checkError) {
            return done(null, checkError);
          }
          if (memberDetails) {
            const roleDetails = await Role.getBy('Project Admin');
            const accountRoleDetails = await Role.getBy('Account Admin');
            let lastData = {};
            lastData = await ConcreteRequest.findOne({
              where: { ProjectId: +memberDetails.ProjectId, isDeleted: false },
              order: [['ConcreteRequestId', 'DESC']],
            });
            if (lastData) {
              const data = lastData.ConcreteRequestId;
              lastData.ConcreteRequestId = 0;
              lastData.ConcreteRequestId = data + 1;
            } else {
              lastData = {};
              lastData.ConcreteRequestId = 1;
            }
            let id = 0;
            const newValue = JSON.parse(JSON.stringify(lastData));
            if (
              newValue &&
              newValue.ConcreteRequestId !== null &&
              newValue.ConcreteRequestId !== undefined
            ) {
              id = newValue.ConcreteRequestId;
            }
            const range = momentRange.range(
              moment(concreteRequestDetail.concretePlacementStart),
              moment(concreteRequestDetail.concretePlacementEnd),
            );
            let totalDays = Array.from(range.by('day'));
            const eventsArray = [];
            let concreteRequestParam = {};
            if (concreteRequestDetail.recurrence === 'Daily') {
              const startTime = concreteRequestDetail.startPicker;
              const endTime = concreteRequestDetail.endPicker;
              let dailyIndex = 0;
              const recurrenceId = await concreteRequestService.insertRecurrenceSeries(
                concreteRequestDetail,
                inputData.user,
                'concreteRequest',
                eventTimeZone.timezone,
              );
              while (dailyIndex < totalDays.length) {
                const data = totalDays[dailyIndex];
                if (
                  moment(data).isBetween(
                    moment(concreteRequestDetail.concretePlacementStart),
                    moment(concreteRequestDetail.concretePlacementEnd),
                    null,
                    '[]',
                  ) ||
                  moment(data).isSame(concreteRequestDetail.concretePlacementStart) ||
                  moment(data).isSame(concreteRequestDetail.concretePlacementEnd)
                ) {
                  id += 1;
                  const date = moment(data).format('MM/DD/YYYY');
                  const chosenTimezoneConcretePlacementStart = moment.tz(
                    `${date} ${startTime}`,
                    'MM/DD/YYYY HH:mm',
                    eventTimeZone.timezone,
                  );
                  const chosenTimezoneConcretePlacementEnd = moment.tz(
                    `${date} ${endTime}`,
                    'MM/DD/YYYY HH:mm',
                    eventTimeZone.timezone,
                  );
                  const chosenTimezonePumpOrderedDate = moment.tz(
                    `${moment(concreteRequestDetail.pumpOrderedDate, 'MM/DD/YYYY').format(
                      'MM/DD/YYYY',
                    )} ${concreteRequestDetail.pumpWorkStart}`,
                    'MM/DD/YYYY HH:mm',
                    eventTimeZone.timezone,
                  );
                  const chosenTimezonePumpWorkStart = moment.tz(
                    `${moment(concreteRequestDetail.pumpOrderedDate, 'MM/DD/YYYY').format(
                      'MM/DD/YYYY',
                    )} ${concreteRequestDetail.pumpWorkStart}`,
                    'MM/DD/YYYY HH:mm',
                    eventTimeZone.timezone,
                  );
                  const chosenTimezonePumpWorkEnd = moment.tz(
                    `${moment(concreteRequestDetail.pumpOrderedDate, 'MM/DD/YYYY').format(
                      'MM/DD/YYYY',
                    )} ${concreteRequestDetail.pumpWorkEnd}`,
                    'MM/DD/YYYY HH:mm',
                    eventTimeZone.timezone,
                  );
                  concreteRequestParam = {
                    description: concreteRequestDetail.description,
                    ProjectId: concreteRequestDetail.ProjectId,
                    notes: concreteRequestDetail.notes,
                    concretePlacementStart: chosenTimezoneConcretePlacementStart
                      .clone()
                      .tz('UTC')
                      .format('YYYY-MM-DD HH:mm:ssZ'),
                    concretePlacementEnd: chosenTimezoneConcretePlacementEnd
                      .clone()
                      .tz('UTC')
                      .format('YYYY-MM-DD HH:mm:ssZ'),
                    isPumpConfirmed: concreteRequestDetail.isPumpConfirmed,
                    isPumpRequired: concreteRequestDetail.isPumpRequired,
                    isConcreteConfirmed: concreteRequestDetail.isConcreteConfirmed,
                    ParentCompanyId: concreteRequestDetail.ParentCompanyId,
                    concreteOrderNumber: concreteRequestDetail.concreteOrderNumber,
                    truckSpacingHours: concreteRequestDetail.truckSpacingHours,
                    slump: concreteRequestDetail.slump,
                    concreteQuantityOrdered: concreteRequestDetail.concreteQuantityOrdered,
                    concreteConfirmedOn: concreteRequestDetail.concreteConfirmedOn
                      ? concreteRequestDetail.concreteConfirmedOn
                      : null,
                    pumpLocation: concreteRequestDetail.pumpLocation,
                    pumpOrderedDate:
                      concreteRequestDetail.pumpOrderedDate && concreteRequestDetail.pumpWorkStart
                        ? chosenTimezonePumpOrderedDate
                          .clone()
                          .tz('UTC')
                          .format('YYYY-MM-DD HH:mm:ssZ')
                        : null,
                    pumpWorkStart:
                      concreteRequestDetail.pumpOrderedDate && concreteRequestDetail.pumpWorkStart
                        ? chosenTimezonePumpWorkStart
                          .clone()
                          .tz('UTC')
                          .format('YYYY-MM-DD HH:mm:ssZ')
                        : null,
                    pumpWorkEnd:
                      concreteRequestDetail.pumpOrderedDate && concreteRequestDetail.pumpWorkEnd
                        ? chosenTimezonePumpWorkEnd.clone().tz('UTC').format('YYYY-MM-DD HH:mm:ssZ')
                        : null,
                    pumpConfirmedOn: concreteRequestDetail.pumpConfirmedOn
                      ? concreteRequestDetail.pumpConfirmedOn
                      : null,
                    cubicYardsTotal: concreteRequestDetail.cubicYardsTotal,
                    hoursToCompletePlacement: concreteRequestDetail.hoursToCompletePlacement,
                    minutesToCompletePlacement: concreteRequestDetail.minutesToCompletePlacement,
                    ConcreteRequestId: id,
                    requestType: 'concreteRequest',
                    status: 'Tentative',
                    primerForPump: concreteRequestDetail.primerForPump,
                    createdBy: memberDetails.id,
                    recurrenceId,
                    LocationId: concreteRequestDetail.LocationId,
                    isCreatedByGuestUser: true,
                  };
                  if (
                    memberDetails.RoleId === roleDetails.id ||
                    memberDetails.RoleId === accountRoleDetails.id ||
                    memberDetails.isAutoApproveEnabled ||
                    projectDetails.ProjectSettings.isAutoApprovalEnabled
                  ) {
                    concreteRequestParam.status = 'Approved';
                    concreteRequestParam.approvedBy = memberDetails.id;
                    concreteRequestParam.approved_at = new Date();
                  }
                  eventsArray.push(concreteRequestParam);
                  // eslint-disable-next-line no-const-assign
                  dailyIndex += +concreteRequestDetail.repeatEveryCount;
                }
              }
              if (eventsArray && eventsArray.length > 0) {
                const isOverlapping = await concreteRequestService.checkDoubleBookingAllowedOrNot(
                  eventsArray,
                  projectDetails,
                  'add',
                );
                if (isOverlapping && isOverlapping.error) {
                  return done(null, {
                    message: isOverlapping.message,
                  });
                }
              }
            }
            if (concreteRequestDetail.recurrence === 'Weekly') {
              const startTime = concreteRequestDetail.startPicker;
              const endTime = concreteRequestDetail.endPicker;
              const startDayWeek = moment(concreteRequestDetail.concretePlacementStart).startOf(
                'week',
              );
              const endDayWeek = moment(concreteRequestDetail.concretePlacementEnd).endOf('week');
              const range1 = momentRange.range(moment(startDayWeek), moment(endDayWeek));
              const totalDaysOfRecurrence = Array.from(range1.by('day'));
              const recurrenceId = await concreteRequestService.insertRecurrenceSeries(
                concreteRequestDetail,
                inputData.user,
                'concreteRequest',
                eventTimeZone.timezone,
              );
              totalDays = totalDaysOfRecurrence;
              let count;
              let weekIncrement;
              if (+concreteRequestDetail.repeatEveryCount > 1) {
                count = +concreteRequestDetail.repeatEveryCount - 1;
                weekIncrement = 7;
              } else {
                count = 1;
                weekIncrement = 0;
              }
              for (
                let indexba = 0;
                indexba < totalDaysOfRecurrence.length;
                indexba += weekIncrement * count
              ) {
                const totalLength = indexba + 6;
                for (let indexb = indexba; indexb <= totalLength; indexb += 1) {
                  const data = totalDaysOfRecurrence[indexb];
                  indexba += 1;
                  if (
                    data &&
                    !moment(data).isBefore(concreteRequestDetail.concretePlacementStart) &&
                    !moment(data).isAfter(concreteRequestDetail.concretePlacementEnd)
                  ) {
                    const day = moment(data).format('dddd');
                    const indexVal = concreteRequestDetail.days.includes(day);
                    if (indexVal) {
                      id += 1;
                      const date = moment(data).format('MM/DD/YYYY');
                      const chosenTimezoneConcretePlacementStart = moment.tz(
                        `${date} ${startTime}`,
                        'MM/DD/YYYY HH:mm',
                        eventTimeZone.timezone,
                      );
                      const chosenTimezoneConcretePlacementEnd = moment.tz(
                        `${date} ${endTime}`,
                        'MM/DD/YYYY HH:mm',
                        eventTimeZone.timezone,
                      );
                      const chosenTimezonePumpOrderedDate = moment.tz(
                        `${moment(concreteRequestDetail.pumpOrderedDate, 'MM/DD/YYYY').format(
                          'MM/DD/YYYY',
                        )} ${concreteRequestDetail.pumpWorkStart}`,
                        'MM/DD/YYYY HH:mm',
                        eventTimeZone.timezone,
                      );
                      const chosenTimezonePumpWorkStart = moment.tz(
                        `${moment(concreteRequestDetail.pumpOrderedDate, 'MM/DD/YYYY').format(
                          'MM/DD/YYYY',
                        )} ${concreteRequestDetail.pumpWorkStart}`,
                        'MM/DD/YYYY HH:mm',
                        eventTimeZone.timezone,
                      );
                      const chosenTimezonePumpWorkEnd = moment.tz(
                        `${moment(concreteRequestDetail.pumpOrderedDate, 'MM/DD/YYYY').format(
                          'MM/DD/YYYY',
                        )} ${concreteRequestDetail.pumpWorkEnd}`,
                        'MM/DD/YYYY HH:mm',
                        eventTimeZone.timezone,
                      );
                      concreteRequestParam = {
                        description: concreteRequestDetail.description,
                        ProjectId: concreteRequestDetail.ProjectId,
                        notes: concreteRequestDetail.notes,
                        concretePlacementStart: chosenTimezoneConcretePlacementStart
                          .clone()
                          .tz('UTC')
                          .format('YYYY-MM-DD HH:mm:ssZ'),
                        concretePlacementEnd: chosenTimezoneConcretePlacementEnd
                          .clone()
                          .tz('UTC')
                          .format('YYYY-MM-DD HH:mm:ssZ'),
                        isPumpConfirmed: concreteRequestDetail.isPumpConfirmed,
                        isPumpRequired: concreteRequestDetail.isPumpRequired,
                        isConcreteConfirmed: concreteRequestDetail.isConcreteConfirmed,
                        ParentCompanyId: concreteRequestDetail.ParentCompanyId,
                        concreteOrderNumber: concreteRequestDetail.concreteOrderNumber,
                        truckSpacingHours: concreteRequestDetail.truckSpacingHours,
                        slump: concreteRequestDetail.slump,
                        concreteQuantityOrdered: concreteRequestDetail.concreteQuantityOrdered,
                        concreteConfirmedOn: concreteRequestDetail.concreteConfirmedOn
                          ? concreteRequestDetail.concreteConfirmedOn
                          : null,
                        pumpLocation: concreteRequestDetail.pumpLocation,
                        pumpOrderedDate:
                          concreteRequestDetail.pumpOrderedDate &&
                            concreteRequestDetail.pumpWorkStart
                            ? chosenTimezonePumpOrderedDate
                              .clone()
                              .tz('UTC')
                              .format('YYYY-MM-DD HH:mm:ssZ')
                            : null,
                        pumpWorkStart:
                          concreteRequestDetail.pumpOrderedDate &&
                            concreteRequestDetail.pumpWorkStart
                            ? chosenTimezonePumpWorkStart
                              .clone()
                              .tz('UTC')
                              .format('YYYY-MM-DD HH:mm:ssZ')
                            : null,
                        pumpWorkEnd:
                          concreteRequestDetail.pumpOrderedDate && concreteRequestDetail.pumpWorkEnd
                            ? chosenTimezonePumpWorkEnd
                              .clone()
                              .tz('UTC')
                              .format('YYYY-MM-DD HH:mm:ssZ')
                            : null,
                        pumpConfirmedOn: concreteRequestDetail.pumpConfirmedOn
                          ? concreteRequestDetail.pumpConfirmedOn
                          : null,
                        cubicYardsTotal: concreteRequestDetail.cubicYardsTotal,
                        hoursToCompletePlacement: concreteRequestDetail.hoursToCompletePlacement,
                        minutesToCompletePlacement:
                          concreteRequestDetail.minutesToCompletePlacement,
                        ConcreteRequestId: id,
                        requestType: 'concreteRequest',
                        status: 'Tentative',
                        primerForPump: concreteRequestDetail.primerForPump,
                        createdBy: memberDetails.id,
                        recurrenceId,
                        LocationId: concreteRequestDetail.LocationId,
                        isCreatedByGuestUser: true,
                      };
                      if (
                        memberDetails.RoleId === roleDetails.id ||
                        memberDetails.RoleId === accountRoleDetails.id ||
                        memberDetails.isAutoApproveEnabled ||
                        projectDetails.ProjectSettings.isAutoApprovalEnabled
                      ) {
                        concreteRequestParam.status = 'Approved';
                        concreteRequestParam.approvedBy = memberDetails.id;
                        concreteRequestParam.approved_at = new Date();
                      }
                      eventsArray.push(concreteRequestParam);
                    }
                  }
                }
              }
              if (eventsArray && eventsArray.length > 0) {
                const isOverlapping = await concreteRequestService.checkDoubleBookingAllowedOrNot(
                  eventsArray,
                  projectDetails,
                  'add',
                );
                if (isOverlapping && isOverlapping.error) {
                  return done(null, {
                    message: isOverlapping.message,
                  });
                }
              }
            }
            if (concreteRequestDetail.recurrence === 'Monthly') {
              const startTime = concreteRequestDetail.startPicker;
              const endTime = concreteRequestDetail.endPicker;
              const startMonth = moment(concreteRequestDetail.concretePlacementStart).startOf(
                'month',
              );
              const startMonthNumber = moment(startMonth).format('MM');
              const endMonth = moment(concreteRequestDetail.concretePlacementEnd).endOf('month');
              const endMonthNumber = moment(endMonth).format('MM');
              let startDate1 = moment(concreteRequestDetail.concretePlacementStart);
              const endDate1 = moment(concreteRequestDetail.concretePlacementEnd).endOf('month');
              const allMonthsInPeriod = [];
              const recurrenceId = await concreteRequestService.insertRecurrenceSeries(
                concreteRequestDetail,
                inputData.user,
                'concreteRequest',
                eventTimeZone.timezone,
              );
              while (startDate1.isBefore(endDate1)) {
                allMonthsInPeriod.push(startDate1.format('YYYY-MM'));
                startDate1 = startDate1.add(1, 'month');
              }
              let currentMonthDates = [];
              let totalNumberOfMonths = endMonthNumber - startMonthNumber;
              if (totalNumberOfMonths < 0) {
                totalNumberOfMonths *= -1;
              }
              let k = 0;
              while (k < allMonthsInPeriod.length + 1) {
                currentMonthDates = Array.from(
                  { length: moment(allMonthsInPeriod[k], 'YYYY-MM').daysInMonth() },
                  (x, j) => moment(allMonthsInPeriod[k], 'YYYY-MM').startOf('month').add(j, 'days'),
                );
                if (concreteRequestDetail.chosenDateOfMonth) {
                  const getDate = currentMonthDates.filter(
                    (value) => moment(value).format('DD') === concreteRequestDetail.dateOfMonth,
                  );
                  if (getDate.length === 1) {
                    if (
                      moment(getDate[0]).isBetween(
                        moment(concreteRequestDetail.concretePlacementStart),
                        moment(concreteRequestDetail.concretePlacementEnd),
                        null,
                        '[]',
                      ) ||
                      moment(getDate[0]).isSame(concreteRequestDetail.concretePlacementStart) ||
                      moment(getDate[0]).isSame(concreteRequestDetail.concretePlacementEnd)
                    ) {
                      id += 1;
                      const date = moment(getDate[0].toDate()).format('MM/DD/YYYY');
                      const chosenTimezoneConcretePlacementStart = moment.tz(
                        `${date} ${startTime}`,
                        'MM/DD/YYYY HH:mm',
                        eventTimeZone.timezone,
                      );
                      const chosenTimezoneConcretePlacementEnd = moment.tz(
                        `${date} ${endTime}`,
                        'MM/DD/YYYY HH:mm',
                        eventTimeZone.timezone,
                      );
                      const chosenTimezonePumpOrderedDate = moment.tz(
                        `${moment(concreteRequestDetail.pumpOrderedDate, 'MM/DD/YYYY').format(
                          'MM/DD/YYYY',
                        )} ${concreteRequestDetail.pumpWorkStart}`,
                        'MM/DD/YYYY HH:mm',
                        eventTimeZone.timezone,
                      );
                      const chosenTimezonePumpWorkStart = moment.tz(
                        `${moment(concreteRequestDetail.pumpOrderedDate, 'MM/DD/YYYY').format(
                          'MM/DD/YYYY',
                        )} ${concreteRequestDetail.pumpWorkStart}`,
                        'MM/DD/YYYY HH:mm',
                        eventTimeZone.timezone,
                      );
                      const chosenTimezonePumpWorkEnd = moment.tz(
                        `${moment(concreteRequestDetail.pumpOrderedDate, 'MM/DD/YYYY').format(
                          'MM/DD/YYYY',
                        )} ${concreteRequestDetail.pumpWorkEnd}`,
                        'MM/DD/YYYY HH:mm',
                        eventTimeZone.timezone,
                      );
                      concreteRequestParam = {
                        description: concreteRequestDetail.description,
                        ProjectId: concreteRequestDetail.ProjectId,
                        notes: concreteRequestDetail.notes,
                        concretePlacementStart: chosenTimezoneConcretePlacementStart
                          .clone()
                          .tz('UTC')
                          .format('YYYY-MM-DD HH:mm:ssZ'),
                        concretePlacementEnd: chosenTimezoneConcretePlacementEnd
                          .clone()
                          .tz('UTC')
                          .format('YYYY-MM-DD HH:mm:ssZ'),
                        isPumpConfirmed: concreteRequestDetail.isPumpConfirmed,
                        isPumpRequired: concreteRequestDetail.isPumpRequired,
                        isConcreteConfirmed: concreteRequestDetail.isConcreteConfirmed,
                        ParentCompanyId: concreteRequestDetail.ParentCompanyId,
                        concreteOrderNumber: concreteRequestDetail.concreteOrderNumber,
                        truckSpacingHours: concreteRequestDetail.truckSpacingHours,
                        slump: concreteRequestDetail.slump,
                        concreteQuantityOrdered: concreteRequestDetail.concreteQuantityOrdered,
                        concreteConfirmedOn: concreteRequestDetail.concreteConfirmedOn
                          ? concreteRequestDetail.concreteConfirmedOn
                          : null,
                        pumpLocation: concreteRequestDetail.pumpLocation,
                        pumpOrderedDate:
                          concreteRequestDetail.pumpOrderedDate &&
                            concreteRequestDetail.pumpWorkStart
                            ? chosenTimezonePumpOrderedDate
                              .clone()
                              .tz('UTC')
                              .format('YYYY-MM-DD HH:mm:ssZ')
                            : null,
                        pumpWorkStart:
                          concreteRequestDetail.pumpOrderedDate &&
                            concreteRequestDetail.pumpWorkStart
                            ? chosenTimezonePumpWorkStart
                              .clone()
                              .tz('UTC')
                              .format('YYYY-MM-DD HH:mm:ssZ')
                            : null,
                        pumpWorkEnd:
                          concreteRequestDetail.pumpOrderedDate && concreteRequestDetail.pumpWorkEnd
                            ? chosenTimezonePumpWorkEnd
                              .clone()
                              .tz('UTC')
                              .format('YYYY-MM-DD HH:mm:ssZ')
                            : null,
                        pumpConfirmedOn: concreteRequestDetail.pumpConfirmedOn
                          ? concreteRequestDetail.pumpConfirmedOn
                          : null,
                        cubicYardsTotal: concreteRequestDetail.cubicYardsTotal,
                        hoursToCompletePlacement: concreteRequestDetail.hoursToCompletePlacement,
                        minutesToCompletePlacement:
                          concreteRequestDetail.minutesToCompletePlacement,
                        ConcreteRequestId: id,
                        requestType: 'concreteRequest',
                        status: 'Tentative',
                        primerForPump: concreteRequestDetail.primerForPump,
                        createdBy: memberDetails.id,
                        recurrenceId,
                        LocationId: concreteRequestDetail.LocationId,
                        isCreatedByGuestUser: true,
                      };
                      if (
                        memberDetails.RoleId === roleDetails.id ||
                        memberDetails.RoleId === accountRoleDetails.id ||
                        memberDetails.isAutoApproveEnabled ||
                        projectDetails.ProjectSettings.isAutoApprovalEnabled
                      ) {
                        concreteRequestParam.status = 'Approved';
                        concreteRequestParam.approvedBy = memberDetails.id;
                        concreteRequestParam.approved_at = new Date();
                      }
                      eventsArray.push(concreteRequestParam);
                    }
                  }
                } else if (allMonthsInPeriod[k]) {
                  const dayOfMonth = concreteRequestDetail.monthlyRepeatType;
                  const week = dayOfMonth.split(' ')[0].toLowerCase();
                  const day = dayOfMonth.split(' ')[1].toLowerCase();
                  const chosenDay = moment(allMonthsInPeriod[k], 'YYYY-MM')
                    .startOf('month')
                    .day(day);
                  const getAllDays = [];
                  if (chosenDay.date() > 7) chosenDay.add(7, 'd');
                  const month = chosenDay.month();
                  while (month === chosenDay.month()) {
                    getAllDays.push(chosenDay.toString());
                    chosenDay.add(7, 'd');
                  }
                  let i = 0;
                  if (week === 'second') {
                    i += 1;
                  } else if (week === 'third') {
                    i += 2;
                  } else if (week === 'fourth') {
                    i += 3;
                  } else if (week === 'last') {
                    i = getAllDays.length - 1;
                  }
                  const finalDay = getAllDays[i];
                  if (
                    moment(finalDay).isBetween(
                      moment(concreteRequestDetail.concretePlacementStart),
                      moment(concreteRequestDetail.concretePlacementEnd),
                      null,
                      '[]',
                    ) ||
                    moment(finalDay).isSame(concreteRequestDetail.concretePlacementStart) ||
                    moment(finalDay).isSame(concreteRequestDetail.concretePlacementEnd)
                  ) {
                    id += 1;
                    const date = moment(finalDay).format('MM/DD/YYYY');
                    const chosenTimezoneConcretePlacementStart = moment.tz(
                      `${date} ${startTime}`,
                      'MM/DD/YYYY HH:mm',
                      eventTimeZone.timezone,
                    );
                    const chosenTimezoneConcretePlacementEnd = moment.tz(
                      `${date} ${endTime}`,
                      'MM/DD/YYYY HH:mm',
                      eventTimeZone.timezone,
                    );
                    const chosenTimezonePumpOrderedDate = moment.tz(
                      `${moment(concreteRequestDetail.pumpOrderedDate, 'MM/DD/YYYY').format(
                        'MM/DD/YYYY',
                      )} ${concreteRequestDetail.pumpWorkStart}`,
                      'MM/DD/YYYY HH:mm',
                      eventTimeZone.timezone,
                    );
                    const chosenTimezonePumpWorkStart = moment.tz(
                      `${moment(concreteRequestDetail.pumpOrderedDate, 'MM/DD/YYYY').format(
                        'MM/DD/YYYY',
                      )} ${concreteRequestDetail.pumpWorkStart}`,
                      'MM/DD/YYYY HH:mm',
                      eventTimeZone.timezone,
                    );
                    const chosenTimezonePumpWorkEnd = moment.tz(
                      `${moment(concreteRequestDetail.pumpOrderedDate, 'MM/DD/YYYY').format(
                        'MM/DD/YYYY',
                      )} ${concreteRequestDetail.pumpWorkEnd}`,
                      'MM/DD/YYYY HH:mm',
                      eventTimeZone.timezone,
                    );
                    concreteRequestParam = {
                      description: concreteRequestDetail.description,
                      ProjectId: concreteRequestDetail.ProjectId,
                      notes: concreteRequestDetail.notes,
                      concretePlacementStart: chosenTimezoneConcretePlacementStart
                        .clone()
                        .tz('UTC')
                        .format('YYYY-MM-DD HH:mm:ssZ'),
                      concretePlacementEnd: chosenTimezoneConcretePlacementEnd
                        .clone()
                        .tz('UTC')
                        .format('YYYY-MM-DD HH:mm:ssZ'),
                      isPumpConfirmed: concreteRequestDetail.isPumpConfirmed,
                      isPumpRequired: concreteRequestDetail.isPumpRequired,
                      isConcreteConfirmed: concreteRequestDetail.isConcreteConfirmed,
                      ParentCompanyId: concreteRequestDetail.ParentCompanyId,
                      concreteOrderNumber: concreteRequestDetail.concreteOrderNumber,
                      truckSpacingHours: concreteRequestDetail.truckSpacingHours,
                      slump: concreteRequestDetail.slump,
                      concreteQuantityOrdered: concreteRequestDetail.concreteQuantityOrdered,
                      concreteConfirmedOn: concreteRequestDetail.concreteConfirmedOn
                        ? concreteRequestDetail.concreteConfirmedOn
                        : null,
                      pumpLocation: concreteRequestDetail.pumpLocation,
                      pumpOrderedDate:
                        concreteRequestDetail.pumpOrderedDate && concreteRequestDetail.pumpWorkStart
                          ? chosenTimezonePumpOrderedDate
                            .clone()
                            .tz('UTC')
                            .format('YYYY-MM-DD HH:mm:ssZ')
                          : null,
                      pumpWorkStart:
                        concreteRequestDetail.pumpOrderedDate && concreteRequestDetail.pumpWorkStart
                          ? chosenTimezonePumpWorkStart
                            .clone()
                            .tz('UTC')
                            .format('YYYY-MM-DD HH:mm:ssZ')
                          : null,
                      pumpWorkEnd:
                        concreteRequestDetail.pumpOrderedDate && concreteRequestDetail.pumpWorkEnd
                          ? chosenTimezonePumpWorkEnd
                            .clone()
                            .tz('UTC')
                            .format('YYYY-MM-DD HH:mm:ssZ')
                          : null,
                      pumpConfirmedOn: concreteRequestDetail.pumpConfirmedOn
                        ? concreteRequestDetail.pumpConfirmedOn
                        : null,
                      cubicYardsTotal: concreteRequestDetail.cubicYardsTotal,
                      hoursToCompletePlacement: concreteRequestDetail.hoursToCompletePlacement,
                      minutesToCompletePlacement: concreteRequestDetail.minutesToCompletePlacement,
                      ConcreteRequestId: id,
                      requestType: 'concreteRequest',
                      status: 'Tentative',
                      primerForPump: concreteRequestDetail.primerForPump,
                      createdBy: memberDetails.id,
                      recurrenceId,
                      LocationId: concreteRequestDetail.LocationId,
                      isCreatedByGuestUser: true,
                    };
                    if (
                      memberDetails.RoleId === roleDetails.id ||
                      memberDetails.RoleId === accountRoleDetails.id ||
                      memberDetails.isAutoApproveEnabled ||
                      projectDetails.ProjectSettings.isAutoApprovalEnabled
                    ) {
                      concreteRequestParam.status = 'Approved';
                      concreteRequestParam.approvedBy = memberDetails.id;
                      concreteRequestParam.approved_at = new Date();
                    }
                    eventsArray.push(concreteRequestParam);
                  }
                }
                k += +concreteRequestDetail.repeatEveryCount;
              }
              if (eventsArray && eventsArray.length > 0) {
                const isOverlapping = await concreteRequestService.checkDoubleBookingAllowedOrNot(
                  eventsArray,
                  projectDetails,
                  'add',
                );
                if (isOverlapping && isOverlapping.error) {
                  return done(null, {
                    message: isOverlapping.message,
                  });
                }
              }
            }
            if (concreteRequestDetail.recurrence === 'Yearly') {
              const startTime = concreteRequestDetail.startPicker;
              const endTime = concreteRequestDetail.endPicker;
              const startMonth = moment(concreteRequestDetail.concretePlacementStart).startOf(
                'month',
              );
              const startMonthNumber = moment(startMonth).format('MM');
              const endMonth = moment(concreteRequestDetail.concretePlacementEnd).endOf('month');
              const endMonthNumber = moment(endMonth).format('MM');
              let startDate1 = moment(concreteRequestDetail.concretePlacementStart);
              const endDate1 = moment(concreteRequestDetail.concretePlacementEnd).endOf('month');
              const allMonthsInPeriod = [];
              const recurrenceId = await concreteRequestService.insertRecurrenceSeries(
                concreteRequestDetail,
                inputData.user,
                'concreteRequest',
                eventTimeZone.timezone,
              );
              while (startDate1.isBefore(endDate1)) {
                allMonthsInPeriod.push(startDate1.format('YYYY-MM'));
                startDate1 = startDate1.add(12, 'month');
              }
              let currentMonthDates = [];
              let totalNumberOfMonths = endMonthNumber - startMonthNumber;
              if (totalNumberOfMonths < 0) {
                totalNumberOfMonths *= -1;
              }
              for (let k = 0; k < allMonthsInPeriod.length + 1; k += 1) {
                currentMonthDates = Array.from(
                  { length: moment(allMonthsInPeriod[k], 'YYYY-MM').daysInMonth() },
                  (x, j) => moment(allMonthsInPeriod[k], 'YYYY-MM').startOf('month').add(j, 'days'),
                );
                if (concreteRequestDetail.chosenDateOfMonth) {
                  const getDate = currentMonthDates.filter(
                    (value) => moment(value).format('DD') === concreteRequestDetail.dateOfMonth,
                  );
                  if (getDate.length === 1) {
                    if (
                      moment(getDate[0]).isBetween(
                        moment(concreteRequestDetail.concretePlacementStart),
                        moment(concreteRequestDetail.concretePlacementEnd),
                        null,
                        '[]',
                      ) ||
                      moment(getDate[0]).isSame(concreteRequestDetail.concretePlacementStart) ||
                      moment(getDate[0]).isSame(concreteRequestDetail.concretePlacementEnd)
                    ) {
                      id += 1;
                      const date = moment(getDate[0].toDate()).format('MM/DD/YYYY');
                      const chosenTimezoneConcretePlacementStart = moment.tz(
                        `${date} ${startTime}`,
                        'MM/DD/YYYY HH:mm',
                        eventTimeZone.timezone,
                      );
                      const chosenTimezoneConcretePlacementEnd = moment.tz(
                        `${date} ${endTime}`,
                        'MM/DD/YYYY HH:mm',
                        eventTimeZone.timezone,
                      );
                      const chosenTimezonePumpOrderedDate = moment.tz(
                        `${moment(concreteRequestDetail.pumpOrderedDate, 'MM/DD/YYYY').format(
                          'MM/DD/YYYY',
                        )} ${concreteRequestDetail.pumpWorkStart}`,
                        'MM/DD/YYYY HH:mm',
                        eventTimeZone.timezone,
                      );
                      const chosenTimezonePumpWorkStart = moment.tz(
                        `${moment(concreteRequestDetail.pumpOrderedDate, 'MM/DD/YYYY').format(
                          'MM/DD/YYYY',
                        )} ${concreteRequestDetail.pumpWorkStart}`,
                        'MM/DD/YYYY HH:mm',
                        eventTimeZone.timezone,
                      );
                      const chosenTimezonePumpWorkEnd = moment.tz(
                        `${moment(concreteRequestDetail.pumpOrderedDate, 'MM/DD/YYYY').format(
                          'MM/DD/YYYY',
                        )} ${concreteRequestDetail.pumpWorkEnd}`,
                        'MM/DD/YYYY HH:mm',
                        eventTimeZone.timezone,
                      );
                      concreteRequestParam = {
                        description: concreteRequestDetail.description,
                        ProjectId: concreteRequestDetail.ProjectId,
                        notes: concreteRequestDetail.notes,
                        concretePlacementStart: chosenTimezoneConcretePlacementStart
                          .clone()
                          .tz('UTC')
                          .format('YYYY-MM-DD HH:mm:ssZ'),
                        concretePlacementEnd: chosenTimezoneConcretePlacementEnd
                          .clone()
                          .tz('UTC')
                          .format('YYYY-MM-DD HH:mm:ssZ'),
                        isPumpConfirmed: concreteRequestDetail.isPumpConfirmed,
                        isPumpRequired: concreteRequestDetail.isPumpRequired,
                        isConcreteConfirmed: concreteRequestDetail.isConcreteConfirmed,
                        ParentCompanyId: concreteRequestDetail.ParentCompanyId,
                        concreteOrderNumber: concreteRequestDetail.concreteOrderNumber,
                        truckSpacingHours: concreteRequestDetail.truckSpacingHours,
                        slump: concreteRequestDetail.slump,
                        concreteQuantityOrdered: concreteRequestDetail.concreteQuantityOrdered,
                        concreteConfirmedOn: concreteRequestDetail.concreteConfirmedOn
                          ? concreteRequestDetail.concreteConfirmedOn
                          : null,
                        pumpLocation: concreteRequestDetail.pumpLocation,
                        pumpOrderedDate:
                          concreteRequestDetail.pumpOrderedDate &&
                            concreteRequestDetail.pumpWorkStart
                            ? chosenTimezonePumpOrderedDate
                              .clone()
                              .tz('UTC')
                              .format('YYYY-MM-DD HH:mm:ssZ')
                            : null,
                        pumpWorkStart:
                          concreteRequestDetail.pumpOrderedDate &&
                            concreteRequestDetail.pumpWorkStart
                            ? chosenTimezonePumpWorkStart
                              .clone()
                              .tz('UTC')
                              .format('YYYY-MM-DD HH:mm:ssZ')
                            : null,
                        pumpWorkEnd:
                          concreteRequestDetail.pumpOrderedDate && concreteRequestDetail.pumpWorkEnd
                            ? chosenTimezonePumpWorkEnd
                              .clone()
                              .tz('UTC')
                              .format('YYYY-MM-DD HH:mm:ssZ')
                            : null,
                        pumpConfirmedOn: concreteRequestDetail.pumpConfirmedOn
                          ? concreteRequestDetail.pumpConfirmedOn
                          : null,
                        cubicYardsTotal: concreteRequestDetail.cubicYardsTotal,
                        hoursToCompletePlacement: concreteRequestDetail.hoursToCompletePlacement,
                        minutesToCompletePlacement:
                          concreteRequestDetail.minutesToCompletePlacement,
                        ConcreteRequestId: id,
                        requestType: 'concreteRequest',
                        status: 'Tentative',
                        primerForPump: concreteRequestDetail.primerForPump,
                        createdBy: memberDetails.id,
                        recurrenceId,
                        LocationId: concreteRequestDetail.LocationId,
                        isCreatedByGuestUser: true,
                      };
                      if (
                        memberDetails.RoleId === roleDetails.id ||
                        memberDetails.RoleId === accountRoleDetails.id ||
                        memberDetails.isAutoApproveEnabled ||
                        projectDetails.ProjectSettings.isAutoApprovalEnabled
                      ) {
                        concreteRequestParam.status = 'Approved';
                        concreteRequestParam.approvedBy = memberDetails.id;
                        concreteRequestParam.approved_at = new Date();
                      }
                      eventsArray.push(concreteRequestParam);
                    }
                  }
                } else if (allMonthsInPeriod[k]) {
                  const dayOfMonth = concreteRequestDetail.monthlyRepeatType;
                  const week = dayOfMonth.split(' ')[0].toLowerCase();
                  const day = dayOfMonth.split(' ')[1].toLowerCase();
                  const chosenDay = moment(allMonthsInPeriod[k], 'YYYY-MM')
                    .startOf('month')
                    .day(day);
                  const getAllDays = [];
                  if (chosenDay.date() > 7) chosenDay.add(7, 'd');
                  const month = chosenDay.month();
                  while (month === chosenDay.month()) {
                    getAllDays.push(chosenDay.toString());
                    chosenDay.add(7, 'd');
                  }
                  let i = 0;
                  if (week === 'second') {
                    i += 1;
                  } else if (week === 'third') {
                    i += 2;
                  } else if (week === 'fourth') {
                    i += 3;
                  } else if (week === 'last') {
                    i = getAllDays.length - 1;
                  }
                  const finalDay = getAllDays[i];
                  if (
                    moment(finalDay).isBetween(
                      moment(concreteRequestDetail.concretePlacementStart),
                      moment(concreteRequestDetail.concretePlacementEnd),
                      null,
                      '[]',
                    ) ||
                    moment(finalDay).isSame(concreteRequestDetail.concretePlacementStart) ||
                    moment(finalDay).isSame(concreteRequestDetail.concretePlacementEnd)
                  ) {
                    id += 1;
                    const date = moment(finalDay).format('MM/DD/YYYY');
                    const chosenTimezoneConcretePlacementStart = moment.tz(
                      `${date} ${startTime}`,
                      'MM/DD/YYYY HH:mm',
                      eventTimeZone.timezone,
                    );
                    const chosenTimezoneConcretePlacementEnd = moment.tz(
                      `${date} ${endTime}`,
                      'MM/DD/YYYY HH:mm',
                      eventTimeZone.timezone,
                    );
                    const chosenTimezonePumpOrderedDate = moment.tz(
                      `${moment(concreteRequestDetail.pumpOrderedDate, 'MM/DD/YYYY').format(
                        'MM/DD/YYYY',
                      )} ${concreteRequestDetail.pumpWorkStart}`,
                      'MM/DD/YYYY HH:mm',
                      eventTimeZone.timezone,
                    );
                    const chosenTimezonePumpWorkStart = moment.tz(
                      `${moment(concreteRequestDetail.pumpOrderedDate, 'MM/DD/YYYY').format(
                        'MM/DD/YYYY',
                      )} ${concreteRequestDetail.pumpWorkStart}`,
                      'MM/DD/YYYY HH:mm',
                      eventTimeZone.timezone,
                    );
                    const chosenTimezonePumpWorkEnd = moment.tz(
                      `${moment(concreteRequestDetail.pumpOrderedDate, 'MM/DD/YYYY').format(
                        'MM/DD/YYYY',
                      )} ${concreteRequestDetail.pumpWorkEnd}`,
                      'MM/DD/YYYY HH:mm',
                      eventTimeZone.timezone,
                    );
                    concreteRequestParam = {
                      description: concreteRequestDetail.description,
                      ProjectId: concreteRequestDetail.ProjectId,
                      notes: concreteRequestDetail.notes,
                      concretePlacementStart: chosenTimezoneConcretePlacementStart
                        .clone()
                        .tz('UTC')
                        .format('YYYY-MM-DD HH:mm:ssZ'),
                      concretePlacementEnd: chosenTimezoneConcretePlacementEnd
                        .clone()
                        .tz('UTC')
                        .format('YYYY-MM-DD HH:mm:ssZ'),
                      isPumpConfirmed: concreteRequestDetail.isPumpConfirmed,
                      isPumpRequired: concreteRequestDetail.isPumpRequired,
                      isConcreteConfirmed: concreteRequestDetail.isConcreteConfirmed,
                      ParentCompanyId: concreteRequestDetail.ParentCompanyId,
                      concreteOrderNumber: concreteRequestDetail.concreteOrderNumber,
                      truckSpacingHours: concreteRequestDetail.truckSpacingHours,
                      slump: concreteRequestDetail.slump,
                      concreteQuantityOrdered: concreteRequestDetail.concreteQuantityOrdered,
                      concreteConfirmedOn: concreteRequestDetail.concreteConfirmedOn
                        ? concreteRequestDetail.concreteConfirmedOn
                        : null,
                      pumpLocation: concreteRequestDetail.pumpLocation,
                      pumpOrderedDate:
                        concreteRequestDetail.pumpOrderedDate && concreteRequestDetail.pumpWorkStart
                          ? chosenTimezonePumpOrderedDate
                            .clone()
                            .tz('UTC')
                            .format('YYYY-MM-DD HH:mm:ssZ')
                          : null,
                      pumpWorkStart:
                        concreteRequestDetail.pumpOrderedDate && concreteRequestDetail.pumpWorkStart
                          ? chosenTimezonePumpWorkStart
                            .clone()
                            .tz('UTC')
                            .format('YYYY-MM-DD HH:mm:ssZ')
                          : null,
                      pumpWorkEnd:
                        concreteRequestDetail.pumpOrderedDate && concreteRequestDetail.pumpWorkEnd
                          ? chosenTimezonePumpWorkEnd
                            .clone()
                            .tz('UTC')
                            .format('YYYY-MM-DD HH:mm:ssZ')
                          : null,
                      pumpConfirmedOn: concreteRequestDetail.pumpConfirmedOn
                        ? concreteRequestDetail.pumpConfirmedOn
                        : null,
                      cubicYardsTotal: concreteRequestDetail.cubicYardsTotal,
                      hoursToCompletePlacement: concreteRequestDetail.hoursToCompletePlacement,
                      minutesToCompletePlacement: concreteRequestDetail.minutesToCompletePlacement,
                      ConcreteRequestId: id,
                      requestType: 'concreteRequest',
                      status: 'Tentative',
                      primerForPump: concreteRequestDetail.primerForPump,
                      createdBy: memberDetails.id,
                      recurrenceId,
                      LocationId: concreteRequestDetail.LocationId,
                      isCreatedByGuestUser: true,
                    };
                    if (
                      memberDetails.RoleId === roleDetails.id ||
                      memberDetails.RoleId === accountRoleDetails.id ||
                      memberDetails.isAutoApproveEnabled ||
                      projectDetails.ProjectSettings.isAutoApprovalEnabled
                    ) {
                      concreteRequestParam.status = 'Approved';
                      concreteRequestParam.approvedBy = memberDetails.id;
                      concreteRequestParam.approved_at = new Date();
                    }
                    eventsArray.push(concreteRequestParam);
                  }
                }
              }
              if (eventsArray && eventsArray.length > 0) {
                const isOverlapping = await concreteRequestService.checkDoubleBookingAllowedOrNot(
                  eventsArray,
                  projectDetails,
                  'add',
                );
                if (isOverlapping && isOverlapping.error) {
                  return done(null, {
                    message: isOverlapping.message,
                  });
                }
              }
            }
            if (concreteRequestDetail.recurrence === 'Does Not Repeat') {
              const recurrenceId = await concreteRequestService.insertRecurrenceSeries(
                concreteRequestDetail,
                inputData.user,
                'concreteRequest',
                eventTimeZone.timezone,
              );
              totalDays.forEach(async (data) => {
                id += 1;
                const chosenTimezoneConcretePlacementStart = moment.tz(
                  `${moment(concreteRequestDetail.concretePlacementStart).format('MM/DD/YYYY')} ${concreteRequestDetail.startPicker
                  }`,
                  'MM/DD/YYYY HH:mm',
                  eventTimeZone.timezone,
                );
                const chosenTimezoneConcretePlacementEnd = moment.tz(
                  `${moment(concreteRequestDetail.concretePlacementEnd).format('MM/DD/YYYY')} ${concreteRequestDetail.endPicker
                  }`,
                  'MM/DD/YYYY HH:mm',
                  eventTimeZone.timezone,
                );
                const chosenTimezonePumpOrderedDate = moment.tz(
                  `${moment(concreteRequestDetail.pumpOrderedDate, 'MM/DD/YYYY').format(
                    'MM/DD/YYYY',
                  )} ${concreteRequestDetail.pumpWorkStart}`,
                  'MM/DD/YYYY HH:mm',
                  eventTimeZone.timezone,
                );
                const chosenTimezonePumpWorkStart = moment.tz(
                  `${moment(concreteRequestDetail.pumpOrderedDate, 'MM/DD/YYYY').format(
                    'MM/DD/YYYY',
                  )} ${concreteRequestDetail.pumpWorkStart}`,
                  'MM/DD/YYYY HH:mm',
                  eventTimeZone.timezone,
                );
                const chosenTimezonePumpWorkEnd = moment.tz(
                  `${moment(concreteRequestDetail.pumpOrderedDate, 'MM/DD/YYYY').format(
                    'MM/DD/YYYY',
                  )} ${concreteRequestDetail.pumpWorkEnd}`,
                  'MM/DD/YYYY HH:mm',
                  eventTimeZone.timezone,
                );
                concreteRequestParam = {
                  description: concreteRequestDetail.description,
                  ProjectId: concreteRequestDetail.ProjectId,
                  notes: concreteRequestDetail.notes,
                  concretePlacementStart: chosenTimezoneConcretePlacementStart
                    .clone()
                    .tz('UTC')
                    .format('YYYY-MM-DD HH:mm:ssZ'),
                  concretePlacementEnd: chosenTimezoneConcretePlacementEnd
                    .clone()
                    .tz('UTC')
                    .format('YYYY-MM-DD HH:mm:ssZ'),
                  isPumpConfirmed: concreteRequestDetail.isPumpConfirmed,
                  isPumpRequired: concreteRequestDetail.isPumpRequired,
                  isConcreteConfirmed: concreteRequestDetail.isConcreteConfirmed,
                  ParentCompanyId: concreteRequestDetail.ParentCompanyId,
                  concreteOrderNumber: concreteRequestDetail.concreteOrderNumber,
                  truckSpacingHours: concreteRequestDetail.truckSpacingHours,
                  slump: concreteRequestDetail.slump,
                  concreteQuantityOrdered: concreteRequestDetail.concreteQuantityOrdered,
                  concreteConfirmedOn: concreteRequestDetail.concreteConfirmedOn
                    ? concreteRequestDetail.concreteConfirmedOn
                    : null,
                  pumpLocation: concreteRequestDetail.pumpLocation,
                  pumpOrderedDate:
                    concreteRequestDetail.pumpOrderedDate && concreteRequestDetail.pumpWorkStart
                      ? chosenTimezonePumpOrderedDate
                        .clone()
                        .tz('UTC')
                        .format('YYYY-MM-DD HH:mm:ssZ')
                      : null,
                  pumpWorkStart:
                    concreteRequestDetail.pumpOrderedDate && concreteRequestDetail.pumpWorkStart
                      ? chosenTimezonePumpWorkStart.clone().tz('UTC').format('YYYY-MM-DD HH:mm:ssZ')
                      : null,
                  pumpWorkEnd:
                    concreteRequestDetail.pumpOrderedDate && concreteRequestDetail.pumpWorkEnd
                      ? chosenTimezonePumpWorkEnd.clone().tz('UTC').format('YYYY-MM-DD HH:mm:ssZ')
                      : null,
                  pumpConfirmedOn: concreteRequestDetail.pumpConfirmedOn
                    ? concreteRequestDetail.pumpConfirmedOn
                    : null,
                  cubicYardsTotal: concreteRequestDetail.cubicYardsTotal,
                  hoursToCompletePlacement: concreteRequestDetail.hoursToCompletePlacement,
                  minutesToCompletePlacement: concreteRequestDetail.minutesToCompletePlacement,
                  ConcreteRequestId: id,
                  requestType: 'concreteRequest',
                  status: 'Tentative',
                  primerForPump: concreteRequestDetail.primerForPump,
                  createdBy: memberDetails.id,
                  recurrenceId,
                  LocationId: concreteRequestDetail.LocationId,
                  isCreatedByGuestUser: true,
                };
                if (
                  memberDetails.RoleId === roleDetails.id ||
                  memberDetails.RoleId === accountRoleDetails.id ||
                  memberDetails.isAutoApproveEnabled ||
                  projectDetails.ProjectSettings.isAutoApprovalEnabled
                ) {
                  concreteRequestParam.status = 'Approved';
                  concreteRequestParam.approvedBy = memberDetails.id;
                  concreteRequestParam.approved_at = new Date();
                }
                eventsArray.push(concreteRequestParam);
              });
              if (eventsArray && eventsArray.length > 0) {
                const isOverlapping = await concreteRequestService.checkDoubleBookingAllowedOrNot(
                  eventsArray,
                  projectDetails,
                  'add',
                );
                if (isOverlapping && isOverlapping.error) {
                  return done(null, {
                    message: isOverlapping.message,
                  });
                }
              }
            }
            let newConcreteRequestData = {};
            if (eventsArray.length > 0) {
              for (let i = 0; i < eventsArray.length; i += 1) {
                newConcreteRequestData = await ConcreteRequest.createInstance(eventsArray[i]);
                const { responsiblePersons, concreteSupplier, pumpSize, mixDesign } =
                  concreteRequestDetail;
                const updateParam = {
                  ConcreteRequestId: newConcreteRequestData.id,
                  ConcreteRequestCode: newConcreteRequestData.ConcreteRequestId,
                  ProjectId: concreteRequestDetail.ProjectId,
                };
                concreteSupplier.forEach(async (element) => {
                  const companyParam = updateParam;
                  companyParam.CompanyId = element;
                  await ConcreteRequestCompany.createInstance(companyParam);
                });
                // location.forEach(async (element) => {
                const locationParam = updateParam;
                // if (element.chosenFromDropdown) {
                // locationParam.ConcreteLocationId = element.id;
                // } else {
                // const existlocations = await ConcreteLocation.findAll({
                //   where: {
                //     ProjectId: concreteRequestDetail.ProjectId,
                //     isDeleted: false,
                //   },
                // });
                // const isLocationExists = existlocations.find(
                //   (item) =>
                //     item.location.toLowerCase().trim() ===
                //     element.location.toLowerCase().trim(),
                // );
                // if (isLocationExists) {
                // locationParam.ConcreteLocationId = isLocationExists.id;
                // } else {
                const object = {
                  location: concreteRequestDetail.location,
                  ProjectId: concreteRequestDetail.ProjectId,
                  isDeleted: false,
                  createdBy: loginUser.id,
                };
                const locationData = await ConcreteLocation.createConcreteLocation(object);
                locationParam.ConcreteLocationId = locationData.id;
                // }
                // }
                await ConcreteRequestLocation.createInstance(locationParam);
                // });
                pumpSize.forEach(async (element) => {
                  const pumpSizeParam = updateParam;
                  if (element.chosenFromDropdown) {
                    pumpSizeParam.ConcretePumpSizeId = element.id;
                  } else {
                    const existPumpSizes = await ConcretePumpSize.findAll({
                      where: {
                        ProjectId: concreteRequestDetail.ProjectId,
                        isDeleted: false,
                      },
                    });
                    const isPumpSizeExists = existPumpSizes.find(
                      (item) =>
                        item.pumpSize.toLowerCase().trim() ===
                        element.pumpSize.toLowerCase().trim(),
                    );
                    if (isPumpSizeExists) {
                      pumpSizeParam.ConcretePumpSizeId = isPumpSizeExists.id;
                    } else {
                      const object2 = {
                        pumpSize: element.pumpSize,
                        ProjectId: concreteRequestDetail.ProjectId,
                        isDeleted: false,
                        createdBy: loginUser.id,
                      };
                      const pumpSizeData = await ConcretePumpSize.createConcretePumpSize(object2);
                      pumpSizeParam.ConcretePumpSizeId = pumpSizeData.id;
                    }
                  }
                  await ConcreteRequestPumpSize.createInstance(pumpSizeParam);
                });
                mixDesign.forEach(async (element) => {
                  const mixDesignParam = updateParam;
                  if (element.chosenFromDropdown) {
                    mixDesignParam.ConcreteMixDesignId = element.id;
                  } else {
                    const existMixDesigns = await ConcreteMixDesign.findAll({
                      where: {
                        ProjectId: concreteRequestDetail.ProjectId,
                        isDeleted: false,
                      },
                    });
                    const isMixDesignExists = existMixDesigns.find(
                      (item) =>
                        item.mixDesign.toLowerCase().trim() ===
                        element.mixDesign.toLowerCase().trim(),
                    );
                    if (isMixDesignExists) {
                      mixDesignParam.ConcreteMixDesignId = isMixDesignExists.id;
                    } else {
                      const object3 = {
                        mixDesign: element.mixDesign,
                        ProjectId: concreteRequestDetail.ProjectId,
                        isDeleted: false,
                        createdBy: loginUser.id,
                      };
                      const mixDesignData =
                        await ConcreteMixDesign.createConcreteMixDesign(object3);
                      mixDesignParam.ConcreteMixDesignId = mixDesignData.id;
                    }
                  }
                  await ConcreteRequestMixDesign.createInstance(mixDesignParam);
                });
                responsiblePersons.forEach(async (element) => {
                  const memberParam = updateParam;
                  memberParam.MemberId = element;
                  await ConcreteRequestResponsiblePerson.createInstance(memberParam);
                });
                // const history = {
                //   ConcreteRequestId: newConcreteRequestData.id,
                //   MemberId: memberDetails.id,
                //   type: 'create',
                //   description: `${loginUser.firstName} ${loginUser.lastName} Created Concrete Booking, ${concreteRequestDetail.description}.`,
                // };
                // const notification = history;
                // notification.ProjectId = concreteRequestDetail.ProjectId;
                // notification.title = 'Concrete Booking Creation';
                // await ConcreteRequestHistory.createInstance(history);
              }
            }
            if (
              Object.keys(newConcreteRequestData).length > 0 &&
              typeof newConcreteRequestData === 'object'
            ) {
              const locationChosen = await Locations.findOne({
                where: {
                  ProjectId: concreteRequestDetail.ProjectId,
                  id: concreteRequestDetail.LocationId,
                },
              });
              const { responsiblePersons } = concreteRequestDetail;
              const history = {
                ConcreteRequestId: newConcreteRequestData.id,
                MemberId: memberDetails.id,
                type: 'create',
                description: `${loginUser.firstName} ${loginUser.lastName} Created Concrete Booking, ${concreteRequestDetail.description} .`,
                locationFollowDescription: `${loginUser.firstName} ${loginUser.lastName} Created Concrete Booking, ${concreteRequestDetail.description}. Location: ${locationChosen.locationPath}.`,
                memberData: [],
              };
              const notification = history;
              notification.ProjectId = concreteRequestDetail.ProjectId;
              notification.LocationId = concreteRequestDetail.LocationId;
              notification.title = 'Concrete Booking Creation';
              await ConcreteRequestHistory.createInstance(history);
              notification.isDeliveryRequest = false;
              notification.requestType = 'concreteRequest';
              notification.recurrenceType = `${concreteRequestDetail.recurrence} From ${moment(
                concreteRequestDetail.concretePlacementStart,
              ).format('MM/DD/YYYY')} to ${moment(
                concreteRequestDetail.concretePlacementEnd,
              ).format('MM/DD/YYYY')}`;
              const newNotification = await Notification.createInstance(notification);
              const memberLocationPreference = await LocationNotificationPreferences.findAll({
                where: {
                  ProjectId: concreteRequestDetail.ProjectId,
                  LocationId: concreteRequestDetail.LocationId,
                  follow: true,
                },
                include: [
                  {
                    association: 'Member',
                    attributes: ['id', 'RoleId'],
                    where: {
                      [Op.and]: [
                        {
                          id: { [Op.ne]: memberDetails.id },
                        },
                      ],
                    },
                    include: [
                      {
                        association: 'User',
                        attributes: ['id', 'firstName', 'lastName', 'email'],
                      },
                    ],
                  },
                ],
              });
              const locationFollowMembers = [];
              memberLocationPreference.forEach(async (element) => {
                locationFollowMembers.push(element.Member.id);
              });
              const personData = await ConcreteRequestResponsiblePerson.findAll({
                where: { ConcreteRequestId: newConcreteRequestData.id, isDeleted: false },
                include: [
                  {
                    association: 'Member',
                    include: [
                      {
                        association: 'User',
                        attributes: ['id', 'firstName', 'lastName', 'email'],
                      },
                    ],
                    where: {
                      [Op.and]: {
                        RoleId: {
                          [Op.notIn]: [1, 2],
                        },
                        id: { [Op.notIn]: locationFollowMembers },
                      },
                    },
                    attributes: ['id', 'RoleId'],
                  },
                ],
                attributes: ['id'],
              });
              const adminData = await Member.findAll({
                where: {
                  [Op.and]: [
                    { ProjectId: concreteRequestDetail.ProjectId },
                    { isDeleted: false },
                    { id: { [Op.in]: responsiblePersons } },
                    { id: { [Op.ne]: newNotification.MemberId } },
                    { id: { [Op.notIn]: locationFollowMembers } },
                  ],
                },
                include: [
                  {
                    association: 'User',
                    attributes: ['id', 'firstName', 'lastName', 'email'],
                  },
                ],
                attributes: ['id'],
              });
              if (memberLocationPreference && memberLocationPreference.length > 0) {
                // here 3-(NotificationPreferenceItemId - When a new delivery/crane/concrete request added to the project)
                await pushNotification.sendMemberLocationPreferencePushNotificationForConcrete(
                  memberLocationPreference,
                  newConcreteRequestData.ConcreteRequestId,
                  history.locationFollowDescription,
                  newConcreteRequestData.requestType,
                  newConcreteRequestData.ProjectId,
                  newConcreteRequestData.id,
                  3,
                );
                // here 3-(NotificationPreferenceItemId - When a new delivery/crane/concrete request added to the project)
                await notificationHelper.createMemberDeliveryLocationInAppNotification(
                  DeliveryPersonNotification,
                  concreteRequestDetail.ProjectId,
                  newNotification.id,
                  memberLocationPreference,
                  3,
                );
              }
              history.memberData = personData;
              history.adminData = adminData;
              history.firstName = loginUser.firstName;
              history.profilePic = loginUser.profilePic;
              history.createdAt = new Date();
              history.ProjectId = concreteRequestDetail.ProjectId;
              history.projectName = projectDetails.projectName;
              // here 3-(NotificationPreferenceItemId - When a new delivery/crane/concrete request added to the project)
              await notificationHelper.createDeliveryPersonNotification(
                adminData,
                [],
                projectDetails,
                newNotification,
                DeliveryPersonNotification,
                memberDetails,
                loginUser,
                3,
                'created a',
                'Concrete Request',
                `concrete Booking (${newConcreteRequestData.ConcreteRequestId} - ${newConcreteRequestData.description})`,
                newConcreteRequestData.ConcreteRequestId,
              );
              const checkMemberNotification = await NotificationPreference.findAll({
                where: {
                  ProjectId: concreteRequestDetail.ProjectId,
                  isDeleted: false,
                },
                attributes: [
                  'id',
                  'MemberId',
                  'ProjectId',
                  'ParentCompanyId',
                  'NotificationPreferenceItemId',
                  'instant',
                  'dailyDigest',
                ],
                include: [
                  {
                    association: 'NotificationPreferenceItem',
                    where: {
                      id: 3,
                      isDeleted: false,
                    },
                    attributes: ['id', 'description', 'inappNotification', 'emailNotification'],
                  },
                ],
              });
              await concreteRequestService.sendEmailNotificationToUser(
                history,
                memberDetails,
                loginUser,
                newConcreteRequestData,
                concreteRequestDetail,
                memberLocationPreference,
              );
              const memberLocationPreferenceNotify = await LocationNotificationPreferences.findAll({
                where: {
                  ProjectId: concreteRequestDetail.ProjectId,
                  LocationId: concreteRequestDetail.LocationId,
                  follow: true,
                },
                include: [
                  {
                    association: 'Member',
                    attributes: ['id', 'RoleId'],
                    where: {
                      [Op.and]: [
                        {
                          id: { [Op.ne]: memberDetails.id },
                        },
                      ],
                    },
                    include: [
                      {
                        association: 'User',
                        attributes: ['id', 'firstName', 'lastName', 'email'],
                      },
                    ],
                  },
                ],
              });
              history.notificationPreference = checkMemberNotification;
              // here 3-(NotificationPreferenceItemId - When a new delivery/crane/concrete request added to the project)
              await pushNotification.sendPushNotificationForConcrete(
                history,
                3,
                concreteRequestDetail.ProjectId,
              );
              history.ConcreteRequestId = newConcreteRequestData.ConcreteRequestId;
              if (memberLocationPreferenceNotify && memberLocationPreferenceNotify.length > 0) {
                history.memberData.push(...memberLocationPreferenceNotify);
              }
              return done(history, false);
            }
            return done(null, {
              message: 'Bookings will not be created for the scheduled date/time',
            });
          }
          return done(null, {
            message: 'You are not allowed create Concrete Booking for this project.',
          });
        });
      } else {
        return done(null, { message: 'Project does not exist.' });
      }
    } catch (e) {
      done(null, e);
    }
  },
  async concreteCheckInputDatas(inputData, done) {
    // await this.getDynamicModel(inputData);
    const concreteRequestData = inputData.body;
    const { concreteSupplier } = concreteRequestData;
    const { responsiblePersons } = concreteRequestData;
    const inputProjectId = +concreteRequestData.ProjectId;
    const memberList = await Member.count({
      where: { id: { [Op.in]: responsiblePersons }, ProjectId: inputProjectId, isDeleted: false },
    });
    const companyList = await Company.count({
      where: {
        [Op.or]: [
          {
            id: { [Op.in]: concreteSupplier },
            ProjectId: +inputProjectId,
            isDeleted: false,
          },
          {
            id: {
              [Op.in]: concreteSupplier,
            },
            isParent: true,
            ParentCompanyId: +concreteRequestData.ParentCompanyId,
            isDeleted: false,
          },
        ],
      },
    });
    if (
      concreteRequestData.responsiblePersons &&
      concreteRequestData.responsiblePersons.length > 0 &&
      memberList !== responsiblePersons.length
    ) {
      return done(null, { message: 'Some Member is not in the project' });
    }
    if (
      concreteRequestData.concreteSupplier &&
      concreteRequestData.concreteSupplier.length > 0 &&
      companyList !== concreteSupplier.length
    ) {
      return done(null, { message: 'Some Company is not in the project' });
    }
    // if (concreteRequestData.location && concreteRequestData.location.length > 0) {
    //   const locationTempArray = [];
    //   concreteRequestData.location.forEach((element) => {
    //     if (element.chosenFromDropdown) {
    //       locationTempArray.push(element.id);
    //     }
    //   });
    //   const concreteLocationCount = await ConcreteLocation.count({
    //     where: { id: { [Op.in]: locationTempArray }, ProjectId: inputProjectId, isDeleted: false },
    //   });
    //   if (concreteLocationCount !== locationTempArray.length) {
    //     return done(null, { message: 'Some location is not in the project' });
    //   }
    // }
    if (concreteRequestData.mixDesign && concreteRequestData.mixDesign.length > 0) {
      const mixDesignTempArray = [];
      concreteRequestData.mixDesign.forEach((element) => {
        if (element.chosenFromDropdown) {
          mixDesignTempArray.push(element.id);
        }
      });
      const concreteMixDesignCount = await ConcreteMixDesign.count({
        where: { id: { [Op.in]: mixDesignTempArray }, ProjectId: inputProjectId, isDeleted: false },
      });
      if (concreteMixDesignCount !== mixDesignTempArray.length) {
        return done(null, { message: 'Some Mix Design is not in the project' });
      }
    }
    if (concreteRequestData.pumpSize && concreteRequestData.pumpSize.length > 0) {
      const pumpSizeTempArray = [];
      concreteRequestData.pumpSize.forEach((element) => {
        if (element.chosenFromDropdown) {
          pumpSizeTempArray.push(element.id);
        }
      });
      const concretePumpSizeCount = await ConcretePumpSize.count({
        where: { id: { [Op.in]: pumpSizeTempArray }, ProjectId: inputProjectId, isDeleted: false },
      });
      if (concretePumpSizeCount !== pumpSizeTempArray.length) {
        return done(null, { message: 'Some Pump Size is not in the project' });
      }
    }
    return done(true, false);
  },
  async getNDRData(inputData, done) {
    try {
      // await this.getDynamicModel(inputData);
      const { params } = inputData;
      const condition = {
        id: params.DeliveryRequestId,
      };
      const deliveryList = await DeliveryRequest.guestGetNDRData(condition);
      return deliveryList;
    } catch (e) {
      throw new Error(e);
    }
  },
  async getSingleCraneRequest(inputData) {
    try {
      // await this.getDynamicModel(inputData);
      const { params } = inputData;
      const getCraneRequest = await CraneRequest.findOne({
        where: { CraneRequestId: params.CraneRequestId, ProjectId: params.ProjectId },
      });
      const craneRequest = await CraneRequest.guestSingleCraneRequestData({
        id: +getCraneRequest.id,
      });
      return craneRequest;
    } catch (e) {
      throw new Error(e);
    }
  },
  async getSingleConcreteRequest(inputData) {
    try {
      // await this.getDynamicModel(inputData);
      const { params } = inputData;
      const getConcreteRequest = await ConcreteRequest.findOne({
        where: {
          ConcreteRequestId: params.ConcreteRequestId,
          ProjectId: params.ProjectId,
          isDeleted: false,
        },
      });
      const concreteRequest = await ConcreteRequest.guestSingleConcreteRequestData({
        id: +getConcreteRequest.id,
      });
      return concreteRequest;
    } catch (e) {
      throw new Error(e);
    }
  },
  async editRequest(inputData, done) {
    try {
      // await this.getDynamicModel(inputData);
      const deliveryData = inputData.body;
      const userDetail = await User.findOne({
        where: { id: inputData.body.userId, isDeleted: false },
      });
      inputData.user = userDetail;
      const loginUser = inputData.user;
      const { recurrenceId } = deliveryData;
      let editSeriesRequests;
      let newRecurrenceId;
      let previousRecordInSeries;
      const projectSettingDetails = await Project.getProjectAndSettings({
        isDeleted: false,
        id: +deliveryData.ProjectId,
      });
      if (projectSettingDetails) {
        const requestData = await DeliveryRequest.getSingleDeliveryRequestData({
          id: deliveryData.id,
        });
        if (deliveryData.seriesOption === 1) {
          const requestArray = [];
          requestArray.push({
            ProjectId: deliveryData.ProjectId,
            deliveryStart: deliveryData.deliveryStart,
            deliveryEnd: deliveryData.deliveryEnd,
            id: deliveryData.id,
          });
          const isOverlapping = await deliveryService.checkDoubleBookingAllowedOrNot(
            requestArray,
            projectSettingDetails,
            'edit',
            deliveryData.GateId,
          );
          if (isOverlapping && isOverlapping.error) {
            return done(null, {
              message: isOverlapping.message,
            });
          }
        }
        if (deliveryData.seriesOption === 2 || deliveryData.seriesOption === 3) {
          let requestSeries = [];
          if (deliveryData.seriesOption === 2) {
            requestSeries = await DeliveryRequest.findAll({
              where: [
                Sequelize.and({
                  recurrenceId,
                  id: {
                    [Op.gte]: deliveryData.id,
                  },
                }),
              ],
            });
          }
          if (deliveryData.seriesOption === 3) {
            requestSeries = await DeliveryRequest.findAll({
              where: [
                Sequelize.and({
                  recurrenceId,
                  deliveryStart: {
                    [Op.gte]: moment.tz(deliveryData.timeZone).utc().format('YYYY-MM-DD HH:mm:ssZ'),
                  },
                }),
              ],
            });
          }
          const requestArray = [];
          for (let i = 0; i < requestSeries.length; i += 1) {
            const deliveryStartDate = await this.convertTimezoneToUtc(
              moment
                .utc(requestSeries[i].deliveryStart)
                .tz(deliveryData.timezone)
                .format('MM/DD/YYYY'),
              deliveryData.timezone,
              deliveryData.deliveryStartTime,
            );
            const deliveryEndDate = await this.convertTimezoneToUtc(
              moment
                .utc(requestSeries[i].deliveryEnd)
                .tz(deliveryData.timezone)
                .format('MM/DD/YYYY'),
              deliveryData.timezone,
              deliveryData.deliveryEndTime,
            );
            requestArray.push({
              ProjectId: deliveryData.ProjectId,
              deliveryStart: !moment(deliveryStartDate).isSame(
                moment(requestSeries[i].deliveryStart),
              )
                ? deliveryStartDate
                : requestSeries[i].deliveryStart,
              deliveryEnd: !moment(deliveryEndDate).isSame(moment(requestSeries[i].deliveryEnd))
                ? deliveryEndDate
                : requestSeries[i].deliveryEnd,
              id: requestSeries[i].id,
            });
          }
          const utcRecurrenceEndDate = requestData.recurrence.recurrenceEndDate;
          const existingRecurrenceEndDate = moment(utcRecurrenceEndDate)
            .tz(deliveryData.timezone)
            .format('YYYY-MM-DD');
          const newRecurrenceEndDate = deliveryData.recurrenceEndDate;
          if (!moment(existingRecurrenceEndDate).isSame(moment(newRecurrenceEndDate))) {
            const startDate = moment(existingRecurrenceEndDate).add(1, 'day');
            const endDate = moment(newRecurrenceEndDate);
            for (let date = startDate; date.isSameOrBefore(endDate); date.add(1, 'day')) {
              requestArray.push({
                ProjectId: deliveryData.ProjectId,
                deliveryStart: await this.convertTimezoneToUtc(
                  moment(date).format('MM/DD/YYYY'),
                  deliveryData.timezone,
                  deliveryData.deliveryStartTime,
                ),
                deliveryEnd: await this.convertTimezoneToUtc(
                  moment(date).format('MM/DD/YYYY'),
                  deliveryData.timezone,
                  deliveryData.deliveryEndTime,
                ),
              });
            }
            if (requestArray.length > 0) {
              const isOverlapping = await deliveryService.checkDoubleBookingAllowedOrNot(
                requestArray,
                projectSettingDetails,
                'edit',
                deliveryData.GateId,
              );
              if (isOverlapping && isOverlapping.error) {
                return done(null, {
                  message: isOverlapping.message,
                });
              }
            }
          } else if (requestArray.length > 0) {
            const isOverlapping = await deliveryService.checkDoubleBookingAllowedOrNot(
              requestArray,
              projectSettingDetails,
              'edit',
              deliveryData.GateId,
            );
            if (isOverlapping && isOverlapping.error) {
              return done(null, {
                message: isOverlapping.message,
              });
            }
          }
        }
      }
      const roleDetails = await Role.getBy('Project Admin');
      const accountRoleDetails = await Role.getBy('Account Admin');
      // This event
      if (deliveryData.seriesOption === 1) {
        editSeriesRequests = await DeliveryRequest.findAll({
          where: [
            Sequelize.and({
              id: deliveryData.id,
            }),
          ],
        });
        if (editSeriesRequests && editSeriesRequests[0] && deliveryData.recurrenceId) {
          const previousRecordInThisEventSeries = await DeliveryRequest.findAll({
            where: [
              Sequelize.and({
                recurrenceId,
                id: {
                  [Op.lt]: deliveryData.id,
                },
              }),
            ],
            order: [['id', 'DESC']],
          });
          const NextSeriesLastRecord = await DeliveryRequest.findAll({
            where: [
              Sequelize.and({
                recurrenceId,
                id: {
                  [Op.gt]: deliveryData.id,
                },
              }),
            ],
            order: [['id', 'DESC']],
          });
          if (
            ((NextSeriesLastRecord && NextSeriesLastRecord.length > 0) ||
              (previousRecordInThisEventSeries && previousRecordInThisEventSeries.length > 0)) &&
            !(
              NextSeriesLastRecord &&
              NextSeriesLastRecord.length > 0 &&
              previousRecordInThisEventSeries &&
              previousRecordInThisEventSeries.length > 0
            )
          ) {
            if (NextSeriesLastRecord && NextSeriesLastRecord.length > 0) {
              const chosenTimezoneDeliveryStart = moment.tz(
                `${deliveryData.nextSeriesRecurrenceStartDate}  '00:00'`,
                'YYYY-MM-DD HH:mm',
                deliveryData.timezone,
              );
              const utcDate = chosenTimezoneDeliveryStart
                .clone()
                .tz('UTC')
                .format('YYYY-MM-DD HH:mm:ssZ');
              await RequestRecurrenceSeries.update(
                {
                  recurrenceStartDate: utcDate,
                },
                {
                  where: {
                    id: NextSeriesLastRecord[0].recurrenceId,
                  },
                },
              );
            }
            if (previousRecordInThisEventSeries && previousRecordInThisEventSeries.length > 0) {
              // const chosenTimezoneDeliveryStart = moment.tz(
              //   `${previousRecordInThisEventSeries[0].deliveryData.previousSeriesRecurrenceEndDate}  '00:00'`,
              //   'YYYY-MM-DD HH:mm',
              //   deliveryData.timezone,
              // );
              // const utcDate = chosenTimezoneDeliveryStart
              //   .clone()
              //   .tz('UTC')
              //   .format('YYYY-MM-DD HH:mm:ssZ');
              await RequestRecurrenceSeries.update(
                {
                  recurrenceEndDate: previousRecordInThisEventSeries[0].deliveryStart,
                },
                {
                  where: {
                    id: previousRecordInThisEventSeries[0].recurrenceId,
                  },
                },
              );
            }
          }
        }
      }
      // This and all following events
      if (deliveryData.seriesOption === 2) {
        editSeriesRequests = await DeliveryRequest.findAll({
          where: [
            Sequelize.and({
              recurrenceId,
              id: {
                [Op.gte]: deliveryData.id,
              },
            }),
          ],
        });
        previousRecordInSeries = await DeliveryRequest.findOne({
          where: [
            Sequelize.and({
              recurrenceId,
              id: {
                [Op.lt]: deliveryData.id,
              },
            }),
          ],
          order: [['id', 'DESC']],
        });
      }
      // All events in the series
      if (deliveryData.seriesOption === 3) {
        editSeriesRequests = await DeliveryRequest.findAll({
          where: [
            Sequelize.and({
              recurrenceId,
              deliveryStart: {
                [Op.gte]: moment.tz(deliveryData.timeZone).utc().format('YYYY-MM-DD HH:mm:ssZ'),
              },
            }),
          ],
        });
      }

      if (editSeriesRequests && editSeriesRequests[0] && editSeriesRequests.length > 0) {
        const requestData = await DeliveryRequest.getSingleDeliveryRequestData({
          id: editSeriesRequests[0].id,
        });
        if (requestData && requestData.recurrence) {
          requestData.recurrence.ParentCompanyId = deliveryData.ParentCompanyId;
          requestData.recurrence.ProjectId = deliveryData.ProjectId;
          if (deliveryData.seriesOption === 1) {
            requestData.recurrence.deliveryStart = deliveryData.recurrenceSeriesStartDate;
            requestData.recurrence.deliveryEnd = deliveryData.recurrenceSeriesEndDate;
          }
          if (deliveryData.seriesOption === 2) {
            requestData.recurrence.deliveryStart = deliveryData.recurrenceSeriesStartDate;
            requestData.recurrence.deliveryEnd = deliveryData.recurrenceEndDate;
          }
          if (deliveryData.seriesOption === 2 && previousRecordInSeries) {
            newRecurrenceId = await concreteRequestService.insertRecurrenceSeries(
              requestData.recurrence,
              loginUser,
              requestData.requestType,
              deliveryData.timezone,
            );
          }
        }
        if (deliveryData.seriesOption === 2 || deliveryData.seriesOption === 3) {
          const utcRecurrenceEndDate = requestData.recurrence.recurrenceEndDate;
          let existingRecurrenceEndDate = moment(utcRecurrenceEndDate)
            .tz(deliveryData.timezone)
            .format('YYYY-MM-DD');
          const newRecurrenceEndDate = deliveryData.recurrenceEndDate;
          if (!moment(existingRecurrenceEndDate).isSame(moment(newRecurrenceEndDate))) {
            const dates = [];
            const chosenTimezoneDeliveryStart = moment.tz(
              `${deliveryData.recurrenceEndDate}  '00:00'`,
              'YYYY-MM-DD HH:mm',
              deliveryData.timezone,
            );
            const utcDate = chosenTimezoneDeliveryStart
              .clone()
              .tz('UTC')
              .format('YYYY-MM-DD HH:mm:ssZ');
            await RequestRecurrenceSeries.update(
              {
                recurrenceEndDate: utcDate,
              },
              {
                where: {
                  id: deliveryData.recurrenceId,
                },
              },
            );
            while (moment(existingRecurrenceEndDate).isBefore(moment(newRecurrenceEndDate))) {
              existingRecurrenceEndDate = moment(existingRecurrenceEndDate).add(1, 'day');
              dates.push(moment(existingRecurrenceEndDate).format('MM/DD/YYYY'));
            }
            await deliveryService.createCopyofDeliveryRequest(
              requestData,
              deliveryData,
              dates,
              loginUser,
              newRecurrenceId || deliveryData.recurrenceId,
            );
          }
        }
        if (deliveryData.seriesOption === 2 && previousRecordInSeries) {
          // const chosenTimezoneDeliveryStart = moment.tz(
          //   `${deliveryData.previousSeriesRecurrenceEndDate}  '00:00'`,
          //   'YYYY-MM-DD HH:mm',
          //   deliveryData.timezone,
          // );
          // const utcDate = chosenTimezoneDeliveryStart
          //   .clone()
          //   .tz('UTC')
          //   .format('YYYY-MM-DD HH:mm:ssZ');
          await RequestRecurrenceSeries.update(
            {
              recurrenceEndDate: previousRecordInSeries.deliveryStart,
            },
            {
              where: {
                id: previousRecordInSeries.recurrenceId,
              },
            },
          );
        }
      }
      for (let indexLoop = 0; indexLoop < editSeriesRequests.length; indexLoop += 1) {
        const seriesData = editSeriesRequests[indexLoop];
        const idDetails = await DeliveryRequest.findOne({
          where: [
            Sequelize.and({
              id: seriesData.id,
            }),
          ],
        });
        if (!idDetails) {
          return done(null, { message: 'Delivery Booking id is not available' });
        }
        const existsDeliveryRequest = await DeliveryRequest.getSingleDeliveryRequestData({
          id: +idDetails.id,
        });
        if (existsDeliveryRequest) {
          await this.checkInputDatas(inputData, async (checkResponse, checkError) => {
            if (checkError) {
              return done(null, checkError);
            }
            const memberData = await Member.getBy({
              UserId: loginUser.id,
              ProjectId: deliveryData.ProjectId,
            });
            const history = {
              DeliveryRequestId: idDetails.id,
              DeliveryId: idDetails.DeliveryId,
              MemberId: memberData.id,
              type: 'edit',
              description: `${loginUser.firstName} ${loginUser.lastName} Edited this Delivery Booking.`,
            };
            const notification = history;
            let lastData = {};
            lastData = await CraneRequest.findOne({
              where: { ProjectId: +deliveryData.ProjectId, isDeleted: false },
              order: [['CraneRequestId', 'DESC']],
            });
            const deliveryRequestList = await DeliveryRequest.findOne({
              where: {
                ProjectId: +deliveryData.ProjectId,
                isDeleted: false,
                isAssociatedWithCraneRequest: true,
              },
              order: [['CraneRequestId', 'DESC']],
            });
            if (deliveryRequestList) {
              if (lastData) {
                if (deliveryRequestList.CraneRequestId > lastData.CraneRequestId) {
                  lastData.CraneRequestId = deliveryRequestList.CraneRequestId;
                }
              } else {
                lastData = {};
                lastData.CraneRequestId = deliveryRequestList.CraneRequestId;
              }
            }
            if (lastData) {
              const data = lastData.CraneRequestId;
              lastData.CraneRequestId = 0;
              lastData.CraneRequestId = data + 1;
            } else {
              lastData = {};
              lastData.CraneRequestId = 1;
            }
            let craneId = 0;
            const newId = JSON.parse(JSON.stringify(lastData));
            if (newId && newId.CraneRequestId !== null && newId.CraneRequestId !== undefined) {
              craneId = newId.CraneRequestId;
            }
            const DeliverParam = {
              description: deliveryData.description,
              escort: deliveryData.escort,
              vehicleDetails: deliveryData.vehicleDetails,
              notes: deliveryData.notes,
              isAssociatedWithCraneRequest: deliveryData.isAssociatedWithCraneRequest,
              requestType: deliveryData.requestType,
              cranePickUpLocation: deliveryData.cranePickUpLocation,
              craneDropOffLocation: deliveryData.craneDropOffLocation,
              // CraneRequestId: deliveryData.CraneRequestId,
              recurrenceId: deliveryData.seriesOption !== 1 ? newRecurrenceId : null,
              LocationId: deliveryData.LocationId,
            };
            if (
              !idDetails.CraneRequestId &&
              deliveryData.requestType === 'deliveryRequestWithCrane'
            ) {
              DeliverParam.CraneRequestId = craneId;
            }
            if (deliveryData.seriesOption === 1) {
              DeliverParam.deliveryStart = deliveryData.deliveryStart;
              DeliverParam.deliveryEnd = deliveryData.deliveryEnd;
            }
            if (deliveryData.seriesOption === 2 || deliveryData.seriesOption === 3) {
              const utcDeliveryStartTimestamp = moment.utc(idDetails.deliveryStart);
              const localStartTimestamp = utcDeliveryStartTimestamp.tz(deliveryData.timezone);
              const utcDeliveryEndTimestamp = moment.utc(idDetails.deliveryEnd);
              const localEndTimestamp = utcDeliveryEndTimestamp.tz(deliveryData.timezone);
              DeliverParam.deliveryStart = await this.convertTimezoneToUtc(
                moment(localStartTimestamp).format('MM/DD/YYYY'),
                deliveryData.timezone,
                deliveryData.deliveryStartTime,
              );
              DeliverParam.deliveryEnd = await this.convertTimezoneToUtc(
                moment(localEndTimestamp).format('MM/DD/YYYY'),
                deliveryData.timezone,
                deliveryData.deliveryEndTime,
              );
            }

            if (
              ((memberData.RoleId === 2 || memberData.RoleId === 1) &&
                idDetails.status === 'Approved') ||
              memberData.isAutoApproveEnabled ||
              projectSettingDetails.ProjectSettings.isAutoApprovalEnabled
            ) {
              DeliverParam.status = 'Approved';
              DeliverParam.approvedBy = memberData.id;
              DeliverParam.approved_at = new Date();
            }
            await DeliveryRequest.update(DeliverParam, {
              where: { id: idDetails.id },
            });
            const { companies, persons, define } = deliveryData;
            const gates = [deliveryData.GateId];
            const equipments = deliveryData.EquipmentId;
            const condition = Sequelize.and({
              ProjectId: deliveryData.ProjectId,
              DeliveryId: idDetails.id,
            });
            const updateParam = {
              DeliveryId: idDetails.id,
              DeliveryCode: idDetails.DeliveryId,
              ProjectId: deliveryData.ProjectId,
              isDeleted: false,
              isActive: true,
            };
            const existCompanies = await DeliverCompany.findAll({ where: condition });
            const existGate = await DeliverGate.findAll({ where: condition });
            const existEquipment = await DeliverEquipment.findAll({ where: condition });
            const existPerson = await DeliveryPerson.findAll({ where: condition });
            const existDefine = await DeliverDefine.findAll({ where: condition });
            await deliveryService.updateValues(condition, async (response, error) => {
              if (!error) {
                const addedCompany = [];
                companies.forEach(async (element, i) => {
                  const index = existCompanies.findIndex((item) => item.CompanyId === element);
                  const companyParam = updateParam;
                  companyParam.CompanyId = element;
                  if (index !== -1) {
                    await DeliverCompany.update(companyParam, {
                      where: { id: existCompanies[index].id },
                    });
                    if (existCompanies[index].isDeleted !== false) {
                      addedCompany.push(existCompanies[index]);
                    }
                  } else {
                    const newCompanyData = await DeliverCompany.createInstance(companyParam);
                    addedCompany.push(newCompanyData);
                  }
                });
                const addedGate = [];
                gates.forEach(async (element, i) => {
                  const index = existGate.findIndex((item) => item.GateId === element);
                  const gateParam = updateParam;
                  gateParam.GateId = element;
                  if (index !== -1) {
                    await DeliverGate.update(gateParam, { where: { id: existGate[index].id } });
                    if (existGate[index].isDeleted !== false) {
                      addedGate.push(existGate[index]);
                    }
                  } else {
                    const newGateData = await DeliverGate.createInstance(gateParam);
                    addedGate.push(newGateData);
                  }
                });
                const addedEquipment = [];
                equipments.forEach(async (element, i) => {
                  const index = existEquipment.findIndex((item) => item.EquipmentId === element);
                  const equipmentParam = updateParam;
                  equipmentParam.EquipmentId = element;
                  if (index !== -1) {
                    await DeliverEquipment.update(equipmentParam, {
                      where: { id: existEquipment[index].id },
                    });
                    if (existEquipment[index].isDeleted !== false) {
                      addedEquipment.push(existEquipment[index]);
                    }
                  } else {
                    const newEquipmentData = await DeliverEquipment.createInstance(equipmentParam);
                    addedEquipment.push(newEquipmentData);
                  }
                });
                const addedPerson = [];
                persons.forEach(async (element, i) => {
                  const index = existPerson.findIndex((item) => item.MemberId === element);
                  const memberParam = updateParam;
                  memberParam.MemberId = element;
                  if (index !== -1) {
                    await DeliveryPerson.update(memberParam, {
                      where: { id: existPerson[index].id },
                    });
                    if (existPerson[index].isDeleted !== false) {
                      addedPerson.push(existPerson[index]);
                    }
                  } else {
                    const newPersonData = await DeliveryPerson.createInstance(memberParam);
                    addedPerson.push(newPersonData);
                  }
                });
                const addedDefineData = [];
                define.forEach(async (element, i) => {
                  const index = existDefine.findIndex(
                    (item) => item.DeliverDefineWorkId === element,
                  );
                  const defineParam = updateParam;
                  defineParam.DeliverDefineWorkId = element;
                  if (index !== -1) {
                    await DeliverDefine.update(defineParam, {
                      where: { id: existDefine[index].id },
                    });
                    if (existDefine[index].isDeleted !== false) {
                      addedDefineData.push(existDefine[index]);
                    }
                  } else {
                    const newDefineData = await DeliverDefine.createInstance(defineParam);
                    addedDefineData.push(newDefineData);
                  }
                });
                const locationChosen = await Locations.findOne({
                  where: {
                    ProjectId: deliveryData.ProjectId,
                    id: deliveryData.LocationId,
                  },
                });
                history.description = `${loginUser.firstName} ${loginUser.lastName} Updated the Delivery Booking, ${deliveryData.description}`;
                history.locationFollowDescription = `${loginUser.firstName} ${loginUser.lastName} Updated the Delivery Booking, ${deliveryData.description}. Location: ${locationChosen.locationPath}.`;
                history.MemberId = memberData.id;
                history.firstName = loginUser.firstName;
                history.profilePic = loginUser.profilePic;
                history.createdAt = new Date();
                history.ProjectId = deliveryData.ProjectId;
                const projectDetails = await Project.findByPk(deliveryData.ProjectId);
                history.projectName = projectDetails.projectName;
                notification.ProjectId = idDetails.ProjectId;
                notification.title = `Delivery Booking Updated by ${loginUser.firstName} ${loginUser.lastName}`;
                if (
                  existsDeliveryRequest &&
                  existsDeliveryRequest.recurrence &&
                  existsDeliveryRequest.recurrence.recurrence
                ) {
                  notification.recurrenceType = `${existsDeliveryRequest.recurrence.recurrence
                    } From ${moment(existsDeliveryRequest.recurrence.recurrenceStartDate).format(
                      'MM/DD/YYYY',
                    )} to ${moment(existsDeliveryRequest.recurrence.recurrenceEndDate).format(
                      'MM/DD/YYYY',
                    )}`;
                }
                notification.requestType = 'deliveryRequest';
                const newNotification = await Notification.createInstance(notification);
                const memberLocationPreference = await LocationNotificationPreferences.findAll({
                  where: {
                    ProjectId: deliveryData.ProjectId,
                    LocationId: deliveryData.LocationId,
                    follow: true,
                  },
                  include: [
                    {
                      association: 'Member',
                      attributes: ['id', 'RoleId'],
                      where: {
                        [Op.and]: [
                          {
                            id: { [Op.ne]: memberData.id },
                          },
                        ],
                      },
                      include: [
                        {
                          association: 'User',
                          attributes: ['id', 'firstName', 'lastName', 'email'],
                        },
                      ],
                    },
                  ],
                });
                const locationFollowMembers = [];
                memberLocationPreference.forEach(async (element) => {
                  locationFollowMembers.push(element.Member.id);
                });
                const adminData = await Member.findAll({
                  where: {
                    [Op.and]: [
                      { ProjectId: deliveryData.ProjectId },
                      { isDeleted: false },
                      { id: { [Op.in]: persons } },
                      { id: { [Op.ne]: newNotification.MemberId } },
                      { id: { [Op.notIn]: locationFollowMembers } },
                    ],
                  },
                  include: [
                    {
                      association: 'User',
                      attributes: ['id', 'firstName', 'lastName', 'email'],
                    },
                  ],
                  attributes: ['id', 'RoleId'],
                });
                if (memberLocationPreference && memberLocationPreference.length > 0) {
                  // here 5-(NotificationPreferenceItemId -When a delivery/crane/concrete request is edited)
                  await pushNotification.sendMemberLocationPreferencePushNotification(
                    memberLocationPreference,
                    deliveryData.DeliveryRequestId,
                    history.locationFollowDescription,
                    deliveryData.requestType,
                    deliveryData.ProjectId,
                    deliveryData.id,
                    5,
                  );
                  // here 5-(NotificationPreferenceItemId -When a delivery/crane/concrete request is edited)
                  await notificationHelper.createMemberDeliveryLocationInAppNotification(
                    DeliveryPersonNotification,
                    deliveryData.ProjectId,
                    newNotification.id,
                    memberLocationPreference,
                    5,
                  );
                }
                history.adminData = adminData;
                if (memberLocationPreference && memberLocationPreference.length > 0) {
                  history.memberData = [];
                  history.memberData.push(...memberLocationPreference);
                }
                // here 5-(NotificationPreferenceItemId -When a delivery/crane/concrete request is edited)
                await notificationHelper.createDeliveryPersonNotification(
                  adminData,
                  [],
                  projectDetails,
                  newNotification,
                  DeliveryPersonNotification,
                  memberData,
                  loginUser,
                  5,
                  'updated a',
                  'Delivery Request',
                  `delivery Booking (${idDetails.DeliveryId} - ${idDetails.description})`,
                  idDetails.id,
                );
                const checkMemberNotification = await NotificationPreference.findAll({
                  where: {
                    ProjectId: deliveryData.ProjectId,
                    isDeleted: false,
                  },
                  attributes: [
                    'id',
                    'MemberId',
                    'ProjectId',
                    'ParentCompanyId',
                    'NotificationPreferenceItemId',
                    'instant',
                    'dailyDigest',
                  ],
                  include: [
                    {
                      association: 'NotificationPreferenceItem',
                      where: {
                        id: 5,
                        isDeleted: false,
                      },
                      attributes: ['id', 'description', 'inappNotification', 'emailNotification'],
                    },
                  ],
                });
                history.notificationPreference = checkMemberNotification;
                const updatedDeliveryRequest = await DeliveryRequest.getSingleDeliveryRequestData({
                  id: +idDetails.id,
                });
                if (
                  updatedDeliveryRequest.status === 'Approved' &&
                  idDetails.status !== 'Approved' &&
                  idDetails.isQueued === false
                ) {
                  const object = {
                    ProjectId: deliveryData.ProjectId,
                    MemberId: memberData.id,
                    DeliveryRequestId: updatedDeliveryRequest.id,
                    isDeleted: false,
                    type: 'approved',
                    description: history.description,
                  };
                  await DeliverHistory.createInstance(object);
                }
                if (!deliveryData.ndrStatus) {
                  await deliveryService.updateEditDeliveryRequestHistory(
                    deliveryData,
                    existsDeliveryRequest,
                    updatedDeliveryRequest,
                    history,
                    loginUser,
                  );
                  // here 5-(NotificationPreferenceItemId -When a delivery/crane/concrete request is edited)
                  await pushNotification.sendDeviceToken(history, 5, deliveryData.ProjectId);
                }
                const editedNDR = await DeliveryRequest.getNDRData({
                  id: idDetails.id,
                });
                if (editedNDR.isQueued === true) {
                  if (
                    editedNDR.description &&
                    editedNDR.deliveryStart &&
                    editedNDR.deliveryEnd &&
                    editedNDR.memberDetails.length > 0 &&
                    editedNDR.companyDetails.length > 0 &&
                    // editedNDR.defineWorkDetails.length > 0 &&
                    editedNDR.gateDetails.length > 0 &&
                    editedNDR.equipmentDetails.length > 0 &&
                    editedNDR.escort !== null
                  ) {
                    await DeliveryRequest.update(
                      { isAllDetailsFilled: true },
                      {
                        where: { id: editedNDR.id },
                      },
                    );
                    if (deliveryData.updateQueuedRequest === 1) {
                      const queuedRequestPayload = {
                        isQueued: false,
                      };
                      if (
                        editedNDR.isQueued === true &&
                        (memberData.RoleId === roleDetails.id ||
                          memberData.RoleId === accountRoleDetails.id ||
                          memberData.isAutoApproveEnabled ||
                          projectSettingDetails.ProjectSettings.isAutoApprovalEnabled)
                      ) {
                        queuedRequestPayload.status = 'Approved';
                        queuedRequestPayload.approvedBy = memberData.id;
                        queuedRequestPayload.approved_at = new Date();
                        const historyUpdateObject = {
                          ProjectId: +deliveryData.ProjectId,
                          MemberId: memberData.id,
                          DeliveryRequestId: editedNDR.id,
                          isDeleted: false,
                          type: 'approved',
                          description: `${loginUser.firstName} ${loginUser.lastName} Approved Delivery Booking, ${editedNDR.description}.`,
                        };
                        await DeliverHistory.createInstance(historyUpdateObject);
                      }
                      await DeliveryRequest.update(queuedRequestPayload, {
                        where: { id: editedNDR.id },
                      });
                    }
                  }
                }
                if (!deliveryData.ndrStatus) {
                  let tagsUpdated = false;
                  let fieldsChanged = false;
                  if (
                    editedNDR.defineWorkDetails.length > 0 &&
                    existsDeliveryRequest.defineWorkDetails.length > 0
                  ) {
                    const addedDfow1 = editedNDR.defineWorkDetails.filter((el) => {
                      return !existsDeliveryRequest.defineWorkDetails.find((element) => {
                        return element.id === el.id;
                      });
                    });
                    const deletedDfow1 = existsDeliveryRequest.defineWorkDetails.filter((el) => {
                      return !existsDeliveryRequest.defineWorkDetails.find((element) => {
                        return element.id === el.id;
                      });
                    });
                    if (addedDfow1.length > 0) {
                      tagsUpdated = true;
                    }
                    if (deletedDfow1.length > 0) {
                      tagsUpdated = true;
                    }
                  }
                  if (
                    editedNDR.gateDetails.length > 0 &&
                    existsDeliveryRequest.gateDetails.length > 0
                  ) {
                    const addedGate1 = editedNDR.gateDetails.filter((el) => {
                      return !existsDeliveryRequest.gateDetails.find((element) => {
                        return element.Gate.id === el.Gate.id;
                      });
                    });
                    const deletedGate1 = existsDeliveryRequest.gateDetails.filter((el) => {
                      return !existsDeliveryRequest.gateDetails.find((element) => {
                        return element.Gate.id === el.Gate.id;
                      });
                    });
                    if (addedGate1.length > 0) {
                      tagsUpdated = true;
                    }
                    if (deletedGate1.length > 0) {
                      tagsUpdated = true;
                    }
                  }
                  if (
                    editedNDR.equipmentDetails.length > 0 &&
                    existsDeliveryRequest.equipmentDetails.length > 0
                  ) {
                    const addedEquipment1 = editedNDR.equipmentDetails.filter((el) => {
                      return !existsDeliveryRequest.equipmentDetails.find((element) => {
                        return element.Equipment.id === el.Equipment.id;
                      });
                    });
                    const deletedEquipment1 = existsDeliveryRequest.equipmentDetails.filter(
                      (el) => {
                        return !existsDeliveryRequest.equipmentDetails.find((element) => {
                          return element.Equipment.id === el.Equipment.id;
                        });
                      },
                    );
                    if (addedEquipment1.length > 0) {
                      tagsUpdated = true;
                    }
                    if (deletedEquipment1.length > 0) {
                      tagsUpdated = true;
                    }
                  }
                  if (
                    editedNDR.companyDetails.length > 0 &&
                    existsDeliveryRequest.companyDetails.length > 0
                  ) {
                    const addedCompany1 = editedNDR.companyDetails.filter((el) => {
                      return !existsDeliveryRequest.companyDetails.find((element) => {
                        return element.Company.id === el.Company.id;
                      });
                    });
                    const deletedCompany1 = existsDeliveryRequest.companyDetails.filter((el) => {
                      return !existsDeliveryRequest.companyDetails.find((element) => {
                        return element.Company.id === el.Company.id;
                      });
                    });
                    if (addedCompany1.length > 0) {
                      tagsUpdated = true;
                    }
                    if (deletedCompany1.length > 0) {
                      tagsUpdated = true;
                    }
                  }
                  if (
                    editedNDR.memberDetails.length > 0 &&
                    existsDeliveryRequest.memberDetails.length > 0
                  ) {
                    const addedMember1 = editedNDR.memberDetails.filter((el) => {
                      return !existsDeliveryRequest.memberDetails.find((element) => {
                        return element.Member.id === el.Member.id;
                      });
                    });
                    const deletedMember1 = existsDeliveryRequest.memberDetails.filter((el) => {
                      return !editedNDR.memberDetails.find((element) => {
                        return element.Member.id === el.Member.id;
                      });
                    });
                    if (addedMember1.length > 0) {
                      tagsUpdated = true;
                    }
                    if (deletedMember1.length > 0) {
                      tagsUpdated = true;
                    }
                  }
                  if (
                    existsDeliveryRequest.description !== editedNDR.description ||
                    existsDeliveryRequest.CraneRequestId !== editedNDR.CraneRequestId ||
                    existsDeliveryRequest.LocationId !== editedNDR.LocationId ||
                    existsDeliveryRequest.requestType !== editedNDR.requestType ||
                    existsDeliveryRequest.vehicleDetails !== editedNDR.vehicleDetails ||
                    existsDeliveryRequest.notes !== editedNDR.notes ||
                    existsDeliveryRequest.isAssociatedWithCraneRequest !==
                    editedNDR.isAssociatedWithCraneRequest ||
                    existsDeliveryRequest.escort !== editedNDR.escort ||
                    existsDeliveryRequest.craneDropOffLocation !== editedNDR.craneDropOffLocation ||
                    existsDeliveryRequest.cranePickUpLocation !== editedNDR.cranePickUpLocation ||
                    tagsUpdated ||
                    existsDeliveryRequest.recurrence !== editedNDR.recurrence ||
                    existsDeliveryRequest.chosenDateOfMonth !== editedNDR.chosenDateOfMonth ||
                    existsDeliveryRequest.dateOfMonth !== editedNDR.dateOfMonth ||
                    existsDeliveryRequest.monthlyRepeatType !== editedNDR.monthlyRepeatType ||
                    existsDeliveryRequest.days !== editedNDR.days ||
                    existsDeliveryRequest.repeatEveryType !== editedNDR.repeatEveryType ||
                    existsDeliveryRequest.repeatEveryCount !== editedNDR.repeatEveryCount
                  ) {
                    fieldsChanged = true;
                  }
                  let deliveryDateTimeChanged = false;
                  if (
                    moment(existsDeliveryRequest.deliveryStart).format('h:mm a') !==
                    moment(editedNDR.deliveryStart).format('h:mm a') ||
                    moment(existsDeliveryRequest.deliveryEnd).format('h:mm a') !==
                    moment(editedNDR.deliveryEnd).format('h:mm a')
                  ) {
                    deliveryDateTimeChanged = true;
                  }
                  if (existsDeliveryRequest.status === 'Delivered') {
                    if (fieldsChanged && memberData.RoleId === 2) {
                      await DeliveryRequest.update(
                        { status: 'Approved' },
                        {
                          where: { id: editedNDR.id },
                        },
                      );
                    }
                    if (
                      (fieldsChanged || deliveryDateTimeChanged) &&
                      memberData.RoleId !== 2 &&
                      !memberData.isAutoApproveEnabled &&
                      !projectSettingDetails.ProjectSettings.isAutoApprovalEnabled
                    ) {
                      await DeliveryRequest.update(
                        { status: 'Pending' },
                        {
                          where: { id: editedNDR.id },
                        },
                      );
                    }
                  }
                  if (existsDeliveryRequest.status === 'Approved') {
                    if (
                      (fieldsChanged || deliveryDateTimeChanged) &&
                      memberData.RoleId !== 2 &&
                      !memberData.isAutoApproveEnabled &&
                      !projectSettingDetails.ProjectSettings.isAutoApprovalEnabled
                    ) {
                      await DeliveryRequest.update(
                        { status: 'Pending' },
                        {
                          where: { id: editedNDR.id },
                        },
                      );
                    }
                    if (
                      ((fieldsChanged || deliveryDateTimeChanged) && memberData.RoleId === 2) ||
                      memberData.isAutoApproveEnabled ||
                      projectSettingDetails.ProjectSettings.isAutoApprovalEnabled
                    ) {
                      await DeliveryRequest.update(
                        { status: 'Approved' },
                        {
                          where: { id: editedNDR.id },
                        },
                      );
                    }
                  }
                  if (
                    existsDeliveryRequest.status === 'Expired' ||
                    existsDeliveryRequest.status === 'Declined'
                  ) {
                    if (
                      (fieldsChanged || deliveryDateTimeChanged) &&
                      memberData.RoleId !== 2 &&
                      !memberData.isAutoApproveEnabled &&
                      !projectSettingDetails.ProjectSettings.isAutoApprovalEnabled
                    ) {
                      await DeliveryRequest.update(
                        { status: 'Pending' },
                        {
                          where: { id: editedNDR.id },
                        },
                      );
                    }
                    if (
                      ((fieldsChanged || deliveryDateTimeChanged) && memberData.RoleId === 2) ||
                      memberData.isAutoApproveEnabled ||
                      projectSettingDetails.ProjectSettings.isAutoApprovalEnabled
                    ) {
                      const isStatusDataUpdated = await DeliveryRequest.update(
                        { status: 'Approved', approvedBy: memberData.id, approved_at: new Date() },
                        {
                          where: { id: editedNDR.id },
                        },
                      );
                      if (isStatusDataUpdated) {
                        const object = {
                          ProjectId: deliveryData.ProjectId,
                          MemberId: memberData.id,
                          DeliveryRequestId: updatedDeliveryRequest.id,
                          isDeleted: false,
                          type: 'approved',
                          description: `${loginUser.firstName} ${loginUser.lastName} Approved the Delivery Booking, ${deliveryData.description}`,
                        };
                        await DeliverHistory.createInstance(object);
                      }
                    }
                  }
                  if (existsDeliveryRequest.status === 'Pending') {
                    if (
                      (fieldsChanged || deliveryDateTimeChanged) &&
                      memberData.RoleId !== 2 &&
                      (memberData.isAutoApproveEnabled ||
                        projectSettingDetails.ProjectSettings.isAutoApprovalEnabled)
                    ) {
                      const isStatusDataUpdated = await DeliveryRequest.update(
                        { status: 'Approved' },
                        {
                          where: { id: editedNDR.id },
                        },
                      );
                      if (isStatusDataUpdated) {
                        const object = {
                          ProjectId: deliveryData.ProjectId,
                          MemberId: memberData.id,
                          DeliveryRequestId: updatedDeliveryRequest.id,
                          isDeleted: false,
                          type: 'approved',
                          description: `${loginUser.firstName} ${loginUser.lastName} Approved the Delivery Request, ${deliveryData.description}`,
                        };
                        await DeliverHistory.createInstance(object);
                      }
                    }
                    if (
                      ((fieldsChanged || deliveryDateTimeChanged) && memberData.RoleId === 2) ||
                      memberData.isAutoApproveEnabled ||
                      projectSettingDetails.ProjectSettings.isAutoApprovalEnabled
                    ) {
                      const isStatusDataUpdated = await DeliveryRequest.update(
                        { status: 'Approved', approvedBy: memberData.id, approved_at: new Date() },
                        {
                          where: { id: editedNDR.id },
                        },
                      );
                      if (isStatusDataUpdated) {
                        const object = {
                          ProjectId: deliveryData.ProjectId,
                          MemberId: memberData.id,
                          DeliveryRequestId: updatedDeliveryRequest.id,
                          isDeleted: false,
                          type: 'approved',
                          description: `${loginUser.firstName} ${loginUser.lastName} Approved the Delivery Request, ${deliveryData.description}`,
                        };
                        await DeliverHistory.createInstance(object);
                      }
                    }
                  }
                  if (+memberData.RoleId === 4 || +memberData.RoleId === 3) {
                    const userEmails = await deliveryService.getMemberDetailData(
                      history,
                      memberLocationPreference,
                    );
                    if (userEmails.length > 0) {
                      userEmails.forEach(async (element) => {
                        if (element.RoleId === 2) {
                          let name;
                          if (!element.firstName) {
                            name = 'user';
                          } else {
                            name = `${element.firstName} ${element.lastName}`;
                          }
                          const memberRole = await Role.findOne({
                            where: {
                              id: memberData.RoleId,
                              isDeleted: false,
                            },
                          });
                          const mailPayload = {
                            name,
                            email: element.email,
                            content: `We would like to inform you that 
                          ${loginUser.firstName} ${loginUser.lastName} - ${memberRole.roleName} has updated a delivery booking ${idDetails.DeliveryId} and waiting for your approval.Kindly review the booking and update the status.`,
                          };
                          const isMemberFollowLocation =
                            await LocationNotificationPreferences.findOne({
                              where: {
                                MemberId: +element.MemberId,
                                ProjectId: +deliveryData.ProjectId,
                                LocationId: +deliveryData.LocationId,
                                isDeleted: false,
                                // follow: true,
                              },
                            });
                          if (isMemberFollowLocation) {
                            const memberNotification = await NotificationPreference.findOne({
                              where: {
                                MemberId: +element.MemberId,
                                ProjectId: +deliveryData.ProjectId,
                                isDeleted: false,
                              },
                              include: [
                                {
                                  association: 'NotificationPreferenceItem',
                                  where: {
                                    id: 9,
                                    isDeleted: false,
                                  },
                                },
                              ],
                            });
                            if (memberNotification && memberNotification.instant) {
                              await MAILER.sendMail(
                                mailPayload,
                                'notifyPAForReApproval',
                                `Delivery Booking updated by ${loginUser.firstName} ${loginUser.lastName} - ${memberRole.roleName}`,
                                `Delivery Booking updated by ${loginUser.firstName} ${loginUser.lastName} - ${memberRole.roleName}`,
                                async (info, err) => {
                                  console.log(info, err);
                                },
                              );
                            }
                            if (memberNotification && memberNotification.dailyDigest) {
                              await deliveryService.createDailyDigestDataApproval(
                                +memberData.RoleId,
                                +element.MemberId,
                                +deliveryData.ProjectId,
                                +deliveryData.ParentCompanyId,
                                loginUser,
                                'updated a',
                                'Delivery Request',
                                `delivery Booking (${idDetails.DeliveryId} - ${idDetails.description})`,
                                'and waiting for your approval',
                                idDetails.id,
                              );
                            }
                          }
                        }
                      });
                      return done(history, false);
                    }
                  } else {
                    return done(history, false);
                  }
                } else {
                  return done(history, false);
                }
              } else {
                return done(null, error);
              }
            });
          });
        }
      }
    } catch (e) {
      return done(null, e);
    }
  },
  async editCraneRequest(inputData, done) {
    try {
      // await this.getDynamicModel(inputData);
      const craneRequestData = inputData.body;
      const userDetail = await User.findOne({
        where: { id: inputData.body.userId, isDeleted: false },
      });
      inputData.user = userDetail;
      const loginUser = inputData.user;
      const { recurrenceId } = craneRequestData;
      let editSeriesRequests;
      let newRecurrenceId;
      let previousRecordInSeries;
      const projectSettingDetails = await Project.getProjectAndSettings({
        isDeleted: false,
        id: +craneRequestData.ProjectId,
      });
      if (projectSettingDetails) {
        const requestData = await CraneRequest.getSingleCraneRequestData({
          id: craneRequestData.id,
        });
        if (craneRequestData.seriesOption === 1) {
          const requestArray = [];
          requestArray.push({
            ProjectId: craneRequestData.ProjectId,
            craneDeliveryStart: craneRequestData.craneDeliveryStart,
            craneDeliveryEnd: craneRequestData.craneDeliveryEnd,
            id: craneRequestData.id,
          });
          const isOverlapping = await craneRequestService.checkDoubleBookingAllowedOrNot(
            requestArray,
            projectSettingDetails,
            'edit',
          );
          if (isOverlapping && isOverlapping.error) {
            return done(null, {
              message: isOverlapping.message,
            });
          }
        }
        if (craneRequestData.seriesOption === 2 || craneRequestData.seriesOption === 3) {
          let requestSeries = [];
          if (craneRequestData.seriesOption === 2) {
            requestSeries = await CraneRequest.findAll({
              where: [
                Sequelize.and({
                  recurrenceId,
                  id: {
                    [Op.gte]: craneRequestData.id,
                  },
                }),
              ],
            });
          }
          if (craneRequestData.seriesOption === 3) {
            requestSeries = await CraneRequest.findAll({
              where: [
                Sequelize.and({
                  recurrenceId,
                  craneDeliveryStart: {
                    [Op.gte]: moment
                      .tz(craneRequestData.timeZone)
                      .utc()
                      .format('YYYY-MM-DD HH:mm:ssZ'),
                  },
                }),
              ],
            });
          }
          const requestArray = [];
          for (let i = 0; i < requestSeries.length; i += 1) {
            const deliveryStartDate = await concreteRequestService.convertTimezoneToUtc(
              moment
                .utc(requestSeries[i].craneDeliveryStart)
                .tz(craneRequestData.timezone)
                .format('MM/DD/YYYY'),
              craneRequestData.timezone,
              craneRequestData.deliveryStartTime,
            );
            const deliveryEndDate = await concreteRequestService.convertTimezoneToUtc(
              moment
                .utc(requestSeries[i].craneDeliveryEnd)
                .tz(craneRequestData.timezone)
                .format('MM/DD/YYYY'),
              craneRequestData.timezone,
              craneRequestData.deliveryEndTime,
            );
            requestArray.push({
              ProjectId: craneRequestData.ProjectId,
              craneDeliveryStart: !moment(deliveryStartDate).isSame(
                moment(requestSeries[i].craneDeliveryStart),
              )
                ? deliveryStartDate
                : requestSeries[i].craneDeliveryStart,
              craneDeliveryEnd: !moment(deliveryEndDate).isSame(
                moment(requestSeries[i].craneDeliveryEnd),
              )
                ? deliveryEndDate
                : requestSeries[i].craneDeliveryEnd,
              id: requestSeries[i].id,
            });
          }
          const utcRecurrenceEndDate = requestData.recurrence.recurrenceEndDate;
          const existingRecurrenceEndDate = moment(utcRecurrenceEndDate)
            .tz(craneRequestData.timezone)
            .format('YYYY-MM-DD');
          const newRecurrenceEndDate = craneRequestData.recurrenceEndDate;
          if (!moment(existingRecurrenceEndDate).isSame(moment(newRecurrenceEndDate))) {
            const startDate = moment(existingRecurrenceEndDate).add(1, 'day');
            const endDate = moment(newRecurrenceEndDate);
            for (let date = startDate; date.isSameOrBefore(endDate); date.add(1, 'day')) {
              requestArray.push({
                ProjectId: craneRequestData.ProjectId,
                craneDeliveryStart: await concreteRequestService.convertTimezoneToUtc(
                  moment(date).format('MM/DD/YYYY'),
                  craneRequestData.timezone,
                  craneRequestData.deliveryStartTime,
                ),
                craneDeliveryEnd: await concreteRequestService.convertTimezoneToUtc(
                  moment(date).format('MM/DD/YYYY'),
                  craneRequestData.timezone,
                  craneRequestData.deliveryEndTime,
                ),
              });
            }
            if (requestArray.length > 0) {
              const isOverlapping = await craneRequestService.checkDoubleBookingAllowedOrNot(
                requestArray,
                projectSettingDetails,
                'edit',
              );
              if (isOverlapping && isOverlapping.error) {
                return done(null, {
                  message: isOverlapping.message,
                });
              }
            }
          } else if (requestArray.length > 0) {
            const isOverlapping = await craneRequestService.checkDoubleBookingAllowedOrNot(
              requestArray,
              projectSettingDetails,
              'edit',
            );
            if (isOverlapping && isOverlapping.error) {
              return done(null, {
                message: isOverlapping.message,
              });
            }
          }
        }
      }
      // This event
      if (craneRequestData.seriesOption === 1) {
        editSeriesRequests = await CraneRequest.findAll({
          where: [
            Sequelize.and({
              id: craneRequestData.id,
            }),
          ],
        });
        if (editSeriesRequests && editSeriesRequests[0] && craneRequestData.recurrenceId) {
          const previousRecordInThisEventSeries = await CraneRequest.findAll({
            where: [
              Sequelize.and({
                recurrenceId,
                id: {
                  [Op.lt]: craneRequestData.id,
                },
              }),
            ],
            order: [['id', 'DESC']],
          });
          const NextSeriesLastRecord = await CraneRequest.findAll({
            where: [
              Sequelize.and({
                recurrenceId,
                id: {
                  [Op.gt]: craneRequestData.id,
                },
              }),
            ],
            order: [['id', 'DESC']],
          });
          if (
            ((NextSeriesLastRecord && NextSeriesLastRecord.length > 0) ||
              (previousRecordInThisEventSeries && previousRecordInThisEventSeries.length > 0)) &&
            !(
              NextSeriesLastRecord &&
              NextSeriesLastRecord.length > 0 &&
              previousRecordInThisEventSeries &&
              previousRecordInThisEventSeries.length > 0
            )
          ) {
            if (NextSeriesLastRecord && NextSeriesLastRecord.length > 0) {
              const chosenTimezoneDeliveryStart = moment.tz(
                `${craneRequestData.nextSeriesRecurrenceStartDate}  '00:00'`,
                'YYYY-MM-DD HH:mm',
                craneRequestData.timezone,
              );
              const utcDate = chosenTimezoneDeliveryStart
                .clone()
                .tz('UTC')
                .format('YYYY-MM-DD HH:mm:ssZ');
              await RequestRecurrenceSeries.update(
                {
                  recurrenceStartDate: utcDate,
                },
                {
                  where: {
                    id: NextSeriesLastRecord[0].recurrenceId,
                  },
                },
              );
            }
            if (previousRecordInThisEventSeries && previousRecordInThisEventSeries.length > 0) {
              // const chosenTimezoneDeliveryStart = moment.tz(
              //   `${craneRequestData.previousSeriesRecurrenceEndDate}  '00:00'`,
              //   'YYYY-MM-DD HH:mm',
              //   craneRequestData.timezone,
              // );
              // const utcDate = chosenTimezoneDeliveryStart
              //   .clone()
              //   .tz('UTC')
              //   .format('YYYY-MM-DD HH:mm:ssZ');
              await RequestRecurrenceSeries.update(
                {
                  recurrenceEndDate: previousRecordInThisEventSeries[0].craneDeliveryStart,
                },
                {
                  where: {
                    id: previousRecordInThisEventSeries[0].recurrenceId,
                  },
                },
              );
            }
          }
        }
      }
      // This and all following events
      if (craneRequestData.seriesOption === 2) {
        editSeriesRequests = await CraneRequest.findAll({
          where: [
            Sequelize.and({
              recurrenceId,
              id: {
                [Op.gte]: craneRequestData.id,
              },
            }),
          ],
        });
        previousRecordInSeries = await CraneRequest.findOne({
          where: [
            Sequelize.and({
              recurrenceId,
              id: {
                [Op.lt]: craneRequestData.id,
              },
            }),
          ],
          order: [['id', 'DESC']],
        });
      }
      // All events in the series
      if (craneRequestData.seriesOption === 3) {
        editSeriesRequests = await CraneRequest.findAll({
          where: [
            Sequelize.and({
              recurrenceId,
              craneDeliveryStart: {
                [Op.gte]: moment.tz(craneRequestData.timeZone).utc().format('YYYY-MM-DD HH:mm:ssZ'),
              },
            }),
          ],
        });
      }
      if (editSeriesRequests && editSeriesRequests[0] && editSeriesRequests.length > 0) {
        const requestData = await CraneRequest.getSingleCraneRequestData({
          id: editSeriesRequests[0].id,
        });
        if (requestData && requestData.recurrence) {
          requestData.recurrence.ParentCompanyId = craneRequestData.ParentCompanyId;
          requestData.recurrence.ProjectId = craneRequestData.ProjectId;
          if (craneRequestData.seriesOption === 1) {
            requestData.recurrence.craneDeliveryStart = craneRequestData.recurrenceSeriesStartDate;
            requestData.recurrence.craneDeliveryEnd = craneRequestData.recurrenceSeriesEndDate;
          }
          if (craneRequestData.seriesOption === 2) {
            requestData.recurrence.craneDeliveryStart = craneRequestData.recurrenceSeriesStartDate;
            requestData.recurrence.craneDeliveryEnd = craneRequestData.recurrenceEndDate;
          }
          if (craneRequestData.seriesOption === 2 && previousRecordInSeries) {
            newRecurrenceId = await concreteRequestService.insertRecurrenceSeries(
              requestData.recurrence,
              loginUser,
              requestData.requestType,
              craneRequestData.timezone,
            );
          }
        }
        if (craneRequestData.seriesOption === 2 || craneRequestData.seriesOption === 3) {
          const utcRecurrenceEndDate = requestData.recurrence.recurrenceEndDate;
          let existingRecurrenceEndDate = moment(utcRecurrenceEndDate)
            .tz(craneRequestData.timezone)
            .format('YYYY-MM-DD');
          const newRecurrenceEndDate = craneRequestData.recurrenceEndDate;
          if (!moment(existingRecurrenceEndDate).isSame(moment(newRecurrenceEndDate))) {
            const dates = [];
            const chosenTimezoneDeliveryStart = moment.tz(
              `${craneRequestData.recurrenceEndDate}  '00:00'`,
              'YYYY-MM-DD HH:mm',
              craneRequestData.timezone,
            );
            const utcDate = chosenTimezoneDeliveryStart
              .clone()
              .tz('UTC')
              .format('YYYY-MM-DD HH:mm:ssZ');
            await RequestRecurrenceSeries.update(
              {
                recurrenceEndDate: utcDate,
              },
              {
                where: {
                  id: craneRequestData.recurrenceId,
                },
              },
            );
            while (moment(existingRecurrenceEndDate).isBefore(moment(newRecurrenceEndDate))) {
              existingRecurrenceEndDate = moment(existingRecurrenceEndDate).add(1, 'day');
              dates.push(moment(existingRecurrenceEndDate).format('MM/DD/YYYY'));
            }
            await craneRequestService.createCopyofCraneRequest(
              requestData,
              craneRequestData,
              dates,
              loginUser,
              newRecurrenceId || craneRequestData.recurrenceId,
            );
          }
        }
        if (craneRequestData.seriesOption === 2 && previousRecordInSeries) {
          // const chosenTimezoneDeliveryStart = moment.tz(
          //   `${craneRequestData.previousSeriesRecurrenceEndDate}  '00:00'`,
          //   'YYYY-MM-DD HH:mm',
          //   craneRequestData.timezone,
          // );
          // const utcDate = chosenTimezoneDeliveryStart
          //   .clone()
          //   .tz('UTC')
          //   .format('YYYY-MM-DD HH:mm:ssZ');
          await RequestRecurrenceSeries.update(
            {
              recurrenceEndDate: previousRecordInSeries.craneDeliveryStart,
            },
            {
              where: {
                id: previousRecordInSeries.recurrenceId,
              },
            },
          );
        }
      }
      for (let indexLoop = 0; indexLoop < editSeriesRequests.length; indexLoop += 1) {
        const seriesData = editSeriesRequests[indexLoop];
        const isCraneRequestExists = await CraneRequest.findOne({
          where: [
            Sequelize.and({
              id: seriesData.id,
            }),
          ],
        });
        if (!isCraneRequestExists) {
          return done(null, { message: 'Crane Booking id is not available' });
        }
        const existsCraneRequest = await CraneRequest.getSingleCraneRequestData({
          id: +seriesData.id,
        });
        this.checkCraneInputDatas(inputData, async (checkResponse, checkError) => {
          if (checkError) {
            return done(null, checkError);
          }
          const memberData = await Member.getBy({
            UserId: loginUser.id,
            ProjectId: craneRequestData.ProjectId,
          });
          const history = {
            CraneRequestId: isCraneRequestExists.id,
            MemberId: memberData.id,
            type: 'edit',
            description: `${loginUser.firstName} ${loginUser.lastName} Edited this Crane Booking.`,
          };
          const notification = history;
          const craneRequestParam = {
            description: craneRequestData.description,
            isEscortNeeded: craneRequestData.isEscortNeeded,
            additionalNotes: craneRequestData.additionalNotes,
            isAssociatedWithDeliveryRequest: craneRequestData.isAssociatedWithDeliveryRequest,
            pickUpLocation: craneRequestData.pickUpLocation,
            dropOffLocation: craneRequestData.dropOffLocation,
            recurrenceId: craneRequestData.seriesOption !== 1 ? newRecurrenceId : null,
            LocationId: craneRequestData.LocationId,
          };
          if (craneRequestData.seriesOption === 1) {
            craneRequestParam.craneDeliveryStart = craneRequestData.craneDeliveryStart;
            craneRequestParam.craneDeliveryEnd = craneRequestData.craneDeliveryEnd;
          }
          if (craneRequestData.seriesOption === 2 || craneRequestData.seriesOption === 3) {
            const utcDeliveryStartTimestamp = moment.utc(isCraneRequestExists.craneDeliveryStart);
            const localStartTimestamp = utcDeliveryStartTimestamp.tz(craneRequestData.timezone);
            const utcDeliveryEndTimestamp = moment.utc(isCraneRequestExists.craneDeliveryEnd);
            const localEndTimestamp = utcDeliveryEndTimestamp.tz(craneRequestData.timezone);
            craneRequestParam.craneDeliveryStart =
              await concreteRequestService.convertTimezoneToUtc(
                moment(localStartTimestamp).format('MM/DD/YYYY'),
                craneRequestData.timezone,
                craneRequestData.deliveryStartTime,
              );
            craneRequestParam.craneDeliveryEnd = await concreteRequestService.convertTimezoneToUtc(
              moment(localEndTimestamp).format('MM/DD/YYYY'),
              craneRequestData.timezone,
              craneRequestData.deliveryEndTime,
            );
          }
          if (
            ((memberData.RoleId === 2 || memberData.RoleId === 1) &&
              isCraneRequestExists.status === 'Approved') ||
            memberData.isAutoApproveEnabled ||
            projectSettingDetails.ProjectSettings.isAutoApprovalEnabled
          ) {
            craneRequestParam.status = 'Approved';
            craneRequestParam.approvedBy = memberData.id;
            craneRequestParam.approved_at = new Date();
          }
          await CraneRequest.update(craneRequestParam, {
            where: { id: isCraneRequestExists.id },
          });
          const { companies, responsiblePersons, definableFeatureOfWorks } = craneRequestData;
          const equipments = craneRequestData.EquipmentId;
          const condition = Sequelize.and({
            ProjectId: craneRequestData.ProjectId,
            CraneRequestId: isCraneRequestExists.id,
          });
          const updateParam = {
            CraneRequestId: +isCraneRequestExists.id,
            CraneRequestCode: +isCraneRequestExists.CraneRequestId,
            ProjectId: +craneRequestData.ProjectId,
            isDeleted: false,
          };
          const existCompanies = await CraneRequestCompany.findAll({ where: condition });
          const existEquipments = await CraneRequestEquipment.findAll({ where: condition });
          const existResponsiblePersons = await CraneRequestResponsiblePerson.findAll({
            where: condition,
          });
          const existDefinableFeatureOfWorks = await CraneRequestDefinableFeatureOfWork.findAll({
            where: condition,
          });
          craneRequestService.updateValues(condition, async (response, error) => {
            if (!error) {
              companies.forEach(async (element, i) => {
                const index = existCompanies.findIndex((item) => item.CompanyId === element);
                const companyParam = updateParam;
                companyParam.CompanyId = element;
                if (index !== -1) {
                  await CraneRequestCompany.update(companyParam, {
                    where: { id: existCompanies[index].id },
                  });
                } else {
                  await CraneRequestCompany.createInstance(companyParam);
                }
              });

              equipments.forEach(async (element, i) => {
                const index = existEquipments.findIndex((item) => item.EquipmentId === element);
                const equipmentParam = updateParam;
                equipmentParam.EquipmentId = element;
                if (index !== -1) {
                  await CraneRequestEquipment.update(equipmentParam, {
                    where: { id: existEquipments[index].id },
                  });
                } else {
                  await CraneRequestEquipment.createInstance(equipmentParam);
                }
              });

              responsiblePersons.forEach(async (element, i) => {
                const index = existResponsiblePersons.findIndex(
                  (item) => item.MemberId === element,
                );
                const memberParam = updateParam;
                memberParam.MemberId = element;
                if (index !== -1) {
                  await CraneRequestResponsiblePerson.update(memberParam, {
                    where: { id: existResponsiblePersons[index].id },
                  });
                } else {
                  await CraneRequestResponsiblePerson.createInstance(memberParam);
                }
              });

              definableFeatureOfWorks.forEach(async (element, i) => {
                const index = existDefinableFeatureOfWorks.findIndex(
                  (item) => item.DeliverDefineWorkId === element,
                );
                const defineParam = updateParam;
                defineParam.DeliverDefineWorkId = element;
                if (index !== -1) {
                  await CraneRequestDefinableFeatureOfWork.update(defineParam, {
                    where: { id: existDefinableFeatureOfWorks[index].id },
                  });
                } else {
                  await CraneRequestDefinableFeatureOfWork.createInstance(defineParam);
                }
              });
              const locationChosen = await Locations.findOne({
                where: {
                  ProjectId: craneRequestData.ProjectId,
                  id: craneRequestData.LocationId,
                },
              });
              history.description = `${loginUser.firstName} ${loginUser.lastName} Updated the Crane Booking ${craneRequestData.description}`;
              history.locationFollowDescription = `${loginUser.firstName} ${loginUser.lastName} Updated the Crane Booking, ${craneRequestData.description}. Location: ${locationChosen.locationPath}.`;
              history.MemberId = memberData.id;
              history.firstName = loginUser.firstName;
              history.profilePic = loginUser.profilePic;
              history.createdAt = new Date();
              history.ProjectId = craneRequestData.ProjectId;
              const projectDetails = await Project.findByPk(craneRequestData.ProjectId);
              history.projectName = projectDetails.projectName;
              notification.ProjectId = isCraneRequestExists.ProjectId;
              if (
                existsCraneRequest &&
                existsCraneRequest.recurrence &&
                existsCraneRequest.recurrence.recurrence
              ) {
                notification.recurrenceType = `${existsCraneRequest.recurrence.recurrence
                  } From ${moment(existsCraneRequest.recurrence.recurrenceStartDate).format(
                    'MM/DD/YYYY',
                  )} to ${moment(existsCraneRequest.recurrence.recurrenceEndDate).format(
                    'MM/DD/YYYY',
                  )}`;
              }
              notification.title = `Crane Booking Updated by ${loginUser.firstName} ${loginUser.lastName}`;
              notification.isDeliveryRequest = false;
              notification.requestType = 'craneRequest';
              const newNotification = await Notification.createInstance(notification);
              const memberLocationPreference = await LocationNotificationPreferences.findAll({
                where: {
                  ProjectId: craneRequestData.ProjectId,
                  LocationId: craneRequestData.LocationId,
                  follow: true,
                },
                include: [
                  {
                    association: 'Member',
                    attributes: ['id', 'RoleId'],
                    where: {
                      [Op.and]: [
                        {
                          id: { [Op.ne]: memberData.id },
                        },
                      ],
                    },
                    include: [
                      {
                        association: 'User',
                        attributes: ['id', 'firstName', 'lastName', 'email'],
                      },
                    ],
                  },
                ],
              });
              const locationFollowMembers = [];
              memberLocationPreference.forEach(async (element) => {
                locationFollowMembers.push(element.Member.id);
              });
              const personData = await CraneRequestResponsiblePerson.findAll({
                where: { CraneRequestId: isCraneRequestExists.id, isDeleted: false },
                include: [
                  {
                    association: 'Member',
                    include: [
                      {
                        association: 'User',
                        attributes: ['id', 'firstName', 'lastName', 'email'],
                      },
                    ],
                    where: {
                      [Op.and]: {
                        RoleId: {
                          [Op.notIn]: [1, 2],
                        },
                        id: { [Op.notIn]: locationFollowMembers },
                      },
                    },
                    attributes: ['id', 'RoleId'],
                  },
                ],
                attributes: ['id'],
              });
              const adminData = await Member.findAll({
                where: {
                  [Op.or]: [
                    {
                      [Op.and]: [
                        { ProjectId: craneRequestData.ProjectId },
                        { isDeleted: false },
                        { id: { [Op.ne]: newNotification.MemberId } },
                        { id: { [Op.in]: responsiblePersons } },
                        { id: { [Op.notIn]: locationFollowMembers } },
                      ],
                    },
                  ],
                },
                include: [
                  {
                    association: 'User',
                    attributes: ['id', 'firstName', 'lastName', 'email'],
                  },
                ],
                attributes: ['id', 'RoleId'],
              });
              if (memberLocationPreference && memberLocationPreference.length > 0) {
                // here 5-(NotificationPreferenceItemId -When a delivery/crane/concrete request is edited)
                await pushNotification.sendMemberLocationPreferencePushNotificationForCrane(
                  memberLocationPreference,
                  craneRequestData.CraneRequestId,
                  history.locationFollowDescription,
                  craneRequestData.requestType,
                  craneRequestData.ProjectId,
                  craneRequestData.id,
                  5,
                );
                // here 5-(NotificationPreferenceItemId -When a delivery/crane/concrete request is edited)
                await notificationHelper.createMemberDeliveryLocationInAppNotification(
                  DeliveryPersonNotification,
                  craneRequestData.ProjectId,
                  newNotification.id,
                  memberLocationPreference,
                  5,
                );
              }
              history.adminData = adminData;
              history.memberData = personData;
              if (memberLocationPreference && memberLocationPreference.length > 0) {
                history.memberData.push(...memberLocationPreference);
              }
              const checkMemberNotification = await NotificationPreference.findAll({
                where: {
                  ProjectId: craneRequestData.ProjectId,
                  isDeleted: false,
                },
                attributes: [
                  'id',
                  'MemberId',
                  'ProjectId',
                  'ParentCompanyId',
                  'NotificationPreferenceItemId',
                  'instant',
                  'dailyDigest',
                ],
                include: [
                  {
                    association: 'NotificationPreferenceItem',
                    where: {
                      id: 5,
                      isDeleted: false,
                    },
                    attributes: ['id', 'description', 'inappNotification', 'emailNotification'],
                  },
                ],
              });
              history.notificationPreference = checkMemberNotification;
              // here 5-(NotificationPreferenceItemId -When a delivery/crane/concrete request is edited)
              await notificationHelper.createDeliveryPersonNotification(
                adminData,
                [],
                projectDetails,
                newNotification,
                DeliveryPersonNotification,
                memberData,
                loginUser,
                5,
                'updated a',
                'Crane Request',
                `crane Booking (${existsCraneRequest.CraneRequestId} - ${existsCraneRequest.description})`,
                existsCraneRequest.CraneRequestId,
              );
              const updatedCraneRequest = await CraneRequest.getSingleCraneRequestData({
                id: +isCraneRequestExists.id,
              });
              if (
                updatedCraneRequest.status === 'Approved' &&
                isCraneRequestExists.status !== 'Approved'
              ) {
                const object = {
                  ProjectId: craneRequestData.ProjectId,
                  MemberId: memberData.id,
                  CraneRequestId: updatedCraneRequest.id,
                  isDeleted: false,
                  type: 'approved',
                  description: history.description,
                };
                await CraneRequestHistory.createInstance(object);
              }

              craneRequestService.updateEditCraneRequestHistory(
                craneRequestData,
                existsCraneRequest,
                updatedCraneRequest,
                history,
                loginUser,
              );
              // here 5-(NotificationPreferenceItemId -When a delivery/crane/concrete request is edited)
              await pushNotification.sendPushNotificationForCrane(
                history,
                5,
                craneRequestData.ProjectId,
              );
              history.CraneRequestId = isCraneRequestExists.CraneRequestId;

              let tagsUpdated = false;
              let fieldsChanged = false;
              if (
                updatedCraneRequest.defineWorkDetails.length > 0 &&
                existsCraneRequest.defineWorkDetails.length > 0
              ) {
                const addedDfow1 = updatedCraneRequest.defineWorkDetails.filter((el) => {
                  return !existsCraneRequest.defineWorkDetails.find((element) => {
                    return element.id === el.id;
                  });
                });
                const deletedDfow1 = existsCraneRequest.defineWorkDetails.filter((el) => {
                  return !existsCraneRequest.defineWorkDetails.find((element) => {
                    return element.id === el.id;
                  });
                });
                if (addedDfow1.length > 0) {
                  tagsUpdated = true;
                }
                if (deletedDfow1.length > 0) {
                  tagsUpdated = true;
                }
              }
              if (
                updatedCraneRequest.equipmentDetails.length > 0 &&
                existsCraneRequest.equipmentDetails.length > 0
              ) {
                const addedEquipment1 = updatedCraneRequest.equipmentDetails.filter((el) => {
                  return !existsCraneRequest.equipmentDetails.find((element) => {
                    return element.Equipment.id === el.Equipment.id;
                  });
                });
                const deletedEquipment1 = existsCraneRequest.equipmentDetails.filter((el) => {
                  return !existsCraneRequest.equipmentDetails.find((element) => {
                    return element.Equipment.id === el.Equipment.id;
                  });
                });
                if (addedEquipment1.length > 0) {
                  tagsUpdated = true;
                }
                if (deletedEquipment1.length > 0) {
                  tagsUpdated = true;
                }
              }
              if (
                updatedCraneRequest.companyDetails.length > 0 &&
                existsCraneRequest.companyDetails.length > 0
              ) {
                const addedCompany1 = updatedCraneRequest.companyDetails.filter((el) => {
                  return !existsCraneRequest.companyDetails.find((element) => {
                    return element.Company.id === el.Company.id;
                  });
                });
                const deletedCompany1 = existsCraneRequest.companyDetails.filter((el) => {
                  return !existsCraneRequest.companyDetails.find((element) => {
                    return element.Company.id === el.Company.id;
                  });
                });
                if (addedCompany1.length > 0) {
                  tagsUpdated = true;
                }
                if (deletedCompany1.length > 0) {
                  tagsUpdated = true;
                }
              }
              if (
                updatedCraneRequest.memberDetails.length > 0 &&
                existsCraneRequest.memberDetails.length > 0
              ) {
                const addedMember1 = updatedCraneRequest.memberDetails.filter((el) => {
                  return !existsCraneRequest.memberDetails.find((element) => {
                    return element.Member.id === el.Member.id;
                  });
                });
                const deletedMember1 = existsCraneRequest.memberDetails.filter((el) => {
                  return !updatedCraneRequest.memberDetails.find((element) => {
                    return element.Member.id === el.Member.id;
                  });
                });
                if (addedMember1.length > 0) {
                  tagsUpdated = true;
                }
                if (deletedMember1.length > 0) {
                  tagsUpdated = true;
                }
              }
              if (
                existsCraneRequest.description !== updatedCraneRequest.description ||
                existsCraneRequest.CraneRequestId !== updatedCraneRequest.CraneRequestId ||
                existsCraneRequest.LocationId !== updatedCraneRequest.LocationId ||
                existsCraneRequest.requestType !== updatedCraneRequest.requestType ||
                existsCraneRequest.additionalNotes !== updatedCraneRequest.additionalNotes ||
                existsCraneRequest.isAssociatedWithDeliveryRequest !==
                updatedCraneRequest.isAssociatedWithDeliveryRequest ||
                existsCraneRequest.isEscortNeeded !== updatedCraneRequest.isEscortNeeded ||
                existsCraneRequest.dropOffLocation !== updatedCraneRequest.dropOffLocation ||
                existsCraneRequest.pickUpLocation !== updatedCraneRequest.pickUpLocation ||
                tagsUpdated
              ) {
                fieldsChanged = true;
              }
              let deliveryDateTimeChanged = false;
              if (
                moment(existsCraneRequest.craneDeliveryStart).format('h:mm a') !==
                moment(updatedCraneRequest.craneDeliveryStart).format('h:mm a') ||
                moment(existsCraneRequest.craneDeliveryEnd).format('h:mm a') !==
                moment(updatedCraneRequest.craneDeliveryEnd).format('h:mm a')
              ) {
                deliveryDateTimeChanged = true;
              }
              if (existsCraneRequest.status === 'Completed') {
                if (fieldsChanged && memberData.RoleId === 2) {
                  await CraneRequest.update(
                    { status: 'Approved' },
                    {
                      where: { id: updatedCraneRequest.id },
                    },
                  );
                }
                if (
                  (fieldsChanged || deliveryDateTimeChanged) &&
                  memberData.RoleId !== 2 &&
                  !memberData.isAutoApproveEnabled &&
                  !projectSettingDetails.ProjectSettings.isAutoApprovalEnabled
                ) {
                  await CraneRequest.update(
                    { status: 'Pending' },
                    {
                      where: { id: updatedCraneRequest.id },
                    },
                  );
                }
              }
              if (existsCraneRequest.status === 'Approved') {
                if (
                  (fieldsChanged || deliveryDateTimeChanged) &&
                  memberData.RoleId !== 2 &&
                  !memberData.isAutoApproveEnabled &&
                  !projectSettingDetails.ProjectSettings.isAutoApprovalEnabled
                ) {
                  await CraneRequest.update(
                    { status: 'Pending' },
                    {
                      where: { id: updatedCraneRequest.id },
                    },
                  );
                }
                if (
                  ((fieldsChanged || deliveryDateTimeChanged) && memberData.RoleId === 2) ||
                  memberData.isAutoApproveEnabled ||
                  projectSettingDetails.ProjectSettings.isAutoApprovalEnabled
                ) {
                  await CraneRequest.update(
                    { status: 'Approved' },
                    {
                      where: { id: updatedCraneRequest.id },
                    },
                  );
                }
              }
              if (
                existsCraneRequest.status === 'Expired' ||
                existsCraneRequest.status === 'Declined'
              ) {
                if (
                  (fieldsChanged || deliveryDateTimeChanged) &&
                  memberData.RoleId !== 2 &&
                  !memberData.isAutoApproveEnabled &&
                  !projectSettingDetails.ProjectSettings.isAutoApprovalEnabled
                ) {
                  await CraneRequest.update(
                    { status: 'Pending' },
                    {
                      where: { id: updatedCraneRequest.id },
                    },
                  );
                }
                if (
                  ((fieldsChanged || deliveryDateTimeChanged) && memberData.RoleId === 2) ||
                  memberData.isAutoApproveEnabled ||
                  projectSettingDetails.ProjectSettings.isAutoApprovalEnabled
                ) {
                  const isStatusDataUpdated = await CraneRequest.update(
                    { status: 'Approved' },
                    {
                      where: { id: updatedCraneRequest.id },
                    },
                  );
                  if (isStatusDataUpdated) {
                    const object = {
                      ProjectId: craneRequestData.ProjectId,
                      MemberId: memberData.id,
                      DeliveryRequestId: updatedCraneRequest.id,
                      isDeleted: false,
                      type: 'approved',
                      description: `${loginUser.firstName} ${loginUser.lastName} Approved the Crane Booking, ${craneRequestData.description}`,
                    };
                    await CraneRequestHistory.createInstance(object);
                  }
                }
              }
              if (existsCraneRequest.status === 'Pending') {
                if (
                  (fieldsChanged || deliveryDateTimeChanged) &&
                  memberData.RoleId !== 2 &&
                  (memberData.isAutoApproveEnabled ||
                    projectSettingDetails.ProjectSettings.isAutoApprovalEnabled)
                ) {
                  const isStatusDataUpdated = await CraneRequest.update(
                    { status: 'Approved' },
                    {
                      where: { id: updatedCraneRequest.id },
                    },
                  );
                  if (isStatusDataUpdated) {
                    const object = {
                      ProjectId: craneRequestData.ProjectId,
                      MemberId: memberData.id,
                      DeliveryRequestId: updatedCraneRequest.id,
                      isDeleted: false,
                      type: 'approved',
                      description: `${loginUser.firstName} ${loginUser.lastName} Approved the Crane Request, ${craneRequestData.description}`,
                    };
                    await CraneRequestHistory.createInstance(object);
                  }
                }
                if (
                  ((fieldsChanged || deliveryDateTimeChanged) && memberData.RoleId === 2) ||
                  memberData.isAutoApproveEnabled ||
                  projectSettingDetails.ProjectSettings.isAutoApprovalEnabled
                ) {
                  const isStatusDataUpdated = await CraneRequest.update(
                    { status: 'Approved' },
                    {
                      where: { id: updatedCraneRequest.id },
                    },
                  );
                  if (isStatusDataUpdated) {
                    const object = {
                      ProjectId: craneRequestData.ProjectId,
                      MemberId: memberData.id,
                      DeliveryRequestId: updatedCraneRequest.id,
                      isDeleted: false,
                      type: 'approved',
                      description: `${loginUser.firstName} ${loginUser.lastName} Approved the Crane Request, ${craneRequestData.description}`,
                    };
                    await CraneRequestHistory.createInstance(object);
                  }
                }
              }
              if (+memberData.RoleId === 4 || +memberData.RoleId === 3) {
                const userEmails = await craneRequestService.getMemberDetailData(
                  history,
                  memberLocationPreference,
                );
                if (userEmails.length > 0) {
                  userEmails.forEach(async (element) => {
                    if (element.RoleId === 2) {
                      let name;
                      if (!element.firstName) {
                        name = 'user';
                      } else {
                        name = `${element.firstName} ${element.lastName}`;
                      }
                      const memberRole = await Role.findOne({
                        where: {
                          id: memberData.RoleId,
                          isDeleted: false,
                        },
                      });
                      const mailPayload = {
                        name,
                        email: element.email,
                        content: `We would like to inform you that 
                          ${loginUser.firstName} ${loginUser.lastName} - ${memberRole.roleName} has updated a crane booking ${isCraneRequestExists.CraneRequestId} and waiting for your approval.Kindly review the booking and update the status.`,
                      };
                      const isMemberFollowLocation = await LocationNotificationPreferences.findOne({
                        where: {
                          MemberId: +element.MemberId,
                          ProjectId: +craneRequestData.ProjectId,
                          LocationId: +craneRequestData.LocationId,
                          isDeleted: false,
                          // follow: true,
                        },
                      });
                      if (isMemberFollowLocation) {
                        const memberNotification = await NotificationPreference.findOne({
                          where: {
                            MemberId: +element.MemberId,
                            ProjectId: +craneRequestData.ProjectId,
                            isDeleted: false,
                          },
                          include: [
                            {
                              association: 'NotificationPreferenceItem',
                              where: {
                                id: 9,
                                isDeleted: false,
                              },
                            },
                          ],
                        });
                        if (memberNotification && memberNotification.instant) {
                          await MAILER.sendMail(
                            mailPayload,
                            'notifyPAForReApproval',
                            `Crane Booking updated by ${loginUser.firstName} ${loginUser.lastName} - ${memberRole.roleName}`,
                            `Crane Booking updated by ${loginUser.firstName} ${loginUser.lastName} - ${memberRole.roleName}`,
                            async (info, err) => {
                              console.log(info, err);
                            },
                          );
                        }
                        if (memberNotification && memberNotification.dailyDigest) {
                          await craneRequestService.createDailyDigestDataApproval(
                            +memberData.RoleId,
                            +element.MemberId,
                            +craneRequestData.ProjectId,
                            +craneRequestData.ParentCompanyId,
                            loginUser,
                            'updated a',
                            'Crane Request',
                            `crane Booking (${existsCraneRequest.CraneRequestId} - ${existsCraneRequest.description})`,
                            'and waiting for your approval',
                            existsCraneRequest.CraneRequestId,
                          );
                        }
                      }
                    }
                  });
                  return done(history, false);
                }
              } else {
                return done(history, false);
              }
            } else {
              return done(null, error);
            }
          });
        });
      }
    } catch (e) {
      done(null, e);
    }
  },
  async editConcreteRequest(inputData, done) {
    try {
      // await this.getDynamicModel(inputData);
      const concreteRequestData = inputData.body;
      const userDetail = await User.findOne({
        where: { id: inputData.body.userId, isDeleted: false },
      });
      inputData.user = userDetail;
      const loginUser = inputData.user;
      const { recurrenceId } = concreteRequestData;
      let editSeriesRequests;
      let newRecurrenceId;
      let previousRecordInSeries;
      const projectSettingDetails = await Project.getProjectAndSettings({
        isDeleted: false,
        id: +concreteRequestData.ProjectId,
      });
      if (projectSettingDetails) {
        const requestData = await ConcreteRequest.getSingleConcreteRequestData({
          id: concreteRequestData.id,
        });
        if (concreteRequestData.seriesOption === 1) {
          const requestArray = [];
          requestArray.push({
            ProjectId: concreteRequestData.ProjectId,
            concretePlacementStart: concreteRequestData.concretePlacementStart,
            concretePlacementEnd: concreteRequestData.concretePlacementEnd,
            id: concreteRequestData.id,
          });
          const isOverlapping = await concreteRequestService.checkDoubleBookingAllowedOrNot(
            requestArray,
            projectSettingDetails,
            'add',
          );
          if (isOverlapping && isOverlapping.error) {
            return done(null, {
              message: isOverlapping.message,
            });
          }
        }
        if (concreteRequestData.seriesOption === 2 || concreteRequestData.seriesOption === 3) {
          let requestSeries = [];
          if (concreteRequestData.seriesOption === 2) {
            requestSeries = await ConcreteRequest.findAll({
              where: [
                Sequelize.and({
                  recurrenceId,
                  id: {
                    [Op.gte]: concreteRequestData.id,
                  },
                }),
              ],
            });
          }
          if (concreteRequestData.seriesOption === 3) {
            requestSeries = await ConcreteRequest.findAll({
              where: [
                Sequelize.and({
                  recurrenceId,
                  concretePlacementStart: {
                    [Op.gte]: moment
                      .tz(concreteRequestData.timeZone)
                      .utc()
                      .format('YYYY-MM-DD HH:mm:ssZ'),
                  },
                }),
              ],
            });
          }
          const requestArray = [];
          for (let i = 0; i < requestSeries.length; i += 1) {
            const deliveryStartDate = await this.convertTimezoneToUtc(
              moment
                .utc(requestSeries[i].concretePlacementStart)
                .tz(concreteRequestData.timezone)
                .format('MM/DD/YYYY'),
              concreteRequestData.timezone,
              concreteRequestData.deliveryStartTime,
            );
            const deliveryEndDate = await this.convertTimezoneToUtc(
              moment
                .utc(requestSeries[i].concretePlacementEnd)
                .tz(concreteRequestData.timezone)
                .format('MM/DD/YYYY'),
              concreteRequestData.timezone,
              concreteRequestData.deliveryEndTime,
            );
            requestArray.push({
              ProjectId: concreteRequestData.ProjectId,
              concretePlacementStart: !moment(deliveryStartDate).isSame(
                moment(requestSeries[i].concretePlacementStart),
              )
                ? deliveryStartDate
                : requestSeries[i].concretePlacementStart,
              concretePlacementEnd: !moment(deliveryEndDate).isSame(
                moment(requestSeries[i].concretePlacementEnd),
              )
                ? deliveryEndDate
                : requestSeries[i].concretePlacementEnd,
              id: requestSeries[i].id,
            });
          }
          const utcRecurrenceEndDate = requestData.recurrence.recurrenceEndDate;
          const existingRecurrenceEndDate = moment(utcRecurrenceEndDate)
            .tz(concreteRequestData.timezone)
            .format('YYYY-MM-DD');
          const newRecurrenceEndDate = concreteRequestData.recurrenceEndDate;
          if (!moment(existingRecurrenceEndDate).isSame(moment(newRecurrenceEndDate))) {
            const startDate = moment(existingRecurrenceEndDate).add(1, 'day');
            const endDate = moment(newRecurrenceEndDate);
            for (let date = startDate; date.isSameOrBefore(endDate); date.add(1, 'day')) {
              requestArray.push({
                ProjectId: concreteRequestData.ProjectId,
                concretePlacementStart: await this.convertTimezoneToUtc(
                  moment(date).format('MM/DD/YYYY'),
                  concreteRequestData.timezone,
                  concreteRequestData.deliveryStartTime,
                ),
                concretePlacementEnd: await this.convertTimezoneToUtc(
                  moment(date).format('MM/DD/YYYY'),
                  concreteRequestData.timezone,
                  concreteRequestData.deliveryEndTime,
                ),
              });
            }
            if (requestArray.length > 0) {
              const isOverlapping = await concreteRequestService.checkDoubleBookingAllowedOrNot(
                requestArray,
                projectSettingDetails,
                'edit',
              );
              if (isOverlapping && isOverlapping.error) {
                return done(null, {
                  message: isOverlapping.message,
                });
              }
            }
          } else if (requestArray.length > 0) {
            const isOverlapping = await concreteRequestService.checkDoubleBookingAllowedOrNot(
              requestArray,
              projectSettingDetails,
              'edit',
            );
            if (isOverlapping && isOverlapping.error) {
              return done(null, {
                message: isOverlapping.message,
              });
            }
          }
        }
      }
      // This event
      if (concreteRequestData.seriesOption === 1) {
        editSeriesRequests = await ConcreteRequest.findAll({
          where: [
            Sequelize.and({
              id: concreteRequestData.id,
            }),
          ],
        });
        if (editSeriesRequests && editSeriesRequests[0] && concreteRequestData.recurrenceId) {
          const previousRecordInThisEventSeries = await ConcreteRequest.findAll({
            where: [
              Sequelize.and({
                recurrenceId,
                id: {
                  [Op.lt]: concreteRequestData.id,
                },
              }),
            ],
            order: [['id', 'DESC']],
          });
          const NextSeriesLastRecord = await ConcreteRequest.findAll({
            where: [
              Sequelize.and({
                recurrenceId,
                id: {
                  [Op.gt]: concreteRequestData.id,
                },
              }),
            ],
            order: [['id', 'DESC']],
          });
          if (
            ((NextSeriesLastRecord && NextSeriesLastRecord.length > 0) ||
              (previousRecordInThisEventSeries && previousRecordInThisEventSeries.length > 0)) &&
            !(
              NextSeriesLastRecord &&
              NextSeriesLastRecord.length > 0 &&
              previousRecordInThisEventSeries &&
              previousRecordInThisEventSeries.length > 0
            )
          ) {
            if (NextSeriesLastRecord && NextSeriesLastRecord.length > 0) {
              const chosenTimezoneDeliveryStart = moment.tz(
                `${concreteRequestData.nextSeriesRecurrenceStartDate}  '00:00'`,
                'YYYY-MM-DD HH:mm',
                concreteRequestData.timezone,
              );
              const utcDate = chosenTimezoneDeliveryStart
                .clone()
                .tz('UTC')
                .format('YYYY-MM-DD HH:mm:ssZ');
              await RequestRecurrenceSeries.update(
                {
                  recurrenceStartDate: utcDate,
                },
                {
                  where: {
                    id: NextSeriesLastRecord[0].recurrenceId,
                  },
                },
              );
            }
            if (previousRecordInThisEventSeries && previousRecordInThisEventSeries.length > 0) {
              // const chosenTimezoneDeliveryStart = moment.tz(
              //   `${concreteRequestData.previousSeriesRecurrenceEndDate}  '00:00'`,
              //   'YYYY-MM-DD HH:mm',
              //   concreteRequestData.timezone,
              // );
              // const utcDate = chosenTimezoneDeliveryStart
              //   .clone()
              //   .tz('UTC')
              //   .format('YYYY-MM-DD HH:mm:ssZ');
              await RequestRecurrenceSeries.update(
                {
                  recurrenceEndDate: previousRecordInThisEventSeries[0].concretePlacementStart,
                },
                {
                  where: {
                    id: previousRecordInThisEventSeries[0].recurrenceId,
                  },
                },
              );
            }
          }
        }
      }
      // This and all following events
      if (concreteRequestData.seriesOption === 2) {
        editSeriesRequests = await ConcreteRequest.findAll({
          where: [
            Sequelize.and({
              recurrenceId,
              id: {
                [Op.gte]: concreteRequestData.id,
              },
            }),
          ],
        });
        previousRecordInSeries = await ConcreteRequest.findOne({
          where: [
            Sequelize.and({
              recurrenceId,
              id: {
                [Op.lt]: concreteRequestData.id,
              },
            }),
          ],
          order: [['id', 'DESC']],
        });
      }
      // All events in the series
      if (concreteRequestData.seriesOption === 3) {
        editSeriesRequests = await ConcreteRequest.findAll({
          where: [
            Sequelize.and({
              recurrenceId,
              concretePlacementStart: {
                [Op.gte]: moment
                  .tz(concreteRequestData.timeZone)
                  .utc()
                  .format('YYYY-MM-DD HH:mm:ssZ'),
              },
            }),
          ],
        });
      }
      if (editSeriesRequests && editSeriesRequests[0] && editSeriesRequests.length > 0) {
        const requestData = await ConcreteRequest.getSingleConcreteRequestData({
          id: editSeriesRequests[0].id,
        });
        if (requestData && requestData.recurrence) {
          requestData.recurrence.ParentCompanyId = concreteRequestData.ParentCompanyId;
          requestData.recurrence.ProjectId = concreteRequestData.ProjectId;
          if (concreteRequestData.seriesOption === 1) {
            requestData.recurrence.concretePlacementStart =
              concreteRequestData.recurrenceSeriesStartDate;
            requestData.recurrence.concretePlacementEnd =
              concreteRequestData.recurrenceSeriesEndDate;
          }
          if (concreteRequestData.seriesOption === 2) {
            requestData.recurrence.concretePlacementStart =
              concreteRequestData.recurrenceSeriesStartDate;
            requestData.recurrence.concretePlacementEnd = concreteRequestData.recurrenceEndDate;
          }
          if (concreteRequestData.seriesOption === 2 && previousRecordInSeries) {
            newRecurrenceId = await concreteRequestService.insertRecurrenceSeries(
              requestData.recurrence,
              loginUser,
              requestData.requestType,
              concreteRequestData.timezone,
            );
          }
        }
        if (concreteRequestData.seriesOption === 2 || concreteRequestData.seriesOption === 3) {
          const utcRecurrenceEndDate = requestData.recurrence.recurrenceEndDate;
          let existingRecurrenceEndDate = moment(utcRecurrenceEndDate)
            .tz(concreteRequestData.timezone)
            .format('YYYY-MM-DD');
          const newRecurrenceEndDate = concreteRequestData.recurrenceEndDate;
          if (!moment(existingRecurrenceEndDate).isSame(moment(newRecurrenceEndDate))) {
            const dates = [];
            const chosenTimezoneDeliveryStart = moment.tz(
              `${concreteRequestData.recurrenceEndDate}  '00:00'`,
              'YYYY-MM-DD HH:mm',
              concreteRequestData.timezone,
            );
            const utcDate = chosenTimezoneDeliveryStart
              .clone()
              .tz('UTC')
              .format('YYYY-MM-DD HH:mm:ssZ');
            await RequestRecurrenceSeries.update(
              {
                recurrenceEndDate: utcDate,
              },
              {
                where: {
                  id: concreteRequestData.recurrenceId,
                },
              },
            );
            while (moment(existingRecurrenceEndDate).isBefore(moment(newRecurrenceEndDate))) {
              existingRecurrenceEndDate = moment(existingRecurrenceEndDate).add(1, 'day');
              dates.push(moment(existingRecurrenceEndDate).format('MM/DD/YYYY'));
            }
            await concreteRequestService.createCopyofConcreteRequest(
              requestData,
              concreteRequestData,
              dates,
              loginUser,
              newRecurrenceId || concreteRequestData.recurrenceId,
            );
          }
        }
        if (concreteRequestData.seriesOption === 2 && previousRecordInSeries) {
          // const chosenTimezoneDeliveryStart = moment.tz(
          //   `${concreteRequestData.previousSeriesRecurrenceEndDate}  '00:00'`,
          //   'YYYY-MM-DD HH:mm',
          //   concreteRequestData.timezone,
          // );
          // const utcDate = chosenTimezoneDeliveryStart
          //   .clone()
          //   .tz('UTC')
          //   .format('YYYY-MM-DD HH:mm:ssZ');
          await RequestRecurrenceSeries.update(
            {
              recurrenceEndDate: previousRecordInSeries.concretePlacementStart,
            },
            {
              where: {
                id: previousRecordInSeries.recurrenceId,
              },
            },
          );
        }
      }

      for (let indexLoop = 0; indexLoop < editSeriesRequests.length; indexLoop += 1) {
        const seriesData = editSeriesRequests[indexLoop];
        const isConcreteRequestExists = await ConcreteRequest.findOne({
          where: [
            Sequelize.and({
              id: seriesData.id,
              isDeleted: false,
            }),
          ],
        });
        if (!isConcreteRequestExists) {
          return done(null, { message: 'Concrete Booking id is not available' });
        }
        const existsConcreteRequest = await ConcreteRequest.getSingleConcreteRequestData({
          id: +seriesData.id,
        });

        const startDate = new Date(concreteRequestData.concretePlacementStart).getTime();
        const currentDate = new Date().getTime();
        const endDate = new Date(concreteRequestData.concretePlacementEnd).getTime();
        const memberData = await Member.getBy({
          UserId: loginUser.id,
          ProjectId: concreteRequestData.ProjectId,
        });
        if (memberData.RoleId === 4) {
          if (startDate < currentDate && endDate < currentDate) {
            return done(null, { message: 'Please enter Future start or end date.' });
          }
        }
        this.concreteCheckInputDatas(inputData, async (_checkResponse, checkError) => {
          if (checkError) {
            return done(null, checkError);
          }
          const history = {
            ConcreteRequestId: isConcreteRequestExists.id,
            MemberId: memberData.id,
            type: 'edit',
            description: `${loginUser.firstName} ${loginUser.lastName} Edited this Concrete Booking.`,
          };
          const notification = history;
          const concreteRequestParam = {
            description: concreteRequestData.description,
            ProjectId: concreteRequestData.ProjectId,
            LocationId: concreteRequestData.LocationId,
            notes: concreteRequestData.notes,
            isPumpConfirmed: concreteRequestData.isPumpConfirmed,
            isPumpRequired: concreteRequestData.isPumpRequired,
            isConcreteConfirmed: concreteRequestData.isConcreteConfirmed,
            ParentCompanyId: concreteRequestData.ParentCompanyId,
            concreteOrderNumber: concreteRequestData.concreteOrderNumber,
            truckSpacingHours: concreteRequestData.truckSpacingHours,
            slump: concreteRequestData.slump,
            concreteQuantityOrdered: concreteRequestData.concreteQuantityOrdered,
            concreteConfirmedOn: concreteRequestData.concreteConfirmedOn
              ? concreteRequestData.concreteConfirmedOn
              : null,
            pumpLocation: concreteRequestData.pumpLocation,
            pumpOrderedDate: concreteRequestData.pumpOrderedDate
              ? concreteRequestData.pumpOrderedDate
              : null,
            pumpWorkStart: concreteRequestData.pumpWorkStart
              ? concreteRequestData.pumpWorkStart
              : null,
            pumpWorkEnd: concreteRequestData.pumpWorkEnd ? concreteRequestData.pumpWorkEnd : null,
            pumpConfirmedOn: concreteRequestData.pumpConfirmedOn
              ? concreteRequestData.pumpConfirmedOn
              : null,
            cubicYardsTotal: concreteRequestData.cubicYardsTotal,
            hoursToCompletePlacement: concreteRequestData.hoursToCompletePlacement,
            minutesToCompletePlacement: concreteRequestData.minutesToCompletePlacement,
            // ConcreteRequestId: concreteRequestData.ConcreteRequestId,
            requestType: 'concreteRequest',
            primerForPump: concreteRequestData.primerForPump,
            recurrenceId: concreteRequestData.seriesOption !== 1 ? newRecurrenceId : null,
          };
          if (concreteRequestData.seriesOption === 1) {
            concreteRequestParam.concretePlacementStart =
              concreteRequestData.concretePlacementStart;
            concreteRequestParam.concretePlacementEnd = concreteRequestData.concretePlacementEnd;
          }
          if (concreteRequestData.seriesOption === 2 || concreteRequestData.seriesOption === 3) {
            const utcDeliveryStartTimestamp = moment.utc(
              isConcreteRequestExists.concretePlacementStart,
            );
            const localStartTimestamp = utcDeliveryStartTimestamp.tz(concreteRequestData.timezone);
            const utcDeliveryEndTimestamp = moment.utc(
              isConcreteRequestExists.concretePlacementEnd,
            );
            const localEndTimestamp = utcDeliveryEndTimestamp.tz(concreteRequestData.timezone);
            concreteRequestParam.concretePlacementStart = await this.convertTimezoneToUtc(
              moment(localStartTimestamp).format('MM/DD/YYYY'),
              concreteRequestData.timezone,
              concreteRequestData.deliveryStartTime,
            );
            concreteRequestParam.concretePlacementEnd = await this.convertTimezoneToUtc(
              moment(localEndTimestamp).format('MM/DD/YYYY'),
              concreteRequestData.timezone,
              concreteRequestData.deliveryEndTime,
            );
          }
          if (
            ((memberData.RoleId === 2 || memberData.RoleId === 1) &&
              isConcreteRequestExists.status === 'Approved') ||
            memberData.isAutoApproveEnabled ||
            projectSettingDetails.ProjectSettings.isAutoApprovalEnabled
          ) {
            concreteRequestParam.status = 'Approved';
            concreteRequestParam.approvedBy = memberData.id;
            concreteRequestParam.approved_at = new Date();
          }
          await ConcreteRequest.update(concreteRequestParam, {
            where: { id: isConcreteRequestExists.id },
          });
          const { responsiblePersons, location, pumpSize, mixDesign, concreteSupplier } =
            concreteRequestData;
          const condition = Sequelize.and({
            ProjectId: concreteRequestData.ProjectId,
            ConcreteRequestId: isConcreteRequestExists.id,
          });
          const updateParam = {
            ConcreteRequestId: +isConcreteRequestExists.id,
            ConcreteRequestCode: +isConcreteRequestExists.ConcreteRequestId,
            ProjectId: +concreteRequestData.ProjectId,
            isDeleted: false,
          };
          const existResponsiblePersons = await ConcreteRequestResponsiblePerson.findAll({
            where: condition,
          });
          const existlocations = await ConcreteRequestLocation.findAll({
            where: condition,
          });
          const existMixDesigns = await ConcreteRequestMixDesign.findAll({
            where: condition,
          });
          const existPumpSizes = await ConcreteRequestPumpSize.findAll({
            where: condition,
          });
          const existCompanies = await ConcreteRequestCompany.findAll({
            where: condition,
          });
          concreteRequestService.updateValues(condition, async (response, error) => {
            if (!error) {
              responsiblePersons.forEach(async (element, i) => {
                const index = existResponsiblePersons.findIndex(
                  (item) => item.MemberId === element,
                );
                const memberParam = updateParam;
                memberParam.MemberId = element;
                if (index !== -1) {
                  await ConcreteRequestResponsiblePerson.update(memberParam, {
                    where: { id: existResponsiblePersons[index].id },
                  });
                } else {
                  await ConcreteRequestResponsiblePerson.createInstance(memberParam);
                }
              });
              if (
                existsConcreteRequest &&
                existsConcreteRequest.locationDetails &&
                existsConcreteRequest.locationDetails[0] &&
                existsConcreteRequest.locationDetails[0].ConcreteLocation
              ) {
                const getConcreteLocationDetail = await ConcreteLocation.findOne({
                  where: {
                    id: existsConcreteRequest.locationDetails[0].ConcreteLocation.id,
                  },
                });

                if (getConcreteLocationDetail) {
                  await ConcreteLocation.update(
                    {
                      location: concreteRequestData.location,
                    },

                    {
                      where: { id: getConcreteLocationDetail.id },
                    },
                  );
                }
              }
              // location.forEach(async (element, i) => {
              //   const index = existlocations.findIndex(
              //     (item) => item.ConcreteLocationId === element.id,
              //   );
              //   const locationParam = updateParam;
              //   locationParam.ConcreteLocationId = element.id;
              //   if (index !== -1) {
              //     await ConcreteRequestLocation.update(locationParam, {
              //       where: { id: existlocations[index].id },
              //     });
              //   } else {
              //     if (element.chosenFromDropdown) {
              //       locationParam.ConcreteLocationId = element.id;
              //     } else {
              //       const object = {
              //         location: element.location,
              //         ProjectId: +concreteRequestData.ProjectId,
              //         isDeleted: false,
              //         createdBy: loginUser.id,
              //       };
              //       const locationData = await ConcreteLocation.createConcreteLocation(object);
              //       locationParam.ConcreteLocationId = locationData.id;
              //     }
              //     await ConcreteRequestLocation.createInstance(locationParam);
              //   }
              // });
              pumpSize.forEach(async (element, i) => {
                const index = existPumpSizes.findIndex(
                  (item) => item.ConcretePumpSizeId === element.id,
                );
                const pumpSizeParam = updateParam;
                pumpSizeParam.ConcretePumpSizeId = element.id;
                if (index !== -1) {
                  await ConcreteRequestPumpSize.update(pumpSizeParam, {
                    where: { id: existPumpSizes[index].id },
                  });
                } else {
                  if (element.chosenFromDropdown) {
                    pumpSizeParam.ConcretePumpSizeId = element.id;
                  } else {
                    const object2 = {
                      pumpSize: element.pumpSize,
                      ProjectId: +concreteRequestData.ProjectId,
                      isDeleted: false,
                      createdBy: loginUser.id,
                    };
                    const pumpSizeData = await ConcretePumpSize.createConcretePumpSize(object2);
                    pumpSizeParam.ConcretePumpSizeId = pumpSizeData.id;
                  }
                  await ConcreteRequestPumpSize.createInstance(pumpSizeParam);
                }
              });
              mixDesign.forEach(async (element, i) => {
                const index = existMixDesigns.findIndex(
                  (item) => item.ConcreteMixDesignId === element.id,
                );
                const mixDesignParam = updateParam;
                mixDesignParam.ConcreteMixDesignId = element.id;
                if (index !== -1) {
                  await ConcreteRequestMixDesign.update(mixDesignParam, {
                    where: { id: existMixDesigns[index].id },
                  });
                } else {
                  if (element.chosenFromDropdown) {
                    mixDesignParam.ConcreteMixDesignId = element.id;
                  } else {
                    const object3 = {
                      mixDesign: element.mixDesign,
                      ProjectId: +concreteRequestData.ProjectId,
                      isDeleted: false,
                      createdBy: loginUser.id,
                    };
                    const mixDesignData = await ConcreteMixDesign.createConcreteMixDesign(object3);
                    mixDesignParam.ConcreteMixDesignId = mixDesignData.id;
                  }
                  await ConcreteRequestMixDesign.createInstance(mixDesignParam);
                }
              });
              concreteSupplier.forEach(async (element, i) => {
                const index = existCompanies.findIndex((item) => item.CompanyId === element);
                const companyParam = updateParam;
                companyParam.CompanyId = element;
                if (index !== -1) {
                  await ConcreteRequestCompany.update(companyParam, {
                    where: { id: existCompanies[index].id },
                  });
                } else {
                  await ConcreteRequestCompany.createInstance(companyParam);
                }
              });
              const locationChosen = await Locations.findOne({
                where: {
                  ProjectId: concreteRequestData.ProjectId,
                  id: concreteRequestData.LocationId,
                },
              });
              history.description = `${loginUser.firstName} ${loginUser.lastName} updated the Concrete Booking, ${concreteRequestData.description}`;
              history.locationFollowDescription = `${loginUser.firstName} ${loginUser.lastName} Created Concrete Booking, ${concreteRequestData.description}. Location: ${locationChosen.locationPath}.`;
              history.MemberId = memberData.id;
              history.firstName = loginUser.firstName;
              history.profilePic = loginUser.profilePic;
              history.createdAt = new Date();
              history.ProjectId = concreteRequestData.ProjectId;
              const projectDetails = await Project.findByPk(concreteRequestData.ProjectId);
              history.projectName = projectDetails.projectName;
              notification.ProjectId = isConcreteRequestExists.ProjectId;
              if (
                existsConcreteRequest &&
                existsConcreteRequest.recurrence &&
                existsConcreteRequest.recurrence.recurrence
              ) {
                notification.recurrenceType = `${existsConcreteRequest.recurrence.recurrence
                  } From ${moment(existsConcreteRequest.recurrence.recurrenceStartDate).format(
                    'MM/DD/YYYY',
                  )} to ${moment(existsConcreteRequest.recurrence.recurrenceEndDate).format(
                    'MM/DD/YYYY',
                  )}`;
              }
              notification.title = `Concrete Booking Updated by ${loginUser.firstName} ${loginUser.lastName}`;
              notification.isDeliveryRequest = false;
              notification.requestType = 'concreteRequest';
              const newNotification = await Notification.createInstance(notification);
              const memberLocationPreference = await LocationNotificationPreferences.findAll({
                where: {
                  ProjectId: concreteRequestData.ProjectId,
                  LocationId: concreteRequestData.LocationId,
                  follow: true,
                },
                include: [
                  {
                    association: 'Member',
                    attributes: ['id', 'RoleId'],
                    where: {
                      [Op.and]: [
                        {
                          id: { [Op.ne]: memberData.id },
                        },
                      ],
                    },
                    include: [
                      {
                        association: 'User',
                        attributes: ['id', 'firstName', 'lastName', 'email'],
                      },
                    ],
                  },
                ],
              });
              const locationFollowMembers = [];
              memberLocationPreference.forEach(async (element) => {
                locationFollowMembers.push(element.Member.id);
              });
              const personData = await ConcreteRequestResponsiblePerson.findAll({
                where: { ConcreteRequestId: isConcreteRequestExists.id, isDeleted: false },
                include: [
                  {
                    association: 'Member',
                    include: [
                      {
                        association: 'User',
                        attributes: ['id', 'firstName', 'lastName', 'email'],
                      },
                    ],
                    where: {
                      [Op.and]: {
                        RoleId: {
                          [Op.notIn]: [1, 2],
                        },
                        id: { [Op.notIn]: locationFollowMembers },
                      },
                    },
                    attributes: ['id', 'RoleId'],
                  },
                ],
                attributes: ['id'],
              });
              const adminData = await Member.findAll({
                where: {
                  [Op.or]: [
                    {
                      [Op.and]: [
                        { ProjectId: concreteRequestData.ProjectId },
                        { isDeleted: false },
                        { id: { [Op.ne]: newNotification.MemberId } },
                        { id: { [Op.in]: responsiblePersons } },
                        { id: { [Op.notIn]: locationFollowMembers } },
                      ],
                    },
                    { [Op.and]: [] },
                  ],
                },
                include: [
                  {
                    association: 'User',
                    attributes: ['id', 'firstName', 'lastName', 'email'],
                  },
                ],
                attributes: ['id'],
              });
              if (memberLocationPreference && memberLocationPreference.length > 0) {
                // here 5-(NotificationPreferenceItemId -When a delivery/crane/concrete request is edited)
                await pushNotification.sendMemberLocationPreferencePushNotificationForConcrete(
                  memberLocationPreference,
                  isConcreteRequestExists.ConcreteRequestId,
                  history.locationFollowDescription,
                  isConcreteRequestExists.requestType,
                  isConcreteRequestExists.ProjectId,
                  isConcreteRequestExists.id,
                  5,
                );
                // here 5-(NotificationPreferenceItemId -When a delivery/crane/concrete request is edited)
                await notificationHelper.createMemberDeliveryLocationInAppNotification(
                  DeliveryPersonNotification,
                  isConcreteRequestExists.ProjectId,
                  newNotification.id,
                  memberLocationPreference,
                  5,
                );
              }
              history.adminData = adminData;
              history.memberData = personData;
              if (memberLocationPreference && memberLocationPreference.length > 0) {
                history.memberData.push(...memberLocationPreference);
              }
              const checkMemberNotification = await NotificationPreference.findAll({
                where: {
                  ProjectId: concreteRequestData.ProjectId,
                  isDeleted: false,
                },
                attributes: [
                  'id',
                  'MemberId',
                  'ProjectId',
                  'ParentCompanyId',
                  'NotificationPreferenceItemId',
                  'instant',
                  'dailyDigest',
                ],
                include: [
                  {
                    association: 'NotificationPreferenceItem',
                    where: {
                      id: 5,
                      isDeleted: false,
                    },
                    attributes: ['id', 'description', 'inappNotification', 'emailNotification'],
                  },
                ],
              });
              history.notificationPreference = checkMemberNotification;
              // here 5-(NotificationPreferenceItemId -When a delivery/crane/concrete request is edited)
              await notificationHelper.createDeliveryPersonNotification(
                adminData,
                [],
                projectDetails,
                newNotification,
                DeliveryPersonNotification,
                memberData,
                loginUser,
                5,
                'updated a',
                'Concrete Request',
                `concrete Booking (${isConcreteRequestExists.ConcreteRequestId} - ${isConcreteRequestExists.description})`,
                isConcreteRequestExists.ConcreteRequestId,
              );
              const updatedConcreteRequest = await ConcreteRequest.getSingleConcreteRequestData({
                id: +isConcreteRequestExists.id,
              });
              if (existsConcreteRequest.status === 'Declined') {
                if (
                  memberData.RoleId !== 2 &&
                  !memberData.isAutoApproveEnabled &&
                  !projectSettingDetails.ProjectSettings.isAutoApprovalEnabled
                ) {
                  await ConcreteRequest.update(
                    { status: 'Tentative' },
                    {
                      where: { id: updatedConcreteRequest.id },
                    },
                  );
                }
                if (
                  memberData.RoleId === 2 ||
                  memberData.isAutoApproveEnabled ||
                  projectSettingDetails.ProjectSettings.isAutoApprovalEnabled
                ) {
                  const isStatusDataUpdated = await ConcreteRequest.update(
                    { status: 'Approved', approvedBy: memberData.id, approved_at: new Date() },
                    {
                      where: { id: updatedConcreteRequest.id },
                    },
                  );
                  if (isStatusDataUpdated) {
                    const object = {
                      ProjectId: concreteRequestData.ProjectId,
                      MemberId: memberData.id,
                      ConcreteRequestId: updatedConcreteRequest.id,
                      isDeleted: false,
                      type: 'approved',
                      description: `${loginUser.firstName} ${loginUser.lastName} Approved the Concrete Request, ${concreteRequestData.description}`,
                    };
                    await ConcreteRequestHistory.createInstance(object);
                  }
                }
              }
              if (existsConcreteRequest.status === 'Tentative') {
                if (
                  memberData.RoleId !== 2 &&
                  (memberData.isAutoApproveEnabled ||
                    projectSettingDetails.ProjectSettings.isAutoApprovalEnabled)
                ) {
                  const isStatusDataUpdated = await ConcreteRequest.update(
                    { status: 'Approved' },
                    {
                      where: { id: updatedConcreteRequest.id },
                    },
                  );
                  if (isStatusDataUpdated) {
                    const object = {
                      ProjectId: concreteRequestData.ProjectId,
                      MemberId: memberData.id,
                      ConcreteRequestId: updatedConcreteRequest.id,
                      isDeleted: false,
                      type: 'approved',
                      description: `${loginUser.firstName} ${loginUser.lastName} Approved the Concrete Request, ${concreteRequestData.description}`,
                    };
                    await ConcreteRequestHistory.createInstance(object);
                  }
                }
                if (
                  memberData.RoleId === 2 ||
                  memberData.isAutoApproveEnabled ||
                  projectSettingDetails.ProjectSettings.isAutoApprovalEnabled
                ) {
                  const isStatusDataUpdated = await ConcreteRequest.update(
                    { status: 'Approved', approvedBy: memberData.id, approved_at: new Date() },
                    {
                      where: { id: updatedConcreteRequest.id },
                    },
                  );
                  if (isStatusDataUpdated) {
                    const object = {
                      ProjectId: concreteRequestData.ProjectId,
                      MemberId: memberData.id,
                      ConcreteRequestId: updatedConcreteRequest.id,
                      isDeleted: false,
                      type: 'approved',
                      description: `${loginUser.firstName} ${loginUser.lastName} Approved the Concrete Request, ${concreteRequestData.description}`,
                    };
                    await ConcreteRequestHistory.createInstance(object);
                  }
                }
              }
              if (existsConcreteRequest.status === 'Approved') {
                if (
                  memberData.RoleId === 4 &&
                  !memberData.isAutoApproveEnabled &&
                  !projectSettingDetails.ProjectSettings.isAutoApprovalEnabled
                ) {
                  await ConcreteRequest.update(
                    { status: 'Tentative' },
                    {
                      where: { id: updatedConcreteRequest.id },
                    },
                  );
                }
              }
              concreteRequestService.updateEditConcreteRequestHistory(
                concreteRequestData,
                existsConcreteRequest,
                updatedConcreteRequest,
                history,
                loginUser,
              );
              // here 5-(NotificationPreferenceItemId -When a delivery/crane/concrete request is edited)
              await pushNotification.sendPushNotificationForConcrete(
                history,
                5,
                concreteRequestData.ProjectId,
              );
              history.ConcreteRequestId = isConcreteRequestExists.ConcreteRequestId;
              return done(history, false);
            }
          });
        });
      }
    } catch (e) {
      done(null, e);
    }
  },

  async createAttachement(inputData, done) {
    try {
      // await this.getDynamicModel(inputData);
      const incomeData = inputData.params;
      const userDetail = await User.findOne({
        where: { id: incomeData.userId, isDeleted: false },
      });
      inputData.user = userDetail;
      const loginUser = inputData.user;
      const bulkData = [];
      const exist = await DeliveryRequest.findOne({ where: { id: incomeData.DeliveryRequestId } });
      const memberDetail = await Member.findOne({
        where: [
          Sequelize.and({
            UserId: inputData.user.id,
            ProjectId: exist.ProjectId,
            isDeleted: false,
          }),
        ],
      });
      if (exist) {
        const locationChosen = await Locations.findOne({
          where: {
            ProjectId: exist.ProjectId,
            id: exist.LocationId,
          },
        });
        const memberLocationPreference = await LocationNotificationPreferences.findAll({
          where: {
            ProjectId: exist.ProjectId,
            LocationId: exist.LocationId,
            follow: true,
          },
          include: [
            {
              association: 'Member',
              attributes: ['id', 'RoleId'],
              where: {
                [Op.and]: [
                  {
                    id: { [Op.ne]: memberDetail.id },
                  },
                ],
              },
              include: [
                {
                  association: 'User',
                  attributes: ['id', 'firstName', 'lastName', 'email'],
                },
              ],
            },
          ],
        });
        const locationFollowMembers = [];
        memberLocationPreference.forEach(async (element) => {
          locationFollowMembers.push(element.Member.id);
        });
        awsConfig.upload(inputData.files, async (result, err) => {
          if (!err) {
            result.forEach((element, i) => {
              const fileData = inputData.files[i];
              let fileName;
              let relativePath;
              let extension;
              if (fileData.originalname !== undefined) {
                fileName = fileData.originalname;
                relativePath = fileData.originalname.split('.');
                extension = relativePath[relativePath.length - 1];
              } else {
                fileName = fileData.name;
                relativePath = fileData.name.split('.');
                extension = relativePath[relativePath.length - 1];
              }

              const data = {
                attachement: element.Location,
                filename: fileName,
                extension,
                DeliveryRequestId: +incomeData.DeliveryRequestId,
                DeliveryId: exist.DeliveryId,
                isDeleted: false,
              };
              bulkData.push(data);
            });
            if (bulkData.length > 0) {
              await DeliverAttachement.createMultipleInstance(bulkData);
              const history = {
                DeliveryRequestId: incomeData.DeliveryRequestId,
                DeliveryId: exist.DeliveryId,
                MemberId: memberDetail.id,
                type: 'attachement',
                description: `${inputData.user.firstName} ${inputData.user.lastName} Attached the file in ${exist.description}`,
                locationFollowDescription: `${inputData.user.firstName} ${inputData.user.lastName} Attached the file in Booking, ${exist.description}. Location: ${locationChosen.locationPath}.`,
              };
              const notification = history;
              notification.ProjectId = exist.ProjectId;
              notification.title = 'Delivery Booking Attachment';
              DeliverHistory.createInstance(history);
              const personData = await DeliveryPerson.findAll({
                where: { DeliveryId: exist.id, isDeleted: false },
                include: [
                  {
                    association: 'Member',
                    include: [
                      {
                        association: 'User',
                        attributes: ['id', 'firstName', 'lastName'],
                      },
                    ],
                    where: {
                      [Op.and]: {
                        RoleId: {
                          [Op.notIn]: [1, 2],
                        },
                        id: { [Op.notIn]: locationFollowMembers },
                      },
                    },
                    attributes: ['id', 'RoleId'],
                  },
                ],
                attributes: ['id'],
              });
              const adminData = await Member.findAll({
                where: {
                  [Op.and]: [
                    { ProjectId: exist.ProjectId },
                    { isDeleted: false },
                    { id: { [Op.notIn]: locationFollowMembers } },
                  ],
                },
                include: [
                  {
                    association: 'User',
                    attributes: ['id', 'firstName', 'lastName'],
                  },
                ],
                attributes: ['id'],
              });
              history.memberData = personData;
              history.adminData = adminData;
              history.firstName = loginUser.firstName;
              history.profilePic = loginUser.profilePic;
              history.createdAt = new Date();
              history.ProjectId = exist.ProjectId;
              const projectDetails = await Project.findByPk(exist.ProjectId);
              history.projectName = projectDetails.projectName;
              notification.requestType = 'deliveryRequest';
              const newNotification = await Notification.createInstance(notification);
              if (memberLocationPreference && memberLocationPreference.length > 0) {
                // here 1-(NotificationPreferenceItemId - When an attachment added to delivery/crane/concrete request)
                await pushNotification.sendMemberLocationPreferencePushNotification(
                  memberLocationPreference,
                  exist.DeliveryRequestId,
                  history.locationFollowDescription,
                  exist.requestType,
                  exist.ProjectId,
                  exist.id,
                  1,
                );
                // here 1-(NotificationPreferenceItemId - When an attachment added to delivery/crane/concrete request)
                await notificationHelper.createMemberDeliveryLocationInAppNotification(
                  DeliveryPersonNotification,
                  exist.ProjectId,
                  newNotification.id,
                  memberLocationPreference,
                  1,
                );
              }
              // here 1-(NotificationPreferenceItemId - When an attachment added to delivery/crane/concrete request)
              await notificationHelper.createDeliveryPersonNotification(
                adminData,
                personData,
                projectDetails,
                newNotification,
                DeliveryPersonNotification,
                memberDetail,
                loginUser,
                1,
                'attached a file in',
                'Delivery Request',
                `delivery Booking (${exist.DeliveryId} - ${exist.description})`,
                incomeData.DeliveryRequestId,
              );
              const checkMemberNotification = await NotificationPreference.findAll({
                where: {
                  ProjectId: exist.ProjectId,
                  isDeleted: false,
                },
                attributes: [
                  'id',
                  'MemberId',
                  'ProjectId',
                  'ParentCompanyId',
                  'NotificationPreferenceItemId',
                  'instant',
                  'dailyDigest',
                ],
                include: [
                  {
                    association: 'NotificationPreferenceItem',
                    where: {
                      id: 1,
                      isDeleted: false,
                    },
                    attributes: ['id', 'description', 'inappNotification', 'emailNotification'],
                  },
                ],
              });
              history.notificationPreference = checkMemberNotification;
              // here 1-(NotificationPreferenceItemId - When an attachment added to delivery/crane/concrete request)
              await pushNotification.sendDeviceToken(history, 1, exist.ProjectId);
              done(history, false);
            }
          } else {
            done(null, err);
          }
        });
      } else {
        done(null, { message: 'Delivery Booking id does not exist' });
      }
    } catch (e) {
      done(null, e);
    }
  },
  async createComment(inputData, done) {
    try {
      let resultedArray;
      // await this.getDynamicModel(inputData);
      const incomeData = inputData.body;
      const userDetail = await User.findOne({
        where: { id: incomeData.userId, isDeleted: false },
      });
      inputData.user = userDetail;
      const loginUser = inputData.user;
      const exist = await DeliveryRequest.findOne({
        include: [
          {
            association: 'memberDetails',
            required: false,
            where: { isDeleted: false, isActive: true },
            attributes: ['id'],
            include: [
              {
                association: 'Member',
                attributes: ['id'],
                include: [
                  {
                    association: 'User',
                    attributes: ['email', 'phoneCode', 'phoneNumber', 'firstName', 'lastName'],
                  },
                ],
              },
            ],
          },
        ],
        where: { id: incomeData.DeliveryRequestId },
      });
      if (exist) {
        const memberData = await Member.findOne({
          where: Sequelize.and({
            UserId: loginUser.id,
            ProjectId: exist.ProjectId,
            isDeleted: false,
          }),
        });
        const locationChosen = await Locations.findOne({
          where: {
            ProjectId: exist.ProjectId,
            id: exist.LocationId,
          },
        });
        const memberLocationPreference = await LocationNotificationPreferences.findAll({
          where: {
            ProjectId: exist.ProjectId,
            LocationId: exist.LocationId,
            follow: true,
          },
          include: [
            {
              association: 'Member',
              attributes: ['id', 'RoleId'],
              where: {
                [Op.and]: [
                  {
                    id: { [Op.ne]: memberData.id },
                  },
                ],
              },
              include: [
                {
                  association: 'User',
                  attributes: ['id', 'firstName', 'lastName', 'email'],
                },
              ],
            },
          ],
        });
        const locationFollowMembers = [];
        memberLocationPreference.forEach(async (element) => {
          locationFollowMembers.push(element.Member.id);
        });
        const bookingMemberDetails = [];
        exist.memberDetails.forEach(async (element) => {
          bookingMemberDetails.push(element.Member.id);
        });
        incomeData.MemberId = memberData.id;
        incomeData.ProjectId = exist.ProjectId;
        const history = {
          DeliveryRequestId: incomeData.DeliveryRequestId,
          DeliveryId: exist.DeliveryId,
          MemberId: memberData.id,
          type: 'comment',
          description: `${loginUser.firstName} ${loginUser.lastName} Commented on ${exist.description} as ${incomeData.comment}`,
          locationFollowDescription: `${loginUser.firstName} ${loginUser.lastName} Commented on ${exist.description} as ${incomeData.comment}. Location: ${locationChosen.locationPath}.`,
        };
        const notification = history;
        notification.ProjectId = exist.ProjectId;
        notification.title = 'Delivery Booking Comment';
        const previousComments = await DeliverComment.findAll({
          where: {
            ProjectId: incomeData.ProjectId,
            DeliveryRequestId: incomeData.DeliveryRequestId,
          },
          attributes: ['id', 'comment'],
        });
        let commentsArray = [];
        commentsArray = previousComments.map((comment) => {
          return JSON.stringify(comment.comment);
        });
        resultedArray = commentsArray.join(',');
        await DeliverComment.createInstance(inputData.body);
        await DeliverHistory.createInstance(history);
        history.firstName = loginUser.firstName;
        history.profilePic = loginUser.profilePic;
        history.createdAt = new Date();
        history.ProjectId = exist.ProjectId;
        const projectDetails = await Project.findByPk(exist.ProjectId);
        history.projectName = projectDetails.projectName;
        notification.requestType = 'deliveryRequest';
        const newNotification = await Notification.createInstance(notification);
        const personData = await DeliveryPerson.findAll({
          where: { DeliveryId: history.DeliveryRequestId, isDeleted: false },
          include: [
            {
              association: 'Member',
              include: [
                {
                  association: 'User',
                  attributes: ['id', 'email', 'firstName', 'lastName'],
                },
              ],
              where: {
                id: { [Op.ne]: newNotification.MemberId },
                [Op.and]: {
                  RoleId: {
                    [Op.notIn]: [1, 2],
                  },
                  id: { [Op.notIn]: locationFollowMembers },
                },
              },
              attributes: ['id', 'RoleId'],
            },
          ],
          attributes: ['id'],
        });
        const adminData = await Member.findAll({
          where: {
            [Op.and]: [
              { ProjectId: exist.ProjectId },
              { isDeleted: false },
              { id: { [Op.in]: bookingMemberDetails } },
              { id: { [Op.ne]: newNotification.MemberId } },
              { id: { [Op.notIn]: locationFollowMembers } },
            ],
          },
          include: [
            {
              association: 'User',
              attributes: ['id', 'email', 'firstName', 'lastName'],
            },
          ],
          attributes: ['id', 'RoleId'],
        });
        if (memberLocationPreference && memberLocationPreference.length > 0) {
          // here 4-(NotificationPreferenceItemId -When a new comment is added to delivery/crane/concrete request)
          await pushNotification.sendMemberLocationPreferencePushNotification(
            memberLocationPreference,
            exist.DeliveryRequestId,
            history.locationFollowDescription,
            exist.requestType,
            exist.ProjectId,
            exist.id,
            4,
          );
          // here 4-(NotificationPreferenceItemId -When a new comment is added to delivery/crane/concrete request)
          await notificationHelper.createMemberDeliveryLocationInAppNotification(
            DeliveryPersonNotification,
            exist.ProjectId,
            newNotification.id,
            memberLocationPreference,
            4,
          );
        }
        history.memberData = personData;
        history.adminData = adminData;
        if (memberLocationPreference && memberLocationPreference.length > 0) {
          history.memberData.push(...memberLocationPreference);
        }
        // here 4-(NotificationPreferenceItemId -When a new comment is added to delivery/crane/concrete request)
        await notificationHelper.createDeliveryPersonNotification(
          adminData,
          personData,
          projectDetails,
          newNotification,
          DeliveryPersonNotification,
          memberData,
          loginUser,
          4,
          'commented in a',
          'Delivery Request',
          `delivery Booking (${exist.DeliveryId} - ${exist.description})`,
          incomeData.DeliveryRequestId,
        );
        const checkMemberNotification = await NotificationPreference.findAll({
          where: {
            ProjectId: exist.ProjectId,
            isDeleted: false,
          },
          attributes: [
            'id',
            'MemberId',
            'ProjectId',
            'ParentCompanyId',
            'NotificationPreferenceItemId',
            'instant',
            'dailyDigest',
          ],
          include: [
            {
              association: 'NotificationPreferenceItem',
              where: {
                id: 4,
                isDeleted: false,
              },
              attributes: ['id', 'description', 'inappNotification', 'emailNotification'],
            },
          ],
        });
        history.notificationPreference = checkMemberNotification;
        // here 4-(NotificationPreferenceItemId -When a new comment is added to delivery/crane/concrete request)
        await pushNotification.sendDeviceToken(history, 4, exist.ProjectId);
        const userEmails = await commentService.getMemberDetailData(
          history,
          memberLocationPreference,
        );
        if (userEmails.length > 0) {
          userEmails.forEach(async (element, i) => {
            const time = moment(exist.deliveryStart).format('MM-DD-YYYY');
            const mailPayload = {
              deliveryId: exist.DeliveryId,
              deliveryDescription: exist.description,
              deliveryStart: time,
              deliveryEnd: exist.deliveryEnd,
              newComment: incomeData.comment,
              previousComments: resultedArray,
              toEmailUserName: element.firstName,
              email: element.email,
              commentedPersonname: loginUser.firstName,
              commentTimeStamp: moment().utc().format('MM-DD-YYYY hh:mm:ss a zz'),
            };
            if (!mailPayload.toEmailUserName) {
              mailPayload.toEmailUserName = element.email;
            }
            const isMemberFollowLocation = await LocationNotificationPreferences.findOne({
              where: {
                MemberId: +element.MemberId,
                ProjectId: +exist.ProjectId,
                LocationId: +exist.LocationId,
                isDeleted: false,
                // follow: true,
              },
            });
            if (isMemberFollowLocation) {
              const memberNotification = await NotificationPreference.findOne({
                where: {
                  MemberId: +element.MemberId,
                  ProjectId: +exist.ProjectId,
                  isDeleted: false,
                },
                include: [
                  {
                    association: 'NotificationPreferenceItem',
                    where: {
                      id: 7,
                      isDeleted: false,
                    },
                  },
                ],
              });
              if (memberNotification && memberNotification.instant) {
                await MAILER.sendMail(
                  mailPayload,
                  'commentadded',
                  `Comments added by ${mailPayload.commentedPersonname} : ${mailPayload.newComment} on Delivery ID ${exist.DeliveryId}`,
                  'Comments added against a Delivery Booking Notification',
                  async (info, err) => {
                    console.log(info, err);
                  },
                );
              }
              if (memberNotification && memberNotification.dailyDigest) {
                await commentService.createDailyDigestData(
                  +element.MemberId,
                  +exist.ProjectId,
                  +incomeData.ParentCompanyId,
                  loginUser,
                  'commented in a',
                  'Delivery Request',
                  `delivery Booking (${exist.DeliveryId} - ${exist.description})`,
                  incomeData.DeliveryRequestId,
                );
              }
            }
            if (i === userEmails.length - 1) {
              done(history, false);
            }
          });
        } else {
          done(history, false);
        }
      } else {
        done(null, { message: 'Delivery Booking id does not exist' });
      }
    } catch (e) {
      done(null, e);
    }
  },
  async createCraneRequestAttachement(inputData, done) {
    try {
      // await this.getDynamicModel(inputData);
      const incomeData = inputData.params;
      const userDetail = await User.findOne({
        where: { id: incomeData.userId, isDeleted: false },
      });
      inputData.user = userDetail;
      const loginUser = inputData.user;
      const bulkData = [];
      const exist = await CraneRequest.findOne({
        where: { CraneRequestId: incomeData.CraneRequestId, ProjectId: incomeData.ProjectId },
      });
      const memberDetail = await Member.findOne({
        where: [
          Sequelize.and({
            UserId: inputData.user.id,
            ProjectId: incomeData.ProjectId,
            isDeleted: false,
          }),
        ],
      });
      if (exist) {
        awsConfig.upload(inputData.files, async (result, err) => {
          if (!err) {
            const locationChosen = await Locations.findOne({
              where: {
                ProjectId: exist.ProjectId,
                id: exist.LocationId,
              },
            });
            const memberLocationPreference = await LocationNotificationPreferences.findAll({
              where: {
                ProjectId: exist.ProjectId,
                LocationId: exist.LocationId,
                follow: true,
              },
              include: [
                {
                  association: 'Member',
                  attributes: ['id', 'RoleId'],
                  where: {
                    [Op.and]: [
                      {
                        id: { [Op.ne]: memberDetail.id },
                      },
                    ],
                  },
                  include: [
                    {
                      association: 'User',
                      attributes: ['id', 'firstName', 'lastName', 'email'],
                    },
                  ],
                },
              ],
            });
            const locationFollowMembers = [];
            memberLocationPreference.forEach(async (element) => {
              locationFollowMembers.push(element.Member.id);
            });
            result.forEach((element, i) => {
              const fileData = inputData.files[i];
              let fileName;
              let relativePath;
              let extension;
              if (fileData.originalname !== undefined) {
                fileName = fileData.originalname;
                relativePath = fileData.originalname.split('.');
                extension = relativePath[relativePath.length - 1];
              } else {
                fileName = fileData.name;
                relativePath = fileData.name.split('.');
                extension = relativePath[relativePath.length - 1];
              }

              const data = {
                attachement: element.Location,
                filename: fileName,
                extension,
                CraneRequestId: +exist.id,
                ProjectId: +incomeData.ProjectId,
                isDeleted: false,
              };
              bulkData.push(data);
            });
            if (bulkData.length > 0) {
              await CraneRequestAttachment.createMultipleInstance(bulkData);
              const history = {
                CraneRequestId: +exist.id,
                MemberId: memberDetail.id,
                type: 'attachement',
                description: `${inputData.user.firstName} ${inputData.user.lastName} Attached the file in ${exist.description}`,
                locationFollowDescription: `${inputData.user.firstName} ${inputData.user.lastName} Attached the file in the Booking, ${exist.description}. Location: ${locationChosen.locationPath}.`,
              };
              history.ProjectId = incomeData.ProjectId;
              const notification = history;
              notification.ProjectId = incomeData.ProjectId;
              notification.title = 'Crane Booking Attachment';
              const personData = await CraneRequestResponsiblePerson.findAll({
                where: { CraneRequestId: exist.id, isDeleted: false },
                include: [
                  {
                    association: 'Member',
                    include: [
                      {
                        association: 'User',
                        attributes: ['id', 'firstName', 'lastName'],
                      },
                    ],
                    where: {
                      [Op.and]: {
                        RoleId: {
                          [Op.notIn]: [1, 2],
                        },
                        id: { [Op.notIn]: locationFollowMembers },
                      },
                    },
                    attributes: ['id', 'RoleId'],
                  },
                ],
                attributes: ['id'],
              });
              const adminData = await Member.findAll({
                where: {
                  [Op.and]: [
                    {
                      ProjectId: incomeData.ProjectId,
                      isDeleted: false,
                      id: { [Op.notIn]: locationFollowMembers },
                    },
                  ],
                },
                include: [
                  {
                    association: 'User',
                    attributes: ['id', 'firstName', 'lastName'],
                  },
                ],
                attributes: ['id'],
              });
              CraneRequestHistory.createInstance(history);
              history.memberData = personData;
              history.adminData = adminData;
              history.firstName = loginUser.firstName;
              history.profilePic = loginUser.profilePic;
              history.createdAt = new Date();
              history.ProjectId = incomeData.ProjectId;
              const projectDetails = await Project.findByPk(incomeData.ProjectId);
              history.projectName = projectDetails.projectName;
              notification.isDeliveryRequest = false;
              notification.requestType = 'craneRequest';
              const newNotification = await Notification.createInstance(notification);
              if (memberLocationPreference && memberLocationPreference.length > 0) {
                // here 2-(NotificationPreferenceItemId - When an attachment deleted on delivery/crane/concrete request)
                await pushNotification.sendMemberLocationPreferencePushNotificationForCrane(
                  memberLocationPreference,
                  exist.CraneRequestId,
                  history.locationFollowDescription,
                  exist.requestType,
                  exist.ProjectId,
                  exist.id,
                  2,
                );
                // here 2-(NotificationPreferenceItemId - When an attachment deleted on delivery/crane/concrete request)
                await notificationHelper.createMemberDeliveryLocationInAppNotification(
                  DeliveryPersonNotification,
                  exist.ProjectId,
                  newNotification.id,
                  memberLocationPreference,
                  2,
                );
              }
              // here 1-(NotificationPreferenceItemId - When an attachment added to delivery/crane/concrete request)
              await notificationHelper.createDeliveryPersonNotification(
                adminData,
                personData,
                projectDetails,
                newNotification,
                DeliveryPersonNotification,
                memberDetail,
                loginUser,
                1,
                'attached a file in a',
                'Crane Request',
                `crane Booking (${exist.CraneRequestId} - ${exist.description})`,
                incomeData.CraneRequestId,
              );
              const checkMemberNotification = await NotificationPreference.findAll({
                where: {
                  ProjectId: incomeData.ProjectId,
                  isDeleted: false,
                },
                attributes: [
                  'id',
                  'MemberId',
                  'ProjectId',
                  'ParentCompanyId',
                  'NotificationPreferenceItemId',
                  'instant',
                  'dailyDigest',
                ],
                include: [
                  {
                    association: 'NotificationPreferenceItem',
                    where: {
                      id: 1,
                      isDeleted: false,
                    },
                    attributes: ['id', 'description', 'inappNotification', 'emailNotification'],
                  },
                ],
              });
              history.notificationPreference = checkMemberNotification;
              // here 1-(NotificationPreferenceItemId - When an attachment added to delivery/crane/concrete request)
              await pushNotification.sendPushNotificationForCrane(history, 1, incomeData.ProjectId);
              done(history, false);
            }
          } else {
            done(null, err);
          }
        });
      } else {
        done(null, { message: 'Crane Booking id does not exist' });
      }
    } catch (e) {
      done(null, e);
    }
  },
  async createCraneRequestComment(inputData, done) {
    try {
      let resultedArray;
      // await this.getDynamicModel(inputData);
      const incomeData = inputData.body;
      const userDetail = await User.findOne({
        where: { id: incomeData.userId, isDeleted: false },
      });
      inputData.user = userDetail;
      const loginUser = inputData.user;
      const exist = await CraneRequest.findOne({
        include: [
          {
            association: 'memberDetails',
            required: false,
            where: { isDeleted: false, isActive: true },
            attributes: ['id'],
            include: [
              {
                association: 'Member',
                attributes: ['id'],
                include: [
                  {
                    association: 'User',
                    attributes: ['email', 'phoneCode', 'phoneNumber', 'firstName', 'lastName'],
                  },
                ],
              },
            ],
          },
        ],
        where: { CraneRequestId: +incomeData.CraneRequestId, ProjectId: +incomeData.ProjectId },
      });
      if (exist) {
        const memberData = await Member.findOne({
          where: Sequelize.and({
            UserId: loginUser.id,
            ProjectId: incomeData.ProjectId,
            isDeleted: false,
          }),
        });
        const locationChosen = await Locations.findOne({
          where: {
            ProjectId: exist.ProjectId,
            id: exist.LocationId,
          },
        });
        const memberLocationPreference = await LocationNotificationPreferences.findAll({
          where: {
            ProjectId: exist.ProjectId,
            LocationId: exist.LocationId,
            follow: true,
          },
          include: [
            {
              association: 'Member',
              attributes: ['id', 'RoleId'],
              where: {
                [Op.and]: [
                  {
                    id: { [Op.ne]: memberData.id },
                  },
                ],
              },
              include: [
                {
                  association: 'User',
                  attributes: ['id', 'firstName', 'lastName', 'email'],
                },
              ],
            },
          ],
        });
        const locationFollowMembers = [];
        memberLocationPreference.forEach(async (element) => {
          locationFollowMembers.push(element.Member.id);
        });
        const bookingMemberDetails = [];
        exist.memberDetails.forEach(async (element) => {
          bookingMemberDetails.push(element.Member.id);
        });
        incomeData.MemberId = memberData.id;
        const history = {
          CraneRequestId: +exist.id,
          MemberId: memberData.id,
          type: 'comment',
          description: `${loginUser.firstName} ${loginUser.lastName} Commented on ${exist.description} as ${incomeData.comment}`,
          locationFollowDescription: `${loginUser.firstName} ${loginUser.lastName} Commented on ${exist.description} as ${incomeData.comment}. Location: ${locationChosen.locationPath}.`,
        };
        const notification = history;
        notification.ProjectId = exist.ProjectId;
        notification.title = 'Crane Booking Comment';
        const previousComments = await CraneRequestComment.findAll({
          where: {
            ProjectId: incomeData.ProjectId,
            CraneRequestId: incomeData.CraneRequestId,
          },
          attributes: ['id', 'comment'],
        });
        let commentsArray = [];
        commentsArray = previousComments.map((comment) => {
          return JSON.stringify(comment.comment);
        });
        resultedArray = commentsArray.join(',');
        const addCraneRequestCommentObject = {
          ProjectId: inputData.body.ProjectId,
          MemberId: memberData.id,
          CraneRequestId: exist.id,
          isDeleted: false,
          comment: inputData.body.comment,
        };
        await CraneRequestComment.createInstance(addCraneRequestCommentObject);
        await CraneRequestHistory.createInstance(history);
        history.firstName = loginUser.firstName;
        history.profilePic = loginUser.profilePic;
        history.createdAt = new Date();
        history.ProjectId = exist.ProjectId;
        const projectDetails = await Project.findByPk(exist.ProjectId);
        history.projectName = projectDetails.projectName;
        notification.isDeliveryRequest = false;
        notification.requestType = 'craneRequest';
        const newNotification = await Notification.createInstance(notification);
        const personData = await CraneRequestResponsiblePerson.findAll({
          where: { CraneRequestId: history.CraneRequestId, isDeleted: false },
          include: [
            {
              association: 'Member',
              include: [
                {
                  association: 'User',
                  attributes: ['id', 'email', 'firstName', 'lastName'],
                },
              ],
              where: {
                id: { [Op.ne]: newNotification.MemberId },
                [Op.and]: {
                  RoleId: {
                    [Op.notIn]: [1, 2],
                  },
                  id: { [Op.notIn]: locationFollowMembers },
                },
              },
              attributes: ['id', 'RoleId'],
            },
          ],
          attributes: ['id'],
        });

        const adminData = await Member.findAll({
          where: {
            [Op.and]: [
              { ProjectId: exist.ProjectId },
              { isDeleted: false },
              { id: { [Op.in]: bookingMemberDetails } },
              { id: { [Op.ne]: newNotification.MemberId } },
              { id: { [Op.notIn]: locationFollowMembers } },
            ],
          },
          include: [
            {
              association: 'User',
              attributes: ['id', 'email', 'firstName', 'lastName'],
            },
          ],
          attributes: ['id'],
        });
        if (memberLocationPreference && memberLocationPreference.length > 0) {
          // here 4-(NotificationPreferenceItemId -When a new comment is added to delivery/crane/concrete request)
          await pushNotification.sendMemberLocationPreferencePushNotificationForCrane(
            memberLocationPreference,
            exist.CraneRequestId,
            history.locationFollowDescription,
            exist.requestType,
            exist.ProjectId,
            exist.id,
            4,
          );
          // here 4-(NotificationPreferenceItemId -When a new comment is added to delivery/crane/concrete request)
          await notificationHelper.createMemberDeliveryLocationInAppNotification(
            DeliveryPersonNotification,
            exist.ProjectId,
            newNotification.id,
            memberLocationPreference,
            4,
          );
        }
        history.memberData = personData;
        history.adminData = adminData;
        if (memberLocationPreference && memberLocationPreference.length > 0) {
          history.memberData.push(...memberLocationPreference);
        }
        // here 4-(NotificationPreferenceItemId -When a new comment is added to delivery/crane/concrete request)
        await notificationHelper.createDeliveryPersonNotification(
          adminData,
          personData,
          projectDetails,
          newNotification,
          DeliveryPersonNotification,
          memberData,
          loginUser,
          4,
          'commented in a',
          'Crane Request',
          `crane Booking (${exist.CraneRequestId} - ${exist.description})`,
          incomeData.CraneRequestId,
        );
        const checkMemberNotification = await NotificationPreference.findAll({
          where: {
            ProjectId: exist.ProjectId,
            isDeleted: false,
          },
          attributes: [
            'id',
            'MemberId',
            'ProjectId',
            'ParentCompanyId',
            'NotificationPreferenceItemId',
            'instant',
            'dailyDigest',
          ],
          include: [
            {
              association: 'NotificationPreferenceItem',
              where: {
                id: 4,
                isDeleted: false,
              },
              attributes: ['id', 'description', 'inappNotification', 'emailNotification'],
            },
          ],
        });
        history.notificationPreference = checkMemberNotification;
        // here 4-(NotificationPreferenceItemId -When a new comment is added to delivery/crane/concrete request)
        await pushNotification.sendPushNotificationForCrane(history, 4, exist.ProjectId);
        const userEmails = await craneRequestCommentService.getMemberDetailData(
          history,
          memberLocationPreference,
        );
        if (userEmails.length > 0) {
          userEmails.forEach(async (element, i) => {
            const time = moment(exist.craneDeliveryStart).format('MM-DD-YYYY');
            const mailPayload = {
              craneId: exist.CraneRequestId,
              craneDescription: exist.description,
              craneDeliveryStart: time,
              craneDeliveryEnd: exist.craneDeliveryEnd,
              newComment: incomeData.comment,
              previousComments: resultedArray,
              toEmailUserName: element.firstName,
              email: element.email,
              commentedPersonname: loginUser.firstName,
              commentTimeStamp: moment().utc().format('MM-DD-YYYY hh:mm:ss a zz'),
            };
            if (!mailPayload.toEmailUserName) {
              mailPayload.toEmailUserName = element.email;
            }
            const isMemberFollowLocation = await LocationNotificationPreferences.findOne({
              where: {
                MemberId: +element.MemberId,
                ProjectId: +exist.ProjectId,
                LocationId: +exist.LocationId,
                isDeleted: false,
                // follow: true,
              },
            });
            if (isMemberFollowLocation) {
              const memberNotification = await NotificationPreference.findOne({
                where: {
                  MemberId: +element.MemberId,
                  ProjectId: +exist.ProjectId,
                  isDeleted: false,
                },
                include: [
                  {
                    association: 'NotificationPreferenceItem',
                    where: {
                      id: 7,
                      isDeleted: false,
                    },
                  },
                ],
              });
              if (memberNotification && memberNotification.instant) {
                await MAILER.sendMail(
                  mailPayload,
                  'cranecommentadded',
                  `Comments added by ${mailPayload.commentedPersonname} : ${mailPayload.newComment} on Crane ID ${exist.CraneRequestId}`,
                  'Comments added against a Crane Booking Notification',
                  async (info, err) => {
                    console.log(info, err);
                  },
                );
              }
              if (memberNotification && memberNotification.dailyDigest) {
                await craneRequestCommentService.createDailyDigestData(
                  +element.MemberId,
                  +exist.ProjectId,
                  +incomeData.ParentCompanyId,
                  loginUser,
                  'commented in a',
                  'Crane Request',
                  `crane Booking (${exist.CraneRequestId} - ${exist.description})`,
                  exist.CraneRequestId,
                );
              }
            }
            if (i === userEmails.length - 1) {
              done(history, false);
            }
          });
        } else {
          done(history, false);
        }
      } else {
        done(null, { message: 'Crane Booking does not exist' });
      }
    } catch (e) {
      done(null, e);
    }
  },
  async createConcreteRequestAttachment(inputData, done) {
    try {
      // await this.getDynamicModel(inputData);
      const incomeData = inputData.params;
      const userDetail = await User.findOne({
        where: { id: incomeData.userId, isDeleted: false },
      });
      inputData.user = userDetail;
      const loginUser = inputData.user;
      const bulkData = [];
      const exist = await ConcreteRequest.findOne({
        where: {
          ConcreteRequestId: incomeData.ConcreteRequestId,
          ProjectId: incomeData.ProjectId,
          isDeleted: false,
        },
      });
      const memberDetail = await Member.findOne({
        where: [
          Sequelize.and({
            UserId: inputData.user.id,
            ProjectId: incomeData.ProjectId,
            isDeleted: false,
          }),
        ],
      });
      if (exist) {
        awsConfig.upload(inputData.files, async (result, err) => {
          if (!err) {
            const locationChosen = await Locations.findOne({
              where: {
                ProjectId: exist.ProjectId,
                id: exist.LocationId,
              },
            });
            const memberLocationPreference = await LocationNotificationPreferences.findAll({
              where: {
                ProjectId: exist.ProjectId,
                LocationId: exist.LocationId,
                follow: true,
              },
              include: [
                {
                  association: 'Member',
                  attributes: ['id', 'RoleId'],
                  where: {
                    [Op.and]: [
                      {
                        id: { [Op.ne]: memberDetail.id },
                      },
                    ],
                  },
                  include: [
                    {
                      association: 'User',
                      attributes: ['id', 'firstName', 'lastName', 'email'],
                    },
                  ],
                },
              ],
            });
            const locationFollowMembers = [];
            memberLocationPreference.forEach(async (element) => {
              locationFollowMembers.push(element.Member.id);
            });
            result.forEach((element, i) => {
              const fileData = inputData.files[i];
              let fileName;
              let relativePath;
              let extension;
              if (fileData.originalname !== undefined) {
                fileName = fileData.originalname;
                relativePath = fileData.originalname.split('.');
                extension = relativePath[relativePath.length - 1];
              } else {
                fileName = fileData.name;
                relativePath = fileData.name.split('.');
                extension = relativePath[relativePath.length - 1];
              }

              const data = {
                attachment: element.Location,
                filename: fileName,
                extension,
                ConcreteRequestId: +exist.id,
                ProjectId: +incomeData.ProjectId,
                isDeleted: false,
              };
              bulkData.push(data);
            });
            if (bulkData.length > 0) {
              await ConcreteRequestAttachment.createMultipleInstance(bulkData);
              const history = {
                ConcreteRequestId: +exist.id,
                MemberId: memberDetail.id,
                type: 'attachment',
                description: `${loginUser.firstName} ${loginUser.lastName} attached the file in ${exist.description}`,
                locationFollowDescription: `${loginUser.firstName} ${loginUser.lastName} attached the file in the Booking ${exist.description}. Location: ${locationChosen.locationPath}.`,
              };
              history.ProjectId = incomeData.ProjectId;
              const notification = history;
              notification.ProjectId = incomeData.ProjectId;
              notification.title = 'Concrete Booking Attachment';
              const personData = await ConcreteRequestResponsiblePerson.findAll({
                where: { ConcreteRequestId: exist.id, isDeleted: false },
                include: [
                  {
                    association: 'Member',
                    include: [
                      {
                        association: 'User',
                        attributes: ['id', 'firstName', 'lastName'],
                      },
                    ],
                    where: {
                      [Op.and]: {
                        RoleId: {
                          [Op.notIn]: [1, 2],
                        },
                        id: { [Op.notIn]: locationFollowMembers },
                      },
                    },
                    attributes: ['id', 'RoleId'],
                  },
                ],
                attributes: ['id'],
              });
              const adminData = await Member.findAll({
                where: {
                  [Op.and]: [
                    {
                      ProjectId: incomeData.ProjectId,
                      isDeleted: false,
                      id: { [Op.notIn]: locationFollowMembers },
                    },
                  ],
                },
                include: [
                  {
                    association: 'User',
                    attributes: ['id', 'firstName', 'lastName'],
                  },
                ],
                attributes: ['id'],
              });
              ConcreteRequestHistory.createInstance(history);
              history.memberData = personData;
              history.adminData = adminData;
              history.firstName = loginUser.firstName;
              history.profilePic = loginUser.profilePic;
              history.createdAt = new Date();
              history.ProjectId = incomeData.ProjectId;
              const projectDetails = await Project.findByPk(incomeData.ProjectId);
              history.projectName = projectDetails.projectName;
              notification.isDeliveryRequest = false;
              notification.requestType = 'concreteRequest';
              const newNotification = await Notification.createInstance(notification);
              if (memberLocationPreference && memberLocationPreference.length > 0) {
                // here 2-(NotificationPreferenceItemId - When an attachment deleted on delivery/crane/concrete request)
                await pushNotification.sendMemberLocationPreferencePushNotificationForConcrete(
                  memberLocationPreference,
                  exist.ConcreteRequestId,
                  history.locationFollowDescription,
                  exist.requestType,
                  exist.ProjectId,
                  exist.id,
                  2,
                );
                // here 2-(NotificationPreferenceItemId - When an attachment deleted on delivery/crane/concrete request)
                await notificationHelper.createMemberDeliveryLocationInAppNotification(
                  DeliveryPersonNotification,
                  exist.ProjectId,
                  newNotification.id,
                  memberLocationPreference,
                  2,
                );
              }
              // here 1-(NotificationPreferenceItemId - When an attachment added to delivery/crane/concrete request)
              await notificationHelper.createDeliveryPersonNotification(
                adminData,
                personData,
                projectDetails,
                newNotification,
                DeliveryPersonNotification,
                memberDetail,
                loginUser,
                1,
                'attached a file in a',
                'Concrete Request',
                `concrete Booking (${exist.ConcreteRequestId} - ${exist.description})`,
                incomeData.ConcreteRequestId,
              );
              const checkMemberNotification = await NotificationPreference.findAll({
                where: {
                  ProjectId: incomeData.ProjectId,
                  isDeleted: false,
                },
                attributes: [
                  'id',
                  'MemberId',
                  'ProjectId',
                  'ParentCompanyId',
                  'NotificationPreferenceItemId',
                  'instant',
                  'dailyDigest',
                ],
                include: [
                  {
                    association: 'NotificationPreferenceItem',
                    where: {
                      id: 1,
                      isDeleted: false,
                    },
                    attributes: ['id', 'description', 'inappNotification', 'emailNotification'],
                  },
                ],
              });
              history.notificationPreference = checkMemberNotification;
              // here 1-(NotificationPreferenceItemId - When an attachment added to delivery/crane/concrete request)
              await pushNotification.sendPushNotificationForConcrete(
                history,
                1,
                incomeData.ProjectId,
              );
              history.ConcreteRequestId = exist.ConcreteRequestId;
              done(history, false);
            }
          } else {
            done(null, err);
          }
        });
      } else {
        done(null, { message: 'Concrete Booking id does not exist' });
      }
    } catch (e) {
      done(null, e);
    }
  },
  async createConcreteRequestComment(inputData, done) {
    try {
      let resultedArray;
      // await this.getDynamicModel(inputData);
      const incomeData = inputData.body;
      const userDetail = await User.findOne({
        where: { id: incomeData.userId, isDeleted: false },
      });
      inputData.user = userDetail;
      const loginUser = inputData.user;
      const exist = await ConcreteRequest.findOne({
        include: [
          {
            association: 'memberDetails',
            required: false,
            where: { isDeleted: false, isActive: true },
            attributes: ['id'],
            include: [
              {
                association: 'Member',
                attributes: ['id'],
                include: [
                  {
                    association: 'User',
                    attributes: ['email', 'phoneCode', 'phoneNumber', 'firstName', 'lastName'],
                  },
                ],
              },
            ],
          },
        ],
        where: {
          ConcreteRequestId: incomeData.ConcreteRequestId,
          ProjectId: incomeData.ProjectId,
          isDeleted: false,
        },
      });
      if (exist) {
        const memberData = await Member.findOne({
          where: Sequelize.and({
            UserId: loginUser.id,
            ProjectId: incomeData.ProjectId,
            isDeleted: false,
          }),
        });
        const locationChosen = await Locations.findOne({
          where: {
            ProjectId: exist.ProjectId,
            id: exist.LocationId,
          },
        });
        const memberLocationPreference = await LocationNotificationPreferences.findAll({
          where: {
            ProjectId: exist.ProjectId,
            LocationId: exist.LocationId,
            follow: true,
          },
          include: [
            {
              association: 'Member',
              attributes: ['id', 'RoleId'],
              where: {
                [Op.and]: [
                  {
                    id: { [Op.ne]: memberData.id },
                  },
                ],
              },
              include: [
                {
                  association: 'User',
                  attributes: ['id', 'firstName', 'lastName', 'email'],
                },
              ],
            },
          ],
        });
        const locationFollowMembers = [];
        memberLocationPreference.forEach(async (element) => {
          locationFollowMembers.push(element.Member.id);
        });
        const bookingMemberDetails = [];
        exist.memberDetails.forEach(async (element) => {
          bookingMemberDetails.push(element.Member.id);
        });
        incomeData.MemberId = memberData.id;
        const history = {
          ConcreteRequestId: +exist.id,
          MemberId: memberData.id,
          type: 'comment',
          description: `${loginUser.firstName} ${loginUser.lastName} Commented on ${exist.description} as ${incomeData.comment}`,
          locationFollowDescription: `${loginUser.firstName} ${loginUser.lastName} Commented on ${exist.description} as ${incomeData.comment}. Location: ${locationChosen.locationPath}.`,
        };
        const notification = history;
        notification.ProjectId = exist.ProjectId;
        notification.title = 'Concrete Booking Comment';
        const previousComments = await ConcreteRequestComment.findAll({
          where: {
            ProjectId: incomeData.ProjectId,
            ConcreteRequestId: incomeData.ConcreteRequestId,
          },
          attributes: ['id', 'comment'],
        });
        let commentsArray = [];
        commentsArray = previousComments.map((comment) => {
          return JSON.stringify(comment.comment);
        });
        resultedArray = commentsArray.join(',');
        const toAddCommentObject = inputData.body;
        // eslint-disable-next-line no-param-reassign
        delete inputData.body.ConcreteRequestId;
        toAddCommentObject.ConcreteRequestId = exist.id;
        await ConcreteRequestComment.createInstance(toAddCommentObject);
        await ConcreteRequestHistory.createInstance(history);
        history.firstName = loginUser.firstName;
        history.profilePic = loginUser.profilePic;
        history.createdAt = new Date();
        history.ProjectId = exist.ProjectId;
        const projectDetails = await Project.findByPk(exist.ProjectId);
        history.projectName = projectDetails.projectName;
        notification.isDeliveryRequest = false;
        notification.requestType = 'concreteRequest';
        const newNotification = await Notification.createInstance(notification);
        const personData = await ConcreteRequestResponsiblePerson.findAll({
          where: { ConcreteRequestId: history.ConcreteRequestId, isDeleted: false },
          include: [
            {
              association: 'Member',
              include: [
                {
                  association: 'User',
                  attributes: ['id', 'email', 'firstName', 'lastName'],
                },
              ],
              where: {
                id: { [Op.ne]: newNotification.MemberId },
                [Op.and]: {
                  RoleId: {
                    [Op.notIn]: [1, 2],
                  },
                  id: { [Op.notIn]: locationFollowMembers },
                },
              },
              attributes: ['id', 'RoleId'],
            },
          ],
          attributes: ['id'],
        });

        const adminData = await Member.findAll({
          where: {
            [Op.and]: [
              { ProjectId: exist.ProjectId },
              { isDeleted: false },
              { id: { [Op.in]: bookingMemberDetails } },
              { id: { [Op.ne]: newNotification.MemberId } },
              { id: { [Op.notIn]: locationFollowMembers } },
            ],
          },
          include: [
            {
              association: 'User',
              attributes: ['id', 'email', 'firstName', 'lastName'],
            },
          ],
          attributes: ['id'],
        });
        history.memberData = personData;
        history.adminData = adminData;
        if (memberLocationPreference && memberLocationPreference.length > 0) {
          history.memberData.push(...memberLocationPreference);
        }
        if (memberLocationPreference && memberLocationPreference.length > 0) {
          // here 4-(NotificationPreferenceItemId -When a new comment is added to delivery/crane/concrete request)
          await pushNotification.sendMemberLocationPreferencePushNotificationForConcrete(
            memberLocationPreference,
            exist.ConcreteRequestId,
            history.locationFollowDescription,
            exist.requestType,
            exist.ProjectId,
            exist.id,
            4,
          );
          // here 4-(NotificationPreferenceItemId -When a new comment is added to delivery/crane/concrete request)
          await notificationHelper.createMemberDeliveryLocationInAppNotification(
            DeliveryPersonNotification,
            exist.ProjectId,
            newNotification.id,
            memberLocationPreference,
            4,
          );
        }
        // here 4-(NotificationPreferenceItemId -When a new comment is added to delivery/crane/concrete request)
        await notificationHelper.createDeliveryPersonNotification(
          adminData,
          personData,
          projectDetails,
          newNotification,
          DeliveryPersonNotification,
          memberData,
          loginUser,
          4,
          'commented in a',
          'Concrete Request',
          `concrete Booking (${exist.ConcreteRequestId} - ${exist.description})`,
          incomeData.ConcreteRequestId,
        );
        const checkMemberNotification = await NotificationPreference.findAll({
          where: {
            ProjectId: exist.ProjectId,
            isDeleted: false,
          },
          attributes: [
            'id',
            'MemberId',
            'ProjectId',
            'ParentCompanyId',
            'NotificationPreferenceItemId',
            'instant',
            'dailyDigest',
          ],
          include: [
            {
              association: 'NotificationPreferenceItem',
              where: {
                id: 4,
                isDeleted: false,
              },
              attributes: ['id', 'description', 'inappNotification', 'emailNotification'],
            },
          ],
        });
        history.notificationPreference = checkMemberNotification;
        history.ConcreteRequestId = exist.ConcreteRequestId;
        // here 4-(NotificationPreferenceItemId -When a new comment is added to delivery/crane/concrete request)
        await pushNotification.sendPushNotificationForConcrete(history, 4, exist.ProjectId);
        const userEmails = await concreteRequestCommentService.getMemberDetailData(
          history,
          memberLocationPreference,
        );
        if (userEmails.length > 0) {
          userEmails.forEach(async (element, i) => {
            const time = moment(exist.concretePlacementStart).format('MM-DD-YYYY');
            const mailPayload = {
              concreteId: exist.ConcreteRequestId,
              concreteDescription: exist.description,
              concreteStart: time,
              concreteEnd: exist.concretePlacementEnd,
              newComment: incomeData.comment,
              previousComments: resultedArray,
              toEmailUserName: element.firstName,
              email: element.email,
              commentedPersonname: loginUser.firstName,
              commentTimeStamp: moment().utc().format('MM-DD-YYYY hh:mm:ss a zz'),
            };
            if (!mailPayload.toEmailUserName) {
              mailPayload.toEmailUserName = element.email;
            }
            const isMemberFollowLocation = await LocationNotificationPreferences.findOne({
              where: {
                MemberId: +element.MemberId,
                ProjectId: +exist.ProjectId,
                LocationId: +exist.LocationId,
                isDeleted: false,
                // follow: true,
              },
            });
            if (isMemberFollowLocation) {
              const memberNotification = await NotificationPreference.findOne({
                where: {
                  MemberId: +element.MemberId,
                  ProjectId: +exist.ProjectId,
                  isDeleted: false,
                },
                include: [
                  {
                    association: 'NotificationPreferenceItem',
                    where: {
                      id: 7,
                      isDeleted: false,
                    },
                  },
                ],
              });
              if (memberNotification && memberNotification.instant) {
                await MAILER.sendMail(
                  mailPayload,
                  'concretecommentadded',
                  `Comments added by ${mailPayload.commentedPersonname} : ${mailPayload.newComment} on Concrete Booking ID ${exist.ConcreteRequestId}`,
                  'Comments added against a Concrete Booking Notification',
                  async (info, err) => {
                    console.log(info, err);
                  },
                );
              }
              if (memberNotification && memberNotification.dailyDigest) {
                await concreteRequestCommentService.createDailyDigestData(
                  +element.MemberId,
                  +exist.ProjectId,
                  +incomeData.ParentCompanyId,
                  loginUser,
                  'commented in a',
                  'Concrete Request',
                  `concrete Booking (${exist.ConcreteRequestId} - ${exist.description})`,
                  exist.ConcreteRequestId,
                );
              }
            }
            if (i === userEmails.length - 1) {
              done(history, false);
            }
          });
        } else {
          done(history, false);
        }
      } else {
        done(null, { message: 'Concrete Booking does not exist' });
      }
    } catch (e) {
      done(null, e);
    }
  },
  async isRequestToMember(req) {
    const { userId, ProjectId } = req.body;
    const memberDetail = await Member.findOne({
      where: {
        isDeleted: false,
        isActive: true,
        UserId: userId,
        ProjectId,
      },
    });
    const data = memberDetail;
    return { data };
  },
  async updateGuestMember(req) {
    try {
      const { userId, ProjectId } = req.body;
      const updateMemberDetail = await Member.update(
        { isRequestedToBeAMember: true },
        {
          where: {
            isDeleted: false,
            isActive: true,
            UserId: +userId,
            ProjectId,
          },
        },
      );
      const memberDetail = await Member.findOne({
        where: {
          isDeleted: false,
          isActive: true,
          UserId: +userId,
          ProjectId,
        },
      });
      const data = memberDetail;

      // send email to all PA for guest request to be a member
      const projectAdminLists = await Member.findAll({
        include: [
          {
            association: 'User',
            attributes: ['id', 'firstName', 'lastName', 'email'],
          },
        ],
        where: {
          ProjectId,
          RoleId: 2,
          status: 'completed',
        },
      });
      const getProject = await Project.findOne({
        where: {
          isDeleted: false,
          id: +ProjectId,
        },
      });
      for (let i = 0; i < projectAdminLists.length; i++) {
        const mailData = {
          email: projectAdminLists[i].User.email,
          adminFirstName: projectAdminLists[i].User.firstName,
          adminLastName: projectAdminLists[i].User.lastName,
          guestFirstName: memberDetail.firstName,
          guestLastName: memberDetail.lastName,
          projectName: getProject.projectName,
        };
        await MAILER.sendMail(
          mailData,
          'guestRequested',
          'Guest Requested',
          'Guest Requested',
          (info, err) => {
            if (err) {
              throw new Error(err);
            } else {
              return { data };
            }
          },
        );
      }
      return { data };
    } catch (error) {
      console.log(error);
      throw new Error(error);
    }
  },
};

module.exports = guestUserService;
