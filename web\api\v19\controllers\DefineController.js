const status = require('http-status');
const { defineService } = require('../services');
const exportService = require('../services/exportService');

const DefineController = {
  async createDefinable(req, res, next) {
    try {
      await defineService.createDefinable(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.CREATED).json({
            message: 'Definable Feature of Work Created Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async getDefinable(req, res, next) {
    try {
      await defineService.getDefinable(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          defineService.lastDefineId(req, async (lastDetail, error1) => {
            if (!error1) {
              res.status(status.OK).json({
                message: 'Definable Feature of Work Listed Successfully.',
                data: response,
                lastId: lastDetail,
              });
            } else {
              next(error1);
            }
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async updateDefinable(req, res, next) {
    try {
      await defineService.updateDefinable(req, (response, error) => {
        if (error) {
          res.status(status.INTERNAL_SERVER_ERROR).json(error);
        } else {
          res.status(status.OK).json({
            message: 'Definable Feature of Work Updated Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async exportDefinable(req, res, next) {
    req.params.export = 1;
    defineService.getDefinable(req, async (response, error) => {
      if (response) {
        res.setHeader('Content-Type', 'application/excel');
        res.setHeader('Content-Disposition', 'attachment; filename=Definable Feature of Work.xlsx');
        await response.xlsx.write(res);
        res.end();
      } else {
        next(error);
      }
    });
  },
  async deleteDefinable(req, res, next) {
    try {
      await defineService.deleteDefinable(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.OK).json({
            message: 'Definable Feature of deleted Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async addDefinable(req, res, next) {
    try {
      await defineService.addDefinable(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.OK).json({
            message: 'Definable Feature of Work Added Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async sampleExcelDownload(req, res, next) {
    try {
      const Workbook = await exportService.exportSampleDocument();
      if (Workbook) {
        res.setHeader('Content-Type', 'application/excel');
        res.setHeader(
          'Content-Disposition',
          `attachment; filename=Definable feature of work sample document.xlsx`,
        );
        await Workbook.xlsx.write(res);
        res.end();
      } else {
        res.status(422).json({ message: 'cannot export document', status: 422 });
      }
    } catch (e) {
      next(e);
    }
  },
};
module.exports = DefineController;
