const status = require('http-status');
const { attachementService } = require('../services');

const DefineController = {
  /**
   * @params {header: JWT 'token'}
   * @params req.body.product.name - required
   * @params req.body.product.type
   *
   * @params req.body.plan.nickName
   * @params req.body.plan.amount
   * @params req.body.plan.currency must be supported format e.g ('inr', 'usd')
   * @params req.body.plan.interval  Either day, week, month or year.
   * @params req.body.plan.usage_type [licensed, metered]
   *
   * @returns {StripePlan}
   */
  async createAttachement(req, res, next) {
    try {
      await attachementService.createAttachement(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.CREATED).json({
            message: 'Uploaded Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async createInspectionAttachement(req, res, next) {
    try {
      await attachementService.createInspectionAttachement(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.CREATED).json({
            message: 'Uploaded Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async getAttachement(req, res, next) {
    try {
      await attachementService.getAttachement(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.OK).json({
            message: 'Attachment Viewed Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },

  async getInspectionAttachement(req, res, next) {
    try {
      await attachementService.getInspectionAttachement(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.OK).json({
            message: 'Attachment Viewed Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },

  async deleteAttachement(req, res, next) {
    try {
      await attachementService.deleteAttachement(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.OK).json({
            message: 'Attachment Deleted Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },

  async deleteInspectionAttachement(req, res, next) {
    try {
      await attachementService.deleteInspectionAttachement(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.OK).json({
            message: 'Attachment Deleted Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
};
module.exports = DefineController;
