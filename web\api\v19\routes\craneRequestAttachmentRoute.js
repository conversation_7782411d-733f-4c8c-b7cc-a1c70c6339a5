const { Router } = require('express');
const { validate } = require('express-validation');
const multer = require('multer');
const passportConfig = require('../config/passport');
const { craneRequestAttachmentController } = require('../controllers');
const { craneRequestAttachmentValidation } = require('../middlewares/validations');

const storage = multer.memoryStorage();
const upload = multer({ storage });

const craneRequestAttachmentRoute = {
  get router() {
    const router = Router();
    router.post(
      '/add_crane_request_attachement/:CraneRequestId/?:ParentCompanyId/:ProjectId',
      upload.array('attachement', 12),
      validate(
        craneRequestAttachmentValidation.createCraneRequestAttachement,
        { keyByField: true },
        { abortEarly: false },
      ),
      passportConfig.isAuthenticated,
      craneRequestAttachmentController.createCraneRequestAttachement,
    );
    router.get(
      '/remove_crane_request_attachement/:id/?:ParentCompanyId/:ProjectId',
      validate(
        craneRequestAttachmentValidation.deleteCraneRequestAttachement,
        { keyByField: true },
        { abortEarly: false },
      ),
      passportConfig.isAuthenticated,
      craneRequestAttachmentController.deleteCraneRequestAttachement,
    );
    router.get(
      '/get_crane_request_attachements/:CraneRequestId/?:ParentCompanyId/:ProjectId',
      validate(
        craneRequestAttachmentValidation.getCraneRequestAttachement,
        { keyByField: true },
        { abortEarly: false },
      ),
      passportConfig.isAuthenticated,
      craneRequestAttachmentController.getCraneRequestAttachements,
    );

    return router;
  },
};
module.exports = craneRequestAttachmentRoute;
