/* eslint-disable no-await-in-loop */
const status = require('http-status');
const Cryptr = require('cryptr');
const {
  Sequelize,
  Enterprise,
  RestrictEmail,
  DeliveryPerson,
  NotificationPreferenceItem,
  NotificationPreference,
  CraneRequestResponsiblePerson,
  ConcreteRequestResponsiblePerson,
  Equipments,
  DeliveryRequest,
  CraneRequest,
  ConcreteRequest,
  CraneRequestHistory,
  DeliverHistory,
  ConcreteRequestHistory,
  Locations,
  LocationNotificationPreferences,
} = require('../models');
let {
  User,
  Member,
  Company,
  Role,
  Project,
  ParentCompany,
  DeliveryPersonNotification,
  Notification,
} = require('../models');
const ApiError = require('../helpers/apiError');
const MAILER = require('../mailer');
const helper = require('../helpers/domainHelper');
const notificationHelper = require('../helpers/notificationHelper');
const awsConfig = require('../middlewares/awsConfig');
const adminService = require('./adminService');
const { bcryptPassword } = require('./password');
// const mixpanelService = require('./mixpanelService');
const deepLinkService = require('./deepLinkService');
const { generatePassword } = require('../helpers/generatePassword');

const cryptr = new Cryptr('a0b1c2d3e4f5g6h7i8j9k10');

// let publicProject;
let publicUser;
let publicMember;
let publicCompany;
let publicParentCompany;

const { Op } = Sequelize;

const memberService = {
  async checkDomain(memberData, inputData) {
    await this.getDynamicModel(inputData);
    const emailDomain = await adminService.emailDomain(memberData);
    const parentNewDetail = await ParentCompany.findOne({
      where: { emailDomainName: emailDomain },
    });
    if (parentNewDetail) {
      return true;
    }
    return false;
  },
  async returnProjectModel() {
    const modelData = await helper.returnProjectModel();
    // publicProject = modelData.Project;
    publicMember = modelData.Member;
    publicCompany = modelData.Company;
    publicParentCompany = modelData.ParentCompany;
    publicUser = modelData.User;
  },
  async checkExistMember(req, done) {
    await this.getDynamicModel(req);
    let isMemberExists = false;
    let isRestrictedEmail = false;
    const firstSplit = req.body.email.split('@')[1];
    const secondSplit = firstSplit.split('.');
    let emailDomainName;
    if (secondSplit.length === 2) {
      emailDomainName = firstSplit;
    } else if (secondSplit.length > 2) {
      const str = firstSplit.substring(firstSplit.indexOf('.') + 1);
      emailDomainName = str;
    }
    const restrict = await RestrictEmail.getBy({ domainName: emailDomainName, isActive: true });
    if (restrict && req.body.RoleId === 2) {
      isRestrictedEmail = true;
    }
    const existUser = await User.findOne({
      where: Sequelize.and(
        Sequelize.where(
          Sequelize.fn('lower', Sequelize.col('email')),
          Sequelize.fn('lower', req.body.email),
        ),
        { isDeleted: false },
      ),
    });
    if (existUser) {
      const existMember = await Member.findOne({
        where: Sequelize.and({
          UserId: existUser.id,
          isDeleted: false,
          ProjectId: req.body.ProjectId,
        }),
      });
      if (existMember) {
        isMemberExists = true;
      }
    }
    if (req.body.RoleId) {
      done({ isRestrictedEmail }, false);
    } else {
      done({ isMemberExists, existUser }, false);
    }
  },
  async createMember(memberData, inputData, projectDetails, roleDetails, loginUser, done) {
    try {
      let newMember;
      let { domainName } = inputData.user;
      const { ParentCompanyId } = inputData.body;
      domainName = await this.getDynamicModel(inputData);
      // const password = generatePassword();
      const { email } = memberData;
      const existUser = await User.findOne({
        where: {
          [Op.and]: [
            {
              isDeleted: false,
              email: { [Sequelize.Op.iLike]: `${email}` },
            },
          ],
        },
      });
      const lastIdValue = await Member.findOne({
        where: { ProjectId: memberData.ProjectId, isDeleted: false },
        order: [['memberId', 'DESC']],
      });
      let id = 0;
      const newValue = JSON.parse(JSON.stringify(lastIdValue));
      if (newValue && newValue.memberId !== null && newValue.memberId !== undefined) {
        id = newValue.memberId;
      }
      const memberId = id + 1;
      this.checkEmailValidation(inputData, async (validation, validationerror) => {
        if (!validationerror) {
          if (existUser) {
            const existMember = await Member.findOne({
              where: Sequelize.and({
                UserId: existUser.id,
                isDeleted: false,
                ProjectId: memberData.ProjectId,
              }),
            });
            if (!existMember) {
              this.checkRole(
                memberData,
                existUser,
                validation,
                inputData,
                async (roleResponse, roleError) => {
                  if (roleError) {
                    return done(null, roleError);
                  }
                  const existNewMember = await Member.findOne({
                    where: Sequelize.and({
                      UserId: existUser.id,
                      isDeleted: false,
                      CompanyId: { [Op.ne]: null },
                    }),
                  });
                  if (existNewMember) {
                    const memberParam = {
                      UserId: existUser.id,
                      firstName: existNewMember.firstName,
                      CompanyId: memberData.CompanyId,
                      RoleId: memberData.RoleId,
                      ParentCompanyId,
                      memberId: +memberId,
                      createdBy: loginUser.id,
                      phoneNumber: existNewMember.phoneNumber,
                      phoneCode: existNewMember.phoneCode,
                      ProjectId: memberData.ProjectId,
                      status: existNewMember.status,
                    };
                    newMember = await Member.createInstance(memberParam);
                    const getNotificationPreferenceItemsList =
                      await NotificationPreferenceItem.findAll({
                        where: { isDeleted: false },
                      });
                    const getProject = await Project.findOne({
                      where: {
                        isDeleted: false,
                        id: +memberData.ProjectId,
                      },
                      include: [
                        {
                          where: { isDeleted: false },
                          association: 'TimeZone',
                          required: false,
                          attributes: ['id', 'location', 'timeZoneOffsetInMinutes'],
                        },
                      ],
                    });
                    const attr = {
                      time: '05:00',
                      timeFormat: 'AM',
                    };
                    let projectObject;
                    if (getProject) {
                      projectObject = getProject.toJSON();
                    }
                    if (projectObject && projectObject.TimeZone) {
                      attr.TimeZoneId = projectObject.TimeZone.id;
                    } else {
                      attr.TimeZoneId = 3;
                    }
                    await Member.update(attr, { where: { id: newMember.id } });
                    for (let index2 = 0;index2 < getNotificationPreferenceItemsList.length;index2 += 1) {
                      const item = getNotificationPreferenceItemsList[index2];
                      if (
                        (item.id === 7 &&
                          item.description ===
                          'When a comment is added to a delivery/crane/concrete request' &&
                          item.itemId === 4 &&
                          item.emailNotification === true &&
                          item.inappNotification === false &&
                          item.isDeleted === false) ||
                        item.inappNotification === true
                      ) {
                        const object = {
                          MemberId: newMember.id,
                          ProjectId: memberData.ProjectId,
                          ParentCompanyId: inputData.body.ParentCompanyId,
                          NotificationPreferenceItemId: item.id,
                          instant: true,
                          dailyDigest: false,
                          isDeleted: false,
                        };
                        await NotificationPreference.createInstance(object);
                        const getLocationsList = await Locations.findAll({
                          where: { isDeleted: false, ProjectId: memberData.ProjectId },
                        });
                        for (let index2 = 0; index2 < getLocationsList.length; index2 += 1) {
                          const location = getLocationsList[index2];
                          const object1 = {
                            MemberId: newMember.id,
                            ProjectId: memberData.ProjectId,
                            ParentCompanyId: inputData.body.ParentCompanyId,
                            LocationId: location.id,
                            follow: false,
                            isDeleted: false,
                          };
                          if (memberData.RoleId === 2 && location.isDefault === true) {
                            object.follow = true;
                          }
                          await LocationNotificationPreferences.createInstance(object1);
                        }
                      } else {
                        const object = {
                          MemberId: newMember.id,
                          ProjectId: memberData.ProjectId,
                          ParentCompanyId: inputData.body.ParentCompanyId,
                          NotificationPreferenceItemId: item.id,
                          instant: false,
                          dailyDigest: true,
                          isDeleted: false,
                        };
                        await NotificationPreference.createInstance(object);
                        const getLocationsList = await Locations.findAll({
                          where: { isDeleted: false, ProjectId: memberData.ProjectId },
                          });
                          for (let index2 = 0; index2 < getLocationsList.length; index2 += 1) {
                          const location = getLocationsList[index2];
                          const object1 = {
                          MemberId: newMember.id,
                          ProjectId: memberData.ProjectId,
                          ParentCompanyId: inputData.body.ParentCompanyId,
                          LocationId: location.id,
                          follow: false,
                          isDeleted: false,
                          };
                          if (memberData.RoleId === 2 && location.isDefault === true) {
                          object.follow = true;
                          }
                          await LocationNotificationPreferences.createInstance(object1);
                          }
                      }
                    }

                    if (memberData.RoleId === 2 || memberData.RoleId === 1) {
                      notificationHelper.memberNotificationCreation(
                        memberData,
                        DeliveryPersonNotification,
                        Notification,
                      );
                    }
                    if (domainName !== null && domainName !== undefined) {
                      memberParam.email = existUser.email;
                      await this.createPublicMember(memberParam, projectDetails, domainName);
                    }
                    memberParam.email = existUser.email;
                    memberParam.firstName = existUser.firstName;
                    memberParam.projectName = projectDetails.projectName;
                    memberParam.type = roleDetails.roleName;
                    delete newMember.password;
                    const loggedInMemberDetail = await Member.findOne({
                      where: Sequelize.and({
                        UserId: inputData.user.id,
                        isDeleted: false,
                        CompanyId: { [Op.ne]: null },
                      }),
                    });
                    if (+loggedInMemberDetail.RoleId === 1 && +memberData.RoleId === 2) {
                      const mailPayload = {
                        email: memberParam.email,
                        accountAdminName: inputData.user.firstName,
                        projectName: memberParam.projectName,
                        projectAdminName: memberParam.firstName,
                      };
                      this.sendMail(
                        mailPayload,
                        'assignPAtoNewProjectByAccountadmin',
                        `You were added to ${mailPayload.projectName} as Project Admin`,
                        'Assigned a New Project',
                        async (info, err) => {
                          if (err) {
                            const newError = new ApiError(err.message, status.BAD_REQUEST);
                            done(null, newError);
                          }
                          done(newMember, false);
                        },
                      );
                    } else {
                      this.sendMail(
                        memberParam,
                        'addproject',
                        'You were added as a member',
                        'Member Onboarded',
                        async (info, err) => {
                          if (err) {
                            const newError = new ApiError(err.message, status.BAD_REQUEST);
                            done(null, newError);
                          }
                          done(newMember, false);
                        },
                      );
                    }
                  } else {
                    const memberParam = {
                      UserId: existUser.id,
                      firstName: memberData.firstName,
                      CompanyId: memberData.CompanyId,
                      RoleId: memberData.RoleId,
                      ParentCompanyId,
                      memberId: +memberId,
                      phoneNumber: memberData.phoneNumber,
                      phoneCode: memberData.phoneCode,
                      createdBy: loginUser.id,
                      ProjectId: memberData.ProjectId,
                    };
                    newMember = await Member.createInstance(memberParam);
                    if (memberData.RoleId === 2 || memberData.RoleId === 1) {
                      notificationHelper.memberNotificationCreation(
                        newMember,
                        DeliveryPersonNotification,
                        Notification,
                      );
                    }
                    if (domainName !== null && domainName !== undefined) {
                      memberParam.email = existUser.email;
                      await this.createPublicMember(memberParam, projectDetails, domainName);
                    }
                    memberParam.email = existUser.email;
                    memberParam.firstName = existUser.firstName;
                    memberParam.type = roleDetails.roleName;
                    memberParam.id = newMember.id;
                    memberParam.requestType = memberData.requestType;
                    memberParam.domainName = domainName;
                    this.sendMail(
                      memberParam,
                      'invite_member',
                      'You were added as a member',
                      'Member Onboarded',
                      async (info, err) => {
                        if (err) {
                          const newError = new ApiError(err.message, status.BAD_REQUEST);
                          done(null, newError);
                        }
                        delete newMember.password;
                        done(newMember, false);
                      },
                    );
                  }
                },
              );
            } else {
              done(null, { message: 'Member Already exist in this project.' });
            }
          } else {
            const userParam = {
              firstName: memberData.firstName,
              email: memberData.email,
              phoneNumber: memberData.phoneNumber,
              phoneCode: memberData.phoneCode,
            };
            const newUser = await User.createInstance(userParam);
            const memberParam = {
              UserId: newUser.id,
              firstName: memberData.firstName,
              CompanyId: memberData.CompanyId,
              RoleId: memberData.RoleId,
              ParentCompanyId,
              memberId: +memberId,
              password: userParam.password,
              phoneNumber: memberData.phoneNumber,
              phoneCode: memberData.phoneCode,
              createdBy: loginUser.id,
              ProjectId: memberData.ProjectId,
            };
            newMember = await Member.createInstance(memberParam);
            if (memberData.RoleId === 2 || memberData.RoleId === 1) {
              notificationHelper.memberNotificationCreation(
                newMember,
                DeliveryPersonNotification,
                Notification,
              );
            }

            if (domainName !== null && domainName !== undefined) {
              memberParam.email = userParam.email;
              await this.createPublicMember(memberParam, projectDetails, domainName);
            }
            memberParam.email = userParam.email;
            memberParam.firstName = newUser.firstName;
            memberParam.type = roleDetails.roleName;
            memberParam.id = newMember.id;
            memberParam.requestType = memberData.requestType;
            memberParam.domainName = domainName;
            this.sendMail(
              memberParam,
              'invite_member',
              'You were added as a member',
              'Member Onboarded',
              async (info, err) => {
                if (err) {
                  const newError = new ApiError(err.message, status.BAD_REQUEST);
                  done(null, newError);
                }
                delete newMember.password;
                done(newMember, false);
              },
            );
          }
        } else {
          done(null, validationerror);
        }
      });
    } catch (e) {
      done(null, e);
    }
  },
  async updateInviteMember(req, done) {
    try {
      req.user = {};
      await this.returnProjectModel();
      req.user.email = req.body.memberDetail.email;
      req.body.ParentCompanyId = req.body.memberDetail.ParentCompanyId;
      const { password } = req.body.memberDetail;
      let newOnepassword;
      await bcryptPassword(password, (encPassword) => {
        newOnepassword = encPassword;
      });
      // const domainName = await this.getDynamicModel(req);
      let getRequestDomain;
      if (req.body && req.body.requestType && req.body.requestType === 1) {
        getRequestDomain = req.body.domainName;
      } else {
        getRequestDomain = cryptr.decrypt(req.body.domainName);
      }
      const models = await helper.getDynamicModel(getRequestDomain);
      const domainName = await this.getDynamicModel(req);
      const inputData = req.body;
      const newData = {};
      const { companyDetail } = req.body;
      const memberDetail = await models.Member.findOne({
        where: { id: inputData.memberDetail.id, isDeleted: false },
      });
      const userDetail = await models.User.findOne({ where: { id: memberDetail.UserId } });
      req.user = userDetail;
      const roleDetails = await models.Role.findOne({ where: { id: memberDetail.RoleId } });
      if (memberDetail.RoleId === 4 && domainName) {
        await publicUser.update(
          { password: newOnepassword },
          { where: { email: userDetail.email } },
        );
      } else {
        await User.update({ password: newOnepassword }, { where: { id: userDetail.id } });
      }
      const existCompanyDetail = await models.Company.findOne({
        where: { id: companyDetail.companyId },
      });
      newData.body = inputData.memberDetail;
      newData.user = {};
      newData.user.email = userDetail.email;
      newData.user.id = userDetail.id;
      newData.body.password = newOnepassword;
      let domain;
      if (req.body && req.body.requestType && req.body.requestType === 1) {
        domain = req.body.domainName;
      } else {
        domain = cryptr.decrypt(inputData.domainName);
      }
      newData.body.domainName = domain;
      memberDetail.email = userDetail.email;
      memberDetail.firstName = userDetail.firstName;
      memberDetail.type = roleDetails.roleName;
      memberDetail.password = newOnepassword;
      if (memberDetail && memberDetail.action === 'onboarding') {
        memberDetail.status = 'completed';
      }
      await this.createMemberNotificationPreference(req);
      await this.setLocationNotificationPreferenceForAMember(memberDetail);
      if (existCompanyDetail) {
        newData.body.CompanyId = existCompanyDetail.id;
        this.editMember(newData, (response, error) => {
          if (error) {
            done(null, error);
          } else {
            done(response, false);
          }
        });
      } else {
        const companyParam = companyDetail;
        companyParam.createdBy = req.user.id;
        companyParam.ProjectId = memberDetail.ProjectId;
        companyParam.ParentCompanyId = memberDetail.ParentCompanyId;
        companyParam.companyAutoId = 1;
        delete companyParam.fullName;
        delete companyParam.lastName;
        const newCompany = await models.Company.create(companyParam);
        newData.body.CompanyId = newCompany.id;
        newData.body.password = newOnepassword;
        let getDomain;
        if (req.body && req.body.requestType && req.body.requestType === 1) {
          getDomain = req.body.domainName;
        } else {
          getDomain = cryptr.decrypt(inputData.domainName);
        }
        newData.body.domainName = getDomain;
        this.editMember(newData, (response, error) => {
          if (error) {
            done(null, error);
          } else {
            done(response, false);
          }
        });
      }
    } catch (e) {
      done(null, e);
    }
  },
  async setLocationNotificationPreferenceForAMember(memberDetail) {
    try {
      const getLocationsList = await Locations.findAll({
        where: { isDeleted: false, ProjectId: memberDetail.ProjectId },
      });
      for (let index2 = 0; index2 < getLocationsList.length; index2 += 1) {
        const location = getLocationsList[index2];
        const object = {
          MemberId: memberDetail.id,
          ProjectId: memberDetail.ProjectId,
          LocationId: location.id,
          follow: false,
          ParentCompanyId: memberDetail.ParentCompanyId,
          isDeleted: false,
        };
        if (memberDetail.RoleId === 2 && location.isDefault === true) {
          object.follow = true;
        }
        await LocationNotificationPreferences.createInstance(object);
      }
    } catch (e) {
      throw (null, e);
    }
  },
  async createMemberNotificationPreference(req) {
    await this.returnProjectModel();
    let getRequestDomain;
    if (req.body && req.body.requestType && req.body.requestType === 1) {
      getRequestDomain = req.body.domainName;
    } else if (req.body && req.body.requestType && req.body.requestType === 0) {
      getRequestDomain = cryptr.decrypt(req.body.domainName);
    }
    let memberDetail;
    if (getRequestDomain) {
      const models = await helper.getDynamicModel(getRequestDomain);
      memberDetail = await models.Member.findOne({
        where: { id: req.body.memberDetail.id, isDeleted: false },
      });
    } else {
      memberDetail = await Member.findOne({
        where: { id: req.body.memberDetail.id, isDeleted: false },
      });
    }
    const getNotificationPreferenceItemsList = await NotificationPreferenceItem.findAll({
      where: { isDeleted: false },
    });
    const getProject = await Project.findOne({
      where: {
        isDeleted: false,
        id: +memberDetail.ProjectId,
      },
      include: [
        {
          where: { isDeleted: false },
          association: 'TimeZone',
          required: false,
          attributes: ['id', 'location', 'timeZoneOffsetInMinutes'],
        },
      ],
    });
    const attr = {
      time: '05:00',
      timeFormat: 'AM',
    };
    let projectObject;
    if (getProject) {
      projectObject = getProject.toJSON();
    }
    if (projectObject && projectObject.TimeZone) {
      attr.TimeZoneId = projectObject.TimeZone.id;
    } else {
      attr.TimeZoneId = 3;
    }
    await Member.update(attr, { where: { id: req.body.memberDetail.id } });

    for (let index2 = 0; index2 < getNotificationPreferenceItemsList.length; index2 += 1) {
      const item = getNotificationPreferenceItemsList[index2];
      if (
        (item.id === 7 &&
          item.description === 'When a comment is added to a delivery/crane/concrete request' &&
          item.itemId === 4 &&
          item.emailNotification === true &&
          item.inappNotification === false &&
          item.isDeleted === false) ||
        item.inappNotification === true
      ) {
        const object = {
          MemberId: req.body.memberDetail.id,
          ProjectId: memberDetail.ProjectId,
          ParentCompanyId: memberDetail.ParentCompanyId,
          NotificationPreferenceItemId: item.id,
          instant: true,
          dailyDigest: false,
          isDeleted: false,
        };
        await NotificationPreference.createInstance(object);
      } else {
        const object = {
          MemberId: req.body.memberDetail.id,
          ProjectId: memberDetail.ProjectId,
          ParentCompanyId: memberDetail.ParentCompanyId,
          NotificationPreferenceItemId: item.id,
          instant: false,
          dailyDigest: true,
          isDeleted: false,
        };
        await NotificationPreference.createInstance(object);
      }
    }
  },
  async createPublicMember(memberData, projectDetails, domainName, password) {
    await this.returnProjectModel();
    const tempData = memberData;
    const userParam = {
      firstName: memberData.firstName,
      email: memberData.email,
      phoneNumber: memberData.phoneNumber,
      phoneCode: memberData.phoneCode,
      password,
    };
    // const existProject = await publicProject.findOne({
    //   where: { publicSchemaId: memberData.ProjectId },
    // });
    let existUser = await publicUser.findOne({ where: { email: memberData.email } });
    if (!existUser) {
      existUser = await publicUser.createInstance(userParam);
    }
    // tempData.ProjectId = existProject.id;
    tempData.UserId = existUser.id;
    let companyData;
    let existCompany;
    if (tempData.CompanyId) {
      companyData = await Company.findOne({ where: { id: tempData.CompanyId } });
      existCompany = await publicCompany.findOne({
        where: { companyName: companyData.companyName, website: companyData.website },
      });
    }

    const enterpriseValue = await Enterprise.findOne({
      where: Sequelize.where(
        Sequelize.fn('lower', Sequelize.col('name')),
        Sequelize.fn('lower', domainName),
      ),
    });
    tempData.isAccount = true;
    tempData.EnterpriseId = enterpriseValue.id;
    if (!existCompany && companyData) {
      const newData = JSON.parse(JSON.stringify(companyData));
      delete newData.id;
      const parentNew = await ParentCompany.findOne({ where: { id: newData.ParentCompanyId } });
      let existPublicParent = await publicParentCompany.findOne({
        where: { emailDomainName: parentNew.emailDomainName },
      });
      if (!existPublicParent) {
        const parentData = JSON.parse(JSON.stringify(parentNew));
        delete parentData.id;
        existPublicParent = await publicParentCompany.create(parentData);
      }
      const newCompanyData = JSON.parse(JSON.stringify(existPublicParent));
      newData.ParentCompanyId = newCompanyData.id;
      existCompany = await publicCompany.create(newData);
    }
    if (existCompany) {
      tempData.CompanyId = existCompany.id;
    }
    tempData.ProjectId = projectDetails.publicSchemaId;
    await publicMember.create(tempData);
  },
  async getDynamicModel(inputData) {
    await this.returnProjectModel();
    let { domainName } = inputData.user;
    const incomeData = inputData;
    const ParentCompanyId = inputData.body.ParentCompanyId
      ? inputData.body.ParentCompanyId
      : inputData.params.ParentCompanyId;
    let domainEnterpriseValue;
    if (domainName) {
      domainEnterpriseValue = await Enterprise.findOne({
        where: { name: domainName.toLowerCase() },
      });
      if (!domainEnterpriseValue) {
        domainName = '';
      }
    }
    let enterpriseValue;
    if (!domainName && ParentCompanyId !== undefined && ParentCompanyId !== 'undefined') {
      const { email } = inputData.user;
      let userData;
      if (email) {
        userData = await publicUser.findOne({ where: { email } });
      }
      if (userData) {
        const memberData = await publicMember.findOne({
          where: { UserId: userData.id, RoleId: { [Op.ne]: 4 }, isDeleted: false },
        });
        if (memberData) {
          if (memberData.isAccount) {
            enterpriseValue = await Enterprise.findOne({
              where: { id: memberData.EnterpriseId, status: 'completed' },
            });
            if (enterpriseValue) {
              domainName = enterpriseValue.name.toLowerCase();
            }
          } else {
            enterpriseValue = await Enterprise.findOne({
              where: { ParentCompanyId, status: 'completed' },
            });
            if (enterpriseValue) {
              domainName = enterpriseValue.name.toLowerCase();
            }
          }
        } else {
          enterpriseValue = await Enterprise.findOne({
            where: { ParentCompanyId, status: 'completed' },
          });
          if (enterpriseValue) {
            domainName = enterpriseValue.name.toLowerCase();
          }
        }
      }
    }
    const modelObj = await helper.getDynamicModel(domainName);
    User = modelObj.User;
    Member = modelObj.Member;
    Company = modelObj.Company;
    Role = modelObj.Role;
    Project = modelObj.Project;
    DeliveryPersonNotification = modelObj.DeliveryPersonNotification;
    Notification = modelObj.Notification;
    ParentCompany = modelObj.ParentCompany;
    // NotificationPreferenceItem = modelObj.NotificationPreferenceItem;
    // NotificationPreference = modelObj.NotificationPreference;
    if (enterpriseValue) {
      const newUser = await User.findOne({ where: { email: inputData.user.email } });
      incomeData.user = newUser;
      // const existProjectId = inputData.params.ProjectId
      //   ? inputData.params.ProjectId
      //   : inputData.body.ProjectId;
      // if (existProjectId) {
      //   const projectDet = await Project.findOne({
      //     where: { publicSchemaId: existProjectId },
      //   });
      //   ProjectId = projectDet.id;
      // }
      // if (incomeData.params.ProjectId) {
      //   incomeData.params.ProjectId = ProjectId;
      // }
      // if (incomeData.body.ProjectId) {
      //   incomeData.body.ProjectId = ProjectId;
      // }
    }
    return domainName;
  },
  async getUserDetail(req, done) {
    try {
      this.returnProjectModel();
      req.user = {};
      let email;
      let domainName;
      if (req.body && req.body.requestType === 0) {
        email = cryptr.decrypt(req.body.email);
        domainName = cryptr.decrypt(req.body.domainName);
      }
      if (req.body && req.body.requestType === 1) {
        email = req.body.email;
        domainName = req.body.domainName;
      }
      req.user.email = email;
      const models = await helper.getDynamicModel(domainName);
      // await this.getDynamicModel(req);
      const inputData = req.body;
      const memberDetail = await models.Member.findOne({
        where: { id: +inputData.memberId, isDeleted: false },
        include: [
          {
            association: 'Company',
          },
        ],
      });
      if (memberDetail) {
        const userDetail = await models.User.findOne({ where: { id: +memberDetail.UserId } });
        req.user = userDetail;
        done({ userDetail, memberDetail }, false);
      } else {
        done(null, {
          status: status.UNPROCESSABLE_ENTITY,
          message: 'You were removed from the project.!',
        });
      }
    } catch (e) {
      done(null, e);
    }
  },
  async inviteMembers(req, done) {
    try {
      const getDomainName = await this.getDynamicModel(req);
      req.user.domainName = getDomainName;
      const inputData = req.body;
      const membersListArray = inputData.membersList;
      const loginUser = req.user;
      const projectDetails = await Project.findByPk(inputData.ProjectId);
      if (projectDetails) {
        if (membersListArray) {
          await Promise.all(
            membersListArray.map(async (memberNew) => {
              const member = memberNew;
              member.ProjectId = inputData.ProjectId;
              member.requestType = inputData.requestType;
              member.ParentCompanyId = inputData.ParentCompanyId;
              const roleDetails = await Role.findByPk(member.RoleId);
              if (roleDetails) {
                await this.createMember(
                  member,
                  req,
                  projectDetails,
                  roleDetails,
                  loginUser,
                  async (info, err) => {
                    if (!err) {
                      done(info, false);
                    } else {
                      done(null, err);
                    }
                  },
                );
              } else {
                done(null, { message: 'Role does not exist.' });
              }
            }),
          );
        }
      } else {
        done(null, { message: 'Project does not exist.' });
      }
    } catch (e) {
      done(null, e);
    }
  },
  async resendInviteLink(req, done) {
    try {
      const data = {
        id: req.body.memberId,
        ParentCompanyId: req.body.ParentCompanyId,
        email: req.body.email,
        type: req.body.type,
        requestType: req.body.requestType,
      };
      const domainName = await this.getDynamicModel(req);
      data.domainName = domainName;
      this.sendMail(
        data,
        'invite_member',
        'You were added as a member',
        'Member Onboarded',
        async (info, err) => {
          if (err) {
            const newError = new ApiError(err.message, status.BAD_REQUEST);
            done(null, newError);
          }
          done(data, false);
        },
      );
    } catch (e) {
      done(null, e);
    }
  },
  async getOverViewDetail(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const project = await Project.findByPk(inputData.params.ProjectId);
      const loginUser = inputData.user;
      const accUser = await User.findOne({
        where: { id: loginUser.id, userType: 'account admin' },
      });
      if (!accUser) {
        if (project) {
          const memberDetail = await Member.findOne({
            include: [
              {
                association: 'User',
                attributes: [
                  'firstName',
                  'email',
                  'phoneNumber',
                  'phoneCode',
                  'lastName',
                  'profilePic',
                  'id',
                ],
              },
              { association: 'Role', attributes: ['roleName'] },
              {
                association: 'Company',
                attributes: [
                  'companyName',
                  'website',
                  'address',
                  'secondAddress',
                  'id',
                  'state',
                  'city',
                  'zipCode',
                  'country',
                ],
              },
            ],
            attributes: ['id', 'RoleId'],
            where: {
              UserId: inputData.user.id,
              ProjectId: inputData.params.ProjectId,
              isDeleted: false,
            },
          });
          if (memberDetail) {
            done(memberDetail, false);
          } else {
            done(null, { message: 'Member Does not exist.' });
          }
        } else {
          done(null, { message: 'Project Id Does not exist.' });
        }
      } else {
        const newMemberDetail = await Member.findOne({
          include: [
            {
              association: 'User',
              attributes: [
                'firstName',
                'email',
                'phoneNumber',
                'phoneCode',
                'lastName',
                'profilePic',
                'id',
              ],
            },
            { association: 'Role', attributes: ['roleName'] },
            {
              association: 'ParentCompany',
              include: [
                {
                  association: 'Company',
                  attributes: [
                    'companyName',
                    'website',
                    'address',
                    'secondAddress',
                    'id',
                    'state',
                    'city',
                    'zipCode',
                    'country',
                  ],
                  where: { isParent: true },
                },
              ],
            },
          ],
          attributes: ['id', 'RoleId'],
          where: {
            UserId: inputData.user.id,
            isDeleted: false,
          },
        });
        done(newMemberDetail, false);
      }
    } catch (e) {
      done(null, e);
    }
  },
  async updateUserProfile(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const incomeData = inputData.body;
      const loginUser = inputData.user;
      const existDetails = await User.findOne({
        where: [
          Sequelize.and({
            phoneNumber: incomeData.phoneNumber,
            phoneCode: incomeData.phoneCode,
            id: { [Op.ne]: loginUser.id },
          }),
        ],
      });
      if (!existDetails) {
        const existCompanyDetails = await Company.findOne({
          where: {
            ProjectId: incomeData.ProjectId,
            id: { [Op.ne]: incomeData.CompanyId },
            companyName: { [Sequelize.Op.iLike]: incomeData.companyName },
          },
        });
        if (!existCompanyDetails) {
          await Member.update(
            {
              firstName: incomeData.firstName,
              phoneNumber: incomeData.phoneNumber,
              phoneCode: incomeData.phoneCode,
            },
            {
              where: {
                UserId: loginUser.id,
              },
            },
          );
          await User.update(
            {
              firstName: incomeData.firstName,
              phoneNumber: incomeData.phoneNumber,
              phoneCode: incomeData.phoneCode,
              lastName: incomeData.lastName,
            },
            {
              where: {
                id: loginUser.id,
              },
            },
          );
          const companyParam = {
            // address: incomeData.address,
            // secondAddress: incomeData.secondAddress,
            // website: incomeData.website,
            companyName: incomeData.companyName,
            // state: incomeData.state,
            // city: incomeData.city,
            // country: incomeData.country,
            // zipCode: incomeData.zipCode,
          };
          await Company.update(companyParam, { where: { id: incomeData.CompanyId } });
          done({ message: 'Details Updated Successfully.' }, false);
        } else {
          done(null, { message: 'Company Name already exist.' });
        }
      } else {
        done(null, { message: 'Mobile Number already exist.' });
      }
    } catch (e) {
      done(null, e);
    }
  },
  async lastMember(inputData, done) {
    try {
      let data;
      const lastData = await Member.findOne({
        where: { ProjectId: +inputData.params.ProjectId, isDeleted: false, RoleId: { [Op.ne]: 1 } },
        order: [['memberId', 'DESC']],
      });
      if (lastData) {
        data = lastData.memberId + 1;
      } else {
        data = 1;
      }
      done({ memberId: data }, false);
    } catch (e) {
      done(null, e);
    }
  },
  async editMember(inputData, done) {
    let getDomainName;
    getDomainName = inputData.body.domainName;
    if (!getDomainName) {
      getDomainName = 'public';
    }
    const models = await helper.getDynamicModel(getDomainName);

    // await this.getDynamicModel(inputData);
    const memberData = inputData.body;
    const loginUser = inputData.user;
    const { email } = memberData;
    const existUser = await models.User.findOne({
      where: Sequelize.and(
        Sequelize.where(
          Sequelize.fn('lower', Sequelize.col('email')),
          Sequelize.fn('lower', email),
        ),
        { isDeleted: false },
      ),
    });
    const parentId = await models.Member.getBy({ UserId: loginUser.id });
    const emailValidationData = await models.ParentCompany.getBy({ id: parentId.ParentCompanyId });
    this.checkRole(
      memberData,
      existUser,
      emailValidationData,
      inputData,
      async (roleResponse, roleError) => {
        if (roleError) {
          done(null, roleError);
        } else {
          const memberParam = {
            firstName: memberData.firstName,
            CompanyId: memberData.CompanyId,
            RoleId: memberData.RoleId,
            phoneNumber: memberData.phoneNumber,
            phoneCode: memberData.phoneCode,
            password: memberData.password,
          };
          if (memberData.status === 'completed') {
            memberParam.status = 'completed';
          }
          memberParam.lastName = memberData.lastName;
          const newMember = await models.Member.updateInstance(memberData.id, memberParam);
          delete memberParam.RoleId;
          delete memberParam.CompanyId;
          await models.User.update(memberParam, { where: { id: existUser.id } });
          await publicUser.update(
            { password: memberParam.password },
            { where: { email: memberData.email } },
          );
          done(newMember, false);
        }
      },
    );
  },
  async editMemberDetail(inputData, done) {
    await this.getDynamicModel(inputData);
    const memberData = inputData.body;
    const loginUser = inputData.user;
    const { email } = memberData;
    const existUser = await User.findOne({
      where: Sequelize.and(
        Sequelize.where(
          Sequelize.fn('lower', Sequelize.col('email')),
          Sequelize.fn('lower', email),
        ),
        { isDeleted: false },
      ),
    });
    const parentId = await Member.getBy({ UserId: loginUser.id });
    const emailValidationData = await ParentCompany.getBy({ id: parentId.ParentCompanyId });
    this.checkRole(
      memberData,
      existUser,
      emailValidationData,
      inputData,
      async (roleResponse, roleError) => {
        if (roleError) {
          done(null, roleError);
        } else {
          const memberParam = {
            firstName: memberData.firstName,
            CompanyId: memberData.CompanyId,
            RoleId: memberData.RoleId,
            phoneNumber: memberData.phoneNumber,
            phoneCode: memberData.phoneCode,
            password: memberData.password,
          };
          if (memberData.status === 'completed') {
            memberParam.status = 'completed';
          }
          if (memberData.lastName) {
            memberParam.lastName = memberData.lastName;
          }
          const newMember = await Member.updateInstance(memberData.id, memberParam);
          delete memberParam.RoleId;
          delete memberParam.CompanyId;
          await User.update(memberParam, { where: { id: existUser.id } });
          done(newMember, false);
        }
      },
    );
  },
  async deleteMember(input, done) {
    try {
      await this.getDynamicModel(input);
      const reqData = input.body;
      let getMembers;
      const { id } = input.body;
      if (reqData.isSelectAll) {
        getMembers = await Member.findAll({
          where: { ProjectId: reqData.ProjectId, isDeleted: false },
          include: ['User'],
        });
      } else {
        getMembers = await Member.findAll({
          where: { ProjectId: reqData.ProjectId, isDeleted: false, id: { [Op.in]: id } },
          include: ['User'],
        });
      }
      if (getMembers && getMembers.length > 0) {
        getMembers.map(async (item, index) => {
          const isMemberMappedToDeliveryRequest = await DeliveryPerson.findOne({
            where: {
              MemberId: +item.id,
              isDeleted: false,
              ProjectId: reqData.ProjectId,
            },
          });
          const isMemberMappedToCraneRequest = await CraneRequestResponsiblePerson.findOne({
            where: {
              MemberId: +item.id,
              isDeleted: false,
              ProjectId: reqData.ProjectId,
            },
          });
          const isMemberMappedToConcreteRequest = await ConcreteRequestResponsiblePerson.findOne({
            where: {
              MemberId: +item.id,
              isDeleted: false,
              ProjectId: reqData.ProjectId,
            },
          });
          const isMemberMappedToEquipment = await Equipments.findOne({
            where: {
              controlledBy: +item.id,
              isDeleted: false,
              ProjectId: reqData.ProjectId,
            },
          });
          if (isMemberMappedToDeliveryRequest) {
            return done(null, {
              message: `${item.User.firstName} ${item.User.lastName} cannot be deleted. ${item.User.firstName} ${item.User.lastName} is mapped to submitted requests`,
            });
          }
          if (isMemberMappedToCraneRequest) {
            return done(null, {
              message: `${item.User.firstName} ${item.User.lastName} cannot be deleted. ${item.User.firstName} ${item.User.lastName} is mapped to submitted requests`,
            });
          }
          if (isMemberMappedToConcreteRequest) {
            return done(null, {
              message: `${item.User.firstName} ${item.User.lastName} cannot be deleted. ${item.User.firstName} ${item.User.lastName} is mapped to submitted requests`,
            });
          }
          if (isMemberMappedToEquipment) {
            return done(null, {
              message: `${item.User.firstName} ${item.User.lastName} cannot be deleted. ${item.User.firstName} ${item.User.lastName} is mapped to an equipment`,
            });
          }
          await Member.update(
            { isDeleted: true },
            {
              where: {
                id: +item.id,
                ProjectId: reqData.ProjectId,
                RoleId: { [Op.ne]: 1 },
                isDeleted: false,
                UserId: { [Op.not]: Sequelize.col('createdBy') },
              },
            },
          );
          if (index === getMembers.length - 1) {
            return done('success', false);
          }
        });
      }
    } catch (e) {
      done(null, e);
    }
  },
  async checkRole(memberData, existUser, validation, inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const checkRole = await Member.findOne({
        where: Sequelize.and({
          UserId: existUser.id,
          isDeleted: false,
          ParentCompanyId: validation.id,
        }),
      });
      if (memberData === null || memberData === undefined) {
        done({ status: true }, false);
      } else if (memberData.RoleId === 2 || memberData.RoleId === 3) {
        if (checkRole) {
          if (checkRole.RoleId !== 4) {
            done({ status: true }, false);
          } else {
            done(null, { message: 'This member is a sub contractor in our organization.' });
          }
        } else {
          done({ status: true }, false);
        }
      } else if (memberData.RoleId === 4) {
        if (checkRole) {
          if (checkRole.RoleId === 2 || checkRole.RoleId === 3) {
            done(null, {
              message:
                'This member is Project Admin/General Contractor in our organization , it is not possible to assign member as a Sub Contractor.',
            });
          } else {
            done({ status: true }, false);
          }
        } else {
          done({ status: true }, false);
        }
      } else {
        done({ status: true }, false);
      }
    } catch (e) {
      done(null, e);
    }
  },
  async checkEmailValidation(inputData, done) {
    await this.getDynamicModel(inputData);
    const memberData = inputData.body;
    const loginUser = inputData.user;
    const parentId = await Member.getBy({ UserId: loginUser.id });
    const emailValidationData = await ParentCompany.getBy({ id: parentId.ParentCompanyId });
    if (memberData.RoleId === 2 || memberData.RoleId === 3) {
      const firstSplit = memberData.email.split('@')[1];
      const secondSplit = firstSplit.split('.');
      let emailDomainName;
      if (secondSplit.length === 2) {
        emailDomainName = firstSplit;
      } else if (secondSplit.length > 2) {
        const str = firstSplit.substring(firstSplit.indexOf('.') + 1);
        emailDomainName = str;
      }
      const restrict = await RestrictEmail.getBy({ domainName: emailDomainName, isActive: true });
      if (restrict) {
        done(null, { message: 'This email is not allowed' });
      } else if (emailValidationData.emailDomainName === emailDomainName) {
        done(emailValidationData, false);
      } else {
        done(null, { message: 'This user is not our organization.' });
      }
    } else {
      done(emailValidationData);
    }
  },
  async sendMail(userData, mailData, mailSubject, tagName, done) {
    await MAILER.sendMail(userData, mailData, mailSubject, tagName, (info, err) => {
      if (err) {
        const newError = new ApiError(err.message, status.BAD_REQUEST);
        done(null, newError);
      } else {
        done(userData, false);
      }
    });
  },
  async uploadProfile(inputData, done) {
    awsConfig.singleUpload(inputData, async (result, err) => {
      if (!err) {
        await User.update(
          {
            profilePic: result[0].Location,
          },
          {
            where: { id: inputData.user.id },
          },
        );
        done({ imageUrl: result[0].Location }, false);
      } else {
        done(null, err);
      }
    });
  },
  async listMember(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { params } = inputData;
      const incomeData = inputData.body;
      const pageNumber = +params.pageNo;
      const pageSize = +params.pageSize;
      const { sort } = inputData.body;
      const { sortByField } = inputData.body;
      const offset = (pageNumber - 1) * pageSize;
      const condition = {
        ProjectId: params.ProjectId,
        isDeleted: false,
        RoleId: { [Op.ne]: 1 },
        isGuestUser: false,
      };
      const memberList = await Member.getAll(
        condition,
        incomeData.nameFilter,
        incomeData.companyFilter,
        incomeData.roleFilter,
        incomeData.statusFilter,
        incomeData.search,
        +params.pageSize,
        offset,
        sort,
        sortByField,
      );
      done(memberList, false);
    } catch (e) {
      done(null, e);
    }
  },
  async searchMember(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { params } = inputData;
      const condition = {
        [Op.and]: [
          {
            ProjectId: +params.ProjectId,
            isDeleted: false,
            RoleId: { [Op.ne]: 1 },
            isActive: true,
            isGuestUser: false
          },
        ],
      };
      const memberList = await Member.searchMemberNDR(condition, params);
      const finalList = [];
      let assignEmail;
      memberList.forEach((element) => {
        if (element.User.firstName && element.User.lastName) {
          assignEmail = `${element.User.firstName} ${element.User.lastName}(${element.User.email})`;
        } else {
          assignEmail = `(${element.User.email})`;
        }
        finalList.push({
          id: element.id,
          emails: element.User.email,
          firstName: element.firstName,
          lastName: element.User.lastName,
          email: assignEmail,
          isGuestUser:element.isGuestUser
        });
      });
      done(finalList, false);
    } catch (e) {
      done(null, e);
    }
  },
  async searchAutoApproveMember(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { params } = inputData;
      const condition = {
        [Op.and]: [
          {
            ProjectId: +params.ProjectId,
            isDeleted: false,
            RoleId: { [Op.in]: [3, 4] },
            isActive: true,
          },
        ],
      };
      const memberList = await Member.searchMemberNDR(condition, params);
      const finalList = [];
      let assignEmail;
      memberList.forEach((element) => {
        if (element.User.firstName && element.User.lastName) {
          assignEmail = `${element.User.firstName} ${element.User.lastName} (${element.User.email})`;
        } else {
          assignEmail = `(${element.User.email})`;
        }
        finalList.push({
          id: element.id,
          emails: element.User.email,
          firstName: element.firstName,
          lastName: element.User.lastName,
          email: assignEmail,
        });
      });
      done(finalList, false);
    } catch (e) {
      done(null, e);
    }
  },
  async searchAllMember(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { params } = inputData;
      const userData = await User.findAll({
        where: {
          isDeleted: false,
          [Op.or]: [
            {
              email: { [Sequelize.Op.iLike]: `% ${params.search}% ` },
            },
            {
              firstName: { [Sequelize.Op.iLike]: `% ${params.search}% ` },
            },
          ],
        },
        attributes: ['id', 'email', 'firstName'],
      });
      const memberData = await Member.findAll({
        where: { ProjectId: params.ProjectId, isDeleted: false },
      });
      const memberList = [];
      memberData.forEach((element) => {
        memberList.push(element.id);
      });
      const finalResult = userData.filter((e) => {
        return memberList.indexOf(e.id) === -1;
      });
      done(finalResult, false);
    } catch (e) {
      done(null, e);
    }
  },
  async listAllMember(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { params } = inputData;
      const memberList = await Member.getAllEmail({
        ProjectId: params.ProjectId,
        isDeleted: false,
        isActive: true,
        //isGuestUser: false,
      });
      done(memberList, false);
    } catch (e) {
      done(null, e);
    }
  },
  async membersForBulkUploadDeliveryRequest(inputData) {
    try {
      await this.getDynamicModel(inputData);
      const { params } = inputData;
      const memberList = await Member.getAllEmail({
        ProjectId: params.ProjectId,
        isDeleted: false,
        isActive: true,
      });
      return memberList;
    } catch (e) {
      console.log(e);
    }
  },
  // Get All Member Lists
  async getAllMemberLists(req) {
    try {
      const {
        search,
        pageSize,
        pageNo,
        sortColumn,
        sortType,
        nameFilter,
        companyFilter,
        roleFilter,
        statusFilter,
      } = req.query;
      let count = 0;
      let offset;
      if (pageNo) {
        offset = (pageNo - 1) * pageSize;
      }
      const commonSearch = {
        [Op.and]: {
          isDeleted: false,
          userType: 'user',
        },
      };
      const totalUsers = await User.findAll({ where: commonSearch });
      const memberLists = await User.getAllMembers(
        pageSize,
        offset,
        search,
        sortColumn,
        sortType,
        nameFilter,
        companyFilter,
        roleFilter,
        statusFilter,
      );
      count = totalUsers.length;
      return { memberLists, count };
    } catch (e) {
      console.log(e);
    }
  },
  // Get Member Detail
  async getMemberDetail(req) {
    try {
      this.getDynamicModel(req);
      const memberDetail = await User.getMemberDetail({ id: req.params.id });
      return memberDetail;
    } catch (e) {
      console.log(e);
    }
  },
  // Get Member associated projects
  async getMemberProjects(req) {
    try {
      const { pageSize, pageNo } = req.query;
      let offset;
      if (pageNo) {
        offset = (pageNo - 1) * pageSize;
      }
      this.getDynamicModel(req);
      const user = {
        UserId: req.params.id,
      };
      const projectLists = await Member.getMembersProject(user, pageSize, offset);
      return projectLists;
    } catch (e) {
      console.log(e);
    }
  },
  // Update Member project status
  async updateMemberProjectStatus(req) {
    try {
      let memberStatusUpdated;
      this.getDynamicModel(req);
      if (req.body && req.body.userStatus && !req.body.action) {
        memberStatusUpdated = await User.update(
          {
            isActive: req.body.memberProjectStatus,
          },
          { where: { id: +req.params.id } },
        );
      } else if (req.body && req.body.userStatus && req.body.action) {
        const MemberDetail = await Member.findOne({
          where: { id: +req.params.id, isDeleted: false },
        });
        // const userDetail = await User.findOne({ where: { id: +MemberDetail.UserId } });
        memberStatusUpdated = await User.update(
          {
            isActive: req.body.memberProjectStatus,
          },
          { where: { id: +MemberDetail.UserId } },
        );
      } else if (req.body && !req.body.userStatus && !req.body.action) {
        memberStatusUpdated = await Member.update(
          {
            memberProjectStatus: req.body.memberProjectStatus,
          },
          { where: { id: +req.params.id } },
        );
      }
      return memberStatusUpdated;
    } catch (e) {
      console.log(e);
    }
  },
  // change Member Password
  async changeMemberPassword(req) {
    try {
      let password = req.body.newPassword;
      const userId = req.params.id;
      await bcryptPassword(password, (encPassword) => {
        password = encPassword;
      });
      const passwordUpdated = await User.update({ password }, { where: { id: +userId } });
      const user = await User.findOne({ where: { id: +userId } });
      const mailPayload = {
        firstName: user.firstName,
        email: user.email,
        password: req.body.newPassword,
      };
      MAILER.sendMail(
        mailPayload,
        'userPasswordChangeBySuperAdmin',
        'Your Password Changed.!',
        'User/s Password Changed by a Super Admin',
        (info, err) => {
          console.log(info, err);
        },
      );
      return passwordUpdated;
    } catch (e) {
      console.log(e);
    }
  },
  // update Member Profile
  async updateMemberProfile(req) {
    try {
      const MemberDetail = req.body;
      const userId = +req.params.id;
      const existDetails = await User.findOne({
        where: [
          Sequelize.and({
            phoneNumber: MemberDetail.phoneNumber,
            phoneCode: MemberDetail.phoneCode,
            id: { [Op.ne]: userId },
          }),
        ],
      });
      if (!existDetails) {
        const existCompanyDetails = await Company.findOne({
          where: Sequelize.and(
            { id: { [Op.ne]: MemberDetail.CompanyId } },
            Sequelize.or({
              companyName: MemberDetail.companyName,
              // website: MemberDetail.website,
            }),
          ),
        });
        if (!existCompanyDetails) {
          await Member.update(
            {
              firstName: MemberDetail.firstName,
              phoneNumber: MemberDetail.phoneNumber,
              phoneCode: MemberDetail.phoneCode,
            },
            {
              where: {
                UserId: userId,
              },
            },
          );
          await User.update(
            {
              firstName: MemberDetail.firstName,
              phoneNumber: MemberDetail.phoneNumber,
              phoneCode: MemberDetail.phoneCode,
              lastName: MemberDetail.lastName,
            },
            {
              where: {
                id: userId,
              },
            },
          );
          const companyParam = {
            address: MemberDetail.address,
            secondAddress: MemberDetail.secondAddress,
            website: MemberDetail.website,
            companyName: MemberDetail.companyName,
            state: MemberDetail.state,
            city: MemberDetail.city,
            country: MemberDetail.country,
            zipCode: MemberDetail.zipCode,
          };
          const companyDetailsUpdated = await Company.update(companyParam, {
            where: { id: MemberDetail.CompanyId },
          });
          return { error: false, message: companyDetailsUpdated };
        }
        return { error: true, message: 'Company Name/Website already exist.' };
      }
      return { error: true, message: 'Mobile Number already exist.' };
    } catch (e) {
      console.log(e);
    }
  },
  // Get All Members
  async getAllMemberListsForAssignProject() {
    try {
      const members = await User.findAll({
        include: [
          {
            where: { isDeleted: false },
            association: 'Members',
            attributes: ['id', 'UserId', 'firstName'],
          },
        ],
        where: { isDeleted: false },
        attributes: ['id', 'firstName', 'lastName', 'email', 'phoneCode', 'phoneNumber'],
      });
      return members;
    } catch (e) {
      console.log(e);
    }
  },
  async activateMember(req, done) {
    try {
      await this.getDynamicModel(req);
      const inputData = req.body;
      const memberDetails = await Member.findOne({
        where: {
          id: inputData.id,
          ProjectId: inputData.ProjectId,
          isDeleted: false,
          isActive: false,
        },
      });
      if (memberDetails) {
        const memberActivated = await Member.update(
          { isActive: true },
          {
            where: {
              id: inputData.id,
              ProjectId: inputData.ProjectId,
              isDeleted: false,
            },
          },
        );
        const getNotificationInAppPreferenceItemsList = await NotificationPreferenceItem.findAll({
          where: { isDeleted: false, inappNotification: true },
        });
        const getNotificationEmailPreferenceItemsList = await NotificationPreferenceItem.findAll({
          where: { isDeleted: false, emailNotification: true },
        });
        getNotificationInAppPreferenceItemsList.forEach(async (element) => {
          if (element.inappNotification) {
            await NotificationPreference.update(
              { instant: true, dailyDigest: false },
              {
                where: {
                  MemberId: +inputData.id,
                  ProjectId: +inputData.ProjectId,
                  ParentCompanyId: +inputData.ParentCompanyId,
                  NotificationPreferenceItemId: element.id,
                },
              },
            );
          }
        });
        getNotificationEmailPreferenceItemsList.forEach(async (element) => {
          if (element.emailNotification) {
            await NotificationPreference.update(
              { instant: false, dailyDigest: true },
              {
                where: {
                  MemberId: +inputData.id,
                  ProjectId: +inputData.ProjectId,
                  ParentCompanyId: +inputData.ParentCompanyId,
                  NotificationPreferenceItemId: element.id,
                },
              },
            );
          }
        });
        return done(memberActivated, false);
      }
      const err = new ApiError('Member not found.', status.BAD_REQUEST);
      return done(null, err);
    } catch (e) {
      return done(null, e);
    }
  },
  async getMappedRequests(req, done) {
    try {
      await this.getDynamicModel(req);
      const inputData = req.body;
      const memberDetails = await Member.findOne({
        where: {
          id: inputData.id,
          ProjectId: inputData.ProjectId,
          isDeleted: false,
          isActive: true,
        },
      });
      if (memberDetails) {
        let deliveryRequestMember = [];
        deliveryRequestMember = await DeliveryRequest.findAll({
          where: {
            ProjectId: inputData.ProjectId,
            isDeleted: false,
            deliveryStart: {
              [Op.gte]: new Date(),
            },
          },
          include: [
            {
              association: 'memberDetails',
              required: true,
              where: { isDeleted: false, isActive: true, MemberId: memberDetails.id },
              attributes: ['id'],
              include: [
                {
                  association: 'Member',
                  required: true,
                  attributes: ['id'],
                  include: [
                    {
                      association: 'User',
                      required: true,
                      attributes: ['email', 'phoneCode', 'phoneNumber', 'firstName', 'lastName'],
                    },
                  ],
                },
              ],
            },
          ],
        });
        const craneRequestMember = await CraneRequest.findAll({
          where: {
            ProjectId: inputData.ProjectId,
            isDeleted: false,
            craneDeliveryStart: {
              [Op.gte]: new Date(),
            },
          },
          include: [
            {
              association: 'memberDetails',
              required: true,
              where: { isDeleted: false, isActive: true, MemberId: memberDetails.id },
              attributes: ['id'],
              include: [
                {
                  association: 'Member',
                  required: true,
                  attributes: ['id'],
                  include: [
                    {
                      association: 'User',
                      required: true,
                      attributes: ['email', 'phoneCode', 'phoneNumber', 'firstName', 'lastName'],
                    },
                  ],
                },
              ],
            },
          ],
        });
        const concreteRequestMember = await ConcreteRequest.findAll({
          where: {
            ProjectId: inputData.ProjectId,
            isDeleted: false,
            concretePlacementStart: {
              [Op.gte]: new Date(),
            },
          },
          include: [
            {
              association: 'memberDetails',
              required: true,
              where: { isDeleted: false, isActive: true, MemberId: memberDetails.id },
              attributes: ['id'],
              include: [
                {
                  association: 'Member',
                  required: true,
                  attributes: ['id'],
                  include: [
                    {
                      association: 'User',
                      required: true,
                      attributes: ['email', 'phoneCode', 'phoneNumber', 'firstName', 'lastName'],
                    },
                  ],
                },
              ],
            },
          ],
        });
        const equipmentsMapped = await Equipments.findAll({
          where: {
            ProjectId: inputData.ProjectId,
            isDeleted: false,
          },
          include: [
            {
              association: 'controllUserDetails',
              required: true,
              where: { isDeleted: false, id: memberDetails.id, isActive: true },
              attributes: { exclude: ['password', 'resetPasswordToken'] },
              include: [
                {
                  association: 'User',
                  required: true,
                  attributes: ['email', 'id', 'firstName', 'lastName'],
                },
              ],
            },
          ],
        });
        if (craneRequestMember) {
          deliveryRequestMember.push(...craneRequestMember);
        }
        if (concreteRequestMember) {
          deliveryRequestMember.push(...concreteRequestMember);
        }
        if (equipmentsMapped) {
          deliveryRequestMember.push(...equipmentsMapped);
        }
        const allMember = await Member.findAll({
          where: Sequelize.and({
            ProjectId: inputData.ProjectId,
            isActive: true,
            isDeleted: false,
          }),
          attribute: ['id', 'firstName', 'UserId'],
          include: [
            {
              association: 'User',
              where: {
                isActive: true,
              },
              attribute: ['id', 'firstName', 'lastName', 'email'],
            },
          ],
        });
        return done({ mappedRequest: deliveryRequestMember, members: allMember }, false);
      }
      const err = new ApiError('Member not found.', status.BAD_REQUEST);
      return done(null, err);
    } catch (e) {
      return done(null, e);
    }
  },
  async deactivateMember(req, done) {
    try {
      await this.getDynamicModel(req);
      const inputData = req.body;
      const loginUser = req.user;
      const { memberSwitchedRequests } = req.body;
      const memberDetails = await Member.findOne({
        where: {
          id: inputData.id,
          ProjectId: inputData.ProjectId,
          isDeleted: false,
          isActive: true,
        },
        include: [
          {
            association: 'User',
            attributes: ['id', 'email'],
            isDeleted: false,
          },
        ],
      });
      if (memberDetails) {
        const deactivatedMemberData = await Member.findOne({
          where: {
            id: inputData.id,
            ProjectId: inputData.ProjectId,
            isDeleted: false,
          },
          include: [
            {
              association: 'User',
              attributes: ['id', 'email', 'firstName', 'lastName'],
              isDeleted: false,
            },
          ],
        });

        let deliveryRequestMember = [];
        deliveryRequestMember = await DeliveryRequest.findAll({
          where: {
            ProjectId: inputData.ProjectId,
            isDeleted: false,
            deliveryStart: {
              [Op.gte]: new Date(),
            },
          },
          include: [
            {
              association: 'memberDetails',
              required: true,
              where: { isDeleted: false, isActive: true, MemberId: memberDetails.id },
              attributes: ['id'],
              include: [
                {
                  association: 'Member',
                  required: true,
                  attributes: ['id'],
                  include: [
                    {
                      association: 'User',
                      required: true,
                      attributes: ['email', 'phoneCode', 'phoneNumber', 'firstName', 'lastName'],
                    },
                  ],
                },
              ],
            },
          ],
        });
        if (deliveryRequestMember && deliveryRequestMember.length > 0) {
          deliveryRequestMember.map(async (object) => {
            const history = {
              DeliveryRequestId: object.id,
              MemberId: memberDetails.id,
              type: 'edit',
              ProjectId: inputData.ProjectId,
              description: `${loginUser.firstName} ${loginUser.lastName} deactivated the Member ${deactivatedMemberData.User.firstName}  ${deactivatedMemberData.User.lastName}`,
            };
            await DeliverHistory.createInstance(history);

            await DeliveryPerson.update(
              { isActive: false },
              {
                where: {
                  MemberId: memberDetails.id,
                  ProjectId: inputData.ProjectId,
                  isDeleted: false,
                  isActive: true,
                  DeliveryId: object.id,
                },
              },
            );
          });
        }
        const craneRequestMember = await CraneRequest.findAll({
          where: {
            ProjectId: inputData.ProjectId,
            isDeleted: false,
            craneDeliveryStart: {
              [Op.gte]: new Date(),
            },
          },
          include: [
            {
              association: 'memberDetails',
              required: true,
              where: { isDeleted: false, isActive: true, MemberId: memberDetails.id },
              attributes: ['id'],
              include: [
                {
                  association: 'Member',
                  required: true,
                  attributes: ['id'],
                  include: [
                    {
                      association: 'User',
                      required: true,
                      attributes: ['email', 'phoneCode', 'phoneNumber', 'firstName', 'lastName'],
                    },
                  ],
                },
              ],
            },
          ],
        });
        if (craneRequestMember && craneRequestMember.length > 0) {
          craneRequestMember.map(async (object) => {
            const history = {
              CraneRequestId: object.id,
              MemberId: memberDetails.id,
              type: 'edit',
              ProjectId: inputData.ProjectId,
              description: `${loginUser.firstName} ${loginUser.lastName} deactivated the Member ${deactivatedMemberData.User.firstName}  ${deactivatedMemberData.User.lastName}`,
            };
            await CraneRequestHistory.createInstance(history);

            await CraneRequestResponsiblePerson.update(
              { isActive: false },
              {
                where: {
                  MemberId: memberDetails.id,
                  ProjectId: inputData.ProjectId,
                  isDeleted: false,
                  isActive: true,
                  CraneRequestId: object.id,
                },
              },
            );
          });
        }
        const concreteRequestMember = await ConcreteRequest.findAll({
          where: {
            ProjectId: inputData.ProjectId,
            isDeleted: false,
            concretePlacementStart: {
              [Op.gte]: new Date(),
            },
          },
          include: [
            {
              association: 'memberDetails',
              required: true,
              where: { isDeleted: false, isActive: true, MemberId: memberDetails.id },
              attributes: ['id'],
              include: [
                {
                  association: 'Member',
                  required: true,
                  attributes: ['id'],
                  include: [
                    {
                      association: 'User',
                      required: true,
                      attributes: ['email', 'phoneCode', 'phoneNumber', 'firstName', 'lastName'],
                    },
                  ],
                },
              ],
            },
          ],
        });
        if (concreteRequestMember && concreteRequestMember.length > 0) {
          concreteRequestMember.map(async (object) => {
            const history = {
              ConcreteRequestId: object.id,
              MemberId: memberDetails.id,
              type: 'edit',
              ProjectId: inputData.ProjectId,
              description: `${loginUser.firstName} ${loginUser.lastName} deactivated the Member, ${deactivatedMemberData.User.firstName} ${deactivatedMemberData.User.lastName}`,
            };
            await ConcreteRequestHistory.createInstance(history);

            await ConcreteRequestResponsiblePerson.update(
              { isActive: false },
              {
                where: {
                  MemberId: memberDetails.id,
                  ProjectId: inputData.ProjectId,
                  isDeleted: false,
                  isActive: true,
                  ConcreteRequestId: object.id,
                },
              },
            );
          });
        }
        if (memberSwitchedRequests && memberSwitchedRequests.length > 0) {
          memberSwitchedRequests.map(async (object) => {
            if (
              object.requestType === 'deliveryRequest' ||
              object.requestType === 'deliveryRequestWithCrane'
            ) {
              const updateDeliveryParam = {
                DeliveryId: object.id,
                DeliveryCode: object.DeliveryId,
                ProjectId: inputData.ProjectId,
                isDeleted: false,
                MemberId: +object.changedMemberId,
                isActive: true,
              };
              const isMemberExists = await DeliveryPerson.findOne({
                where: updateDeliveryParam,
              });
              if (!isMemberExists) {
                const getMemberObject = await Member.findOne({
                  where: {
                    id: +object.changedMemberId,
                  },
                  include: [
                    {
                      association: 'User',
                      attributes: ['id', 'email', 'firstName', 'lastName'],
                      isDeleted: false,
                    },
                  ],
                });
                if (deliveryRequestMember && deliveryRequestMember.length > 0) {
                  const history = {
                    DeliveryRequestId: object.id,
                    MemberId: memberDetails.id,
                    type: 'edit',
                    ProjectId: inputData.ProjectId,
                    description: `${loginUser.firstName} ${loginUser.lastName} added the member  ${getMemberObject.User.firstName} ${getMemberObject.User.lastName}`,
                  };
                  await DeliverHistory.createInstance(history);
                }
                const existMember = await DeliveryPerson.findAll({
                  where: {
                    ProjectId: inputData.ProjectId,
                    DeliveryId: object.id,
                  },
                });
                const index = existMember.findIndex(
                  (item) => item.MemberId === +object.changedMemberId,
                );
                if (index !== -1) {
                  await DeliveryPerson.update(updateDeliveryParam, {
                    where: {
                      id: existMember[index].id,
                      DeliveryId: object.id,
                      ProjectId: inputData.ProjectId,
                    },
                  });
                } else {
                  await DeliveryPerson.createInstance(updateDeliveryParam);
                }
              }
            }
            if (object.requestType === 'craneRequest') {
              const updateCraneParam = {
                CraneRequestId: object.id,
                CraneRequestCode: object.CraneRequestId,
                ProjectId: inputData.ProjectId,
                isDeleted: false,
                MemberId: +object.changedMemberId,
                isActive: true,
              };
              const isMemberExists = await CraneRequestResponsiblePerson.findOne({
                where: updateCraneParam,
              });
              if (!isMemberExists) {
                const getMemberObject = await Member.findOne({
                  where: {
                    id: +object.changedMemberId,
                  },
                  include: [
                    {
                      association: 'User',
                      attributes: ['id', 'email', 'firstName', 'lastName'],
                      isDeleted: false,
                    },
                  ],
                });
                if (craneRequestMember && craneRequestMember.length > 0) {
                  const history = {
                    CraneRequestId: object.id,
                    MemberId: memberDetails.id,
                    type: 'edit',
                    ProjectId: inputData.ProjectId,
                    description: `${loginUser.firstName} ${loginUser.lastName} added the member ${getMemberObject.User.firstName} ${getMemberObject.User.lastName}`,
                  };
                  await CraneRequestHistory.createInstance(history);
                }
                const existMember = await CraneRequestResponsiblePerson.findAll({
                  where: {
                    ProjectId: inputData.ProjectId,
                    CraneRequestId: object.id,
                  },
                });
                const index = existMember.findIndex(
                  (item) => item.MemberId === +object.changedMemberId,
                );
                if (index !== -1) {
                  await CraneRequestResponsiblePerson.update(updateCraneParam, {
                    where: {
                      id: existMember[index].id,
                      CraneRequestId: object.id,
                      ProjectId: inputData.ProjectId,
                    },
                  });
                } else {
                  await CraneRequestResponsiblePerson.createInstance(updateCraneParam);
                }
              }
            }
            if (object.requestType === 'concreteRequest') {
              const updateConcreteParam = {
                ConcreteRequestId: object.id,
                ConcreteRequestCode: object.ConcreteRequestId,
                ProjectId: inputData.ProjectId,
                isDeleted: false,
                MemberId: +object.changedMemberId,
                isActive: true,
              };
              const isMemberExists = await ConcreteRequestResponsiblePerson.findOne({
                where: updateConcreteParam,
              });
              if (!isMemberExists) {
                const getMemberObject = await Member.findOne({
                  where: {
                    id: +object.changedMemberId,
                  },
                  include: [
                    {
                      association: 'User',
                      attributes: ['id', 'email', 'firstName', 'lastName'],
                      isDeleted: false,
                    },
                  ],
                });
                if (concreteRequestMember && concreteRequestMember.length > 0) {
                  const history = {
                    ConcreteRequestId: object.id,
                    MemberId: memberDetails.id,
                    type: 'edit',
                    ProjectId: inputData.ProjectId,
                    description: `${loginUser.firstName} ${loginUser.lastName} added the member ${getMemberObject.User.firstName} ${getMemberObject.User.lastName}`,
                  };
                  await ConcreteRequestHistory.createInstance(history);
                }
                const existMember = await ConcreteRequestResponsiblePerson.findAll({
                  where: {
                    ProjectId: inputData.ProjectId,
                    ConcreteRequestId: object.id,
                  },
                });
                const index = existMember.findIndex(
                  (item) => item.MemberId === +object.changedMemberId,
                );
                if (index !== -1) {
                  await ConcreteRequestResponsiblePerson.update(updateConcreteParam, {
                    where: {
                      id: existMember[index].id,
                      ProjectId: inputData.ProjectId,
                      ConcreteRequestId: object.id,
                    },
                  });
                } else {
                  await ConcreteRequestResponsiblePerson.createInstance(updateConcreteParam);
                }
              }
            }
            if (!object.requestType) {
              await Equipments.update(
                { controlledBy: object.changedMemberId },
                {
                  where: {
                    controlledBy: memberDetails.id,
                    ProjectId: inputData.ProjectId,
                    isDeleted: false,
                    isActive: true,
                  },
                },
              );
            }
          });
        }
        // return false;
        await Equipments.update(
          {
            controlledBy: null,
          },
          {
            where: {
              controlledBy: memberDetails.id,
              ProjectId: inputData.ProjectId,
              isDeleted: false,
              isActive: true,
            },
          },
        );
        const memberDeactivated = await Member.update(
          { isActive: false },
          {
            where: {
              id: memberDetails.id,
              ProjectId: inputData.ProjectId,
              isDeleted: false,
              isActive: true,
            },
          },
        );
        await NotificationPreference.update(
          { instant: false, dailyDigest: false },
          {
            where: {
              MemberId: +inputData.id,
              ProjectId: +inputData.ProjectId,
              ParentCompanyId: +inputData.ParentCompanyId,
            },
          },
        );
        global.io.emit('deactivatedMember', {
          email: memberDetails.User.email,
          status: 'memberDeactivated',
        });
        return done(memberDeactivated, false);
      }
      const err = new ApiError('Member not found.', status.BAD_REQUEST);
      return done(null, err);
    } catch (e) {
      return done(null, e);
    }
  },
  async getOnboardingInviteLink(req, done) {
    try {
      await this.getDynamicModel(req);
      const memberData = req.body;
      const memberDetails = await Member.findOne({
        where: {
          id: memberData.id,
          ProjectId: memberData.ProjectId,
          isDeleted: false,
          isActive: true,
        },
      });
      if (memberDetails) {
        let user = memberData;
        user.link = process.env.BASE_URL;
        user = await deepLinkService.getInviteMemberDeepLink(user);
        return done(user, false);
      }
      const err = new ApiError('Member not found.', status.BAD_REQUEST);
      return done(null, err);
    } catch (e) {
      return done(null, e);
    }
  },
  async getMemberData(req, done) {
    try {
      await this.getDynamicModel(req);
      const memberData = req.body;
      const getUser = await User.findOne({
        where: {
          id: req.user.id,
        },
      });
      const memberDetails = await Member.findOne({
        where: {
          UserId: getUser.id,
          ProjectId: +memberData.ProjectId,
          isDeleted: false,
          isActive: true,
        },
        include: [
          {
            association: 'Project',
            attributes: ['id', 'projectName'],
          },
          {
            association: 'Company',
            attributes: ['id', 'companyName'],
          },
          {
            association: 'User',
            attributes: ['id', 'firstName', 'lastName', 'email'],
          },
          {
            association: 'Role',
            attributes: ['roleName'],
          },
        ],
      });
      return done(memberDetails, false);
    } catch (e) {
      return done(null, e);
    }
  },
  
  async listRetoolMembers(inputData) {
    try {
      const req = inputData;
      const membersList = await Member.findAll({
        where: { ProjectId: req.body.ProjectId, isDeleted: false,isGuestUser:false },
        include: [{
          association: 'User',
          attributes: [
            'id',
            'firstName',
            'lastName',
            'email',
            'phoneNumber',
            'phoneCode',
            'profilePic',
            'isActive',
          ],
        },],
      });
      return membersList;
    } catch (e) {
      throw new Error(e);
    }
  },

  async listRegisteredMembers(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { params } = inputData;
      const memberList = await Member.getAllEmail({
        ProjectId: params.ProjectId,
        isDeleted: false,
        isActive: true,
        isGuestUser: false,
      });
      done(memberList, false);
    } catch (e) {
      done(null, e);
    }
  },
  async listGuestMembers(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { params } = inputData;
      const incomeData = inputData.body;
      const pageNumber = +params.pageNo;
      const pageSize = +params.pageSize;
      const { sort } = inputData.body;
      const { sortByField } = inputData.body;
      const offset = (pageNumber - 1) * pageSize;
      const condition = {
        ProjectId: params.ProjectId,
        isDeleted: false,
        RoleId: { [Op.ne]: 1 },
        isGuestUser: true,
      };
      const memberList = await Member.getAll(
        condition,
        incomeData.nameFilter,
        incomeData.companyFilter,
        incomeData.roleFilter,
        incomeData.statusFilter,
        incomeData.search,
        +params.pageSize,
        offset,
        sort,
        sortByField,
      );
      done(memberList, false);
    } catch (e) {
      done(null, e);
    }
  },

  async addGuestAsMember(req) {
  try{
    const ProjectId = req.body.ProjectId;
    const ParentCompanyId = req.body.ParentCompanyId;
    const guestId= req.body.guestUserId;
    const guestMemberId = req.body.guestId
    let generatePasswordvalue = generatePassword();
    let originalPawword = generatePasswordvalue;
    const creatorId = req.body.memberId;
    await bcryptPassword(generatePasswordvalue, (encPassword) => {
      generatePasswordvalue = encPassword;
    });
    const userUpdateData ={
      password:generatePasswordvalue,
    }
    await User.update(userUpdateData, { where: { id: +guestId, email:req.body.guestEmail, } });
    const memberUpdateData ={
      isGuestUser: false, 
      createdBy:+creatorId,
      RoleId: +req.body.selectedRoleId,
      status: 'completed',
      password:generatePasswordvalue,
    }
      const memberDetail = await Member.update(memberUpdateData,
        {
          where: {
            isDeleted: false,
            isActive:true,
            UserId:guestId,
            ProjectId,
          },
        },
      );
     const data = memberDetail;
     const notificationDetail = {
      ProjectId : +ProjectId,
      id: +guestMemberId,
      ParentCompanyId: +ParentCompanyId,
      RoleId: +req.body.selectedRoleId,
     }
    await this.createGuestMemberNotificationPreference(notificationDetail);
     await this.setLocationNotificationPreferenceForAMember(notificationDetail);
     const getProject = await Project.findOne({
      where: {
        isDeleted: false,
        id: +ProjectId,
      },
    });
     //send intimate Email to guest user
     const mailData = {
      email:req.body.guestEmail,
      approverFirstName : req.body.memberFirstName,
      approverLastName : req.body.memberLastName,
      password:originalPawword,
      guestFirstName: req.body.guestFirstName,
      guestLastName: req.body.guestLastName,
      projectName:getProject.projectName,
      link: process.env.BASE_URL,
     }
      await MAILER.sendMail(mailData, 'guestApproved', 'Request Accepted', 'Request Approved', (info, err) => {
        if (err) {
          throw new Error(err);
        } else {
          return {data}
        }
      });

       return  {data} ;

    } catch(error){
      console.log(error);
      throw new Error(error);
    }
    
  },

  async createGuestMemberNotificationPreference(req) {
    await this.returnProjectModel();
    let memberDetail;
      memberDetail = await Member.findOne({
        where: { id: +req.id, isDeleted: false },
      });
    const getNotificationPreferenceItemsList = await NotificationPreferenceItem.findAll({
      where: { isDeleted: false },
    });
    const getProject = await Project.findOne({
      where: {
        isDeleted: false,
        id: +memberDetail.ProjectId,
      },
      include: [
        {
          where: { isDeleted: false },
          association: 'TimeZone',
          required: false,
          attributes: ['id', 'location', 'timeZoneOffsetInMinutes'],
        },
      ],
    });
    const attr = {
      time: '05:00',
      timeFormat: 'AM',
    };
    let projectObject;
    if (getProject) {
      projectObject = getProject.toJSON();
    }
    if (projectObject && projectObject.TimeZone) {
      attr.TimeZoneId = projectObject.TimeZone.id;
    } else {
      attr.TimeZoneId = 3;
    }
    await Member.update(attr, { where: { id: +req.id } });

    for (let index2 = 0; index2 < getNotificationPreferenceItemsList.length; index2 += 1) {
      const item = getNotificationPreferenceItemsList[index2];
      if (
        (item.id === 7 &&
          item.description === 'When a comment is added to a delivery/crane/concrete request' &&
          item.itemId === 4 &&
          item.emailNotification === true &&
          item.inappNotification === false &&
          item.isDeleted === false) ||
        item.inappNotification === true
      ) {
        const object = {
          MemberId: +req.id,
          ProjectId: memberDetail.ProjectId,
          ParentCompanyId: memberDetail.ParentCompanyId,
          NotificationPreferenceItemId: +item.id,
          instant: true,
          dailyDigest: false,
          isDeleted: false,
        };
        await NotificationPreference.createInstance(object);
      } else {
        const object = {
          MemberId: +req.id,
          ProjectId: memberDetail.ProjectId,
          ParentCompanyId: memberDetail.ParentCompanyId,
          NotificationPreferenceItemId: item.id,
          instant: false,
          dailyDigest: true,
          isDeleted: false,
        };
        await NotificationPreference.createInstance(object);
      }
    }
  },

  async rejectGuestRequest(req) {
    try{
      const ProjectId = +req.body.ProjectId;
      const ParentCompanyId = +req.body.ParentCompanyId;
      const guestId= +req.body.guestUserId;
      const guestMemberId = req.body.guestId;
      const creatorFirstName = req.body.memberFirstName;
      const creatorLastName = req.body.memberLastName;
        const memberDetail = await Member.update(
          { status: 'declined' },
          {
            where: {
              isDeleted: false,
              isActive:true,
              UserId:guestId,
              ProjectId:ProjectId,
              ParentCompanyId:ParentCompanyId,
            },
          },
        );  
        const getProject = await Project.findOne({
          where: {
            isDeleted: false,
            id: +ProjectId,
          },
        });
  
        // send email to guest member that PA rejects your request to be a member
        const mailData = {
          email:req.body.guestEmail,
          rejectorFirstName : req.body.memberFirstName,
          rejectorLastName : req.body.memberLastName,
          guestFirstName: req.body.guestFirstName,
          guestLastName: req.body.guestLastName,
          projectName:getProject.projectName,
        }
          await MAILER.sendMail(mailData, 'guestRejected', 'Request Rejected', 'Request Rejected', (info, err) => {
            if (err) {
              throw new Error(err);
            } else {
              return {mailData}
            }
          });          
          return {mailData}
    } catch(error){
      throw new Error(error);
    }
  },

};

module.exports = memberService;
