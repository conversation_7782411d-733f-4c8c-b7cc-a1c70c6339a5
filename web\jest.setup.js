// Mock environment variables
process.env.MAILGUN_API_KEY = 'test-mailgun-key';
process.env.NODE_ENV = 'test';
process.env.DEVICE_SERVER_KEY = 'test-fcm-server-key';
process.env.FIRE_BASE_JSON_PATH = './api/v21/config/db/follo-296102-firebase-adminsdk-g028d-a496313451.json';
process.env.BASE_URL = 'http://localhost:3000';

// Mock Firebase Admin
jest.mock('firebase-admin', () => ({
    initializeApp: jest.fn(),
    credential: {
        cert: jest.fn()
    },
    messaging: jest.fn(() => ({
        send: jest.fn().mockResolvedValue('message-id')
    }))
}));

// Mock FCM Push
jest.mock('fcm-push', () => {
    return jest.fn().mockImplementation(() => ({
        send: jest.fn().mockResolvedValue('fcm-response')
    }));
});

// Mock moment with timezone support
jest.mock('moment', () => {
    const moment = jest.requireActual('moment');
    require('moment-timezone');
    return moment;
});

// Mock database connection
jest.mock('sequelize', () => {
    const actualSequelize = jest.requireActual('sequelize');
    return {
        ...actualSequelize,
        Sequelize: jest.fn(() => ({
            authenticate: jest.fn().mockResolvedValue(undefined),
            define: jest.fn(),
            transaction: jest.fn(() => ({
                commit: jest.fn(),
                rollback: jest.fn()
            }))
        }))
    };
});

// Global test timeout
jest.setTimeout(10000); 