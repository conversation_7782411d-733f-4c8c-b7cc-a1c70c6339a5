/* eslint-disable no-await-in-loop */
const path = require('path');
const status = require('http-status');
const stripe = require('stripe')(process.env.STRIPE_API_KEY, {
  maxNetworkRetries: 3,
});
const { Worker } = require('worker_threads');
const moment = require('moment');
const creditCardType = require('credit-card-type');
// const mixpanelService = require('./mixpanelService');
const Cryptr = require('cryptr');
const fs = require('fs');
const { PDFDocument } = require('pdf-lib');
const axios = require('axios');
const {
  Sequelize,
  StripePlan,
  RestrictEmail,
  Enterprise,
  ProjectBillingHistories,
  NotificationPreference,
  NotificationPreferenceItem,
  TimeZone,
  ProjectSettings,
  Locations,
  LocationNotificationPreferences,
} = require('../models');
const helper = require('../helpers/domainHelper');
const notificationHelper = require('../helpers/notificationHelper');
let {
  Project,
  Member,
  User,
  Role,
  Company,
  ParentCompany,
  Notification,
  DeliveryPersonNotification,
} = require('../models');
const { StripeSubscription } = require('../models');

const stripeService = require('./stripeService');
const MAILER = require('../mailer');
const ApiError = require('../helpers/apiError');
const deepLinkService = require('./deepLinkService');

const { Op } = Sequelize;

let publicUser;
let publicMember;
// let publicCompany;
let publicProject;
let domainNewName;

const { generatePassword } = require('../helpers/generatePassword');
const awsConfig = require('../middlewares/awsConfig');

const imageConversionProcess = path.join(__dirname, './imageConversionProcess.js');

const cryptr = new Cryptr('a0b1c2d3e4f5g6h7i8j9k10');

const projectService = {
  async existProject(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const loginUser = inputData.user;
      const parentId = await Member.findOne({ where: { UserId: loginUser.id, isDeleted: false } });
      const existProject = await Project.findOne({
        where: Sequelize.and({
          ParentCompanyId: parentId.ParentCompanyId,
          projectName: inputData.body.projectName,
        }),
      });
      if (existProject) {
        done(null, { message: 'Project Name Already exist.' });
      } else {
        done({ message: 'success' }, false);
      }
    } catch (e) {
      done(null, e);
    }
  },
  async getDynamicModel(inputData) {
    await this.returnProjectModel();
    domainNewName = inputData.user.domainName;
    const incomeData = inputData;
    const ParentCompanyId = inputData.body.ParentCompanyId
      ? inputData.body.ParentCompanyId
      : inputData.params.ParentCompanyId;
    let enterpriseValue;
    let domainEnterpriseValue;
    if (domainNewName) {
      domainEnterpriseValue = await Enterprise.findOne({
        where: { name: domainNewName.toLowerCase() },
      });
      if (!domainEnterpriseValue) {
        domainNewName = '';
      }
    }
    if (!domainNewName && ParentCompanyId !== undefined && ParentCompanyId !== 'undefined') {
      const { email } = inputData.user;
      let userData;
      if (email) {
        userData = await publicUser.findOne({ where: { email, isDeleted: false } });
      }
      if (userData) {
        const memberData = await publicMember.findOne({
          where: { UserId: userData.id, RoleId: { [Op.ne]: 4 }, isDeleted: false },
        });
        if (memberData) {
          if (memberData.isAccount) {
            enterpriseValue = await Enterprise.findOne({
              where: { id: memberData.EnterpriseId, status: 'completed' },
            });
            if (enterpriseValue) {
              domainNewName = enterpriseValue.name.toLowerCase();
            }
          } else {
            enterpriseValue = await Enterprise.findOne({
              where: { ParentCompanyId, status: 'completed' },
            });
            if (enterpriseValue) {
              domainNewName = enterpriseValue.name.toLowerCase();
            }
          }
        } else {
          enterpriseValue = await Enterprise.findOne({
            where: { ParentCompanyId, status: 'completed' },
          });
          if (enterpriseValue) {
            domainNewName = enterpriseValue.name.toLowerCase();
          }
        }
      }
    }
    const modelObj = await helper.getDynamicModel(domainNewName);
    User = modelObj.User;
    Member = modelObj.Member;
    Role = modelObj.Role;
    Company = modelObj.Company;
    Project = modelObj.Project;
    ParentCompany = modelObj.ParentCompany;
    DeliveryPersonNotification = modelObj.DeliveryPersonNotification;
    Notification = modelObj.Notification;
    if (enterpriseValue) {
      const newUser = await User.findOne({
        where: { email: inputData.user.email, isDeleted: false },
      });
      incomeData.user = newUser;
    }
  },
  async returnProjectModel() {
    const modelData = await helper.returnProjectModel();
    publicMember = modelData.Member;
    publicUser = modelData.User;
    publicProject = modelData.Project;
    // publicCompany = modelData.Company;
  },
  async initialSetupCreateProject(inputData, done) {
    await this.getDynamicModel(inputData);
    const projectData = inputData.body;
    const loginUser = inputData.user;
    const parentId = await Member.getBy({ UserId: loginUser.id });
    const emailValidationData = await ParentCompany.getBy({ id: parentId.ParentCompanyId });
    const passData = inputData;
    passData.emailValidationData = emailValidationData;
    if (projectData.email !== loginUser.email) {
      const firstSplit = projectData.email.split('@')[1];
      const secondSplit = firstSplit.split('.');
      let emailDomainName;
      if (secondSplit.length === 2) {
        emailDomainName = firstSplit;
      } else if (secondSplit.length > 2) {
        const str = firstSplit.substring(firstSplit.indexOf('.') + 1);
        emailDomainName = str;
      }
      const restrict = await RestrictEmail.getBy({ domainName: emailDomainName, isActive: 1 });
      if (restrict) {
        done(null, { message: 'This email is not allowed' });
      } else if (emailValidationData.emailDomainName === emailDomainName) {
        done(passData, false);
      } else {
        done(null, { message: 'This user is not our organization.' });
      }
    } else {
      done(passData, false);
    }
  },
  async createProject(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      this.initialSetupCreateProject(inputData, (passData, errorValue) => {
        if (!errorValue) {
          this.findProjectPlan(passData, (response, error) => {
            if (!error) {
              done(response, false);
            } else {
              done(null, error);
            }
          });
        } else {
          done(null, errorValue);
        }
      });
    } catch (e) {
      done(null, e);
    }
  },
  async createAccountProject(inputData, done) {
    try {
      const startDate = new Date(inputData.body.startDate).getTime();
      const endDate = new Date(inputData.body.endDate).getTime();
      if (startDate < endDate) {
        await this.getDynamicModel(inputData);
        if (domainNewName) {
          const enterPriseValue = await Enterprise.findOne({
            where: Sequelize.where(
              Sequelize.fn('lower', Sequelize.col('name')),
              Sequelize.fn('lower', domainNewName),
            ),
          });
          const projectCount = await Member.findAll({ where: { UserId: inputData.user.id } });
          if (enterPriseValue) {
            if (enterPriseValue.projectCount < projectCount.length + 1) {
              done(null, { message: 'Your Project limit reached.!' });
            } else {
              const incomeData = inputData;
              incomeData.body.email = incomeData.user.email;
              this.initialSetupCreateProject(incomeData, async (passData, errorValue) => {
                if (!errorValue) {
                  await this.returnProjectModel();
                  const tempInput = incomeData;
                  tempInput.domainBo = true;
                  tempInput.emailValidationData = passData.emailValidationData;
                  this.createNewProject(tempInput, (response, error) => {
                    if (!error) {
                      done(response, false);
                    } else {
                      done(null, error);
                    }
                  });
                } else {
                  done(null, errorValue);
                }
              });
            }
          } else {
            done(null, { message: 'Something went wrong' });
          }
        } else {
          done(null, { message: 'Something went wrong' });
        }
      } else {
        done(null, { message: 'End Date must be higher than Start Date' });
      }
    } catch (e) {
      done(null, e);
    }
  },
  async findProjectPlan(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const projectData = inputData.body;
      const planDetails = await StripePlan.getBy({ id: projectData.PlanId });
      const loginUser = inputData.user;
      const CompanyDet = await Member.findOne({
        where: {
          UserId: loginUser.id,
          isDeleted: false,
        },
        include: [
          {
            association: 'Company',
            required: true,
            attributes: ['id', 'city', 'country', 'address', 'zipCode', 'state'],
          },
        ],
        attributes: ['id'],
      });
      if (planDetails) {
        if (planDetails.Plan.planType.toLowerCase() === 'trial plan') {
          const existProject = await Project.count({
            where: Sequelize.and({
              createdBy: loginUser.id,
              PlanId: projectData.PlanId,
              isDeleted: false,
            }),
          });
          let projectCount = 1;
          // <EMAIL>(loginUser.id == 5) - prod env
          if (process.env.NODE_ENV === 'prod' && +loginUser.id === 5) {
            projectCount = 20;
          }
          // <EMAIL>(loginUser.id == 227) - staging env
          if (process.env.NODE_ENV === 'staging' && +loginUser.id === 227) {
            projectCount = 10;
          }
          if (existProject <= +projectCount) {
            const { id } = inputData.user;
            const user = await User.getBy({ id });
            const customerId = user.stripeCustomerId;
            let customer;
            if (customerId) {
              customer = await stripe.customers.retrieve(customerId);
            } else {
              customer = await stripe.customers.create({
                email: user.email,
                name: `${user.firstName} ${user.lastName}`,
                phone: `${user.phoneCode} ${user.phoneNumber}`,
                address: {
                  city: CompanyDet.city,
                  country: CompanyDet.country,
                  line1: CompanyDet.address,
                  postal_code: CompanyDet.zipCode,
                  state: CompanyDet.state,
                },
              });
              await user.updateInstance(user.id, {
                stripeCustomerId: customer.id,
              });
            }
            const plan = await StripePlan.getBy({ id: projectData.PlanId });
            // You need to make sure that you always pass trial_end or trial_period_days
            // when you create the subscription instead.
            const subscribedToTrialPlan = await stripe.subscriptions.create({
              customer: customer.id,
              // Either charge_automatically, or send_invoice.When charging automatically, Stripe will attempt to pay this subscription at the end of the cycle using the default source attached to the customer.When sending an invoice, Stripe will email your customer an invoice with payment instructions.Defaults to charge_automatically.
              collection_method: 'send_invoice',
              trial_period_days: 14,
              days_until_due: 0,
              items: [{ plan: plan.stripePlanId }],
            });
            delete subscribedToTrialPlan.status;
            subscribedToTrialPlan.status = 'active';
            const newStripeSubscription = await StripeSubscription.createInstance(
              user,
              subscribedToTrialPlan,
            );
            const newInputData1 = inputData;
            newInputData1.stripeDetails = newStripeSubscription;
            this.createNewProject(newInputData1, (response, error) => {
              if (error) {
                done(null, error);
              } else {
                done(response, false);
              }
            });
          } else {
            done(null, { message: 'Trial Plan Limit Reached.' });
          }
        } else if (planDetails.Plan.planType.toLowerCase() === 'project plan') {
          const planData = await StripePlan.getBy({ id: projectData.PlanId });
          const newInputData = inputData;
          newInputData.planData = planData;
          newInputData.CompanyDet = CompanyDet.Company;
          // stripeService.addCard(newInputData, async (result, err) => {
          // if (err) {
          // done(null, err);
          // }
          //  else
          // if (result !== null && result !== undefined) {
          // await User.update(
          //   {
          //     stripeCustomerId: result.stripeCustomerId,
          //   },
          //   { where: { id: loginUser.id } },
          // );
          if (projectData && projectData.stripeCustomerId) {
            const user = await User.findOne({
              where: {
                stripeCustomerId: projectData.stripeCustomerId,
              },
            });
            const subscription = await stripe.subscriptions.list({
              customer: projectData.stripeCustomerId,
              limit: 1,
            });
            const latestSubscriptionObject = subscription.data[0];
            const newSubscriptionCreated = await StripeSubscription.create({
              UserId: user.id,
              status: 'active',
              subscriptionId: latestSubscriptionObject.id,
            });
            newInputData.stripeDetails = newSubscriptionCreated;

            this.createNewProject(newInputData, async (response, error) => {
              if (error) {
                done(null, error);
              } else {
                // const getProjectDetail = await Member.findOne({
                //   include: [
                //     {
                //       association: 'User',
                //       attributes: ['email', 'firstName', 'lastName'],
                //     },
                //     {
                //       association: 'Company',
                //       attributes: [
                //         'companyName',
                //         'address',
                //         'secondAddress',
                //         'country',
                //         'city',
                //         'state',
                //         'zipCode',
                //       ],
                //     },
                //   ],
                //   where: {
                //     id: response.id,
                //   },
                // });
                // const projectDetails = await Project.findOne({
                //   where: { id: response.ProjectId },
                // });
                // const getCardInfo = creditCardType(projectData.cardDetails.number);
                // const cardType = getCardInfo[0].niceType.toLowerCase();
                // const invoiceNo = Math.floor(Math.random() * 324324 + 1);
                // const mailData = {
                //   email: getProjectDetail.User.email,
                //   firstName: getProjectDetail.User.firstName,
                //   invoiceNo,
                //   receiptNo: Math.floor(Math.random() * 899776 + 1),
                //   footerInvoiceNo: invoiceNo,
                //   invoiceDate: moment(getProjectDetail.endDate).format('DD-MM-YYYY'),
                //   paymentDate: moment(getProjectDetail.endDate).format('DD-MM-YYYY'),
                //   address: getProjectDetail.Company.address,
                //   city: getProjectDetail.Company.city,
                //   state: getProjectDetail.Company.state,
                //   zipcode: getProjectDetail.Company.zipCode,
                //   country: getProjectDetail.Company.country,
                //   projectName: getProjectDetail.projectName,
                //   projectStartDate: moment(getProjectDetail.startDate).format('DD-MM-YYYY'),
                //   projectEndDate: moment(getProjectDetail.endDate).format('DD-MM-YYYY'),
                //   datePaid: moment().format('DD-MM-YYYY'),
                //   cardNo: projectData.cardDetails.number.substring(
                //     projectData.cardDetails.number.length - 4,
                //   ),
                //   amount: (planDetails.stripeAmount / 100).toFixed(2),
                //   quantity: 1,
                //   interval: planDetails.stripePlanName,
                //   subTotal: (planDetails.stripeAmount / 100).toFixed(2),
                //   total: (planDetails.stripeAmount / 100).toFixed(2),
                //   invoiceSubTotal: (planDetails.stripeAmount / 100).toFixed(2),
                //   planName: 'Project Plan',
                //   cardType,
                // };
                // mailData.projectName = projectDetails.projectName;
                // mailData.startDate = projectDetails.startDate;
                // mailData.endDate = projectDetails.endDate;
                // this.sendMail(mailData, 'upgradeplan','Upgrade Plan', async (info, mailErr) => {
                //   console.log(info, mailErr);
                // });
                done(response, false);
              }
            });
          }

          // }
          // });
        } else {
          done({ message: 'Please Contact Sales team.' }, false);
        }
      } else {
        done(null, { message: 'Plan Does not exist.' });
      }
    } catch (e) {
      done(null, e);
    }
  },
  async getPlansAndProjects(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const condition = {};
      const { params } = inputData;
      const pageNumber = +params.pageNo;
      const pageSize = +params.pageSize;
      const { sort } = params;
      const { sortByField } = params;
      const { search } = inputData.body;
      const offset = (pageNumber - 1) * pageSize;
      const projectData = await Project.getPlansAndProjects(
        inputData.user,
        condition,
        pageSize,
        offset,
        sort,
        sortByField,
        search,
      );
      this.getStripeDate(inputData.user, projectData.rows, 0, [], (response, err) => {
        if (!err) {
          const responseData = {
            rows: response,
            count: projectData.count,
          };
          done(responseData, false);
        } else {
          done(null, err);
        }
      });
    } catch (e) {
      done(null, e);
    }
  },
  async getStripeDate(user, inputData, index, resultData, done) {
    try {
      if (inputData.length > 0) {
        const newData = JSON.parse(JSON.stringify(inputData[index]));
        newData.cancel_at_period_end = false;
        if (
          newData &&
          newData.StripeSubscription &&
          user.id === newData.StripeSubscription.UserId
        ) {
          newData.enableBilling = true;
        } else {
          newData.enableBilling = false;
        }
        if (
          newData.StripeSubscriptionId !== null &&
          newData.isSuperAdminCreatedProject === false &&
          newData.PlanId !== 1
        ) {
          try {
            const subDetail = await stripe.subscriptions.retrieve(
              newData.StripeSubscription.subscriptionId,
            );
            newData.subDetail = {
              subscribedOn: new Date(subDetail.current_period_start * 1000),
              autoRenewal: new Date(subDetail.current_period_end * 1000),
            };
            if (subDetail.cancel_at_period_end) {
              newData.subDetail.autoRenewal = 'Not applicable';
              newData.cancel_at_period_end = true;
              newData.cancel_at = new Date(subDetail.cancel_at * 1000);
              newData.subDetail.status = 'To be cancel';
            } else if (subDetail.status === 'canceled') {
              newData.subDetail.autoRenewal = '------';
              newData.cancel_at_period_end = true;
              newData.cancel_at = new Date(subDetail.cancel_at * 1000);
              newData.subDetail.status = 'Canceled';
            }
            resultData.push(newData);
          } catch (error) {
            if (process.env.NODE_ENV === 'testing' || process.env.NODE_ENV === 'staging') {
              if (error.type === 'StripeInvalidRequestError' && error.statusCode === 404) {
                newData.subDetail = {
                  status: 'Not applicable',
                };
                newData.cancel_at_period_end = true;
                resultData.push(newData);
              }
            }
          }
        } else if (newData.PlanId !== 1) {
          newData.subDetail = {
            subscribedOn: new Date(newData.startDate),
            autoRenewal: new Date(newData.endDate),
          };
          resultData.push(newData);
        } else {
          resultData.push(newData);
        }
        if (index < inputData.length - 1) {
          this.getStripeDate(user, inputData, index + 1, resultData, (response, err) => {
            if (!err) {
              done(response, false);
            } else {
              done(null, err);
            }
          });
        } else {
          done(resultData, false);
        }
      } else {
        done(inputData, false);
      }
    } catch (e) {
      done(null, e);
    }
  },
  async getProSubStripeDate(newData) {
    const subscriptionData = newData;
    subscriptionData.cancel_at_period_end = false;
    subscriptionData.subDetail = {};
    if (newData.PlanId !== 1) {
      let subDetail;
      if (newData && newData.StripeSubscription && newData.StripeSubscription.subscriptionId) {
        subDetail = await stripe.subscriptions.retrieve(newData.StripeSubscription.subscriptionId);
      }
      if (subDetail) {
        subscriptionData.subDetail = {
          subscribedOn: new Date(subDetail.current_period_start * 1000),
          autoRenewal: new Date(subDetail.current_period_end * 1000),
        };
        if (subDetail.cancel_at_period_end) {
          subscriptionData.subDetail.autoRenewal = 'Not applicable';
          subscriptionData.cancel_at_period_end = true;
          subscriptionData.cancel_at = new Date(subDetail.cancel_at * 1000);
          subscriptionData.subDetail.status = 'To be cancel';
        } else if (subDetail.status === 'canceled') {
          subscriptionData.subDetail.autoRenewal = '------';
          subscriptionData.cancel_at_period_end = true;
          subscriptionData.cancel_at = new Date(subDetail.cancel_at * 1000);
          subscriptionData.subDetail.status = 'Canceled';
        }
      } else {
        subscriptionData.subDetail.subscribedOn = newData.subscribedOn;
        subscriptionData.subDetail.status = newData.status;
      }
      return subscriptionData;
    }
    subscriptionData.subDetail.subscribedOn = null;
    subscriptionData.subDetail.status = newData.status;
    return subscriptionData;
  },
  async getProjects(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const condition = {};
      const incomeData = inputData.body;
      const { params } = inputData;
      const pageNumber = +params.pageNo;
      const pageSize = +params.pageSize;
      let searchCondition;
      const offset = (pageNumber - 1) * pageSize;
      if (incomeData.idFilter) {
        condition.id = incomeData.idFilter;
      }
      if (incomeData.nameFilter) {
        condition.projectName = {
          [Sequelize.Op.iLike]: `%${incomeData.nameFilter}%`,
        };
      }

      if (incomeData.search) {
        const searchDefault = [
          {
            projectName: {
              [Sequelize.Op.iLike]: `%${incomeData.search}%`,
            },
          },
        ];

        if (!Number.isNaN(+incomeData.search)) {
          searchCondition = {
            [Op.and]: [
              {
                [Op.or]: [
                  searchDefault,
                  {
                    [Op.and]: [
                      {
                        id: incomeData.search,
                      },
                    ],
                  },
                ],
              },
            ],
          };
        } else {
          searchCondition = {
            [Op.and]: [
              {
                [Op.or]: searchDefault,
              },
            ],
          };
        }
      }
      const projectData = await Project.getProjectsProjectAdmin(
        inputData.user,
        condition,
        searchCondition,
        pageSize,
        offset,
      );
      done(projectData, false);
      // this.getPagination(projectData, offset, pageSize, [], incomeData.search, (response, err) => {
      //   if (!err) {
      //     const responseData = {
      //       rows: response,
      //       count: projectData.length,
      //     };
      //     done(responseData, false);
      //   } else {
      //     done(null, err);
      //   }
      // });
    } catch (e) {
      done(null, e);
    }
  },
  async getPagination(projectData, index, pageSize, resultData, search, done) {
    try {
      const element = projectData[index];
      if (element) {
        const { projectAdminDetails } = element;

        if (search) {
          let searchValue = false;
          if (search === element.id || element.projectName.startsWith(search)) {
            searchValue = true;
          } else {
            projectAdminDetails.forEach((data) => {
              const { email } = data.User;
              const { firstName } = data.User;
              if (email.startsWith(search) || firstName.startsWith(search)) {
                searchValue = true;
              }
            });
          }
          if (searchValue) {
            resultData.push(element);
          }
        } else {
          resultData.push(element);
        }
        if (index + 1 < pageSize) {
          await this.getPagination(
            projectData,
            index + 1,
            pageSize,
            resultData,
            search,
            (response, error) => {
              if (error) {
                done(null, error);
              } else {
                done(response, false);
              }
            },
          );
        } else {
          done(resultData, false);
        }
      } else {
        done(resultData, false);
      }
    } catch (e) {
      done(null, e);
    }
  },
  async createNewProject(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const projectData = inputData.body;
      const loginUser = inputData.user;
      let startDate;
      let endDate;
      const StripeSubscriptionId =
        inputData.stripeDetails && inputData.stripeDetails.id ? inputData.stripeDetails.id : null;
      const planData = await StripePlan.getBy({ id: projectData.PlanId });
      if (planData.stripePlanName === 'free') {
        startDate = moment().format('YYYY-MM-DD');
        endDate = moment().add(14, 'days').format('YYYY-MM-DD');
      } else if (
        planData.stripeProductName.toLowerCase() === 'project plan' &&
        planData.stripePlanName.toLowerCase() === 'monthly'
      ) {
        startDate = moment().format('YYYY-MM-DD');
        endDate = moment(startDate, 'YYYY-MM-DD').add(1, 'M').format('YYYY-MM-DD');
      } else if (
        planData.stripeProductName.toLowerCase() === 'project plan' &&
        planData.stripePlanName.toLowerCase() === 'yearly'
      ) {
        startDate = moment().format('YYYY-MM-DD');
        endDate = moment(startDate, 'YYYY-MM-DD').add(12, 'M').format('YYYY-MM-DD');
      }
      const projectParam = {
        projectName: projectData.projectName,
        projectLocation: projectData.projectLocation,
        projectLocationLatitude: projectData.projectLocationLatitude,
        projectLocationLongitude: projectData.projectLocationLongitude,
        PlanId: projectData.PlanId,
        startDate,
        endDate,
        ParentCompanyId: projectData.ParentCompanyId,
        subscribedOn: new Date(),
        createdBy: loginUser.id,
        TimeZoneId: null,
      };
      const timezoneList = await TimeZone.getAll();
      const getUserTimeZone = timezoneList.filter(
        (object) => object.timezone === projectData.timezone,
      );
      if (getUserTimeZone && getUserTimeZone.length > 0) {
        const userTimeZone = getUserTimeZone[0];
        projectParam.TimeZoneId = userTimeZone.id;
      }
      if (StripeSubscriptionId) {
        projectParam.StripeSubscriptionId = StripeSubscriptionId;
      }
      const newProject = await Project.createInstance(projectParam);
      const createObject = {
        deliveryWindowTime: 0,
        deliveryWindowTimeUnit: 'minutes',
        ProjectId: +newProject.id,
      };
      await ProjectSettings.create(createObject);
      const projectObject = {
        ProjectId: +newProject.id,
        projectName: projectData.projectName,
      };
      this.generatePublicUrlForCurrentProject(projectObject);
      let domainValue;
      let publicProjectId;
      if (inputData.domainBo) {
        domainValue = await Enterprise.findOne({
          where: Sequelize.where(
            Sequelize.fn('lower', Sequelize.col('name')),
            Sequelize.fn('lower', domainNewName),
          ),
        });
        projectParam.EnterpriseId = domainValue.id;
        projectParam.isAccount = true;
        projectParam.publicSchemaId = newProject.id;
        const publicProjectParam = await publicProject.create(projectParam);
        publicProjectId = publicProjectParam.id;
        await Project.update({ publicSchemaId: publicProjectId }, { where: { id: newProject.id } });
      }
      const existUser = await User.findOne({
        where: Sequelize.and(
          Sequelize.where(
            Sequelize.fn('lower', Sequelize.col('email')),
            Sequelize.fn('lower', projectData.email),
          ),
          { isDeleted: false },
        ),
      });
      let publicUserId;
      if (inputData.domainBo) {
        const newUserValue = await publicUser.findOne({
          where: Sequelize.and(
            Sequelize.where(
              Sequelize.fn('lower', Sequelize.col('email')),
              Sequelize.fn('lower', projectData.email),
            ),
            { isDeleted: false },
          ),
        });
        publicUserId = newUserValue.id;
      }
      if (existUser) {
        this.createMember(
          inputData,
          existUser,
          newProject,
          publicUserId,
          publicProjectId,
          (response, error) => {
            if (error) {
              done(null, error);
            } else {
              done(response, false);
            }
          },
        );
      } else {
        const userParam = {
          firstName: projectData.firstName,
          email: projectData.email,
          password: generatePassword(),
          phoneNumber: projectData.phoneNumber,
        };
        const newUser = await User.createInstance(userParam);
        if (inputData.domainBo) {
          const newUserValue = await publicUser.create(userParam);
          publicUserId = newUserValue.id;
        }
        this.createMember(
          inputData,
          newUser,
          newProject,
          publicUserId,
          publicProjectId,
          (response, error) => {
            if (error) {
              done(null, error);
            } else {
              done(response, false);
            }
          },
        );
      }
    } catch (e) {
      done(null, e);
    }
  },
  async createMember(inputData, user, newProject, publicUserId, publicProjectId, done) {
    try {
      await this.getDynamicModel(inputData);
      const projectData = inputData.body;
      const { ParentCompanyId } = projectData;
      const loginUser = inputData.user;
      const CompanyDet = await Member.findOne({
        where: {
          UserId: loginUser.id,
          isDeleted: false,
        },
        include: [
          {
            association: 'Company',
            required: true,
            // where: { isParent: true },
            attributes: ['id'],
          },
        ],
        attributes: ['id'],
      });
      let roleDetails;
      if (inputData.domainBo) {
        roleDetails = await Role.getBy('Account Admin');
      } else {
        roleDetails = await Role.getBy('Project Admin');
      }
      if (CompanyDet != null) {
        const memberParam = {
          UserId: user.id,
          firstName: projectData.firstName,
          CompanyId: CompanyDet.Company.id,
          RoleId: roleDetails.id,
          createdBy: user.id,
          phoneNumber: user.phoneNumber,
          phoneCode: user.phoneCode,
          memberId: 1,
          ParentCompanyId,
          password: generatePassword(),
          ProjectId: newProject.id,
          status: 'completed',
        };

        let newMember;
        if (loginUser.email === user.email) {
          newMember = await Member.createInstance(memberParam);
          await this.createMemberNotificationPreference(newMember, 'member');
          await this.createMemberLocationFollowPreference(
            newProject.id,
            ParentCompanyId,
            newMember.id,
            newProject.projectName,
            user.id,
            'member',
          );
          if (memberParam.RoleId === 2 || memberParam.RoleId === 1) {
            notificationHelper.memberNotificationCreation(
              newMember,
              DeliveryPersonNotification,
              Notification,
            );
          }
          if (inputData.domainBo) {
            memberParam.UserId = publicUserId;
            // const companyData = await Company.findOne({ where: { id: memberParam.CompanyId } });
            // const existCompany = await publicCompany.findOne({
            //   where: { companyName: companyData.companyName, website: companyData.website },
            // });
            // memberParam.CompanyId = existCompany.id;
            const enterpriseValue = await Enterprise.findOne({
              where: Sequelize.where(
                Sequelize.fn('lower', Sequelize.col('name')),
                Sequelize.fn('lower', domainNewName),
              ),
            });
            memberParam.isAccount = true;
            memberParam.EnterpriseId = enterpriseValue.id;
            memberParam.ProjectId = publicProjectId;
            await publicMember.create(memberParam);
          }
        } else {
          const memberArray = [
            memberParam,
            {
              UserId: loginUser.id,
              firstName: loginUser.firstName,
              CompanyId: CompanyDet.id,
              RoleId: roleDetails.id,
              ParentCompanyId,
              createdBy: user.id,
              password: generatePassword(),
              phoneNumber: user.phoneNumber,
              memberId: 1,
              phoneCode: user.phoneCode,
              ProjectId: newProject.id,
              status: 'completed',
            },
          ];
          newMember = await Member.createMultipleInstance(memberArray);
          await this.createMemberNotificationPreference(newMember, 'members');
          await this.createMemberLocationFollowPreference(
            newProject.id,
            ParentCompanyId,
            newMember,
            newProject.projectName,
            loginUser.id,
            'members',
          );
          if (inputData.domainBo) {
            await publicMember.createMultipleInstance(memberArray);
          }
        }
        done(newMember, false);
      } else {
        done(null, { message: 'Something went wrong' });
      }
    } catch (e) {
      done(null, e);
    }
  },
  async getNewDynamicModel(inputData) {
    let { domainNewValue } = inputData;
    if (!domainNewValue) {
      const { email } = inputData.user;
      let userData;
      if (email) {
        userData = await User.findOne({ where: { email } });
      }
      if (userData) {
        const memberData = await Member.findOne({
          where: { UserId: userData.id, RoleId: { [Op.ne]: 4 }, isDeleted: false },
        });
        if (memberData) {
          if (memberData.isAccount) {
            const enterpriseValue = await Enterprise.findOne({
              where: { id: memberData.EnterpriseId, status: 'completed' },
            });
            if (enterpriseValue) {
              domainNewValue = enterpriseValue.name.toLowerCase();
            }
          }
        }
      }
    }
    return domainNewValue;
  },
  async getProjectsCompany(inputData, projectList, done) {
    try {
      await this.getDynamicModel(inputData);
      const companyList = [];
      projectList.forEach((item) => {
        const companyData = item.ParentCompany.Company[0];
        const index = companyList.findIndex(
          (companyNew) => companyNew.id === item.ParentCompany.id,
        );

        if (index === -1) {
          companyList.push({
            id: item.ParentCompany.id,
            companyName: companyData.companyName,
            projectList: [{ id: item.id, projectName: item.projectName }],
          });
        } else {
          companyList[index].projectList.push({ id: item.id, projectName: item.projectName });
        }
      });
      if (companyList && companyList.length > 0) {
        companyList.sort((a, b) => {
          const companyA = a.companyName.toLowerCase();
          const companyB = b.companyName.toLowerCase();
          return companyA.localeCompare(companyB);
        });
      }
      done(companyList, false);
    } catch (e) {
      done(null, e);
    }
  },
  async upgradePlan(inputData, done) {
    await this.getDynamicModel(inputData);
    const projectData = inputData.body;
    const loginUser = inputData.user;
    const planDetails = await StripePlan.getBy({ id: projectData.PlanId });
    const projectDetails = await Project.findByPk(projectData.ProjectId);
    const existingPlanDetails = await StripePlan.getBy({ id: projectDetails.PlanId });
    if (planDetails && projectDetails) {
      if (
        projectDetails.PlanId === projectData.PlanId &&
        projectDetails.status !== 'canceled' &&
        projectDetails.status !== 'overdue'
      ) {
        return done(null, { message: 'You have already subscribed to this plan.' });
      }
      if (
        existingPlanDetails.Plan.planType.toLowerCase() === 'project plan' ||
        existingPlanDetails.Plan.planType.toLowerCase() === 'trial plan'
      ) {
        stripeService.cancelSubscription(inputData, async () => {
          this.convertPlan(
            inputData,
            planDetails,
            projectData,
            loginUser,
            async (planResult, planErr) => {
              if (planErr) {
                return done(null, planErr);
              }
              // const getProjectDetail = await Member.findOne({
              //   include: [
              //     {
              //       association: 'User',
              //       attributes: ['email', 'firstName', 'lastName'],
              //     },
              //     {
              //       association: 'Company',
              //       attributes: [
              //         'companyName',
              //         'address',
              //         'secondAddress',
              //         'country',
              //         'city',
              //         'state',
              //         'zipCode',
              //       ],
              //     },
              //   ],
              //   where: {
              //     ProjectId: projectData.ProjectId,
              //     isDeleted: false,
              //   },
              // });
              // const getCardInfo = creditCardType(projectData.cardDetails.number);
              // const cardType = getCardInfo[0].niceType.toLowerCase();
              // const invoices = await stripe.invoices.list({
              //   customer: planResult.stripeCustomerId,
              //   limit: 1,
              // });
              // let invoiceNo = invoices.data[0].number;
              // let receiptNo = invoices.data[0].receipt_number;
              // if (!invoiceNo) {
              //   invoiceNo = Math.floor(Math.random() * 324324 + 1);
              // }
              // if (!receiptNo) {
              //   receiptNo = Math.floor(Math.random() * 899776 + 1);
              // }
              // const mailData = {
              //   email: getProjectDetail.User.email,
              //   firstName: getProjectDetail.User.firstName,
              //   lastName: getProjectDetail.User.lastName,
              //   invoiceNo,
              //   receiptNo,
              //   footerInvoiceNo: invoiceNo,
              //   invoiceDate: moment(getProjectDetail.endDate).format('DD-MM-YYYY'),
              //   paymentDate: '-',
              //   address: getProjectDetail.Company.address,
              //   city: getProjectDetail.Company.city,
              //   state: getProjectDetail.Company.state,
              //   zipcode: getProjectDetail.Company.zipCode,
              //   country: getProjectDetail.Company.country,
              //   datePaid: moment().format('DD-MM-YYYY'),
              //   cardNo: projectData.cardDetails.number.substring(
              //     projectData.cardDetails.number.length - 4,
              //   ),
              //   amount: (planDetails.stripeAmount / 100).toFixed(2),
              //   quantity: 1,
              //   subTotal: (planDetails.stripeAmount / 100).toFixed(2),
              //   total: (planDetails.stripeAmount / 100).toFixed(2),
              //   invoiceSubTotal: (planDetails.stripeAmount / 100).toFixed(2),
              //   planName: 'Project Plan',
              //   cardType,
              // };
              // const upgradedProject = await Project.findByPk(projectData.ProjectId);
              // const billingHistoryPayload = {
              //   ProjectId: projectData.ProjectId,
              //   paidUserId: loginUser.id,
              //   projectCreatedUserId: upgradedProject.createdBy,
              //   paidDate: new Date(getProjectDetail.startDate),
              //   paymentMethod: 'Online',
              //   amountPaid: (planDetails.stripeAmount / 100).toFixed(2),
              //   status: 'completed',
              // };
              // const billingHistory = await ProjectBillingHistories.createInstance(
              //   billingHistoryPayload,
              // );
              // const upgradedPlan = await StripePlan.getBy({ id: upgradedProject.PlanId });
              // mailData.projectName = upgradedProject.projectName;
              // mailData.interval = upgradedPlan.stripePlanName;
              // mailData.projectStartDate = moment(upgradedProject.startDate).format('DD-MM-YYYY');
              // mailData.projectEndDate = moment(upgradedProject.endDate).format('DD-MM-YYYY');
              // this.sendMail(mailData, 'upgradeplan', async (info, err) => {
              //   if (err) {
              //     return done(null, err);
              //   }
              //   return done(planResult, false);
              // });
              return done(planResult, false);
            },
          );
        });
      } else {
        this.convertPlan(
          inputData,
          planDetails,
          projectData,
          loginUser,
          async (planResult, planErr) => {
            if (planErr) {
              return done(null, planErr);
            }
            return done(planResult, false);
            // this.sendMail(inputData, 'upgradeplan', async (info, err) => {
            //   if (err) {
            //     return done(null, err);
            //   }
            //   return done(planResult, false);
            // });
          },
        );
      }
    } else {
      return done(null, { message: 'Plan/Project Does not exist.' });
    }
  },
  async sendMail(userData, mailData, done) {
    const user = userData;
    // user.email = userData && userData.user && userData.user.email;
    await MAILER.sendMail(
      user,
      'upgradeplan',
      `Follo Upgrade - Congratulations! ${userData.projectName} have upgraded for - 
      ${userData.interval}  from  ${userData.projectStartDate} to ${userData.projectEndDate}`,
      'Plan Upgraded',
      (info, err) => {
        if (err) {
          const newError = new ApiError(err.message, status.BAD_REQUEST);
          return done(null, newError);
        }
        return done(userData, false);
      },
    );
  },
  async convertPlan(inputData, planDetails, projectData, loginUser, done) {
    await this.getDynamicModel(inputData);
    if (planDetails.Plan.planType.toLowerCase() === 'project plan') {
      const CompanyDet = await Member.findOne({
        where: {
          UserId: loginUser.id,
          ProjectId: inputData.body.ProjectId,
          isDeleted: false,
        },
        include: [
          {
            association: 'Company',
            required: true,
            // where: { ProjectId: inputData.body.ProjectId },
            attributes: ['id', 'city', 'country', 'address', 'zipCode', 'state'],
          },
        ],
        attributes: ['id'],
      });
      const planData = await StripePlan.getBy({ id: projectData.PlanId });
      const newInputData = inputData;
      newInputData.planData = planData;
      const newCompany = CompanyDet.Company;
      newInputData.CompanyDet = newCompany;
      // stripeService.addCard(newInputData, async (result, err) => {
      // if (err) {
      // return done(null, err);
      // }
      if (
        inputData.body.stripeCustomerId !== null &&
        inputData.body.stripeCustomerId !== undefined
      ) {
        await User.update(
          {
            stripeCustomerId: inputData.body.stripeCustomerId,
          },
          { where: { id: inputData.user.id } },
        );
        let startDate;
        let endDate;
        if (planData.stripeProductName.toLowerCase() === 'trial plan') {
          startDate = moment().format('YYYY-MM-DD');
          endDate = moment().add(14, 'days').format('YYYY-MM-DD');
        } else if (
          planData.stripeProductName.toLowerCase() === 'project plan' &&
          planData.stripePlanName === 'monthly'
        ) {
          startDate = moment().format('YYYY-MM-DD');
          endDate = moment(startDate, 'YYYY-MM-DD').add(1, 'M').format('YYYY-MM-DD');
        } else if (
          planData.stripeProductName.toLowerCase() === 'project plan' &&
          planData.stripePlanName === 'yearly'
        ) {
          startDate = moment().format('YYYY-MM-DD');
          endDate = moment(startDate, 'YYYY-MM-DD').add(12, 'M').format('YYYY-MM-DD');
        }
        const subscription = await stripe.subscriptions.list({
          customer: inputData.body.stripeCustomerId,
          limit: 1,
        });
        const latestSubscriptionObject = subscription.data[0];
        const result = latestSubscriptionObject;
        const newSubscriptionCreated = await StripeSubscription.create({
          UserId: inputData.user.id,
          status: 'active',
          subscriptionId: latestSubscriptionObject.id,
        });
        result.stripeCustomerId = inputData.body.stripeCustomerId;
        const newProject = await Project.update(
          {
            PlanId: projectData.PlanId,
            StripeSubscriptionId: newSubscriptionCreated.id,
            status: '',
            subscribedOn: new Date(),
            startDate,
            endDate,
          },
          { where: { id: projectData.ProjectId } },
        );
        newProject.stripeCustomerId = result.stripeCustomerId;
        return done(newProject, false);
      }
      return done(null, { message: 'Something went wrong.' });
      // });
    }
    if (planDetails.Plan.planType.toLowerCase() === 'trial plan') {
      const existProject = await Project.count({
        where: Sequelize.and({
          createdBy: loginUser.id,
          PlanId: projectData.PlanId,
          isDeleted: false,
        }),
      });
      let projectCount = 2;
      // <EMAIL>(loginUser.id == 5) - prod env
      if (process.env.NODE_ENV === 'prod' && +loginUser.id === 5) {
        projectCount = 20;
      }
      // <EMAIL>(loginUser.id == 227) - staging env
      if (process.env.NODE_ENV === 'staging' && +loginUser.id === 227) {
        projectCount = 10;
      }
      if (existProject >= +projectCount) {
        return done(null, { message: 'Trial Plan Limit Reached.' });
      }
      const newProject = await Project.update(
        {
          PlanId: projectData.PlanId,
          status: '',
          StripeSubscriptionId: null,
          subscribedOn: new Date(),
        },
        { where: { id: projectData.ProjectId } },
      );
      return done(newProject, false);
    }
    if (planDetails.Plan.planType.toLowerCase() === 'enterprise plan') {
      await Project.updateInstance(
        { id: projectData.ProjectId },
        { PlanId: projectData.PlanId, status: '' },
      );
      return done({ message: 'Please Contact Sales team.' }, false);
    }
    return done(null, { message: 'Plan Id does not exist' });
  },
  async getProjectDetails(req) {
    try {
      await this.getDynamicModel(req);
      const projectDetails = await Project.findByPk(+req.params.ProjectId);
      return projectDetails;
    } catch (e) {
      console.log(e);
    }
  },
  // Get All Project Lists
  async getProjectList(req) {
    try {
      const {
        search,
        pageSize,
        pageNo,
        sortColumn,
        sortType,
        projectId,
        projectName,
        companyName,
      } = req.query;
      let count = 0;
      let offset;
      if (pageNo) {
        offset = (pageNo - 1) * pageSize;
      }
      const projectList = await Project.getAllProjects(
        pageSize,
        offset,
        search,
        sortColumn,
        sortType,
        projectId,
        projectName,
        companyName,
      );
      count = projectList.length;
      return { projectList, count };
    } catch (e) {
      console.log(e);
    }
  },
  // Updated Existing project
  async editMemberProject(req) {
    try {
      let updatedProject;
      const projectData = req.body;
      const { id } = req.params;
      if (projectData) {
        let startDate;
        let endDate;
        if (projectData.planType.toLowerCase() === 'trial plan') {
          startDate = moment().format('YYYY-MM-DD');
          endDate = moment().add(14, 'days').format('YYYY-MM-DD');
        } else if (
          projectData.planType.toLowerCase() === 'project plan' &&
          projectData.interval.toLowerCase() === 'monthly'
        ) {
          startDate = moment().format('YYYY-MM-DD');
          endDate = moment(startDate, 'YYYY-MM-DD').add(1, 'M').format('YYYY-MM-DD');
        } else if (
          projectData.planType.toLowerCase() === 'project plan' &&
          projectData.interval.toLowerCase() === 'yearly'
        ) {
          startDate = moment().format('YYYY-MM-DD');
          endDate = moment(startDate, 'YYYY-MM-DD').add(12, 'M').format('YYYY-MM-DD');
        }
        const selectedPlanName = projectData.planType.toLowerCase();
        const selectedInterval = projectData.interval.toLowerCase();
        let plan;
        let planDetails;
        if (selectedPlanName) {
          plan = selectedPlanName.split(' ');
          for (let i = 0; i < plan.length; i += 1) {
            plan[i] = plan[i][0].toUpperCase() + plan[i].substr(1);
          }
        }
        // eslint-disable-next-line prettier/prettier
        const selectedPlan = plan.join(' ');
        if (selectedPlan === 'Project Plan') {
          planDetails = await StripePlan.findOne({
            where: {
              stripeProductName: selectedPlan,
              stripePlanName: selectedInterval,
            },
          });
        } else {
          planDetails = await StripePlan.findOne({
            where: {
              stripeProductName: selectedPlan,
            },
          });
        }
        const projectDetail = await Project.findByPk(id);
        const stripeSubscription = await StripeSubscription.getBy({
          id: projectDetail.StripeSubscriptionId,
        });
        if (stripeSubscription && stripeSubscription.status !== 'inactive') {
          await stripe.subscriptions.del(stripeSubscription.subscriptionId);
          await Project.update({ status: 'canceled' }, { where: { id } });
          await stripeSubscription.updateInstance(stripeSubscription.id, { status: 'inactive' });
        }
        updatedProject = await Project.update(
          {
            PlanId: planDetails.id,
            projectName: projectData.projectName,
            projectLocation: projectData.projectLocation,
            subscribedOn: new Date(),
            status: '',
            startDate,
            endDate,
            isSuperAdminCreatedProject: true,
          },
          { where: { id: req.params.id } },
        );
      }
      const mailObject = {
        name: projectData.assignedTo,
        projectName: projectData.projectName,
        email: projectData.email,
      };
      await MAILER.sendMail(
        mailObject,
        'editProjectBySA',
        'Project Details Updated.!',
        'Project Details Edited by Super Admin',
        (info, err) => {
          console.log(info, err);
        },
      );
      return updatedProject;
    } catch (e) {
      console.log(e);
    }
  },
  // Updated Existing project
  async getMemberProject(req) {
    try {
      const { id } = req.params;
      const project = await Project.getMemberProject({ id });
      return project;
    } catch (e) {
      console.log(e);
    }
  },
  // assign New Project To Member
  async assignNewProjectToMember(req) {
    try {
      const { userId } = req.body;
      const projectData = req.body;
      let planDetails;
      if (projectData.planType.toLowerCase() === 'trial plan') {
        planDetails = await StripePlan.getBy({
          stripeProductName: 'Trial Plan',
        });
      }
      if (projectData.planType.toLowerCase() === 'project plan')
        planDetails = await StripePlan.getBy({
          stripeProductName: 'Project Plan',
          stripePlanName: projectData.interval.toLowerCase(),
        });
      const CompanyDetails = await Member.findOne({
        where: {
          UserId: userId,
          isDeleted: false,
        },
        include: [
          {
            association: 'Company',
            required: true,
            attributes: [
              'id',
              'companyName',
              'website',
              'address',
              'secondAddress',
              'country',
              'city',
              'companyAutoId',
              'state',
              'zipCode',
              'scope',
              'logo',
              'ParentCompanyId',
            ],
          },
        ],
        attributes: ['id', 'ParentCompanyId'],
      });
      const parentId = await Member.getBy({ UserId: userId });
      const emailValidationData = await ParentCompany.getBy({ id: parentId.ParentCompanyId });
      const projectParam = {
        projectName: projectData.projectName,
        projectLocation: projectData.projectLocation,
        PlanId: planDetails.PlanId,
        startDate: projectData.projectStartDate,
        endDate: projectData.projectEndDate,
        ParentCompanyId: emailValidationData.id,
        subscribedOn: new Date(),
        createdBy: userId,
        isSuperAdminCreatedProject: true,
      };
      const newProject = await Project.createInstance(projectParam);
      const roleDetails = await Role.getBy('Project Admin');
      if (CompanyDetails != null) {
        const companyParam = CompanyDetails.Company;
        const companyObject = {
          companyName: companyParam.companyName,
          website: companyParam.website,
          address: companyParam.address,
          secondAddress: companyParam.secondAddress,
          country: companyParam.country,
          city: companyParam.city,
          companyAutoId: companyParam.companyAutoId,
          state: companyParam.state,
          zipCode: companyParam.zipCode,
          scope: companyParam.scope,
          logo: companyParam.logo,
          createdBy: req.user.id,
          ProjectId: newProject.id,
          ParentCompanyId: companyParam.ParentCompanyId,
          isParent: false,
          isDeleted: false,
        };
        const newCompany = await Company.create(companyObject);
        const memberParam = {
          UserId: userId,
          firstName: projectData.firstName,
          CompanyId: newCompany.id,
          RoleId: roleDetails.id,
          createdBy: req.user.id,
          phoneNumber: projectData.phoneNumber,
          phoneCode: projectData.phoneCode,
          memberId: 1,
          ParentCompanyId: emailValidationData.id,
          password: generatePassword(),
          ProjectId: newProject.id,
          status: 'completed',
        };
        const newMember = await Member.createInstance(memberParam);
        await this.createMemberNotificationPreference(newMember, 'member');
        await this.createMemberLocationFollowPreference(
          newProject.id,
          emailValidationData.id,
          newMember.id,
          newProject.projectName,
          userId,
          'member',
        );
        const mailObject = {
          firstName: projectData.assignedTo,
          projectName: newProject.projectName,
          email: req.body.email,
        };
        await MAILER.sendMail(
          mailObject,
          'addproject',
          'You were added as a member',
          'Assigned a New Project',
          (info, err) => {
            console.log(info, err);
          },
        );
        return newMember;
      }
    } catch (err) {
      console.log(err);
    }
  },
  // Project Billing Histories
  async projectsBillingHistories(req) {
    try {
      const { search, pageSize, pageNo, sortColumn, sortType, userName, projectName, planType } =
        req.query;
      let count = 0;
      let offset;
      if (pageNo) {
        offset = (pageNo - 1) * pageSize;
      }
      const projectList = await Project.getAllProjectsForBilling(
        pageSize,
        offset,
        sortColumn,
        sortType,
        userName,
        projectName,
        planType,
        search,
      );
      count = projectList.length;
      return { projectList, count };
    } catch (err) {
      console.log(err);
    }
  },
  // Single Project Billing Histories
  async getProjectBillingHistories(req) {
    try {
      const { search, recepientName, paymentMethod, amount } = req.query;
      const billingStatus = req.query.status;
      const projectId = req.params.id;
      const projectBillingHistories = await ProjectBillingHistories.getProjectsWithBillingHistories(
        projectId,
        recepientName,
        billingStatus,
        paymentMethod,
        amount,
        search,
      );
      return { projectBillingHistories };
    } catch (err) {
      console.log(err);
    }
  },
  async createMemberNotificationPreference(memberData, data) {
    await this.returnProjectModel();
    if (data === 'member') {
      const memberDetail = await Member.findOne({
        where: { id: memberData.id, isDeleted: false },
      });
      const getNotificationPreferenceItemsList = await NotificationPreferenceItem.findAll({
        where: { isDeleted: false },
      });
      const getProject = await Project.findOne({
        where: {
          isDeleted: false,
          id: +memberDetail.ProjectId,
        },
        include: [
          {
            where: { isDeleted: false },
            association: 'TimeZone',
            required: false,
            attributes: ['id', 'location', 'timeZoneOffsetInMinutes'],
          },
        ],
      });
      const attr = {
        time: '05:00',
        timeFormat: 'AM',
      };
      let projectObject;
      if (getProject) {
        projectObject = getProject.toJSON();
      }
      if (projectObject && projectObject.TimeZone) {
        attr.TimeZoneId = projectObject.TimeZone.id;
      } else {
        attr.TimeZoneId = 3;
      }
      await Member.update(attr, { where: { id: memberDetail.id } });
      for (let index2 = 0; index2 < getNotificationPreferenceItemsList.length; index2 += 1) {
        const item = getNotificationPreferenceItemsList[index2];
        if (
          (item.id === 7 &&
            item.description === 'When a comment is added to a delivery/crane/concrete request' &&
            item.itemId === 4 &&
            item.emailNotification === true &&
            item.inappNotification === false &&
            item.isDeleted === false) ||
          item.inappNotification === true
        ) {
          const object = {
            MemberId: memberDetail.id,
            ProjectId: memberDetail.ProjectId,
            ParentCompanyId: memberDetail.ParentCompanyId,
            NotificationPreferenceItemId: item.id,
            instant: true,
            dailyDigest: false,
            isDeleted: false,
          };
          await NotificationPreference.createInstance(object);
        } else {
          const object = {
            MemberId: memberDetail.id,
            ProjectId: memberDetail.ProjectId,
            ParentCompanyId: memberDetail.ParentCompanyId,
            NotificationPreferenceItemId: item.id,
            instant: false,
            dailyDigest: true,
            isDeleted: false,
          };
          await NotificationPreference.createInstance(object);
        }
      }
    } else if (data === 'members') {
      const getNotificationPreferenceItemsList = await NotificationPreferenceItem.findAll({
        where: { isDeleted: false },
      });
      memberData.map(async (member) => {
        const memberData1 = member;

        const getProject = await Project.findOne({
          where: {
            isDeleted: false,
            id: +memberData1.ProjectId,
          },
          include: [
            {
              where: { isDeleted: false },
              association: 'TimeZone',
              required: false,
              attributes: ['id', 'location', 'timeZoneOffsetInMinutes'],
            },
          ],
        });
        const attr = {
          time: '05:00',
          timeFormat: 'AM',
        };
        let projectObject;
        if (getProject) {
          projectObject = getProject.toJSON();
        }
        if (projectObject && projectObject.TimeZone) {
          attr.TimeZoneId = projectObject.TimeZone.id;
        } else {
          attr.TimeZoneId = 3;
        }
        await Member.update(attr, { where: { id: memberData1.id } });
        for (let index2 = 0; index2 < getNotificationPreferenceItemsList.length; index2 += 1) {
          const item = getNotificationPreferenceItemsList[index2];
          if (
            (item.id === 7 &&
              item.description === 'When a comment is added to a delivery/crane/concrete request' &&
              item.itemId === 4 &&
              item.emailNotification === true &&
              item.inappNotification === false &&
              item.isDeleted === false) ||
            item.inappNotification === true
          ) {
            const object = {
              MemberId: memberData1.id,
              ProjectId: memberData1.ProjectId,
              ParentCompanyId: memberData1.ParentCompanyId,
              NotificationPreferenceItemId: item.id,
              instant: true,
              dailyDigest: false,
              isDeleted: false,
            };
            await NotificationPreference.createInstance(object);
          } else {
            const object = {
              MemberId: memberData1.id,
              ProjectId: memberData1.ProjectId,
              ParentCompanyId: memberData1.ParentCompanyId,
              NotificationPreferenceItemId: item.id,
              instant: false,
              dailyDigest: true,
              isDeleted: false,
            };
            await NotificationPreference.createInstance(object);
          }
        }
      });
    }
  },
  async getTotalProjects() {
    const data = await Project.findAll({
      where: {
        isDeleted: false,
      },
      include: [
        {
          association: 'userDetails',
          attributes: ['id', 'firstName', 'email', 'lastName'],
        },
        {
          association: 'ParentCompany',
          attributes: ['id'],
          include: [
            {
              association: 'Company',
              where: { isParent: true },
              attributes: ['id', 'companyName'],
              required: true,
            },
          ],
        },
      ],
      attributes: [
        'id',
        'projectName',
        [Sequelize.literal('date("startDate")'), 'startDate'],
        [Sequelize.literal('date("endDate")'), 'endDate'],
        'status',
      ],
    }).then((projects) => {
      if (projects && projects.length > 0) {
        projects.map((project) => {
          const projectData = project;
          if (!projectData.status) projectData.status = 'active';
          return project;
        });
        return projects;
      }
    });
    return data;
  },
  async extendProjectDuration(req) {
    const project = await Project.findOne({
      where: {
        isDeleted: false,
        id: req.body.ProjectId,
      },
      attributes: ['id', 'StripeSubscriptionId', 'status', 'startDate', 'endDate', 'PlanId'],
    });
    if (project && project.StripeSubscriptionId) {
      const subscriptionObject = await StripeSubscription.findOne({
        where: {
          id: project.StripeSubscriptionId,
        },
      });
      if (subscriptionObject) {
        const subscription = await stripe.subscriptions.update(subscriptionObject.subscriptionId, {
          trial_end:
            new Date(moment(req.body.date, 'YYYY-MM-DD').format('YYYY-MM-DD 23:59:59')).getTime() /
            1000,
        });
        if (subscription) {
          const currentDate1 = moment().format('YYYY-MM-DD');
          const requestedEndDate1 = moment(req.body.date, 'YYYY-MM-DD').format('YYYY-MM-DD');
          const splittedCurrentDate = currentDate1.split('-');
          const currentDate = moment([
            splittedCurrentDate[0],
            splittedCurrentDate[1] - 1,
            splittedCurrentDate[2],
          ]);
          const splittedRequestedEndDate = requestedEndDate1.split('-');
          const requestedEndDate = moment([
            splittedRequestedEndDate[0],
            splittedRequestedEndDate[1] - 1,
            splittedRequestedEndDate[2],
          ]);
          const numberOfDaysExtended = requestedEndDate.diff(currentDate, 'days');
          const updateObject = {
            status: null,
            endDate: moment(req.body.date).format('YYYY-MM-DD 00:00:00+00'),
          };
          if (+numberOfDaysExtended >= 365) {
            updateObject.PlanId = 2;
          }
          const projectExtended = await Project.update(updateObject, { where: { id: project.id } });
          if (projectExtended) {
            return true;
          }
          return false;
        }
        return false;
      }
      return false;
    }
    if (project && !project.StripeSubscriptionId) {
      const currentDate1 = moment().format('YYYY-MM-DD');
      const requestedEndDate1 = moment(req.body.date, 'YYYY-MM-DD').format('YYYY-MM-DD');
      const splittedCurrentDate = currentDate1.split('-');
      const currentDate = moment([
        splittedCurrentDate[0],
        splittedCurrentDate[1] - 1,
        splittedCurrentDate[2],
      ]);
      const splittedRequestedEndDate = requestedEndDate1.split('-');
      const requestedEndDate = moment([
        splittedRequestedEndDate[0],
        splittedRequestedEndDate[1] - 1,
        splittedRequestedEndDate[2],
      ]);
      const numberOfDaysExtended = requestedEndDate.diff(currentDate, 'days');
      const updateObject = {
        status: null,
        endDate: moment(req.body.date).format('YYYY-MM-DD 00:00:00+00'),
      };
      if (+numberOfDaysExtended >= 365) {
        updateObject.PlanId = 2;
      }
      const projectExtended = await Project.update(updateObject, { where: { id: project.id } });
      if (projectExtended) {
        return true;
      }
      return false;
    }
    return false;
  },

  async updateProjectSharingSettings(payload) {
    try {
      const projectSettings = await ProjectSettings.update(payload, {
        where: {
          ProjectId: payload.ProjectId,
        },
      });
      return projectSettings;
    } catch (err) {
      return err;
    }
  },
  async uploadLogisticPlan(inputData, done) {
    const { params } = inputData;
    let uploadedPdfFileLink;
    let uploadedPdfFileName;
    awsConfig.logisticPlanUpload(inputData, async (res, err) => {
      if (!err) {
        uploadedPdfFileLink = res[0].fileLink;
        uploadedPdfFileName = res[0].fileName;
        const payload = {
          isPdfUploaded: true,
          pdfOriginalName: uploadedPdfFileName,
          projectLogisticPlanUrl: uploadedPdfFileLink,
          convertedImageLinks: null,
          sitePlanStatus: 'uploading',
        };
        const projectSettings = await ProjectSettings.update(payload, {
          where: {
            ProjectId: +params.ProjectId,
          },
        });
        if (projectSettings) {
          done(
            {
              fileUrl: uploadedPdfFileLink,
              fileName: uploadedPdfFileName,
            },
            false,
          );
        } else {
          done(null, err);
        }
      } else {
        done(null, err);
      }
    });
  },

  // async uploadLogisticPlan(inputData, done) {
  //   const { params } = inputData;
  //   const fileType = inputData.file.originalname.replace(/^.*\./, '');
  //   try {
  //     const result = await new Promise((resolve, reject) => {
  //       awsConfig.logisticPlanUpload(inputData, async (result1, err) => {
  //         if (!err) {
  //           resolve(result1);
  //         } else {
  //           reject(err);
  //         }
  //       });
  //     });
  //     const payload = {
  //       pdfOriginalName: result[0].fileName,
  //       projectLogisticPlanUrl: result[0].fileLink,
  //       isPdfUploaded: true,
  //       ProjectId: params.ProjectId,
  //       convertedImageLinks: null,
  //     };
  //     const projectSettings = await ProjectSettings.update(payload, {
  //       where: {
  //         ProjectId: payload.ProjectId,
  //       },
  //     });
  //     const fileName = `${inputData.user.id}_${params.ProjectId}_${params.projectName}`;
  //     if (fileType.toLowerCase() === 'pdf') {
  //       const pdfFileLink = inputData.fileUrl;
  //       // const fileName = inputData.fileName;
  //       const ProjectId = inputData.projectId;
  //       let singleString;
  //       const worker = new Worker(imageConversionProcess);
  //       const object = JSON.stringify({
  //         pdfFileLink,
  //         fileName,
  //       });
  //       worker.postMessage(object);
  //       worker.on('message', async (data) => {
  //         if (data.success) {
  //           const payload = {
  //             isPdfUploaded: true,
  //             ProjectId: +ProjectId,
  //             convertedImageLinks: JSON.stringify(data.result),
  //           };
  //           const projectSettings2 = await ProjectSettings.update(payload, {
  //             where: {
  //               ProjectId: payload.ProjectId,
  //             },
  //           });
  //           // //return { success: true, data: data.result };
  //           const socketObject = {
  //             success: true,
  //           };
  //           global.io.emit('pdfToImageConvertion', socketObject);
  //           worker.terminate();
  //         } else {
  //           const payload = {
  //             isPdfUploaded: false,
  //             ProjectId: ProjectId,
  //             convertedImageLinks: null,
  //           };
  //           const projectSettings = await ProjectSettings.update(payload, {
  //             where: {
  //               ProjectId: payload.ProjectId,
  //             },
  //           });
  //           const socketObject = {
  //             success: false,
  //           };
  //           global.io.emit('pdfToImageConvertion', socketObject);
  //           worker.terminate();
  //         }
  //       });
  //       worker.on('exit', (data) => {
  //         console.log('worker thread exit ', data);
  //       });
  //       return { success: true, data: 'success' };
  //     }
  //     if (projectSettings) {
  //       return done({ fileUrl: result[0].fileLink, fileName: result[0].fileName }, false);
  //     }
  //   } catch (error) {
  //     done(null, error);
  //   }
  // },
  async generatePublicUrlForExistingProjects() {
    const projectList = await Project.findAll({
      where: { isDeleted: false },
    });
    for (let index = 0; index < projectList.length; index += 1) {
      const project = projectList[index];
      const textToBeEncrypted = `${project.id}_${project.projectName}`;
      const uniqueStringForAProject = cryptr.encrypt(textToBeEncrypted);
      const projectPubliWebsiteUrl =
        await deepLinkService.getGuestUserDeepLink(uniqueStringForAProject);
      const payload = {
        publicWebsiteUrl: projectPubliWebsiteUrl.link,
      };
      await ProjectSettings.update(payload, {
        where: {
          ProjectId: +project.id,
        },
      });
      if (+projectList.length === index + 1) {
        return {
          error: false,
          status: 'done',
        };
      }
    }
  },
  async decodeProjectDetailUrl(req) {
    let ProjectId;
    if (req.body.encodeUrl) {
      const url = req.body.encodeUrl;
      const decodedUrl = cryptr.decrypt(url);
      const splitCodeUrl = decodedUrl.split('_');
      ProjectId = +splitCodeUrl[0];
    } else {
      // ProjectId = req.body.ProjectId;
    }
    const projectList = await Project.findOne({
      where: { id: ProjectId },
      attributes: [
        'id',
        'projectName',
        'projectLocation',
        'projectLocationLatitude',
        'projectLocationLongitude',
        'ParentCompanyId',
        'status',
      ],
      include: ['ProjectSettings'],
    });
    return { projectList };
  },
  async createMemberLocationFollowPreference(
    ProjectId,
    ParentCompanyId,
    MemberId,
    projectName,
    createdBy,
    data,
  ) {
    if (data === 'member') {
      const locationObject = {
        ProjectId,
        ParentCompanyId,
        notes: null,
        MemberId,
        createdBy,
        platform: 'web',
        locationName: projectName,
        locationPath: projectName,
        isDefault: true,
      };
      const location = await Locations.create(locationObject);
      const object = {
        MemberId,
        ProjectId,
        LocationId: location.id,
        follow: true,
        ParentCompanyId,
        isDeleted: false,
      };
      await LocationNotificationPreferences.createInstance(object);
    } else if (data === 'members') {
      MemberId.map(async (memberData) => {
        const memberData1 = memberData;
        const locationObject = {
          ProjectId,
          ParentCompanyId,
          notes: null,
          MemberId: memberData1.id,
          createdBy,
          platform: 'web',
          locationName: projectName,
          locationPath: projectName,
          isDefault: true,
        };
        const location = await Locations.create(locationObject);
        const object = {
          MemberId: memberData1.id,
          ProjectId,
          LocationId: location.id,
          follow: true,
          ParentCompanyId,
          isDeleted: false,
        };
        await LocationNotificationPreferences.createInstance(object);
      });
    }
  },
  async generatePublicUrlForCurrentProject(data) {
    try {
      const textToBeEncrypted = `${data.ProjectId}_${data.projectName}`;
      const uniqueStringForAProject = cryptr.encrypt(textToBeEncrypted);
      const projectPublicWebsiteUrl =
        await deepLinkService.getGuestUserDeepLink(uniqueStringForAProject);
      const payload = {
        publicWebsiteUrl: projectPublicWebsiteUrl.link,
      };
      await ProjectSettings.update(payload, {
        where: {
          ProjectId: +data.ProjectId,
        },
      });
    } catch (error) {
      console.error('Error:', error);
    }
  },

  async updateDashboardLogisticPlan(payload) {
    try {
      const projectSettings = await ProjectSettings.update(payload, {
        where: {
          ProjectId: payload.ProjectId,
        },
      });
      return projectSettings;
    } catch (err) {
      return err;
    }
  },

  async retoolParentCompanyWithProjects(done) {
    try {
      const projectList = await Project.retoolParentCompanyWithProjects();
      const companyList = [];
      projectList.forEach((item) => {
        const companyData = item.ParentCompany.Company[0];
        const index = companyList.findIndex(
          (companyNew) => companyNew.id === item.ParentCompany.id,
        );

        if (index === -1) {
          companyList.push({
            id: item.ParentCompany.id,
            companyName: companyData.companyName,
            projectList: [{ id: item.id, projectName: item.projectName }],
          });
        } else {
          companyList[index].projectList.push({ id: item.id, projectName: item.projectName });
        }
      });
      if (companyList && companyList.length > 0) {
        companyList.sort((a, b) => {
          const companyA = a.companyName.toLowerCase();
          const companyB = b.companyName.toLowerCase();
          return companyA.localeCompare(companyB);
        });
      }
      // done(companyList, false);
      return companyList;
    } catch (e) {
      throw new Error(e);
    }
  },
  async updatedSitePlanInAWS(req) {
    try {
      const { statusCode, imageLinks, ProjectId } = req.body;
      if (statusCode === 200) {
        const cloudFrontURl = process.env.CLOUD_FRONT_URL;
        const modifiedImageLinks = imageLinks.map((link) => cloudFrontURl + link);
        const payload = {
          isPdfUploaded: true,
          convertedImageLinks: JSON.stringify(modifiedImageLinks),
          sitePlanStatus: 'uploaded',
        };
        await ProjectSettings.update(payload, {
          where: {
            ProjectId: +ProjectId,
          },
        });
        global.io.emit('pdfToImageConvertion', payload);
      } else {
        const payload = {
          isPdfUploaded: false,
          convertedImageLinks: null,
          sitePlanStatus: 'failed',
        };
        await ProjectSettings.update(payload, {
          where: {
            ProjectId: +ProjectId,
          },
        });
        global.io.emit('pdfToImageConvertion', payload);
      }
      return true;
    } catch (err) {
      return err;
    }
  },
  // async uploadSiteLogisticPlan(req, done) {
  //   const { params } = req;
  //   try {
  //     let result = [];
  //     for (let i = 0; i < req.files.length; i++) {
  //       await new Promise((resolve, reject) => {
  //         awsConfig.sitePlanUpload(req.files[i], async (result1, err) => {
  //           if (!err) {
  //             result.push(result1[0].fileLink)
  //             resolve(result1);
  //           } else {
  //             reject(err);
  //           }
  //         });
  //       });
  //     }
  //     const pdfUrl = result.shift();
  //     const payload = {
  //       isPdfUploaded: true,
  //       ProjectId: +params.ProjectId,
  //       projectLogisticPlanUrl: pdfUrl,
  //       convertedImageLinks: JSON.stringify(result),
  //     };
  //     const projectSettings = await ProjectSettings.update(payload, {
  //       where: {
  //         ProjectId: payload.ProjectId,
  //       },
  //     });
  //     if (projectSettings) {
  //       return projectSettings;
  //     }
  //   } catch (error) {
  //     done(null, error);
  //   }
  // },
};

module.exports = projectService;
