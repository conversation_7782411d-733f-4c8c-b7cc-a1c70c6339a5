const { Router } = require('express');
const { validate } = require('express-validation');
const passportConfig = require('../config/passport');
const { CommentController } = require('../controllers');
const { commentValidation } = require('../middlewares/validations');

const commentRoute = {
  get router() {
    const router = Router();
    router.post(
      '/create_comment',
      validate(commentValidation.addComment, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      CommentController.createComment,
    );
    router.post(
      '/create_inspection_comment',
      // validate(commentValidation.addComment, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      CommentController.createInspectionComment,
    );
    router.get(
      '/get_comment/:DeliveryRequestId/:ParentCompanyId/:ProjectId',
      validate(commentValidation.getComment, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      CommentController.getComment,
    );

    router.get(
      '/get_inspection_comment/:InspectionRequestId/:ParentCompanyId/:ProjectId',
      validate(commentValidation.getInspectionComment, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      CommentController.getInspectionComment,
    );

    return router;
  },
};
module.exports = commentRoute;
