const passport = require('passport');
const { Strategy: JWTStrategy, ExtractJwt } = require('passport-jwt');
const helper = require('../../helpers/domainHelper');

const { User } = require('../../models');

let modelName;

// passport.serializeUser((user, done) => {
//   done(null, user.id);
// });

// passport.deserializeUser((arg, done) => {
//   modelName.getBy({ id: arg.id }, (err, user) => {
//     const data = user;
//     data.domainName = arg.domainName;
//     done(err, data);
//   });
// });

const findUser = (arg) => {
  const user = modelName.findOne({ where: { id: arg.id } });
  user.domainName = arg.domainName;
  return user;
};

const findUserPayload = async (arg) => {
  let tempModel;
  const db = await helper.checkDomain(arg.domainName);
  if (db !== null) {
    tempModel = db.DynamicUser;
  } else {
    tempModel = User;
  }
  const user = await tempModel.findOne({ where: { id: arg.id } });
  user.domainName = arg.domainName;
  return user;
};

const opts = {
  jwtFromRequest: ExtractJwt.fromAuthHeaderWithScheme('JWT'),
  secretOrKey: process.env.JWT_SECRET,
  ignoreExpiration: false,
};

passport.use(
  new JWTStrategy(opts, async (jwtPayload, done) => {
    const db = await helper.checkDomain(jwtPayload.sub.domainName);
    if (db !== null) {
      modelName = db.DynamicUser;
    } else {
      modelName = User;
    }
    findUser(jwtPayload.sub)
      .then((user) => {
        const data = user;
        data.domainName = jwtPayload.sub.domainName;
        delete data.password;
        done(null, data);
      })
      .catch((err) => done(err, false));
  }),
);

exports.isAuthenticated = passport.authenticate('jwt', { session: false });
exports.findUserPayload = findUserPayload;
