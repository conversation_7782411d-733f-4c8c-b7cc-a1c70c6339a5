const { parentPort } = require('worker_threads');
const moment = require('moment');

const { parse } = require('flatted');
const {
  DeliveryRequest,
  Member,
  DeliveryPerson,
  DeliverGate,
  DeliverEquipment,
  DeliverCompany,
  Role,
  Gates,
  Equipments,
  DeliverDefineWork,
  DeliverDefine,
  DeliverHistory,
  DeliveryPersonNotification,
  User,
  Notification,
  NotificationPreference,
  Locations,
} = require('../models');
const notificationHelper = require('../helpers/notificationHelper');
const pushNotification = require('../config/fcm');
const companyService = require('./companyService');
const deliveryService = require('./deliveryService');
const craneRequestService = require('./craneRequestService');
const { Sequelize } = require('../models');

const { Op } = Sequelize;

parentPort.on('message', async (message) => {
  const { projectDetails, loginUser, ndrRecords, ProjectId, inputData } = parse(message);
  await deliveryService.getDynamicModel(inputData);
  let id = 0;
  let equipmentData;

  /* eslint-disable no-restricted-syntax */
  /* eslint-disable no-await-in-loop */
  for (const [i, element] of ndrRecords.entries()) {
    let createdNDR = {};
    const getRow = element;
    getRow.shift();
    const row = {
      description: getRow[1],
      responsible_company: getRow[2],
      definable_feature_of_work: getRow[3],
      responsible_person: getRow[4],
      is_escort_needed: getRow[5],
      date: getRow[6] ? moment(getRow[6]).format('MM-DD-YYYY') : getRow[6],
      from_time: getRow[7],
      to_time: getRow[8],
      gate: getRow[9],
      equipment: getRow[10],
      picking_from: getRow[11],
      picking_to: getRow[12],
      vehicle_detail: getRow[13],
      additional_notes: getRow[14],
      location: getRow[15],
    };

    if (row.description) {
      // if (startDate > currentDate && endDate > currentDate) {
      if (projectDetails) {
        const memberDetails = await Member.getBy({
          UserId: loginUser.id,
          ProjectId,
          isActive: true,
          isDeleted: false,
        });
        if (memberDetails) {
          const lastIdValue = await DeliveryRequest.findOne({
            where: { ProjectId: memberDetails.ProjectId, isDeleted: false },
            order: [['DeliveryId', 'DESC']],
          });
          const newValue = JSON.parse(JSON.stringify(lastIdValue));
          if (newValue && newValue.DeliveryId !== null && newValue.DeliveryId !== undefined) {
            id = newValue.DeliveryId;
          }
          let startDate;
          let endDate;
          if (row.date) {
            startDate = moment(new Date(`${row.date} ${row.from_time}`).toUTCString()).add(
              -inputData.headers.timezoneoffset,
              'm',
            );
            endDate = moment(new Date(`${row.date} ${row.to_time}`).toUTCString()).add(
              -inputData.headers.timezoneoffset,
              'm',
            );
            startDate = startDate.toDate().toString();
            endDate = endDate.toDate().toString();
          } else {
            startDate = null;
            endDate = null;
          }

          const DeliverParam = {
            description: row.description,
            vehicleDetails: row.vehicle_detail ? row.vehicle_detail : '',
            notes: row.additional_notes ? row.additional_notes : '',
            DeliveryId: id + 1,
            deliveryStart: startDate,
            deliveryEnd: endDate,
            ProjectId,
            createdBy: memberDetails.id,
            isQueued: true,
            status: 'Pending',
          };
          let escort;
          if (row.is_escort_needed === 'escort needed') {
            escort = true;
          } else {
            escort = false;
          }
          DeliverParam.escort = escort;
          // const roleDetails = await Role.getBy('Project Admin');
          // const accountRoleDetails = await Role.getBy('Account Admin');
          // if (
          //   memberDetails.RoleId === roleDetails.id ||
          //   memberDetails.RoleId === accountRoleDetails.id
          // ) {
          //   DeliverParam.status = 'Approved';
          //   DeliverParam.approvedBy = memberDetails.id;
          //   DeliverParam.approved_at = new Date();
          // }
          const newDeliverData = await DeliveryRequest.createInstance(DeliverParam);
          const history = {
            DeliveryRequestId: newDeliverData.id,
            DeliveryId: newDeliverData.DeliveryId,
            MemberId: memberDetails.id,
            type: 'create',
            description: `${loginUser.firstName} ${loginUser.lastName} Created Delivery Booking, ${DeliverParam.description}.`,
          };
          await DeliverHistory.createInstance(history);
          const updateParam = {
            DeliveryId: newDeliverData.id,
            DeliveryCode: newDeliverData.DeliveryId,
            ProjectId,
          };
          if (row.responsible_company) {
            const responseData =
              await companyService.dfowAndCompanyForBulkUploadDeliveryRequest(inputData);
            const companiesData = responseData.newCompanyList;
            const companyParam = updateParam;
            const chosenCompany = companiesData.filter(
              (company) => company.companyName === row.responsible_company,
            );
            if (chosenCompany && chosenCompany.length > 0) {
              companyParam.CompanyId = chosenCompany[0].id;
              await DeliverCompany.createInstance(companyParam);
            } else {
              await DeliveryRequest.update(
                { isDeleted: true },
                {
                  where: { id: updateParam.DeliveryId },
                },
              );
            }
          }
          if (row.gate) {
            const gateParam = updateParam;
            const gateData = await Gates.findOne({
              where: { gateName: row.gate, ProjectId },
            });
            if (gateData) {
              gateParam.GateId = gateData.id;
              await DeliverGate.createInstance(gateParam);
            } else {
              await DeliveryRequest.update(
                { isDeleted: true },
                {
                  where: { id: updateParam.DeliveryId },
                },
              );
            }
          }
          if (row.equipment) {
            const equipmentParam = updateParam;
            equipmentData = await Equipments.findOne({
              where: { equipmentName: row.equipment, ProjectId },
              include: [
                {
                  required: false,
                  where: { isDeleted: false, isActive: true },
                  association: 'PresetEquipmentType',
                  attributes: ['id', 'equipmentType', 'isCraneType'],
                },
              ],
            });
            if (equipmentData) {
              if (equipmentData.PresetEquipmentType.isCraneType) {
                const requestParam = {};
                if (row.picking_from) {
                  requestParam.cranePickUpLocation = row.picking_from;
                }
                if (row.picking_to) {
                  requestParam.craneDropOffLocation = row.picking_to;
                }
                requestParam.isAssociatedWithCraneRequest = true;
                equipmentParam.EquipmentId = equipmentData.id;
                requestParam.requestType = 'deliveryRequestWithCrane';
                const payload = {
                  params: {
                    ProjectId,
                  },
                };
                await craneRequestService.lastCraneRequest(payload, (lastDetail, error1) => {
                  if (!error1) {
                    requestParam.CraneRequestId = lastDetail.CraneRequestId;
                  }
                });
                await DeliverEquipment.createInstance(equipmentParam);
                await DeliveryRequest.update(requestParam, {
                  where: { id: newDeliverData.id },
                });
              } else {
                equipmentParam.EquipmentId = equipmentData.id;
                await DeliverEquipment.createInstance(equipmentParam);
              }
            } else {
              await DeliveryRequest.update(
                { isDeleted: true },
                {
                  where: { id: updateParam.DeliveryId },
                },
              );
            }
          }
          if (row.location) {
            const locationData = await Locations.findOne({
              where: { locationPath: row.location, ProjectId },
            });
            if (locationData) {
              await DeliveryRequest.update(
                { LocationId: locationData.id },
                {
                  where: { id: updateParam.DeliveryId },
                },
              );
            } else {
              await DeliveryRequest.update(
                { isDeleted: true },
                {
                  where: { id: updateParam.DeliveryId },
                },
              );
            }
          }
          if (row.definable_feature_of_work) {
            const defineParam = updateParam;
            const defineData = await DeliverDefineWork.findOne({
              where: {
                DFOW: row.definable_feature_of_work,
                ProjectId,
              },
            });
            if (defineData) {
              defineParam.DeliverDefineWorkId = defineData.id;
              await DeliverDefine.createInstance(defineParam);
            } else {
              await DeliveryRequest.update(
                { isDeleted: true },
                {
                  where: { id: updateParam.DeliveryId },
                },
              );
            }
          }
          const persons = [];
          if (row.responsible_person) {
            let responsiblePerson = '';
            if (typeof row.responsible_person === 'object') {
              responsiblePerson = row.responsible_person.text;
            } else {
              responsiblePerson = row.responsible_person;
            }
            const memberParam = updateParam;
            const userData = await User.findOne({
              where: { email: responsiblePerson },
            });
            let memberData;
            if (userData) {
              memberData = await Member.findOne({
                where: { UserId: userData.id, ProjectId, isDeleted: false },
              });
            }
            if (memberData) {
              memberParam.MemberId = memberData.id;
              persons.push(memberParam.MemberId);
              await DeliveryPerson.createInstance(memberParam);
            } else {
              await DeliveryRequest.update(
                { isDeleted: true },
                {
                  where: { id: updateParam.DeliveryId },
                },
              );
            }
          }
          const loginMemberParam = updateParam;
          const loginMemberData = await Member.findOne({
            where: { UserId: +loginUser.id, ProjectId, isDeleted: false },
          });
          loginMemberParam.MemberId = loginMemberData.id;
          const memberExists = await DeliveryPerson.findOne({
            where: {
              MemberId: loginMemberParam.MemberId,
              ProjectId,
              DeliveryId: updateParam.DeliveryId,
            },
          });
          if (!memberExists) {
            persons.push(loginMemberParam.MemberId);
            await DeliveryPerson.createInstance(loginMemberParam);
          }
          if (newDeliverData) {
            createdNDR = await DeliveryRequest.getNDRData({
              id: newDeliverData.id,
            });
          }
          if (
            createdNDR &&
            createdNDR.description &&
            createdNDR.deliveryStart &&
            createdNDR.deliveryEnd &&
            createdNDR.LocationId &&
            createdNDR.memberDetails.length > 0 &&
            createdNDR.companyDetails.length > 0 &&
            createdNDR.defineWorkDetails.length > 0 &&
            createdNDR.gateDetails.length > 0 &&
            createdNDR.equipmentDetails.length > 0 &&
            createdNDR.escort !== null
          ) {
            if (
              equipmentData &&
              equipmentData.PresetEquipmentType &&
              equipmentData.PresetEquipmentType.isCraneType
            ) {
              if (row.picking_from && row.picking_to) {
                await DeliveryRequest.update(
                  { isAllDetailsFilled: false, isQueued: true },
                  {
                    where: { id: createdNDR.id },
                  },
                );
              }
            } else {
              await DeliveryRequest.update(
                { isAllDetailsFilled: true, isQueued: false },
                {
                  where: { id: createdNDR.id },
                },
              );
            }
            const notification = history;
            notification.ProjectId = ProjectId;
            notification.title = 'Delivery Booking Creation';
            const newNotification = await Notification.createInstance(notification);
            const adminData = await Member.findAll({
              where: {
                [Op.or]: [
                  {
                    [Op.and]: [
                      { ProjectId },
                      { isDeleted: false },
                      { [Op.or]: [{ RoleId: [2, 1] }] },
                      { id: { [Op.ne]: newNotification.MemberId } },
                    ],
                  },
                  { [Op.and]: [{ id: { [Op.in]: persons } }] },
                ],
              },
              include: [
                {
                  association: 'User',
                  attributes: ['id', 'firstName', 'lastName'],
                },
              ],
              attributes: ['id'],
            });
            const personData = await DeliveryPerson.findAll({
              where: { DeliveryId: history.DeliveryRequestId, isDeleted: false },
              include: [
                {
                  association: 'Member',
                  include: [
                    {
                      association: 'User',
                      attributes: ['id', 'email', 'firstName', 'lastName'],
                    },
                  ],
                  where: {
                    id: { [Op.ne]: newNotification.MemberId },
                    [Op.and]: {
                      RoleId: {
                        [Op.notIn]: [1, 2],
                      },
                    },
                  },
                },
              ],
              attributes: ['id'],
            });
            history.memberData = personData;
            history.adminData = adminData;
            history.firstName = loginUser.firstName;
            history.profilePic = loginUser.profilePic;
            history.createdAt = new Date();
            history.ProjectId = ProjectId;
            history.projectName = projectDetails.projectName;
            // here 3-(NotificationPreferenceItemId - When a new delivery/crane/concrete request added to the project)
            await notificationHelper.createDeliveryPersonNotification(
              adminData,
              [],
              projectDetails,
              newNotification,
              DeliveryPersonNotification,
              memberDetails,
              loginUser,
              3,
              'created a',
              'Delivery Request',
              `delivery Booking (${newDeliverData.DeliveryId} - ${newDeliverData.description})`,
              newDeliverData.id,
            );
            const checkMemberNotification = await NotificationPreference.findAll({
              where: {
                ProjectId,
                isDeleted: false,
              },
              attributes: [
                'id',
                'MemberId',
                'ProjectId',
                'ParentCompanyId',
                'NotificationPreferenceItemId',
                'instant',
                'dailyDigest',
              ],
              include: [
                {
                  association: 'NotificationPreferenceItem',
                  where: {
                    id: 3,
                    isDeleted: false,
                  },
                  attributes: ['id', 'description', 'inappNotification', 'emailNotification'],
                },
              ],
            });
            history.notificationPreference = checkMemberNotification;
            // here 3-(NotificationPreferenceItemId - When a new delivery/crane/concrete request added to the project)
            await pushNotification.sendDeviceToken(history, 3, ProjectId);
          }
        }
      }
    }
    if (ndrRecords.length - 1 === i) {
      console.log('success');
    }
  }
  parentPort.postMessage('success');
});
