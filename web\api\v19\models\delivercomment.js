module.exports = (sequelize, DataTypes) => {
  const DeliverComment = sequelize.define(
    'DeliverComment',
    {
      ProjectId: DataTypes.INTEGER,
      MemberId: DataTypes.INTEGER,
      DeliveryRequestId: DataTypes.INTEGER,
      publicSchemaId: {
        type: DataTypes.INTEGER,
      },
      isDeleted: DataTypes.BOOLEAN,
      comment: DataTypes.STRING,
    },
    {},
  );
  DeliverComment.associate = (models) => {
    // associations can be defined here
    DeliverComment.belongsTo(models.Member);
    DeliverComment.belongsTo(models.DeliveryRequest);
  };

  DeliverComment.getAll = async (attr) => {
    const newDeliverComment = await DeliverComment.findAll({
      where: { ...attr },
    });
    return newDeliverComment;
  };
  DeliverComment.createInstance = async (paramData) => {
    const newDeliverComment = await DeliverComment.create(paramData);
    return newDeliverComment;
  };
  return DeliverComment;
};
