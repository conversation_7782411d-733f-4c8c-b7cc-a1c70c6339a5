const { Router } = require('express');
const { BillingController } = require('../controllers');
const passportConfig = require('../config/passport');
const checkAdmin = require('../middlewares/checkAdmin');

const projectRoute = {
  get router() {
    const router = Router();

    router.post(
      '/payOnline',
      passportConfig.isAuthenticated,
      checkAdmin.isAccountAdmin,
      BillingController.payOnline,
    );
    router.get(
      '/listBilling',
      passportConfig.isAuthenticated,
      checkAdmin.isAccountAdmin,
      BillingController.getBillingInfo,
    );
    router.post(
      '/payOffline',
      passportConfig.isAuthenticated,
      checkAdmin.isAccountAdmin,
      BillingController.payOffline,
    );

    return router;
  },
};
module.exports = projectRoute;
