const status = require('http-status');
const { gateService } = require('../services');

const GateController = {
  async addGates(req, res, next) {
    gateService.addGates(req, async (gateDetail, error) => {
      if (error) {
        next(error);
      } else {
        res.status(status.CREATED).json({
          message: 'Gate added successfully.',
          data: gateDetail,
        });
      }
    });
  },
  async listGates(req, res, next) {
    gateService.listGates(req, async (gateDetail, error) => {
      if (error) {
        next(error);
      } else {
        gateService.lastGate(req, (lastDetail, error1) => {
          if (!error1) {
            res.status(status.OK).json({
              message: 'Gate Listed successfully.',
              data: gateDetail,
              lastId: lastDetail,
            });
          } else {
            next(error1);
          }
        });
      }
    });
  },
  async updateGates(req, res, next) {
    gateService.updateGates(req, async (projectDetail, error) => {
      if (error) {
        next(error);
      } else {
        let messageText = 'Gate Updated successfully.';
        if (req.body.isActive) {
          messageText = 'Gate Activated Successfully.';
        }
        res.status(status.OK).json({
          message: messageText,
          data: projectDetail,
        });
      }
    });
  },
  async deleteGates(req, res, next) {
    gateService.deleteGates(req, async (projectDetail, error) => {
      if (error) {
        next(error);
      } else {
        res.status(status.OK).json({
          message: 'Gate deleted successfully.',
          data: projectDetail,
        });
      }
    });
  },
  async getMappedRequests(req, res, next) {
    gateService.getMappedRequests(req, async (response, error) => {
      if (error) {
        next(error);
      } else {
        res.status(status.OK).json({
          message: 'Gate Mapped Bookings Listed Successfully.',
          data: {
            mappedRequest: response.mappedRequest,
            gates: response.gates,
          },
        });
      }
    });
  },
  async deactivateGate(req, res, next) {
    gateService.deactivateGate(req, async (response, error) => {
      if (error) {
        next(error);
      } else {
        res.status(status.OK).json({
          message: 'Gate Deactivated Successfully.',
        });
      }
    });
  },
};
module.exports = GateController;
