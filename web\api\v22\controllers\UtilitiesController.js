const status = require('http-status');
const UtilitiesService = require('../services/UtilitiesService')

const UtilitiesController = {
    async addUtilities(req, res, next) {
        UtilitiesService.addUtilities(req, async (Utilities, error) => {
            if (error) {
                next(error);
            } else {
                res.status(status.CREATED).json({
                    message: 'Utilities added successfully.',
                    data: Utilities,
                });
            }
        });
    },
    async listUtilities(req, res, next) {
        UtilitiesService.listUtilities(req, async (UtilitiesDetail, error) => {
            if (error) {
                next(error);
            } else {
                res.status(status.OK).json({
                    message: 'Utilities Listed successfully.',
                    data: UtilitiesDetail,
                });
            }
        });
    }
};
module.exports = UtilitiesController;
