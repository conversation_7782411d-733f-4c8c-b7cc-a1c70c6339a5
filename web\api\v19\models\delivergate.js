module.exports = (sequelize, DataTypes) => {
  const DeliverGate = sequelize.define(
    'DeliverGate',
    {
      DeliveryId: DataTypes.INTEGER,
      isDeleted: DataTypes.BOOLEAN,
      DeliveryCode: DataTypes.INTEGER,
      publicSchemaId: {
        type: DataTypes.INTEGER,
      },
      GateId: DataTypes.INTEGER,
      ProjectId: DataTypes.INTEGER,
      isActive: DataTypes.BOOLEAN,
    },
    {},
  );
  DeliverGate.associate = (models) => {
    // associations can be defined here
    DeliverGate.belongsTo(models.DeliveryRequest, {
      as: 'deliveryrequest',
      foreignKey: 'DeliveryId',
    });
    DeliverGate.belongsTo(models.Gates, {
      as: 'Gates',
      foreignKey: 'GateId',
    });
    DeliverGate.belongsTo(models.Gates);
  };
  DeliverGate.createInstance = async (paramData) => {
    const newDeliverGate = await DeliverGate.create(paramData);
    return newDeliverGate;
  };
  return DeliverGate;
};
