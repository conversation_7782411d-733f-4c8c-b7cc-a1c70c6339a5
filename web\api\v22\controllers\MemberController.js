const status = require('http-status');
const { memberService } = require('../services');
const { Role } = require('../models');

const MemberController = {
  async checkExistMember(req, res, next) {
    try {
      await memberService.checkExistMember(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.OK).json(response);
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async inviteMembers(req, res, next) {
    try {
      await memberService.inviteMembers(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.CREATED).json({
            message: 'Invites sent Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async resendInviteLink(req, res, next) {
    try {
      await memberService.resendInviteLink(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.CREATED).json({
            message: 'Success! Invite link resent.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async getOverViewDetail(req, res, next) {
    try {
      await memberService.getOverViewDetail(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.OK).json({
            message: 'Member listed Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async updateUserProfile(req, res, next) {
    try {
      await memberService.updateUserProfile(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.OK).json({
            message: 'Profile Updated Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async editMember(req, res, next) {
    try {
      await memberService.editMemberDetail(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.OK).json({
            message: 'Member Updated Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async updateInviteMember(req, res, next) {
    try {
      await memberService.updateInviteMember(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.OK).json({
            message: 'Member Updated Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async deleteMember(req, res, next) {
    try {
      await memberService.deleteMember(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.OK).json({
            message: 'Member Deleted Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async listMember(req, res, next) {
    try {
      await memberService.listMember(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          memberService.lastMember(req, (lastDetail, error1) => {
            if (!error1) {
              res.status(status.OK).json({
                message: 'Member listed Successfully.',
                data: response,
                lastId: lastDetail,
              });
            } else {
              next(error1);
            }
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async searchMember(req, res, next) {
    try {
      await memberService.searchMember(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.OK).json(response);
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async searchAutoApproveMember(req, res, next) {
    try {
      await memberService.searchAutoApproveMember(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.OK).json(response);
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async searchAllMember(req, res, next) {
    try {
      await memberService.searchAllMember(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.OK).json(response);
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async getUserDetail(req, res, next) {
    try {
      await memberService.getUserDetail(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.OK).json(response);
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async listAllMember(req, res, next) {
    try {
      await memberService.listAllMember(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.OK).json({
            message: 'Member listed Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async getRoles(req, res, next) {
    try {
      const roleList = await Role.getAll({ frontend: true });
      roleList.sort((a, b) => (a.roleName.toLowerCase() > b.roleName.toLowerCase() ? 1 : -1));
      res.status(status.OK).json({
        message: 'Role list.',
        data: roleList,
      });
    } catch (e) {
      next(e);
    }
  },
  /**
   *
   * @param {super admin token} req
   * @param {members array lists,status,message} res
   * @param {return if any exception} next
   */
  async getAllMemberLists(req, res, next) {
    try {
      const members = await memberService.getAllMemberLists(req);
      if (members) {
        res.status(status.OK).json({
          status: 200,
          message: 'Members listed Successfully.',
          data: members.memberLists,
          count: members.count,
        });
      } else {
        res.status(status.OK).json({
          status: 200,
          message: 'Members listed successfully.',
          data: [],
          count: 0,
        });
      }
    } catch (e) {
      next(e);
    }
  },
  /**
   *
   * @param {super admin token,member id} req
   * @param {member - basic info,company info,status,message} res
   * @param {return if any exception} next
   */
  async getMemberDetail(req, res, next) {
    try {
      const getMemberDetail = await memberService.getMemberDetail(req);
      if (getMemberDetail) {
        res.status(status.OK).json({
          message: 'Member Detail Viewed Successfully.',
          data: getMemberDetail,
        });
      } else {
        res.status(status.OK).json({
          message: 'Members listed successfully.',
          data: {},
        });
      }
    } catch (e) {
      next(e);
    }
  },
  /**
   *
   * @param {super admin token,member id} req
   * @param {projects list,status,message} res
   * @param {return if any exception} next
   */
  async getMemberProjects(req, res, next) {
    try {
      const projectLists = await memberService.getMemberProjects(req);
      if (projectLists) {
        res.status(status.OK).json({
          message: 'Member Projects listed Successfully.',
          data: projectLists,
          count: projectLists.length,
        });
      } else {
        res.status(status.OK).json({
          message: 'Members Projects listed successfully.',
          data: [],
          count: 0,
        });
      }
    } catch (e) {
      next(e);
    }
  },
  /**
   *
   * @param {super admin token, new password for member,member id} req
   * @param {status,message} res
   * @param {return if any exception} next
   */
  async changeMemberPassword(req, res, next) {
    try {
      const updatedPassword = await memberService.changeMemberPassword(req);
      if (updatedPassword) {
        res.status(status.OK).json({
          message: 'Password updated successfully.',
        });
      } else {
        res.status(status.UNPROCESSABLE_ENTITY).json({
          message: 'Cannot able to updated password.',
        });
      }
    } catch (e) {
      next(e);
    }
  },
  /**
   *
   * @param {super admin token,member id} req
   * @param {status,message,projects list} res
   * @param {return if any exception} next
   */
  async updateMemberProfile(req, res, next) {
    try {
      const updatedMemberProfile = await memberService.updateMemberProfile(req);
      if (updatedMemberProfile && !updatedMemberProfile.error) {
        res.status(status.OK).json({
          status: 200,
          message: 'Member Profile Updated Successfully.',
        });
      } else {
        res.status(status.UNPROCESSABLE_ENTITY).json({
          status: 422,
          message: updatedMemberProfile.message,
        });
      }
    } catch (e) {
      next(e);
    }
  },
  /**
   *
   * @param {super admin token,member id} req
   * @param {status,message} res
   * @param {return if any exception} next
   */
  async updateMemberProjectStatus(req, res, next) {
    try {
      const statusUpdated = await memberService.updateMemberProjectStatus(req);
      if (req.body && req.body.userStatus) {
        if (statusUpdated) {
          res.status(status.OK).json({
            status: 200,
            message: 'User Status Updated Successfully.',
          });
        } else {
          res.status(status.UNPROCESSABLE_ENTITY).json({
            status: 422,
            message: 'Cannot able to updated user status',
          });
        }
      } else if (req.body && !req.body.userStatus) {
        res.status(status.OK).json({
          status: 200,
          message: 'Members Project Status Updated Successfully.',
        });
      } else {
        res.status(status.UNPROCESSABLE_ENTITY).json({
          status: 422,
          message: 'Cannot able to updated members project status',
        });
      }
    } catch (e) {
      next(e);
    }
  },
  /**
   *
   * @param {super admin token} req
   * @param {members array,status,message} res
   * @param {return if any exception} next
   */
  async getAllMemberListsForAssignProject(req, res, next) {
    try {
      const membersList = await memberService.getAllMemberListsForAssignProject(req);
      if (membersList) {
        res.status(status.OK).json({
          status: 200,
          message: 'Members Listed Successfully.',
          data: membersList,
        });
      } else {
        res.status(status.UNPROCESSABLE_ENTITY).json({
          status: 422,
          message: 'Cannot able to list members',
        });
      }
    } catch (e) {
      next(e);
    }
  },
  async deactivateMember(req, res, next) {
    try {
      await memberService.deactivateMember(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.OK).json({
            message: 'Member Deactivated Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async activateMember(req, res, next) {
    try {
      await memberService.activateMember(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.OK).json({
            message: 'Member Activated Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async getMappedRequests(req, res, next) {
    try {
      await memberService.getMappedRequests(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.OK).json({
            message: 'Member Mapped Bookings Listed Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async getOnboardingInviteLink(req, res, next) {
    try {
      await memberService.getOnboardingInviteLink(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.OK).json({
            message: 'Onboarding Link Sent Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async getMemberData(req, res, next) {
    try {
      await memberService.getMemberData(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.OK).json({
            message: 'Member Data Sent Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async listRetoolMembers(req, res) {
    try {
      const members = await memberService.listRetoolMembers(req);
      res.status(200).json({
        message: 'Member listed Successfully.',
        data: members,
      });
    } catch (e) {
      res.status(500).json({ error: e.message });
    }
  },
  async listRegisteredMembers(req, res, next) {
    try {
      await memberService.listRegisteredMembers(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.OK).json({
            message: 'Member listed Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },

  async listGuestMembers(req, res, next) {
    try {
      await memberService.listGuestMembers(req, (response, error) => {
        if (error) {
          next(error);
        } else {
          res.status(status.OK).json({
            message: 'Member listed Successfully.',
            data: response,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async addGuestAsMember(req, res) {
    try {
      const user = await memberService.addGuestAsMember(req);
      if (user) {
        return res.status(200).json(user);
      }
    } catch (error) {
      return res.status(500).json({ message: 'Internal server error', error });
    }
  },
  async rejectGuestRequest(req, res) {
    try {
      const user = await memberService.rejectGuestRequest(req);
      if (user) {
        return res.status(200).json(user);
      }
    } catch (error) {
      return res.status(500).json({ message: 'Internal server error', error });
    }
  },
};
module.exports = MemberController;
