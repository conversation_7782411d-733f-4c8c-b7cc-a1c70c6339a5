const { Router } = require('express');
const { ProjectSettingsController } = require('../controllers');
const passportConfig = require('../config/passport');

const ProjectSettingsRoute = {
  get router() {
    const router = Router();
    router.get(
      '/get_projectDetails',
      passportConfig.isAuthenticated,
      ProjectSettingsController.getProjectSettings,
    );

    router.put(
      '/',
      passportConfig.isAuthenticated,
      ProjectSettingsController.updateProjectSettings,
    );

    router.get('/set_default_delivery_window', ProjectSettingsController.setDefaultDeliveryWindow);
    router.get('/get_guest_projectDetails', ProjectSettingsController.getProjectSettings);
    return router;
  },
};
module.exports = ProjectSettingsRoute;
