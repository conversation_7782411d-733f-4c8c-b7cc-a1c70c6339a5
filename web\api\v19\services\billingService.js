const status = require('http-status');
const { BillingHistory, Enterprise, Sequelize } = require('../models');
let { User } = require('../models');
const MAILER = require('../mailer');
const ApiError = require('../helpers/apiError');
const helper = require('../helpers/domainHelper');
const stripeService = require('./stripeService');
const awsConfig = require('../middlewares/awsConfig');

const { Op } = Sequelize;
let publicUser;
let publicMember;

const billingService = {
  async uploadProfile(inputData, done) {
    awsConfig.singleUpload(inputData, async (result, err) => {
      if (!err) {
        done({ imageUrl: result[0].Location }, false);
      } else {
        done(null, err);
      }
    });
  },
  async payOnline(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const getOnlinePayment = await stripeService.payOnline(inputData);
      const receiptUrl = getOnlinePayment ? getOnlinePayment.charges.data[0].receipt_url : null;
      const { ParentCompanyId } = inputData.query;
      if (ParentCompanyId) {
        const enterpriseValue = await Enterprise.findOne({ where: { ParentCompanyId } });
        if (enterpriseValue) {
          const billingData = {
            EnterpriseId: enterpriseValue.id,
            status: 'completed',
            paymentMethod: 'online',
            amount: inputData.body.amount,
            UserId: inputData.user.id,
            lastPayment: new Date(),
            receiptUrl,
          };
          const billingDetails = await BillingHistory.create(billingData);
          const adminDetails = await User.findAll({
            where: Sequelize.or({ userType: ['super admin', 'folloit admin'] }),
          });
          billingDetails.firstName = inputData.user.firstName;
          adminDetails.forEach(async (element) => {
            billingDetails.email = element.email;
            this.sendMail(billingDetails, 'accountadminpay', async (info, err) => {
              if (err) {
                const newError = new ApiError(err.message, status.BAD_REQUEST);
                done(null, newError);
              }
              done(billingDetails, false);
            });
          });
        }
      }
    } catch (e) {
      done(null, e);
    }
  },
  async returnProjectModel() {
    const modelData = await helper.returnProjectModel();
    publicUser = modelData.User;
    publicMember = modelData.Member;
  },
  async getBillingInfo(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { ParentCompanyId } = inputData.query;
      if (ParentCompanyId) {
        const enterpriseValue = await Enterprise.findOne({ where: { ParentCompanyId } });
        if (enterpriseValue) {
          const billingDetails = await BillingHistory.findAndCountAll({
            include: [
              {
                association: 'User',
                attributes: ['id', 'firstName', 'lastName'],
              },
              {
                association: 'Enterprise',
                attributes: ['id', 'name'],
              },
            ],

            where: { EnterpriseId: enterpriseValue.id },
          });
          billingDetails.nextPayDet = await BillingHistory.findOne({
            order: [['lastPayment', 'DESC']],
            limit: 1,
          });
          const newPayDate = new Date(billingDetails.nextPayDet.lastPayment);
          newPayDate.setFullYear(newPayDate.getFullYear() + 1);

          billingDetails.nextPayDet.nextPayment = newPayDate;
          done(billingDetails, false);
        }
      }
    } catch (e) {
      done(null, e);
    }
  },
  async getDynamicModel(inputData) {
    await this.returnProjectModel();
    let { domainName } = inputData.user;
    const incomeData = inputData;
    let enterpriseValue;
    let ProjectId;
    const ParentCompanyId = inputData.body.ParentCompanyId
      ? inputData.body.ParentCompanyId
      : inputData.params.ParentCompanyId;
    let domainEnterpriseValue;
    if (domainName) {
      domainEnterpriseValue = await Enterprise.findOne({
        where: { name: domainName.toLowerCase() },
      });
      if (!domainEnterpriseValue) {
        domainName = '';
      }
    }
    if (!domainName && ParentCompanyId !== undefined && ParentCompanyId !== 'undefined') {
      const { email } = inputData.user;
      let userData;
      if (email) {
        userData = await publicUser.findOne({ where: { email } });
      }
      if (userData) {
        const memberData = await publicMember.findOne({
          where: { UserId: userData.id, RoleId: { [Op.ne]: 4 }, isDeleted: false },
        });
        if (memberData) {
          if (memberData.isAccount) {
            enterpriseValue = await Enterprise.findOne({
              where: { id: memberData.EnterpriseId, status: 'completed' },
            });
            if (enterpriseValue) {
              domainName = enterpriseValue.name.toLowerCase();
            }
          } else {
            enterpriseValue = await Enterprise.findOne({
              where: { ParentCompanyId, status: 'completed' },
            });
            if (enterpriseValue) {
              domainName = enterpriseValue.name.toLowerCase();
            }
          }
        } else {
          enterpriseValue = await Enterprise.findOne({
            where: { ParentCompanyId, status: 'completed' },
          });
          if (enterpriseValue) {
            domainName = enterpriseValue.name.toLowerCase();
          }
        }
      }
    }
    const modelObj = await helper.getDynamicModel(domainName);
    User = modelObj.User;

    if (enterpriseValue) {
      const newUser = await User.findOne({ where: { email: inputData.user.email } });
      incomeData.user = newUser;
    }
    return ProjectId;
  },
  async payOffline(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { ParentCompanyId } = inputData.query;
      if (ParentCompanyId) {
        const enterpriseValue = await Enterprise.findOne({ where: { ParentCompanyId } });
        if (enterpriseValue) {
          const billingData = {
            EnterpriseId: enterpriseValue.id,
            status: 'pending',
            paymentMethod: 'offline',
            amount: inputData.body.amount,
            UserId: inputData.user.id,
            lastPayment: new Date(),
          };
          const billingDetails = await BillingHistory.create(billingData);
          const adminDetails = await User.findAll({
            where: Sequelize.or({ userType: ['super admin', 'folloit admin'] }),
          });
          billingDetails.firstName = inputData.user.firstName;
          adminDetails.forEach(async (element) => {
            billingDetails.email = element.email;
            this.sendMail(billingDetails, 'accountadminpay', async (info, err) => {
              if (err) {
                const newError = new ApiError(err.message, status.BAD_REQUEST);
                done(null, newError);
              }
              done(billingDetails, false);
            });
          });
        }
      }
    } catch (e) {
      done(null, e);
    }
  },
  async sendMail(userData, mailData, done) {
    await MAILER.sendMail(
      userData,
      mailData,
      'Account Admin Payment',
      'Account Admin Payment',
      (info, err) => {
        if (err) {
          const newError = new ApiError(err.message, status.BAD_REQUEST);
          done(null, newError);
        } else {
          done(userData, false);
        }
      },
    );
  },
};
module.exports = billingService;
