module.exports = (sequelize, DataTypes) => {
  const Role = sequelize.define(
    'Role',
    {
      roleName: DataTypes.STRING,
      frontend: DataTypes.BOOLEAN,
      isDeleted: DataTypes.BOOLEAN,
    },
    {},
  );
  Role.getAll = async (attr) => {
    const role = await Role.findAll({ where: { ...attr } });
    return role;
  };
  Role.getBy = async (attr) => {
    const role = await Role.findOne({ where: { roleName: attr } });
    return role;
  };
  Role.bulkCreateRoles = async (userRoles) => {
    await Role.bulkCreate(userRoles);
  };

  return Role;
};
