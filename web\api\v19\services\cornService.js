/* eslint-disable no-await-in-loop */
const moment = require('moment');
const momenttz = require('moment-timezone');
const cron = require('node-cron');
const fs = require('fs');
const _ = require('lodash');
const stripe = require('stripe')(process.env.STRIPE_API_KEY, {
  maxNetworkRetries: 3,
});
const moment1 = require('moment-timezone');
const {
  Project,
  DeliveryRequest,
  CraneRequest,
  Sequelize,
  DigestNotification,
  Member,
  User,
  ConcreteRequest,
  TimeZone,
  SchedulerReport,
  SchedulerDateRange,
} = require('../models');
const {
  convertToCron,
  convertToCronMonthly,
  convertToCronYearly,
} = require('../helpers/queryBuilderExternal');

const { Op } = Sequelize;

const MAILER = require('../mailer');
const awsConfig = require('../middlewares/awsConfig');

const cornService = {
  async checkOverDue() {
    let projectDetail;
    if (process.env.NODE_ENV !== 'prod') {
      projectDetail = await Project.findAll({
        include: [
          {
            association: 'ParentCompany',
            attributes: ['id'],
            include: [
              {
                association: 'Company',
                where: { isParent: true },
                attributes: [
                  'companyName',
                  'address',
                  'secondAddress',
                  'country',
                  'city',
                  'state',
                  'zipCode',
                ],
                required: true,
              },
            ],
          },
          {
            association: 'userDetails',
            attributes: ['email', 'firstName', 'lastName', 'stripeCustomerId'],
          },
          {
            subQuery: false,
            association: 'StripeSubscription',
          },
          {
            subQuery: false,
            association: 'stripePlan',
            include: ['Plan'],
          },
        ],
        where: {
          [Op.and]: {
            [Op.or]: [{ isAccount: null }, { isAccount: false }],
            isSuperAdminCreatedProject: false,
          },
        },
        order: [['id', 'ASC']],
      });
    } else {
      projectDetail = await Project.findAll({
        include: [
          {
            association: 'ParentCompany',
            attributes: ['id'],
            include: [
              {
                association: 'Company',
                where: { isParent: true },
                attributes: [
                  'companyName',
                  'address',
                  'secondAddress',
                  'country',
                  'city',
                  'state',
                  'zipCode',
                ],
                required: true,
              },
            ],
          },
          {
            association: 'userDetails',
            attributes: ['email', 'firstName', 'lastName', 'stripeCustomerId'],
          },
          {
            subQuery: false,
            association: 'StripeSubscription',
          },
          {
            subQuery: false,
            association: 'stripePlan',
            include: ['Plan'],
          },
        ],
        where: {
          [Op.and]: {
            [Op.or]: [{ isAccount: null }, { isAccount: false }],
            id: { [Op.ne]: 7 },
            isSuperAdminCreatedProject: false,
          },
        },
        order: [['id', 'ASC']],
      });
    }
    if (projectDetail.length > 0) {
      this.checkProject(projectDetail, 0, [], [], [], [], async (response, err) => {
        if (!err) {
          // if (response.remainder.length > 0) {
          //   await this.sendMail(response.remainder, 0);
          // }
          // if (response.trialExpired.length > 0) {
          //   await this.sendTrialExpiredMail(response.trialExpired, 0);
          // }
          if (response.projectSubscription.length > 0) {
            await this.sendProjectSubscription(response.projectSubscription, 0);
          }
        }
      });
    }
  },
  async checkNDRExpiration() {
    await DeliveryRequest.update(
      { status: 'Expired' },
      {
        where: {
          status: 'Pending',
          deliveryStart: {
            [Op.lte]: new Date(),
          },
        },
        order: [['id', 'ASC']],
      },
    );
    await CraneRequest.update(
      { status: 'Expired' },
      {
        where: {
          status: 'Pending',
          craneDeliveryStart: {
            [Op.lte]: new Date(),
          },
        },
        order: [['id', 'ASC']],
      },
    );
    // await ConcreteRequest.update(
    //   { status: 'Expired' },
    //   {
    //     where: {
    //       status: 'Tentative',
    //       concretePlacementStart: {
    //         [Op.lte]: new Date(),
    //       },
    //     },
    //     order: [['id', 'ASC']],
    //   },
    // );
  },
  async sendMail(remainderData, index) {
    const userData = remainderData[index];
    const mailData = {
      email: userData.userDetails.email,
      firstName: userData.userDetails.firstName,
      projectName: userData.projectName,
      planName: 'Trial Plan',
    };
    const trialEndingDate = moment(userData.subscribedOn).add(14, 'days').format('DD-MM-YYYY');
    await MAILER.sendMail(
      mailData,
      'trailPlanEndingRemainder',
      `Follo trial is ending on ${trialEndingDate} - Oh no, upgrade time
      ${userData.projectName}`,
      'Trail Plan Ending Remainder Notification',
      async () => {
        await Project.update({ mailSendOn: new Date() }, { where: { id: userData.id } });
        if (index < remainderData.length - 1) {
          this.sendMail(remainderData, index + 1);
        } else {
          return { status: true };
        }
      },
    );
  },
  async sendTrialExpiredMail(trialExpired, index) {
    const userData = trialExpired[index];
    const mailData = {
      email: userData.userDetails.email,
      firstName: userData.userDetails.firstName,
      projectName: userData.projectName,
      planName: 'Trial Plan',
    };
    await MAILER.sendMail(
      mailData,
      'trailPlanExpired',
      `Follo trial is over by ${moment(userData.subscribedOn)
        .add(14, 'days')
        .utc()
        .format('DD-MM-YYYY hh:mm:ss a zz')} - Oh no,
       upgrade time ${userData.projectName}`,
      'Trail Plan Expired Notification',
      async () => {
        if (index < trialExpired.length - 1) {
          this.sendTrialExpiredMail(trialExpired, index + 1);
        } else {
          return { status: true };
        }
      },
    );
  },
  async sendProjectSubscription(projectSubscription, index) {
    const userData = projectSubscription[index];
    // const invoice = await stripe.invoices.retrieveUpcoming({
    //   customer: userData.userDetails.stripeCustomerId,
    // });
    const companyInfo = userData.ParentCompany.Company[0];
    const invoiceNo = Math.floor(Math.random() * 324324 + 1);
    const mailData = {
      email: userData.userDetails.email,
      firstName: userData.userDetails.firstName,
      lastName: userData.userDetails.lastName,
      invoiceNo,
      footerInvoiceNo: invoiceNo,
      invoiceDate: moment().format('DD-MM-YYYY'),
      paymentDate: 'NEXT 5 DAYS',
      address: companyInfo.address,
      city: companyInfo.city,
      state: companyInfo.state,
      zipcode: companyInfo.zipCode,
      country: companyInfo.country,
      projectName: userData.projectName,
      projectStartDate: moment(userData.startDate).format('DD-MM-YYYY'),
      projectEndDate: moment(userData.endDate).format('DD-MM-YYYY'),
      amount: (userData.stripePlan.stripeAmount / 100).toFixed(2),
      quantity: 1,
      subTotal: (userData.stripePlan.stripeAmount / 100).toFixed(2),
      total: (userData.stripePlan.stripeAmount / 100).toFixed(2),
      invoiceSubTotal: (userData.stripePlan.stripeAmount / 100).toFixed(2),
      planName: 'Project Plan',
    };
    await MAILER.sendMail(
      mailData,
      'projectSubscriptionInvoice',
      `Hurry Up - Your Project plan going to Expire for the ${userData.projectName} on ${moment(
        userData.endDate,
      ).format('DD/MM/YYYY')}`,
      'Project Subscription Invoice Notification',
      async () => {
        await Project.update({ mailSendOn: new Date() }, { where: { id: userData.id } });
        if (index < projectSubscription.length - 1) {
          this.sendProjectSubscription(projectSubscription, index + 1);
        } else {
          return { status: true };
        }
      },
    );
  },
  async checkProject(
    projectDetail,
    index,
    overDueProject,
    remainderProject,
    expiredTrialPlans,
    remainderProjectSubscription,
    done,
  ) {
    const element = projectDetail[index];
    // if (element.id === 373) {
    //   console.log('=========================================');
    //   remainderProjectSubscription.push(element);
    // }

    // trail period over - status is 'trailoverdue'
    // subscribed plan period over - status is 'overdue'
    if (element.status !== 'overdue' && element.status !== 'trialoverdue') {
      if (element.PlanId === 2 || element.PlanId === 3) {
        let stripeDetail;
        if (element && element.StripeSubscription && element.StripeSubscription.subscriptionId) {
          stripeDetail = await stripe.subscriptions.retrieve(
            element.StripeSubscription.subscriptionId,
          );
        }
        if (stripeDetail && stripeDetail.status === 'canceled') {
          await Project.update({ status: 'overdue' }, { where: { id: element.id } });
          overDueProject.push(element.id);
        } else {
          if (element.StripeSubscription !== null) {
            if (
              element.StripeSubscription.subscriptionId !== '' &&
              element.StripeSubscription.subscriptionId !== null
            ) {
              const subDetail = await stripe.subscriptions.retrieve(
                element.StripeSubscription.subscriptionId,
              );
              if (
                subDetail.collection_method === 'past_due' ||
                subDetail.collection_method === 'unpaid' ||
                subDetail.collection_method === 'canceled'
              ) {
                await Project.update({ status: 'overdue' }, { where: { id: element.id } });
                overDueProject.push(element.id);
              }
            } else {
              await Project.update({ status: 'overdue' }, { where: { id: element.id } });
              overDueProject.push(element.id);
            }
          } else {
            // project name - UCI ICMC , id - 31 => One year subscription - End date:11/24/2022
            // project name - Onboarding Tower , id - 33 => Two year subscription - End date:12/15/2023
            // project name - AMR , id - 32 => End date:12/31/2022
            // project name - Sand Island WWTP Secondary Treatment Phase I , id - 35 => End date:  March 1st 2026
            // project name - UW IEB , id - 52 => End Date - June 28th 2024
            // project name - SJVA - San Jacinto Valley Academy , id- 79 ==>End Date -oct 1 2022
            if (
              +element.id === 31 ||
              +element.id === 32 ||
              +element.id === 33 ||
              +element.id === 35 ||
              +element.id === 52
              // ||
              // +element.id === 79
            ) {
              const endDate = moment(element.endDate).format('YYYY-MM-DD');
              const todayDate = moment().format('YYYY-MM-DD');
              if (endDate === todayDate) {
                await Project.update({ status: 'overdue' }, { where: { id: element.id } });
                overDueProject.push(element.id);
              }
            }
            if (
              +element.id !== 31 &&
              +element.id !== 32 &&
              +element.id !== 33 &&
              +element.id !== 35 &&
              +element.id !== 52
              // &&
              // +element.id !== 79
            ) {
              const endDate = new Date(element.endDate);
              const todayDate = new Date();
              if (todayDate === endDate) {
                await Project.update({ status: 'overdue' }, { where: { id: element.id } });
                overDueProject.push(element.id);
              }
            }
          }
          const currentDate = moment().format('YYYY-MM-DD');
          const splittedcurrentDate = currentDate.split('-');
          const currentDate1 = moment([
            splittedcurrentDate[0],
            splittedcurrentDate[1] - 1,
            splittedcurrentDate[2],
          ]);
          const projectEndDate = moment(element.endDate).format('YYYY-MM-DD');
          const splittedProjectEndDate = projectEndDate.split('-');
          const projectEndDate1 = moment([
            splittedProjectEndDate[0],
            splittedProjectEndDate[1] - 1,
            splittedProjectEndDate[2],
          ]);
          if (projectEndDate1.diff(currentDate1, 'days') === 5 && !element.mailSendOn) {
            remainderProjectSubscription.push(element);
          }
        }
      } else if (element.PlanId === 1) {
        let subDetail;
        if (element && element.StripeSubscription && element.StripeSubscription.subscriptionId) {
          subDetail = await stripe.subscriptions.retrieve(
            element.StripeSubscription.subscriptionId,
          );
        }
        if (subDetail && subDetail.status === 'canceled') {
          await Project.update({ status: 'trialoverdue' }, { where: { id: element.id } });
          overDueProject.push(element.id);
        } else {
          const startDate = new Date(element.subscribedOn).getTime();
          const mailSendDate = new Date(element.mailSendOn).getTime();
          const currentDate = new Date().getTime();
          const diffTime = Math.abs(currentDate - startDate);
          const diffMailTime = Math.abs(mailSendDate - startDate);
          const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
          const mailDays = Math.ceil(diffMailTime / (1000 * 60 * 60 * 24));
          if (diffDays >= 12 && diffDays < 14) {
            element.diffDays = 14 - diffDays;
            if (mailDays !== diffDays) {
              remainderProject.push(element);
            }
          }
          // project name - Kaiser MOA Lab Remediation , id - 20 => 75 days trial period
          // project name - John Sealy Modernization Phase 3 , id - 34 => End Date - October 31st 2023
          // project name - Chapman Place , id - 37 => End Date - March 25th 2022
          // project name - American Airlines LAX Terminal 4 Headhouse and Concourse , id - 38 => End Date - December 31st 2022
          // project name - P-290 Earth Covered Magazines , id - 43 => End Date - April 15th 2022
          // project name - Caltech Resnick Sustainability Center , id - 44 => End Date - April 1st 2025
          // project name - Travis County Civil and Family Courts Facility , id - 27 => End Date - June 17th 2022
          // project name - UNCH Surgical Tower (Test) , id - 53 => End Date - July 4th 2022
          // project name - WKBH , id - 55 => End Date - July 31st 2022
          // project name - Hawaii County Emergency Call Center , id - 56 => End Date - June 30th 2023
          if (
            diffDays >= 14 &&
            +element.id !== 20 &&
            +element.id !== 34 &&
            +element.id !== 37 &&
            +element.id !== 38 &&
            +element.id !== 43 &&
            +element.id !== 44 &&
            +element.id !== 27 &&
            +element.id !== 53 &&
            +element.id !== 55 &&
            +element.id !== 56
          ) {
            if (subDetail) {
              if (
                subDetail &&
                subDetail.status !== 'trialing' &&
                (moment(new Date(subDetail.trial_end * 1000)).isBefore(
                  moment().format('DD-MM-YYYY'),
                ) ||
                  moment(new Date(subDetail.trial_end * 1000)).isSame(
                    moment().format('DD-MM-YYYY'),
                  ))
              ) {
                await Project.update({ status: 'trialoverdue' }, { where: { id: element.id } });
                overDueProject.push(element.id);
              }
            } else {
              await Project.update({ status: 'trialoverdue' }, { where: { id: element.id } });
              overDueProject.push(element.id);
            }
          }
          if (element.id === 20 && diffDays >= 75) {
            await Project.update({ status: 'trialoverdue' }, { where: { id: element.id } });
            overDueProject.push(element.id);
          }
          if (
            element.id === 37 ||
            element.id === 38 ||
            element.id === 43 ||
            element.id === 44 ||
            element.id === 34 ||
            element.id === 27 ||
            element.id === 53 ||
            element.id === 55 ||
            element.id === 56
          ) {
            const endDate = moment(element.endDate).format('YYYY-MM-DD');
            const todayDate = moment().format('YYYY-MM-DD');
            if (endDate === todayDate) {
              await Project.update({ status: 'trialoverdue' }, { where: { id: element.id } });
              overDueProject.push(element.id);
            }
          }
          if (diffDays === 14) {
            expiredTrialPlans.push(element);
          }
        }
      }
    }
    if (index < projectDetail.length - 1) {
      this.checkProject(
        projectDetail,
        index + 1,
        overDueProject,
        remainderProject,
        expiredTrialPlans,
        remainderProjectSubscription,
        (response, err) => {
          if (!err) {
            done(response, false);
          } else {
            done(null, err);
          }
        },
      );
    } else {
      done(
        {
          overdue: overDueProject,
          remainder: remainderProject,
          trialExpired: expiredTrialPlans,
          projectSubscription: remainderProjectSubscription,
        },
        false,
      );
    }
  },
  async checkDailyDigestEmailNotification() {
    const timeZones = await TimeZone.findAll({
      where: { isDeleted: false },
    });
    for (let index = 0; index < timeZones.length; index += 1) {
      const object = timeZones[index];
      const getTimeFormat = moment().tz(object.timezone).format('hh:mm A');
      const getMembers = await Member.findAll({
        where: {
          TimeZoneId: object.id,
          isDeleted: false,
        },
        include: {
          association: 'User',
          isDeleted: false,
          attributes: ['id', 'email'],
        },
      });
      if (getMembers.length > 0) {
        for (let index1 = 0; index1 < getMembers.length; index1 += 1) {
          const member = getMembers[index1];
          member.memberTimeFormat = member.time + member.timeFormat;
          if (
            moment(member.memberTimeFormat, 'hh:mm A').format('LT') ===
            moment(getTimeFormat, 'hh:mm A').format('LT')
          ) {
            const memberDigestNotification = await DigestNotification.findAll({
              where: {
                isDeleted: false,
                isSent: false,
                MemberId: member.id,
                ProjectId: member.ProjectId,
              },
            });
            await this.sendEmail(member, memberDigestNotification);
          }
        }
      }
    }
  },
  async sendEmail(memberData, memberDigestNotification) {
    if (memberDigestNotification.length > 0) {
      const projectData = await Project.findOne({
        where: { isDeleted: false, id: memberData.ProjectId },
      });
      let mailContent = `
                    <tr>
                      <td style="padding:0px 30px 0px;">
                        <h1 style="color: #070707;font-size:15px;margin-bottom:0px;">
                          ${projectData.projectName}
                          </h1>
                          $mailContentData
                      </td>
                    </tr>
                    `;
      let getMailContentData;
      await memberDigestNotification.map(async (notification, index) => {
        if (index === 0) {
          getMailContentData = notification.description;
        } else {
          getMailContentData += notification.description;
        }
      });
      mailContent = mailContent.replace('$mailContentData', `${getMailContentData}`);
      const mailData = {
        email: memberData.User.email,
        mailContent,
        mailSentTime: ` ${moment().format('MMM DD')} at ${moment().format('hh:mm A')}`,
      };
      await MAILER.sendMail(
        mailData,
        'dailyDigestNotification',
        `Follo - Daily Digest (${moment().format('MMM DD,YYYY')})`,
        `Follo - Daily Digest (${moment().format('MMM DD,YYYY')})`,
        async () => {
          await DigestNotification.update(
            { isSent: true },
            {
              where: {
                isDeleted: false,
                isSent: false,
                MemberId: memberData.id,
                ProjectId: memberData.ProjectId,
              },
            },
          );
          return { status: true };
        },
      );
    } else {
      return true;
    }
  },
  async schedulerReportRequest(inputData, done) {
    try {
      const { params } = inputData;
      const loginUser = inputData.user;
      const incomeData = inputData.body;

      const {
        reportName,
        reportType,
        outputFormat,
        recurrence,
        repeatEvery,
        sendTo,
        subject,
        message,
      } = incomeData;

      if (reportType === 'Weekly Calendar' && outputFormat === 'PDF') {
        const startTime = moment(incomeData.startTime, 'HH:mm:ss');
        const endTime = moment(incomeData.endTime, 'HH:mm:ss');
        const duration = moment.duration(endTime.diff(startTime));
        const hourDifference = duration.asHours();
        if (hourDifference > 12) {
          done(
            {
              message:
                'Currently, we are not supporting more than 12 hours time difference for PDF export. please change the start and end time in the filter section.',
            },
            false,
          );
          return;
        }
      }

      const createDataFormation = {
        isSaved: false,
        reportName,
        reportType,
        ProjectId: parseInt(params.ProjectId),
        outputFormat: outputFormat.trim(),
        recurrence,
        recurrenceEndDate:
          incomeData.recurrence !== 'Does Not Repeat'
            ? incomeData.repeatEvery.recurrenceEndDate
            : null,
        repeatEvery: repeatEvery === '' ? {} : repeatEvery,
        sendTo,
        subject,
        message,
        status: incomeData.status || null,
        isDeleted: false,
        createdBy: loginUser.id,
        lastRun: null,
        timezone: incomeData.timezone,
        startDate: `${incomeData.startDate}/${incomeData.startTime}`,
        endDate: `${incomeData.endDate}/${incomeData.endTime}`,
        memberFilterId: incomeData.memberId,
        parentFilterCompanyId: incomeData.parentCompanyId,
        gateId: incomeData.gateId,
        defineId: incomeData.defineId,
        companyId: incomeData.companyId,
        equipmentId: incomeData.equipmentId,
        companyFilter: incomeData.companyFilter ? incomeData.companyFilter : '',
        equipmentFilter: incomeData.equipmentFilter ? incomeData.equipmentFilter : '',
        templateFilterType: incomeData.templateType ? incomeData.templateType : '[]',
        selectedHeaders: incomeData.selectedHeaders ? incomeData.selectedHeaders : '[]',
        isEndDateMeet: false,
        idFilter: incomeData.idFilter ? incomeData.idFilter : 0,
        sort: incomeData.sort ? incomeData.sort : 'DESC',
        sortByField: incomeData.sortByField ? incomeData.sortByField : 'id',
        queuedNdr: incomeData.queuedNdr ? incomeData.queuedNdr : false,
        pickFrom: incomeData.pickFrom ? incomeData.pickFrom : '',
        pickTo: incomeData.pickTo ? incomeData.pickTo : '',
        locationFilter: incomeData.locationFilter ? incomeData.locationFilter : '',
        descriptionFilter: incomeData.descriptionFilter ? incomeData.descriptionFilter : '',
        orderNumberFilter: incomeData.orderNumberFilter ? incomeData.orderNumberFilter : '',
        mixDesignFilter: incomeData.mixDesignFilter ? incomeData.mixDesignFilter : '',
        truckspacingFilter: incomeData.truckspacingFilter ? incomeData.truckspacingFilter : '',
        slumpFilter: incomeData.slumpFilter ? incomeData.slumpFilter : '',
        primerFilter: incomeData.primerFilter ? incomeData.primerFilter : '',
        quantityFilter: incomeData.quantityFilter ? incomeData.quantityFilter : '',
        dateRangeId:
          incomeData.dateRangeId && incomeData.dateRangeId !== 0 ? incomeData.dateRangeId : null,
        customStartDate:
          incomeData.customStartDate && incomeData.customStartDate !== ''
            ? incomeData.customStartDate
            : null,
        customEndDate:
          incomeData.customEndDate && incomeData.customEndDate !== ''
            ? incomeData.customEndDate
            : null,
      };

      // console.log('*******createDataFormation*********', createDataFormation);

      if (incomeData.runReportAt) {
        const serverDate = momenttz(incomeData.runReportAt);
        // Use this format for converting client datetime to server datetime.
        // const serverDate = momenttz(incomeData.runReportAt).subtract(inputData.headers.timezoneoffset, 'minutes').format();
        createDataFormation.runReportAt = incomeData.runReportAt;
        if (incomeData.recurrence === 'Does Not Repeat') {
          createDataFormation.cronExpression = convertToCron(serverDate);
        } else {
          if (incomeData.recurrence === 'Daily' && incomeData.repeatEvery.day) {
            createDataFormation.cronExpression = `${serverDate.minute()} ${serverDate.hour()} */${incomeData.repeatEvery.day
              } * *`;
          }

          if (
            incomeData.recurrence === 'Weekly' &&
            incomeData.repeatEvery.day &&
            incomeData.repeatEvery.specificDays.length > 0
          ) {
            createDataFormation.cronExpression = `${serverDate.minute()} ${serverDate.hour()} * * ${incomeData.repeatEvery.specificDays.join(
              ',',
            )}/${incomeData.repeatEvery.day}`;
          }

          if (
            incomeData.recurrence === 'Monthly' &&
            incomeData.repeatEvery.options &&
            incomeData.repeatEvery.specificDays
          ) {
            if (incomeData.repeatEvery.options === 'on_day') {
              createDataFormation.cronExpression = `${serverDate.minute()} ${serverDate.hour()} ${incomeData.repeatEvery.specificDays.day
                } */${incomeData.repeatEvery.day} *`;
            } else {
              createDataFormation.cronExpression = convertToCronMonthly(
                incomeData.repeatEvery.specificDays.order,
                incomeData.repeatEvery.specificDays.specificDay,
                incomeData.repeatEvery.day,
                serverDate.hour(),
                serverDate.minute(),
              );
            }
          }

          if (
            incomeData.recurrence === 'Yearly' &&
            incomeData.repeatEvery.options &&
            incomeData.repeatEvery.specificDays
          ) {
            if (incomeData.repeatEvery.options === 'on_day') {
              createDataFormation.cronExpression = `${serverDate.minute()} ${serverDate.hour()} ${incomeData.repeatEvery.specificDays.day
                } ${incomeData.repeatEvery.specificDays.month} *`;
            }
            if (incomeData.repeatEvery.options === 'on_the') {
              createDataFormation.cronExpression = `0 ${serverDate.minute()} ${serverDate.hour()} ? 8 WED#4`;
              createDataFormation.cronExpression = convertToCronYearly(
                incomeData.repeatEvery.specificDays.order,
                incomeData.repeatEvery.specificDays.specificDay,
                incomeData.repeatEvery.specificDays.month,
                serverDate.hour(),
                serverDate.minute(),
              );
            }
          }
        }
      }
      // console.log('*********cronExpression************', createDataFormation.cronExpression);

      if (!cron.validate(createDataFormation.cronExpression)) {
        throw new Error('cron expression is invalid');
      }

      const newScheduler = await SchedulerReport.createInstance(createDataFormation);
      const user = await User.findOne({
        where: {
          id: loginUser.id,
        },
      });

      if (user) {
        newScheduler.createdUser = user;
        newScheduler.firstName = user.firstName;
        newScheduler.lastName = user.lastName;
      }

      if (newScheduler) {
        this.cronSchedulerJob(newScheduler);
        done({ message: 'Reports scheduled successfully' }, false);
      } else {
        done(null, { message: 'Something went wrong while creating scheduler record' });
      }
    } catch (e) {
      console.log('******ERROR*******', e);
      done(null, e);
    }
  },
  async getSchedulerReportRequest(inputData, done) {
    try {
      const { query, body } = inputData;
      let offset;
      if (query.pageNo) {
        offset = (Number(query.pageNo) - 1) * Number(query.pageSize);
      }
      const getReports = await SchedulerReport.getAll(
        Number(query.ProjectId),
        +query.pageSize,
        offset,
        query.sortByField,
        query.sort,
        Number(query.createdUserId),
        query.reportName,
        query.templateType,
        query.lastRun,
        query.search,
        query.timezone,
        body.saved,
        !body.hasOwnProperty('saved'),
      );
      return done({ scheduledReports: getReports.scheduledData, count: getReports.count }, false);
    } catch (e) {
      console.log('******ERROR*******', e);
      done(null, e);
    }
  },
  async getRerunReportRequest(inputData, done) {
    try {
      const regex = /^https:\/\/\S+\.(pdf|xlsx|csv)$/;
      const { id, ProjectId } = inputData.query;
      const schedulerData = await SchedulerReport.findOne({
        where: { isDeleted: false, id, ProjectId },
        include: [{ association: 'createdUser', attributes: ['email', 'firstName', 'lastName'] }],
      });
      if (schedulerData) {
        const s3_url = await this.generateReportAll(JSON.parse(JSON.stringify(schedulerData)));
        if (regex.test(s3_url.trim())) {
          done(s3_url, false);
        } else {
          done('No data found', false);
        }
      } else {
        done('No data found', false);
      }
    } catch (e) {
      console.log('*************ERROR*****************', e);
      done(null, e);
    }
  },
  async getSchedulerTimelineNames(inputData, done) {
    try {
      const schedulerDateRange = await SchedulerDateRange.findAll();
      if (schedulerDateRange) {
        done(JSON.parse(JSON.stringify(schedulerDateRange)), false);
      } else {
        done('No data found', false);
      }
    } catch (e) {
      console.log('*************ERROR*****************', e);
      done(null, e);
    }
  },
  async payloadConcreteGenerationAndSendS3URL(schedulerData) {
    try {
      const { concreteReportService } = require('.');
      const payload = {
        params: {
          ProjectId: schedulerData.ProjectId,
          void: 0,
          pageSize: 10,
          pageNo: 1,
        },
        user: {
          id: schedulerData.createdBy,
          firstName: schedulerData.firstName || 'Test',
          lastName: schedulerData.lastName || '',
        },
        body: {
          truckspacingFilter: schedulerData.truckspacingFilter
            ? schedulerData.truckspacingFilter
            : '',
          slumpFilter: schedulerData.slumpFilter ? schedulerData.slumpFilter : '',
          mixDesignFilter: schedulerData.mixDesignFilter ? schedulerData.mixDesignFilter : '',
          orderNumberFilter: schedulerData.orderNumberFilter ? schedulerData.orderNumberFilter : '',
          primerFilter: schedulerData.primerFilter ? schedulerData.primerFilter : '',
          quantityFilter: schedulerData.quantityFilter ? schedulerData.quantityFilter : '',
          locationFilter: schedulerData.locationFilter ? schedulerData.locationFilter : '',
          descriptionFilter: schedulerData.descriptionFilter ? schedulerData.descriptionFilter : '',
          sort: schedulerData.sort ? schedulerData.sort : 'DESC',
          sortByField: schedulerData.sortByField ? schedulerData.sortByField : 'id',
          queuedNdr: schedulerData.queuedNdr ? schedulerData.queuedNdr : false,
          idFilter: schedulerData.idFilter ? schedulerData.idFilter : 0,
          selectedHeaders: schedulerData.selectedHeaders,
          concreteSupplierFilter: schedulerData.companyFilter,
          startdate: schedulerData.customStartDate
            ? schedulerData.customStartDate
            : schedulerData.startDate.split('/')[0],
          enddate: schedulerData.customEndDate
            ? schedulerData.customEndDate
            : schedulerData.endDate.split('/')[0],
          start: schedulerData.customStartDate
            ? schedulerData.customStartDate
            : schedulerData.startDate.split('/')[0],
          end: schedulerData.customEndDate
            ? schedulerData.customEndDate
            : schedulerData.endDate.split('/')[0],
          statusFilter: schedulerData.status ? schedulerData.status : '',
          memberFilter: schedulerData.memberFilterId,
          gateFilter: schedulerData.gateId,
          equipmentFilter: schedulerData.equipmentId,
          templateType: schedulerData.templateFilterType,
          defineFilter: schedulerData.defineId,
          startTime: schedulerData.startDate.split('/')[1],
          endTime: schedulerData.endDate.split('/')[1],
          eventStartTime: schedulerData.startDate.split('/')[1],
          eventEndTime: schedulerData.endDate.split('/')[1],
          ParentCompanyId: schedulerData.parentFilterCompanyId,
          timezone: schedulerData.timezone,
          exportType: schedulerData.outputFormat,
          reportName: schedulerData.reportName,
          isDST: true,
          generatedDate: momenttz().utc().tz(schedulerData.timezone).format('ddd, MMM DD YYYY'),
          typeFormat: 'export',
          currentViewMonth: 'Week',
          currentEnd: `${schedulerData.endDate.split('/')[0]} ${schedulerData.endDate.split('/')[1]
            }`,
          currentStart: `${schedulerData.startDate.split('/')[0]} ${schedulerData.startDate.split('/')[1]
            }`,
        },
        headers: {
          timezoneoffset: momenttz.tz(schedulerData.timezone).utcOffset(),
        },
      };
      return await concreteReportService.exportReportForScheduler(payload);
    } catch (e) {
      console.log('******ERROR*******', e);
      return e;
    }
  },
  async payloadCraneGenerationAndSendS3URL(schedulerData) {
    try {
      const { craneReportService } = require('.');
      const payload = {
        params: {
          ProjectId: schedulerData.ProjectId,
          void: 0,
          pageSize: 10,
          pageNo: 1,
        },
        user: {
          id: schedulerData.createdBy,
          firstName: schedulerData.firstName || 'Test',
          lastName: schedulerData.lastName || '',
        },
        body: {
          descriptionFilter: schedulerData.descriptionFilter ? schedulerData.descriptionFilter : '',
          sort: schedulerData.sort ? schedulerData.sort : 'DESC',
          sortByField: schedulerData.sortByField ? schedulerData.sortByField : 'CraneRequestId',
          queuedNdr: schedulerData.queuedNdr ? schedulerData.queuedNdr : false,
          pickFrom: schedulerData.pickFrom ? schedulerData.pickFrom : '',
          pickTo: schedulerData.pickTo ? schedulerData.pickTo : '',
          idFilter: schedulerData.idFilter ? schedulerData.idFilter : 0,
          selectedHeaders: schedulerData.selectedHeaders,
          companyFilter: schedulerData.companyFilter,
          startdate: schedulerData.customStartDate
            ? schedulerData.customStartDate
            : schedulerData.startDate.split('/')[0],
          enddate: schedulerData.customEndDate
            ? schedulerData.customEndDate
            : schedulerData.endDate.split('/')[0],
          start: schedulerData.customStartDate
            ? schedulerData.customStartDate
            : schedulerData.startDate.split('/')[0],
          end: schedulerData.customEndDate
            ? schedulerData.customEndDate
            : schedulerData.endDate.split('/')[0],
          statusFilter: schedulerData.status ? schedulerData.status : '',
          memberFilter: schedulerData.memberFilterId,
          gateFilter: schedulerData.gateId,
          equipmentFilter: schedulerData.equipmentFilter,
          templateType: schedulerData.templateFilterType,
          defineFilter: schedulerData.defineId,
          startTime: schedulerData.startDate.split('/')[1],
          endTime: schedulerData.endDate.split('/')[1],
          eventStartTime: schedulerData.startDate.split('/')[1],
          eventEndTime: schedulerData.endDate.split('/')[1],
          ParentCompanyId: schedulerData.parentFilterCompanyId,
          timezone: schedulerData.timezone,
          exportType: schedulerData.outputFormat,
          reportName: schedulerData.reportName,
          isDST: true,
          generatedDate: momenttz().utc().tz(schedulerData.timezone).format('ddd, MMM DD YYYY'),
          typeFormat: 'export',
          currentViewMonth: 'Week',
          currentEnd: `${schedulerData.endDate.split('/')[0]} ${schedulerData.endDate.split('/')[1]
            }`,
          currentStart: `${schedulerData.startDate.split('/')[0]} ${schedulerData.startDate.split('/')[1]
            }`,
        },
        headers: {
          timezoneoffset: momenttz.tz(schedulerData.timezone).utcOffset(),
        },
      };
      return await craneReportService.exportReportForScheduler(payload);
    } catch (e) {
      console.log('********ERROR/*************', e);
      return e;
    }
  },
  async payloadDeliveryGenerationAndSendS3URL(schedulerData) {
    try {
      const { deliveryReportService } = require('.');
      const payload = {
        params: {
          ProjectId: schedulerData.ProjectId,
          void: 0,
          pageSize: 10,
          pageNo: 1,
        },
        user: {
          id: schedulerData.createdBy,
          firstName: schedulerData.createdUser.firstName || 'Test',
          lastName: schedulerData.createdUser.lastName || '',
        },
        body: {
          descriptionFilter: schedulerData.descriptionFilter ? schedulerData.descriptionFilter : '',
          sort: schedulerData.sort ? schedulerData.sort : 'DESC',
          sortByField: schedulerData.sortByField ? schedulerData.sortByField : 'id',
          queuedNdr: schedulerData.queuedNdr ? schedulerData.queuedNdr : false,
          selectedHeaders: schedulerData.selectedHeaders,
          companyFilter: schedulerData.companyId,
          startdate: schedulerData.customStartDate
            ? schedulerData.customStartDate
            : schedulerData.startDate.split('/')[0],
          enddate: schedulerData.customEndDate
            ? schedulerData.customEndDate
            : schedulerData.endDate.split('/')[0],
          start: schedulerData.customStartDate
            ? schedulerData.customStartDate
            : schedulerData.startDate.split('/')[0],
          end: schedulerData.customEndDate
            ? schedulerData.customEndDate
            : schedulerData.endDate.split('/')[0],
          statusFilter: schedulerData.status ? schedulerData.status : '',
          memberFilter: schedulerData.memberFilterId,
          gateFilter: schedulerData.gateId,
          equipmentFilter: schedulerData.equipmentId,
          templateType: schedulerData.templateFilterType,
          defineFilter: schedulerData.defineId,
          startTime: schedulerData.startDate.split('/')[1],
          endTime: schedulerData.endDate.split('/')[1],
          eventStartTime: schedulerData.startDate.split('/')[1],
          eventEndTime: schedulerData.endDate.split('/')[1],
          ParentCompanyId: schedulerData.parentFilterCompanyId,
          timezone: schedulerData.timezone,
          exportType: schedulerData.outputFormat,
          reportName: schedulerData.reportName,
          isDST: true,
          generatedDate: momenttz().utc().tz(schedulerData.timezone).format('ddd, MMM DD YYYY'),
          typeFormat: 'export',
          currentViewMonth: 'Week',
          currentEnd: `${schedulerData.endDate.split('/')[0]} ${schedulerData.endDate.split('/')[1]
            }`,
          currentStart: `${schedulerData.startDate.split('/')[0]} ${schedulerData.startDate.split('/')[1]
            }`,
        },
        headers: {
          timezoneoffset: momenttz.tz(schedulerData.timezone).utcOffset(),
        },
      };
      return await deliveryReportService.exportReportForScheduler(payload);
    } catch (e) {
      console.log('********ERROR/*************', e);
      return e;
    }
  },
  async payloadWeeklyCalendarGenerationAndSendS3URL(schedulerData) {
    try {
      const { deliveryReportService } = require('.');
      const payload = {
        params: {
          ProjectId: schedulerData.ProjectId,
          void: 0,
        },
        user: {
          id: schedulerData.createdBy,
          firstName: schedulerData.createdUser.firstName || 'Test',
          lastName: schedulerData.createdUser.lastName || '',
        },
        body: {
          companyFilter: schedulerData.companyId,
          startDate: schedulerData.customStartDate
            ? schedulerData.customStartDate
            : schedulerData.startDate.split('/')[0],
          endDate: schedulerData.customEndDate
            ? schedulerData.customEndDate
            : schedulerData.endDate.split('/')[0],
          start: schedulerData.customStartDate
            ? schedulerData.customStartDate
            : schedulerData.startDate.split('/')[0],
          end: schedulerData.customEndDate
            ? schedulerData.customEndDate
            : schedulerData.endDate.split('/')[0],
          statusFilter: schedulerData.status ? schedulerData.status : '',
          memberFilter: schedulerData.memberFilterId,
          gateFilter: schedulerData.gateId,
          equipmentFilter: schedulerData.equipmentId,
          templateType: schedulerData.templateFilterType,
          defineFilter: schedulerData.defineId,
          startTime: schedulerData.startDate.split('/')[1],
          endTime: schedulerData.endDate.split('/')[1],
          eventStartTime: schedulerData.startDate.split('/')[1],
          eventEndTime: schedulerData.endDate.split('/')[1],
          ParentCompanyId: schedulerData.parentFilterCompanyId,
          timezone: schedulerData.timezone,
          exportType: schedulerData.outputFormat,
          reportName: schedulerData.reportName,
          isDST: true,
          generatedDate: momenttz().utc().tz(schedulerData.timezone).format('ddd, MMM DD YYYY'),
          typeFormat: 'export',
          currentViewMonth: 'Week',
          currentEnd: `${schedulerData.endDate.split('/')[0]} ${schedulerData.endDate.split('/')[1]
            }`,
          currentStart: `${schedulerData.startDate.split('/')[0]} ${schedulerData.startDate.split('/')[1]
            }`,
        },
        headers: {
          timezoneoffset: 0,
        },
      };
      return await deliveryReportService.exportWeeklyCalendarReportForScheduler(payload);
    } catch (e) {
      console.log('*******Errro*****', e);
    }
  },
  async payloadHeatMapGenerationAndSendS3URL(schedulerData) {
    try {
      const { deliveryReportService } = require('.');
      const payload = {
        params: {
          ProjectId: schedulerData.ProjectId,
          void: 0,
          sortOrder: 'asc',
          pageSize: 10,
          pageNo: 1,
        },
        user: {
          id: schedulerData.createdBy,
          firstName: schedulerData.createdUser.firstName || 'Test',
          lastName: schedulerData.createdUser.lastName || '',
        },
        body: {
          companyFilter: schedulerData.companyId,
          startDate: schedulerData.customStartDate
            ? schedulerData.customStartDate
            : schedulerData.startDate.split('/')[0],
          endDate: schedulerData.customEndDate
            ? schedulerData.customEndDate
            : schedulerData.endDate.split('/')[0],
          statusFilter: schedulerData.status ? schedulerData.status : '',
          memberFilter: schedulerData.memberFilterId,
          gateFilter: schedulerData.gateId,
          equipmentFilter: schedulerData.equipmentId,
          templateType: schedulerData.templateFilterType,
          defineFilter: schedulerData.defineId,
          startTime: schedulerData.startDate.split('/')[1],
          endTime: schedulerData.endDate.split('/')[1],
          ParentCompanyId: schedulerData.parentFilterCompanyId,
          timezone: schedulerData.timezone,
          exportType: 'PDF',
          reportName: schedulerData.reportName,
          isDST: true,
          generatedDate: momenttz().utc().tz(schedulerData.timezone).format('ddd, MMM DD YYYY'),
          typeFormat: 'export',
        },
      };

      return await deliveryReportService.exportHeatMapReportForSceduler(payload);
    } catch (e) {
      console.log('**********Error***************', e);
    }
  },
  async generateReportAll(schedulerData) {
    try {
      if (schedulerData.reportType === 'Heat Map') {
        return await this.payloadHeatMapGenerationAndSendS3URL(schedulerData);
      }
      if (schedulerData.reportType === 'Weekly Calendar') {
        return await this.payloadWeeklyCalendarGenerationAndSendS3URL(schedulerData);
      }
      if (schedulerData.reportType === 'Delivery') {
        return await this.payloadDeliveryGenerationAndSendS3URL(schedulerData);
      }
      if (schedulerData.reportType === 'Crane') {
        return await this.payloadCraneGenerationAndSendS3URL(schedulerData);
      }
      if (schedulerData.reportType === 'Concrete') {
        return await this.payloadConcreteGenerationAndSendS3URL(schedulerData);
      }
    } catch (e) {
      console.log('******ERROR*********', e);
    }
  },
  async cronSchedulerJob(schedulerData) {
    try {
      const regex = /^https:\/\/\S+\.(pdf|xlsx|csv)$/;
      if (schedulerData.recurrence === 'Does Not Repeat') {
        const job = cron.schedule(
          schedulerData.cronExpression,
          async () => {
            try {
              const s3_url = await this.generateReportAll(schedulerData);
              // console.log('*******s3_url**********', s3_url);
              if (regex.test(s3_url.trim())) {
                const lastRun = momenttz().utc().format();
                const isEndDateMeet = true;
                // console.log('*******lastRun***********', lastRun);
                await SchedulerReport.updateInstance(schedulerData.id, {
                  lastRun,
                  s3_url,
                  isEndDateMeet,
                });
                schedulerData.lastRun = lastRun;
                schedulerData.s3_url = s3_url;
                schedulerData.isEndDateMeet = isEndDateMeet;
                schedulerData.emailTemplate = 'schedulerReport';
                const mailStatus = await MAILER.sendReportMail(schedulerData);
                job.stop();
              } else {
                const lastRun = momenttz().utc().format();
                const isEndDateMeet = true;
                // console.log('*******lastRun***********', lastRun);
                await SchedulerReport.updateInstance(schedulerData.id, { lastRun, isEndDateMeet });
                schedulerData.lastRun = lastRun;
                schedulerData.isEndDateMeet = isEndDateMeet;
                schedulerData.emailTemplate = 'schedulerReportNoData';
                schedulerData.message =
                  'There is no data found in the report for the scheduled date';
                const mailStatus = await MAILER.sendReportMail(schedulerData);
                job.stop();
              }
            } catch (e) {
              console.log('(ERROR)', e);
            }
          },
          {
            name: schedulerData.id,
            timezone: schedulerData.timezone,
          },
        );
      } else {
        const job = cron.schedule(
          schedulerData.cronExpression,
          async () => {
            const currentDate = momenttz().utc().tz(schedulerData.timezone).format('YYYY-MM-DD');
            switch (schedulerData.dateRangeId) {
              case 1:
                schedulerData.currentStartDate = currentDate;
                schedulerData.currentEndDate = currentDate;
                break;
              case 2:
                const next7Days = momenttz()
                  .utc()
                  .tz(schedulerData.timezone)
                  .add(7, 'days')
                  .format('YYYY-MM-DD');
                schedulerData.currentStartDate = currentDate;
                schedulerData.currentEndDate = next7Days;
                break;
              case 3:
                const next15Days = momenttz()
                  .utc()
                  .tz(schedulerData.timezone)
                  .add(15, 'days')
                  .format('YYYY-MM-DD');
                schedulerData.currentStartDate = currentDate;
                schedulerData.currentEndDate = next15Days;
                break;
              case 4:
                const thisMonth = momenttz().utc().tz(schedulerData.timezone);
                const startDate = thisMonth.clone().startOf('month').format('YYYY-MM-DD');
                const endDate = thisMonth.clone().endOf('month').format('YYYY-MM-DD');
                schedulerData.currentStartDate = startDate;
                schedulerData.currentEndDate = endDate;
                break;
              case 5:
                const nextMonth = momenttz().utc().tz(schedulerData.timezone).add(1, 'month');
                const nextStartDate = nextMonth.clone().startOf('month').format('YYYY-MM-DD');
                const nextEndDate = nextMonth.clone().endOf('month').format('YYYY-MM-DD');
                schedulerData.currentStartDate = nextStartDate;
                schedulerData.currentEndDate = nextEndDate;
                break;
              case 6:
                const last7Days = momenttz()
                  .utc()
                  .tz(schedulerData.timezone)
                  .subtract(7, 'days')
                  .format('YYYY-MM-DD');
                schedulerData.currentStartDate = last7Days;
                schedulerData.currentEndDate = currentDate;
                break;
              case 7:
                const last15Days = momenttz()
                  .utc()
                  .tz(schedulerData.timezone)
                  .subtract(15, 'days')
                  .format('YYYY-MM-DD');
                schedulerData.currentStartDate = last15Days;
                schedulerData.currentEndDate = currentDate;
                break;
              default:
                break;
            }
            const { recurrenceEndDate } = schedulerData;
            const diffInDays = moment(recurrenceEndDate).diff(moment(currentDate), 'days');
            let isEndDateMeet = false;
            if (diffInDays == 0) {
              isEndDateMeet = true;
            } else if (diffInDays < 0) {
              isEndDateMeet = true;
              await SchedulerReport.updateInstance(schedulerData.id, { isEndDateMeet });
              job.stop();
              return;
            }
            // console.log('*******diffInDays**********', diffInDays);
            const s3_url = await this.generateReportAll(schedulerData);
            // console.log('*******s3_url**********', s3_url);
            if (regex.test(s3_url.trim())) {
              const lastRun = momenttz().utc().format();
              // console.log('*******lastRun***********', lastRun);
              await SchedulerReport.updateInstance(schedulerData.id, {
                lastRun,
                s3_url,
                isEndDateMeet,
              });
              schedulerData.lastRun = lastRun;
              schedulerData.s3_url = s3_url;
              schedulerData.isEndDateMeet = isEndDateMeet;
              schedulerData.emailTemplate = 'schedulerReport';
              const mailStatus = await MAILER.sendReportMail(schedulerData);
              if (diffInDays == 0) {
                job.stop();
              }
            } else {
              const lastRun = momenttz().utc().format();
              // console.log('*******lastRun***********', lastRun);
              await SchedulerReport.updateInstance(schedulerData.id, { lastRun, isEndDateMeet });
              schedulerData.lastRun = lastRun;
              schedulerData.isEndDateMeet = isEndDateMeet;
              schedulerData.emailTemplate = 'schedulerReportNoData';
              schedulerData.message = 'There is no data found in the report for the scheduled date';
              const mailStatus = await MAILER.sendReportMail(schedulerData);
              if (diffInDays == 0) {
                job.stop();
              }
            }
          },
          {
            name: schedulerData.id,
            timezone: schedulerData.timezone,
          },
        );
      }
    } catch (e) {
      console.log('*************ERROR*****************', e);
    }
  },
  async runtimeScheduler() {
    try {
      const schedulerDatas = await SchedulerReport.findAll({
        where: { isDeleted: false, isEndDateMeet: false, isSaved: false },
      });
      _.forEach(schedulerDatas, (data) => {
        this.cronSchedulerJob(JSON.parse(JSON.stringify(data)));
      });
    } catch (e) {
      console.log('*************ERROR*****************', e);
    }
  },
};

module.exports = cornService;
