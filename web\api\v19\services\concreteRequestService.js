/* eslint-disable no-restricted-syntax */
/* eslint-disable no-await-in-loop */
/* eslint-disable no-loop-func */
/* eslint-disable no-lone-blocks */
/* eslint-disable no-param-reassign */
/* eslint-disable no-nested-ternary */
/* eslint-disable array-callback-return */
const moment = require('moment');
const Moment = require('moment');
const Cryptr = require('cryptr');
const MomentRange = require('moment-range');
const httpStatus = require('http-status');
const ApiError = require('../helpers/apiError');

const momentRange = MomentRange.extendMoment(Moment);
const {
  Sequelize,
  Enterprise,
  NotificationPreference,
  DigestNotification,
  TimeZone,
  CalendarSetting,
  Locations,
  LocationNotificationPreferences,
} = require('../models');
let {
  DeliveryRequest,
  Member,
  DeliveryPerson,
  DeliverCompany,
  Role,
  DeliverDefineWork,
  Company,
  Project,
  VoidList,
  User,
  Notification,
  DeliveryPersonNotification,
  ConcreteRequestResponsiblePerson,
  InspectionRequest,
} = require('../models');
const {
  ConcreteRequest,
  ConcreteRequestHistory,
  ConcreteRequestCompany,
  ConcreteLocation,
  ConcreteMixDesign,
  ConcretePumpSize,
  ConcreteRequestLocation,
  ConcreteRequestMixDesign,
  ConcreteRequestPumpSize,
  CraneRequest,
  RequestRecurrenceSeries,
} = require('../models');
const MAILER = require('../mailer');
const helper = require('../helpers/domainHelper');
// const mixpanelService = require('./mixpanelService');
const notificationHelper = require('../helpers/notificationHelper');
const pushNotification = require('../config/fcm');
const calendarSettingsService = require('./calendarSettingsService');
const voidService = require('./voidService');

let publicUser;
let publicMember;
const { Op } = Sequelize;
const concreteRequestService = {
  async returnProjectModel() {
    const modelData = await helper.returnProjectModel();
    // publicProject = modelData.Project;
    publicMember = modelData.Member;
    publicUser = modelData.User;
  },
  async getDynamicModel(inputData) {
    await this.returnProjectModel();
    let { domainName } = inputData.user;
    const incomeData = inputData;
    let enterpriseValue;
    let ProjectId;
    const ParentCompanyId = inputData.body.ParentCompanyId
      ? inputData.body.ParentCompanyId
      : inputData.params.ParentCompanyId;
    let domainEnterpriseValue;
    if (domainName) {
      domainEnterpriseValue = await Enterprise.findOne({
        where: { name: domainName.toLowerCase() },
      });
      if (!domainEnterpriseValue) {
        domainName = '';
      }
    }
    if (!domainName && ParentCompanyId !== undefined && ParentCompanyId !== 'undefined') {
      const { email } = inputData.user;
      let userData;
      if (email) {
        userData = await publicUser.findOne({ where: { email } });
      }
      if (userData) {
        const memberData = await publicMember.findOne({
          where: { UserId: userData.id, RoleId: { [Op.ne]: 4 }, isDeleted: false },
        });
        if (memberData) {
          if (memberData.isAccount) {
            enterpriseValue = await Enterprise.findOne({
              where: { id: memberData.EnterpriseId, status: 'completed' },
            });
            if (enterpriseValue) {
              domainName = enterpriseValue.name.toLowerCase();
            }
          } else {
            enterpriseValue = await Enterprise.findOne({
              where: { ParentCompanyId, status: 'completed' },
            });
            if (enterpriseValue) {
              domainName = enterpriseValue.name.toLowerCase();
            }
          }
        } else {
          enterpriseValue = await Enterprise.findOne({
            where: { ParentCompanyId, status: 'completed' },
          });
          if (enterpriseValue) {
            domainName = enterpriseValue.name.toLowerCase();
          }
        }
      }
    }
    const modelObj = await helper.getDynamicModel(domainName);
    DeliveryRequest = modelObj.DeliveryRequest;
    Member = modelObj.Member;
    DeliveryPerson = modelObj.DeliveryPerson;
    DeliverCompany = modelObj.DeliverCompany;
    Role = modelObj.Role;
    DeliverDefineWork = modelObj.DeliverDefineWork;
    Company = modelObj.Company;
    Project = modelObj.Project;
    User = modelObj.User;
    VoidList = modelObj.VoidList;
    DeliveryPersonNotification = modelObj.DeliveryPersonNotification;
    ConcreteRequestResponsiblePerson = modelObj.ConcreteRequestResponsiblePerson;
    Notification = modelObj.Notification;
    if (enterpriseValue) {
      const newUser = await User.findOne({ where: { email: inputData.user.email } });
      incomeData.user = newUser;
    }
    return ProjectId;
  },

  async listConcreteRequest(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { params } = inputData;
      const loginUser = inputData.user;
      const incomeData = inputData.body;
      let order;
      const newResult = { count: 0, rows: [] };
      if (params.void >= 1 && params.void <= 0) {
        done(null, { message: 'Please enter void as 1 or 0' });
      } else {
        const memberDetails = await Member.findOne({
          where: Sequelize.and({
            UserId: loginUser.id,
            ProjectId: params.ProjectId,
            isDeleted: false,
            isActive: true,
          }),
        });
        if (memberDetails) {
          const voidConcreteDelivery = [];
          const voidConcreteRequestList = await VoidList.findAll({
            where: {
              ProjectId: params.ProjectId,
              ConcreteRequestId: { [Op.ne]: null },
            },
          });
          voidConcreteRequestList.forEach(async (element) => {
            voidConcreteDelivery.push(element.ConcreteRequestId);
          });
          const voidInspection = [];
          const voidInspectionRequestList = await VoidList.findAll({
            where: {
              ProjectId: params.ProjectId,
              InspectionRequestId: { [Op.ne]: null },
            },
          });
          voidInspectionRequestList.forEach(async (element) => {
            voidInspection.push(element.InspectionRequestId);
          });
          const offset = (+params.pageNo - 1) * +params.pageSize;
          const concreteCondition = {
            ProjectId: +params.ProjectId,
            isDeleted: false,
          };
          if (params.void === '0' || params.void === 0) {
            concreteCondition['$ConcreteRequest.id$'] = {
              [Op.and]: [{ [Op.notIn]: voidConcreteDelivery }],
            };
          } else {
            concreteCondition['$ConcreteRequest.id$'] = {
              [Op.and]: [{ [Op.in]: voidConcreteDelivery }],
            };
          }

          const inspectionCondition = {
            ProjectId: +params.ProjectId,
            isDeleted: false,
          };
          if (params.void === '0' || params.void === 0) {
            inspectionCondition['$InspectionRequest.id$'] = {
              [Op.and]: [{ [Op.notIn]: voidInspection }],
            };
          } else {
            inspectionCondition['$InspectionRequest.id$'] = {
              [Op.and]: [{ [Op.in]: voidInspection }],
            };
          }
          inspectionCondition.description = { [Sequelize.Op.iLike]: `%${incomeData.search}%` };
          const roleId = memberDetails.RoleId;
          const memberId = memberDetails.id;
          const getConcreteRequest = await ConcreteRequest.getAll(
            inputData,
            concreteCondition,
            roleId,
            memberId,
            incomeData.descriptionFilter,
            incomeData.locationFilter,
            incomeData.concreteSupplierFilter,
            incomeData.orderNumberFilter,
            incomeData.statusFilter,
            incomeData.mixDesignFilter,
            incomeData.startdate,
            incomeData.enddate,
            incomeData.memberFilter,
            incomeData.search,
            order,
            incomeData.sort,
            incomeData.sortByField,
          );
          const getInspectionRequest = await InspectionRequest.getAll(
            inspectionCondition,
            roleId,
            memberId,
            '',
            '',
            '',
            order,
            incomeData.sort,
            incomeData.sortByField,
          );
          const newData = [...getConcreteRequest, ...getInspectionRequest];
          newResult.rows = newData;
          newResult.count = newData.length;
          done(newResult, false);
        } else {
          done(null, { message: 'Project Id/Member does not exist' });
        }
      }
    } catch (e) {
      done(null, e);
    }
  },

  async listConcreteBookingRequest(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { params } = inputData;
      const loginUser = inputData.user;
      const incomeData = inputData.body;
      let order;
      const newResult = { count: 0, rows: [] };
      if (params.void >= 1 && params.void <= 0) {
        done(null, { message: 'Please enter void as 1 or 0' });
      } else {
        const memberDetails = await Member.findOne({
          where: Sequelize.and({
            UserId: loginUser.id,
            ProjectId: params.ProjectId,
            isDeleted: false,
            isActive: true,
          }),
        });
        if (memberDetails) {
          const voidConcreteDelivery = [];
          const voidConcreteRequestList = await VoidList.findAll({
            where: {
              ProjectId: params.ProjectId,
              ConcreteRequestId: { [Op.ne]: null },
            },
          });
          voidConcreteRequestList.forEach(async (element) => {
            voidConcreteDelivery.push(element.ConcreteRequestId);
          });
          // const voidInspection = [];
          // const voidInspectionRequestList = await VoidList.findAll({
          //   where: {
          //     ProjectId: params.ProjectId,
          //     InspectionRequestId: { [Op.ne]: null },
          //   },
          // });
          // voidInspectionRequestList.forEach(async (element) => {
          //   voidInspection.push(element.InspectionRequestId);
          // });
          const offset = (+params.pageNo - 1) * +params.pageSize;
          const concreteCondition = {
            ProjectId: +params.ProjectId,
            isDeleted: false,
          };
          if (params.void === '0' || params.void === 0) {
            concreteCondition['$ConcreteRequest.id$'] = {
              [Op.and]: [{ [Op.notIn]: voidConcreteDelivery }],
            };
          } else {
            concreteCondition['$ConcreteRequest.id$'] = {
              [Op.and]: [{ [Op.in]: voidConcreteDelivery }],
            };
          }

          // const inspectionCondition = {
          //   ProjectId: +params.ProjectId,
          //   isDeleted: false,
          // };
          // if (params.void === '0' || params.void === 0) {
          //   inspectionCondition['$InspectionRequest.id$'] = {
          //     [Op.and]: [{ [Op.notIn]: voidInspection }],
          //   };
          // } else {
          //   inspectionCondition['$InspectionRequest.id$'] = {
          //     [Op.and]: [{ [Op.in]: voidInspection }],
          //   };
          // }
          //inspectionCondition['description'] = { [Sequelize.Op.iLike]: `%${incomeData.search}%` }
          const roleId = memberDetails.RoleId;
          const memberId = memberDetails.id;
          const getConcreteRequest = await ConcreteRequest.getAll(
            inputData,
            concreteCondition,
            roleId,
            memberId,
            incomeData.descriptionFilter,
            incomeData.locationFilter,
            incomeData.concreteSupplierFilter,
            incomeData.orderNumberFilter,
            incomeData.statusFilter,
            incomeData.mixDesignFilter,
            incomeData.startdate,
            incomeData.enddate,
            incomeData.memberFilter,
            incomeData.search,
            order,
            incomeData.sort,
            incomeData.sortByField,
          );
          // const getInspectionRequest = await InspectionRequest.getAll(
          //   inspectionCondition,
          //   roleId,
          //   memberId,
          //   '',
          //   '',
          //   '',
          //   order,
          //   incomeData.sort,
          //   incomeData.sortByField
          // );
          const newData = [...getConcreteRequest];
          newResult.rows = newData;
          newResult.count = newData.length;
          done(newResult, false);
        } else {
          done(null, { message: 'Project Id/Member does not exist' });
        }
      }
    } catch (e) {
      console.log('Error in else part', e);
      done(null, e);
    }
  },

  // async listConcreteRequest(inputData, done) {
  //   try {
  //     await this.getDynamicModel(inputData);
  //     const { params } = inputData;
  //     console.log(params, "params in concreate======")
  //     const loginUser = inputData.user;
  //     const incomeData = inputData.body;
  //     let order;
  //     const newResult = { count: 0, rows: [] };
  //     if (params.void >= 1 && params.void <= 0) {
  //       done(null, { message: 'Please enter void as 1 or 0' });
  //     } else {
  //       const memberDetails = await Member.findOne({
  //         where: Sequelize.and({
  //           UserId: loginUser.id,
  //           ProjectId: params.ProjectId,
  //           isDeleted: false,
  //           isActive: true,
  //         }),
  //       });
  //       if (memberDetails) {
  //         const voidConcreteDelivery = [];
  //         const voidConcreteRequestList = await VoidList.findAll({
  //           where: {
  //             ProjectId: params.ProjectId,
  //             ConcreteRequestId: { [Op.ne]: null },
  //           },
  //         });
  //         const voidInspection = [];
  //         const voidInspectionRequestList = await VoidList.findAll({
  //           where: {
  //             ProjectId: params.ProjectId,
  //             InspectionRequestId: { [Op.ne]: null },
  //           },
  //         });
  //         voidConcreteRequestList.forEach(async (element) => {
  //           voidConcreteDelivery.push(element.ConcreteRequestId);
  //         });
  //         voidInspectionRequestList.forEach(async (element) => {
  //           voidInspection.push(element.ConcreteRequestId);
  //         });
  //         const offset = (+params.pageNo - 1) * +params.pageSize;
  //         const concreteCondition = {
  //           ProjectId: +params.ProjectId,
  //           isDeleted: false,
  //         };
  //         if (params.void === '0' || params.void === 0) {
  //           concreteCondition['$ConcreteRequest.id$'] = {
  //             [Op.and]: [{ [Op.notIn]: voidConcreteDelivery }],
  //           };
  //         } else {
  //           concreteCondition['$ConcreteRequest.id$'] = {
  //             [Op.and]: [{ [Op.in]: voidConcreteDelivery }],
  //           };
  //         }
  //         if (params.void === '0' || params.void === 0) {
  //           concreteCondition['$InspectionRequest.id$'] = {
  //             [Op.and]: [{ [Op.notIn]: voidInspection }],
  //           };
  //         } else {
  //           concreteCondition['$ConcreteRequest.id$'] = {
  //             [Op.and]: [{ [Op.in]: voidInspection }],
  //           };
  //         }
  //         const roleId = memberDetails.RoleId;
  //         const memberId = memberDetails.id;
  //         const getConcreteRequest = await ConcreteRequest.getAll(
  //           inputData,
  //           concreteCondition,
  //           roleId,
  //           memberId,
  //           incomeData.descriptionFilter,
  //           incomeData.locationFilter,
  //           incomeData.concreteSupplierFilter,
  //           incomeData.orderNumberFilter,
  //           incomeData.statusFilter,
  //           incomeData.mixDesignFilter,
  //           incomeData.startdate,
  //           incomeData.enddate,
  //           incomeData.memberFilter,
  //           incomeData.search,
  //           order,
  //           incomeData.sort,
  //           incomeData.sortByField,
  //         );
  //         newResult.rows = getConcreteRequest.slice(offset, offset + +params.pageSize);
  //         newResult.count = getConcreteRequest.length;
  //         done(newResult, false);
  //       } else {
  //         done(null, { message: 'Project Id/Member does not exist' });
  //       }
  //     }
  //   } catch (e) {
  //     done(null, e);
  //   }
  // },
  async listNDR(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { params } = inputData;
      const loginUser = inputData.user;
      const incomeData = inputData.body;
      let { sort } = inputData.body;
      let { sortByField } = inputData.body;
      let order;
      let searchCondition = {};
      if (params.void >= 1 && params.void <= 0) {
        done(null, { message: 'Please enter void as 1 or 0' });
      } else {
        const memberDetails = await Member.findOne({
          where: Sequelize.and({
            UserId: loginUser.id,
            ProjectId: params.ProjectId,
            isDeleted: false,
            isActive: true,
          }),
        });
        if (memberDetails) {
          const voidDelivery = [];
          const voidList = await VoidList.findAll({
            where: {
              ProjectId: params.ProjectId,
              isDeliveryRequest: true,
              DeliveryRequestId: { [Op.ne]: null },
            },
          });
          voidList.forEach(async (element) => {
            voidDelivery.push(element.DeliveryRequestId);
          });
          const offset = (+params.pageNo - 1) * +params.pageSize;
          const condition = {
            ProjectId: +params.ProjectId,
            isQueued: incomeData.queuedNdr,
          };
          if (params.void === '0' || params.void === 0) {
            condition['$DeliveryRequest.id$'] = {
              [Op.and]: [{ [Op.notIn]: voidDelivery }],
            };
          } else {
            condition['$DeliveryRequest.id$'] = {
              [Op.and]: [{ [Op.in]: voidDelivery }],
            };
          }
          if (incomeData.descriptionFilter) {
            condition.description = {
              [Sequelize.Op.iLike]: `%${incomeData.descriptionFilter}%`,
            };
          }
          if (incomeData.pickFrom) {
            condition.cranePickUpLocation = {
              [Sequelize.Op.iLike]: `%${incomeData.pickFrom}%`,
            };
          }
          if (incomeData.pickTo) {
            condition.craneDropOffLocation = {
              [Sequelize.Op.iLike]: `%${incomeData.pickTo}%`,
            };
          }
          if (incomeData.equipmentFilter) {
            condition['$equipmentDetails.Equipment.id$'] = incomeData.equipmentFilter;
          }
          if (incomeData.statusFilter) {
            condition.status = incomeData.statusFilter;
          }
          if (incomeData.dateFilter) {
            const startDateTime = moment(incomeData.dateFilter, 'YYYY-MM-DD')
              .startOf('day')
              .utcOffset(Number(inputData.headers.timezoneoffset), true);
            const endDateTime = moment(incomeData.dateFilter, 'YYYY-MM-DD')
              .endOf('day')
              .utcOffset(Number(inputData.headers.timezoneoffset), true);
            condition.deliveryStart = {
              [Op.between]: [moment(startDateTime), moment(endDateTime)],
            };
          }
          if (incomeData.upcoming) {
            condition.deliveryStart = {
              [Op.gt]: new Date(),
            };
            order = 'ASC';
            sort = 'ASC';
            sortByField = 'deliveryStart';
          }
          if (incomeData.search) {
            const searchDefault = [
              {
                '$approverDetails.User.firstName$': {
                  [Sequelize.Op.iLike]: `%${incomeData.search}%`,
                },
              },
              {
                '$equipmentDetails.Equipment.equipmentName$': {
                  [Sequelize.Op.iLike]: `%${incomeData.search}%`,
                },
              },
              {
                description: {
                  [Sequelize.Op.iLike]: `%${incomeData.search}%`,
                },
              },
              {
                cranePickUpLocation: {
                  [Sequelize.Op.iLike]: `%${incomeData.search}%`,
                },
              },
              {
                craneDropOffLocation: {
                  [Sequelize.Op.iLike]: `%${incomeData.search}%`,
                },
              },
            ];
            if (!Number.isNaN(+incomeData.search)) {
              searchCondition = {
                [Op.and]: [
                  {
                    [Op.or]: [
                      searchDefault,
                      {
                        [Op.and]: [
                          {
                            DeliveryId: +incomeData.search,
                            isDeleted: false,
                            ProjectId: +params.ProjectId,
                          },
                        ],
                      },
                    ],
                  },
                ],
              };
            } else {
              searchCondition = {
                [Op.and]: [
                  {
                    [Op.or]: searchDefault,
                  },
                ],
              };
            }
          }
          const roleId = memberDetails.RoleId;
          const memberId = memberDetails.id;
          if (
            incomeData.companyFilter ||
            incomeData.gateFilter ||
            incomeData.memberFilter ||
            incomeData.assignedFilter ||
            (memberDetails.RoleId === 4 &&
              (params.void === '0' || params.void === 0) &&
              !incomeData.upcoming) ||
            (memberDetails.RoleId === 3 &&
              (params.void === '0' || params.void === 0) &&
              !incomeData.upcoming)
          ) {
            const result = { count: 0, rows: [] };
            const deliveryList = await DeliveryRequest.getCalendarData(
              condition,
              roleId,
              memberId,
              searchCondition,
              order,
              sort,
              sortByField,
            );
            this.getSearchData(
              incomeData,
              deliveryList.rows,
              [],
              +params.pageSize,
              0,
              0,
              memberDetails,
              async (checkResponse, checkError) => {
                if (!checkError) {
                  this.getLimitData(
                    checkResponse,
                    0,
                    +params.pageSize,
                    [],
                    incomeData,
                    inputData.headers.timezoneoffset,
                    async (newResponse, newError) => {
                      if (!newError) {
                        if (sort === 'ASC') {
                          newResponse.sort(function (a, b) {
                            // eslint-disable-next-line no-nested-ternary
                            return a[sortByField] > b[sortByField]
                              ? 1
                              : b[sortByField] > a[sortByField]
                                ? -1
                                : 0;
                          });
                        } else {
                          newResponse.sort(function (a, b) {
                            // eslint-disable-next-line no-nested-ternary
                            return b[sortByField] > a[sortByField]
                              ? 1
                              : a[sortByField] > b[sortByField]
                                ? -1
                                : 0;
                          });
                        }
                        result.rows = newResponse.slice(offset, offset + +params.pageSize);
                        result.count = checkResponse.length;
                        done(result, false);
                      } else {
                        done(null, { message: 'Something went wrong' });
                      }
                    },
                  );
                } else {
                  done(null, { message: 'Something went wrong' });
                }
              },
            );
          } else {
            const newResult = { count: 0, rows: [] };
            const deliveryList = await DeliveryRequest.getAll(
              condition,
              roleId,
              memberId,
              +params.pageSize,
              offset,
              searchCondition,
              order,
              sort,
              sortByField,
            );
            this.getLimitData(
              deliveryList,
              0,
              +params.pageSize,
              [],
              incomeData,
              inputData.headers.timezoneoffset,
              async (newResponse, newError) => {
                if (!newError) {
                  if (sort === 'ASC') {
                    newResponse.sort(function (a, b) {
                      // eslint-disable-next-line no-nested-ternary
                      return a[sortByField] > b[sortByField]
                        ? 1
                        : b[sortByField] > a[sortByField]
                          ? -1
                          : 0;
                    });
                  } else {
                    newResponse.sort(function (a, b) {
                      // eslint-disable-next-line no-nested-ternary
                      return b[sortByField] > a[sortByField]
                        ? 1
                        : a[sortByField] > b[sortByField]
                          ? -1
                          : 0;
                    });
                  }
                  newResult.rows = newResponse.slice(offset, offset + +params.pageSize);
                  newResult.count = deliveryList.length;
                  done(newResult, false);
                } else {
                  done(null, { message: 'Something went wrong' });
                }
              },
            );
          }
        } else {
          done(null, { message: 'Project Id/Member does not exist' });
        }
      }
    } catch (e) {
      done(null, e);
    }
  },
  async getLimitData(result, index, limit, finalResult, incomeData, timezoneoffset, done) {
    if (index < limit) {
      finalResult.push(result);
      this.getLimitData(
        result,
        index + 1,
        limit,
        finalResult,
        incomeData,
        timezoneoffset,
        (response, err) => {
          if (!err) {
            done(result, false);
          } else {
            done(null, err);
          }
        },
      );
    } else {
      done(result, false);
    }
  },
  async lastConcreteRequest(inputData, done) {
    try {
      const { params } = inputData;
      let data;
      let lastData = {};
      lastData = await ConcreteRequest.findOne({
        where: { ProjectId: params.ProjectId, isDeleted: false },
        order: [['ConcreteRequestId', 'DESC']],
      });
      if (lastData) {
        data = lastData.ConcreteRequestId + 1;
      } else {
        data = 1;
      }
      done({ ConcreteRequestId: data }, false);
    } catch (e) {
      done(null, e);
    }
  },
  async newConcreteRequest(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const eventTimeZone = await TimeZone.findOne({
        where: {
          isDeleted: false,
          id: +inputData.body.TimeZoneId,
        },
        attributes: [
          'id',
          'location',
          'isDayLightSavingEnabled',
          'timeZoneOffsetInMinutes',
          'dayLightSavingTimeInMinutes',
          'timezone',
        ],
      });
      if (!eventTimeZone) {
        return done(null, { message: 'Provide a valid timezone' });
      }
      const concreteRequestDetail = inputData.body;
      const loginUser = inputData.user;
      const projectDetails = await Project.getProjectAndSettings({
        isDeleted: false,
        id: +concreteRequestDetail.ProjectId,
      });
      let startDate;
      let endDate;
      let pumpStartDate;
      let pumpEndDate;
      const memberDetails = await Member.getBy({
        UserId: loginUser.id,
        ProjectId: concreteRequestDetail.ProjectId,
        isActive: true,
        isDeleted: false,
      });
      if (concreteRequestDetail.recurrence) {
        startDate = await this.compareDeliveryDateWithDeliveryWindowDate(
          concreteRequestDetail.concretePlacementStart,
          concreteRequestDetail.startPicker,
          eventTimeZone.timezone,
          projectDetails.ProjectSettings.deliveryWindowTime,
          projectDetails.ProjectSettings.deliveryWindowTimeUnit,
        );
        endDate = await this.compareDeliveryDateWithDeliveryWindowDate(
          concreteRequestDetail.concretePlacementEnd,
          concreteRequestDetail.endPicker,
          eventTimeZone.timezone,
          projectDetails.ProjectSettings.deliveryWindowTime,
          projectDetails.ProjectSettings.deliveryWindowTimeUnit,
        );
      }
      if (concreteRequestDetail.startPicker === concreteRequestDetail.endPicker) {
        return done(null, {
          message: 'Placement Start Time and Anticipated Completion Time should not be the same',
        });
      }
      if (concreteRequestDetail.startPicker > concreteRequestDetail.endPicker) {
        return done(null, {
          message: 'Please enter Placement Start Time lesser than Anticipated Completion Time',
        });
      }
      if (+memberDetails.RoleId === 4 && (startDate || endDate)) {
        if (startDate || endDate) {
          if (projectDetails.ProjectSettings.deliveryWindowTime === 0) {
            if (concreteRequestDetail.recurrence === 'Does Not Repeat') {
              return done(null, { message: 'Please enter Future Concrete Placement Date/Time' });
            }
            return done(null, {
              message: 'Please enter Future Concrete Placement or Recurrence End Date/Time',
            });
          }
          return done(null, {
            message: `Bookings can not be submitted within ${projectDetails.ProjectSettings.deliveryWindowTime} ${projectDetails.ProjectSettings.deliveryWindowTimeUnit} prior to the event`,
          });
        }
      }
      if (concreteRequestDetail.isPumpRequired) {
        if (concreteRequestDetail.pumpWorkStart === concreteRequestDetail.pumpWorkEnd) {
          return done(null, {
            message: 'Pump Show up Time and Completion Time should not be the same',
          });
        }
        if (concreteRequestDetail.pumpWorkStart > concreteRequestDetail.pumpWorkEnd) {
          return done(null, {
            message: 'Please enter Pump Show up Time lesser than Pump Completion Time',
          });
        }
        pumpStartDate = await this.compareDeliveryDateWithDeliveryWindowDate(
          concreteRequestDetail.pumpOrderedDate,
          concreteRequestDetail.pumpWorkStart,
          eventTimeZone.timezone,
          projectDetails.ProjectSettings.deliveryWindowTime,
          projectDetails.ProjectSettings.deliveryWindowTimeUnit,
        );
        pumpEndDate = await this.compareDeliveryDateWithDeliveryWindowDate(
          concreteRequestDetail.pumpOrderedDate,
          concreteRequestDetail.pumpWorkEnd,
          eventTimeZone.timezone,
          projectDetails.ProjectSettings.deliveryWindowTime,
          projectDetails.ProjectSettings.deliveryWindowTimeUnit,
        );
        if (+memberDetails.RoleId === 4 && (pumpStartDate || pumpEndDate)) {
          return done(null, { message: 'Please enter Future Pump Ordered Date/Time' });
        }
      }
      if (projectDetails && projectDetails.ProjectSettings) {
        this.checkInputDatas(inputData, async (checkResponse, checkError) => {
          if (checkError) {
            return done(null, checkError);
          }
          if (memberDetails) {
            const roleDetails = await Role.getBy('Project Admin');
            const accountRoleDetails = await Role.getBy('Account Admin');
            let lastData = {};
            lastData = await ConcreteRequest.findOne({
              where: { ProjectId: +memberDetails.ProjectId, isDeleted: false },
              order: [['ConcreteRequestId', 'DESC']],
            });
            if (lastData) {
              const data = lastData.ConcreteRequestId;
              lastData.ConcreteRequestId = 0;
              lastData.ConcreteRequestId = data + 1;
            } else {
              lastData = {};
              lastData.ConcreteRequestId = 1;
            }
            let id = 0;
            const newValue = JSON.parse(JSON.stringify(lastData));
            if (
              newValue &&
              newValue.ConcreteRequestId !== null &&
              newValue.ConcreteRequestId !== undefined
            ) {
              id = newValue.ConcreteRequestId;
            }
            const range = momentRange.range(
              moment(concreteRequestDetail.concretePlacementStart),
              moment(concreteRequestDetail.concretePlacementEnd),
            );
            let totalDays = Array.from(range.by('day'));
            const eventsArray = [];
            let concreteRequestParam = {};
            if (concreteRequestDetail.recurrence === 'Daily') {
              const startTime = concreteRequestDetail.startPicker;
              const endTime = concreteRequestDetail.endPicker;
              let dailyIndex = 0;
              const recurrenceId = await this.insertRecurrenceSeries(
                concreteRequestDetail,
                inputData.user,
                'concreteRequest',
                eventTimeZone.timezone,
              );
              while (dailyIndex < totalDays.length) {
                const data = totalDays[dailyIndex];
                if (
                  moment(data).isBetween(
                    moment(concreteRequestDetail.concretePlacementStart),
                    moment(concreteRequestDetail.concretePlacementEnd),
                    null,
                    '[]',
                  ) ||
                  moment(data).isSame(concreteRequestDetail.concretePlacementStart) ||
                  moment(data).isSame(concreteRequestDetail.concretePlacementEnd)
                ) {
                  id += 1;
                  const date = moment(data).format('MM/DD/YYYY');
                  const chosenTimezoneConcretePlacementStart = moment.tz(
                    `${date} ${startTime}`,
                    'MM/DD/YYYY HH:mm',
                    eventTimeZone.timezone,
                  );
                  const chosenTimezoneConcretePlacementEnd = moment.tz(
                    `${date} ${endTime}`,
                    'MM/DD/YYYY HH:mm',
                    eventTimeZone.timezone,
                  );
                  const chosenTimezonePumpOrderedDate = moment.tz(
                    `${moment(concreteRequestDetail.pumpOrderedDate, 'MM/DD/YYYY').format(
                      'MM/DD/YYYY',
                    )} ${concreteRequestDetail.pumpWorkStart}`,
                    'MM/DD/YYYY HH:mm',
                    eventTimeZone.timezone,
                  );
                  const chosenTimezonePumpWorkStart = moment.tz(
                    `${moment(concreteRequestDetail.pumpOrderedDate, 'MM/DD/YYYY').format(
                      'MM/DD/YYYY',
                    )} ${concreteRequestDetail.pumpWorkStart}`,
                    'MM/DD/YYYY HH:mm',
                    eventTimeZone.timezone,
                  );
                  const chosenTimezonePumpWorkEnd = moment.tz(
                    `${moment(concreteRequestDetail.pumpOrderedDate, 'MM/DD/YYYY').format(
                      'MM/DD/YYYY',
                    )} ${concreteRequestDetail.pumpWorkEnd}`,
                    'MM/DD/YYYY HH:mm',
                    eventTimeZone.timezone,
                  );
                  concreteRequestParam = {
                    description: concreteRequestDetail.description,
                    ProjectId: concreteRequestDetail.ProjectId,
                    notes: concreteRequestDetail.notes,
                    concretePlacementStart: chosenTimezoneConcretePlacementStart
                      .clone()
                      .tz('UTC')
                      .format('YYYY-MM-DD HH:mm:ssZ'),
                    concretePlacementEnd: chosenTimezoneConcretePlacementEnd
                      .clone()
                      .tz('UTC')
                      .format('YYYY-MM-DD HH:mm:ssZ'),
                    isPumpConfirmed: concreteRequestDetail.isPumpConfirmed,
                    isPumpRequired: concreteRequestDetail.isPumpRequired,
                    isConcreteConfirmed: concreteRequestDetail.isConcreteConfirmed,
                    ParentCompanyId: concreteRequestDetail.ParentCompanyId,
                    concreteOrderNumber: concreteRequestDetail.concreteOrderNumber,
                    truckSpacingHours: concreteRequestDetail.truckSpacingHours,
                    slump: concreteRequestDetail.slump,
                    concreteQuantityOrdered: concreteRequestDetail.concreteQuantityOrdered,
                    concreteConfirmedOn: concreteRequestDetail.concreteConfirmedOn
                      ? concreteRequestDetail.concreteConfirmedOn
                      : null,
                    pumpLocation: concreteRequestDetail.pumpLocation,
                    pumpOrderedDate:
                      concreteRequestDetail.pumpOrderedDate && concreteRequestDetail.pumpWorkStart
                        ? chosenTimezonePumpOrderedDate
                          .clone()
                          .tz('UTC')
                          .format('YYYY-MM-DD HH:mm:ssZ')
                        : null,
                    pumpWorkStart:
                      concreteRequestDetail.pumpOrderedDate && concreteRequestDetail.pumpWorkStart
                        ? chosenTimezonePumpWorkStart
                          .clone()
                          .tz('UTC')
                          .format('YYYY-MM-DD HH:mm:ssZ')
                        : null,
                    pumpWorkEnd:
                      concreteRequestDetail.pumpOrderedDate && concreteRequestDetail.pumpWorkEnd
                        ? chosenTimezonePumpWorkEnd.clone().tz('UTC').format('YYYY-MM-DD HH:mm:ssZ')
                        : null,
                    pumpConfirmedOn: concreteRequestDetail.pumpConfirmedOn
                      ? concreteRequestDetail.pumpConfirmedOn
                      : null,
                    cubicYardsTotal: concreteRequestDetail.cubicYardsTotal,
                    hoursToCompletePlacement: concreteRequestDetail.hoursToCompletePlacement,
                    minutesToCompletePlacement: concreteRequestDetail.minutesToCompletePlacement,
                    ConcreteRequestId: id,
                    requestType: 'concreteRequest',
                    status: 'Tentative',
                    primerForPump: concreteRequestDetail.primerForPump,
                    createdBy: memberDetails.id,
                    recurrenceId,
                    LocationId: concreteRequestDetail.LocationId,
                    OriginationAddress: concreteRequestDetail.originationAddress,
                    vehicleType: concreteRequestDetail.vehicleType,
                    OriginationAddressPump: concreteRequestDetail.originationAddressPump,
                    vehicleTypePump: concreteRequestDetail.vehicleTypePump,
                  };
                  if (
                    memberDetails.RoleId === roleDetails.id ||
                    memberDetails.RoleId === accountRoleDetails.id ||
                    memberDetails.isAutoApproveEnabled ||
                    projectDetails.ProjectSettings.isAutoApprovalEnabled
                  ) {
                    concreteRequestParam.status = 'Approved';
                    concreteRequestParam.approvedBy = memberDetails.id;
                    concreteRequestParam.approved_at = new Date();
                  }
                  eventsArray.push(concreteRequestParam);
                  // eslint-disable-next-line no-const-assign
                  dailyIndex += +concreteRequestDetail.repeatEveryCount;
                }
              }
              if (eventsArray && eventsArray.length > 0) {
                const isOverlapping = await this.checkDoubleBookingAllowedOrNot(
                  eventsArray,
                  projectDetails,
                  'add',
                );
                if (isOverlapping && isOverlapping.error) {
                  return done(null, {
                    message: isOverlapping.message,
                  });
                }
              }
            }
            if (concreteRequestDetail.recurrence === 'Weekly') {
              const startTime = concreteRequestDetail.startPicker;
              const endTime = concreteRequestDetail.endPicker;
              const startDayWeek = moment(concreteRequestDetail.concretePlacementStart).startOf(
                'week',
              );
              const endDayWeek = moment(concreteRequestDetail.concretePlacementEnd).endOf('week');
              const range1 = momentRange.range(moment(startDayWeek), moment(endDayWeek));
              const totalDaysOfRecurrence = Array.from(range1.by('day'));
              const recurrenceId = await this.insertRecurrenceSeries(
                concreteRequestDetail,
                inputData.user,
                'concreteRequest',
                eventTimeZone.timezone,
              );
              totalDays = totalDaysOfRecurrence;
              let count;
              let weekIncrement;
              if (+concreteRequestDetail.repeatEveryCount > 1) {
                count = +concreteRequestDetail.repeatEveryCount - 1;
                weekIncrement = 7;
              } else {
                count = 1;
                weekIncrement = 0;
              }
              for (
                let indexba = 0;
                indexba < totalDaysOfRecurrence.length;
                indexba += weekIncrement * count
              ) {
                const totalLength = indexba + 6;
                for (let indexb = indexba; indexb <= totalLength; indexb += 1) {
                  const data = totalDaysOfRecurrence[indexb];
                  indexba += 1;
                  if (
                    data &&
                    !moment(data).isBefore(concreteRequestDetail.concretePlacementStart) &&
                    !moment(data).isAfter(concreteRequestDetail.concretePlacementEnd)
                  ) {
                    const day = moment(data).format('dddd');
                    const indexVal = concreteRequestDetail.days.includes(day);
                    if (indexVal) {
                      id += 1;
                      const date = moment(data).format('MM/DD/YYYY');
                      const chosenTimezoneConcretePlacementStart = moment.tz(
                        `${date} ${startTime}`,
                        'MM/DD/YYYY HH:mm',
                        eventTimeZone.timezone,
                      );
                      const chosenTimezoneConcretePlacementEnd = moment.tz(
                        `${date} ${endTime}`,
                        'MM/DD/YYYY HH:mm',
                        eventTimeZone.timezone,
                      );
                      const chosenTimezonePumpOrderedDate = moment.tz(
                        `${moment(concreteRequestDetail.pumpOrderedDate, 'MM/DD/YYYY').format(
                          'MM/DD/YYYY',
                        )} ${concreteRequestDetail.pumpWorkStart}`,
                        'MM/DD/YYYY HH:mm',
                        eventTimeZone.timezone,
                      );
                      const chosenTimezonePumpWorkStart = moment.tz(
                        `${moment(concreteRequestDetail.pumpOrderedDate, 'MM/DD/YYYY').format(
                          'MM/DD/YYYY',
                        )} ${concreteRequestDetail.pumpWorkStart}`,
                        'MM/DD/YYYY HH:mm',
                        eventTimeZone.timezone,
                      );
                      const chosenTimezonePumpWorkEnd = moment.tz(
                        `${moment(concreteRequestDetail.pumpOrderedDate, 'MM/DD/YYYY').format(
                          'MM/DD/YYYY',
                        )} ${concreteRequestDetail.pumpWorkEnd}`,
                        'MM/DD/YYYY HH:mm',
                        eventTimeZone.timezone,
                      );
                      concreteRequestParam = {
                        description: concreteRequestDetail.description,
                        ProjectId: concreteRequestDetail.ProjectId,
                        notes: concreteRequestDetail.notes,
                        concretePlacementStart: chosenTimezoneConcretePlacementStart
                          .clone()
                          .tz('UTC')
                          .format('YYYY-MM-DD HH:mm:ssZ'),
                        concretePlacementEnd: chosenTimezoneConcretePlacementEnd
                          .clone()
                          .tz('UTC')
                          .format('YYYY-MM-DD HH:mm:ssZ'),
                        isPumpConfirmed: concreteRequestDetail.isPumpConfirmed,
                        isPumpRequired: concreteRequestDetail.isPumpRequired,
                        isConcreteConfirmed: concreteRequestDetail.isConcreteConfirmed,
                        ParentCompanyId: concreteRequestDetail.ParentCompanyId,
                        concreteOrderNumber: concreteRequestDetail.concreteOrderNumber,
                        truckSpacingHours: concreteRequestDetail.truckSpacingHours,
                        slump: concreteRequestDetail.slump,
                        concreteQuantityOrdered: concreteRequestDetail.concreteQuantityOrdered,
                        concreteConfirmedOn: concreteRequestDetail.concreteConfirmedOn
                          ? concreteRequestDetail.concreteConfirmedOn
                          : null,
                        pumpLocation: concreteRequestDetail.pumpLocation,
                        pumpOrderedDate:
                          concreteRequestDetail.pumpOrderedDate &&
                            concreteRequestDetail.pumpWorkStart
                            ? chosenTimezonePumpOrderedDate
                              .clone()
                              .tz('UTC')
                              .format('YYYY-MM-DD HH:mm:ssZ')
                            : null,
                        pumpWorkStart:
                          concreteRequestDetail.pumpOrderedDate &&
                            concreteRequestDetail.pumpWorkStart
                            ? chosenTimezonePumpWorkStart
                              .clone()
                              .tz('UTC')
                              .format('YYYY-MM-DD HH:mm:ssZ')
                            : null,
                        pumpWorkEnd:
                          concreteRequestDetail.pumpOrderedDate && concreteRequestDetail.pumpWorkEnd
                            ? chosenTimezonePumpWorkEnd
                              .clone()
                              .tz('UTC')
                              .format('YYYY-MM-DD HH:mm:ssZ')
                            : null,
                        pumpConfirmedOn: concreteRequestDetail.pumpConfirmedOn
                          ? concreteRequestDetail.pumpConfirmedOn
                          : null,
                        cubicYardsTotal: concreteRequestDetail.cubicYardsTotal,
                        hoursToCompletePlacement: concreteRequestDetail.hoursToCompletePlacement,
                        minutesToCompletePlacement:
                          concreteRequestDetail.minutesToCompletePlacement,
                        ConcreteRequestId: id,
                        requestType: 'concreteRequest',
                        status: 'Tentative',
                        primerForPump: concreteRequestDetail.primerForPump,
                        createdBy: memberDetails.id,
                        recurrenceId,
                        LocationId: concreteRequestDetail.LocationId,
                        OriginationAddress: concreteRequestDetail.originationAddress,
                        vehicleType: concreteRequestDetail.vehicleType,
                        OriginationAddressPump: concreteRequestDetail.originationAddressPump,
                        vehicleTypePump: concreteRequestDetail.vehicleTypePump,
                      };
                      if (
                        memberDetails.RoleId === roleDetails.id ||
                        memberDetails.RoleId === accountRoleDetails.id ||
                        memberDetails.isAutoApproveEnabled ||
                        projectDetails.ProjectSettings.isAutoApprovalEnabled
                      ) {
                        concreteRequestParam.status = 'Approved';
                        concreteRequestParam.approvedBy = memberDetails.id;
                        concreteRequestParam.approved_at = new Date();
                      }
                      eventsArray.push(concreteRequestParam);
                    }
                  }
                }
              }
              if (eventsArray && eventsArray.length > 0) {
                const isOverlapping = await this.checkDoubleBookingAllowedOrNot(
                  eventsArray,
                  projectDetails,
                  'add',
                );
                if (isOverlapping && isOverlapping.error) {
                  return done(null, {
                    message: isOverlapping.message,
                  });
                }
              }
            }
            if (concreteRequestDetail.recurrence === 'Monthly') {
              const startTime = concreteRequestDetail.startPicker;
              const endTime = concreteRequestDetail.endPicker;
              const startMonth = moment(concreteRequestDetail.concretePlacementStart).startOf(
                'month',
              );
              const startMonthNumber = moment(startMonth).format('MM');
              const endMonth = moment(concreteRequestDetail.concretePlacementEnd).endOf('month');
              const endMonthNumber = moment(endMonth).format('MM');
              let startDate1 = moment(concreteRequestDetail.concretePlacementStart);
              const endDate1 = moment(concreteRequestDetail.concretePlacementEnd).endOf('month');
              const allMonthsInPeriod = [];
              const recurrenceId = await this.insertRecurrenceSeries(
                concreteRequestDetail,
                inputData.user,
                'concreteRequest',
                eventTimeZone.timezone,
              );
              while (startDate1.isBefore(endDate1)) {
                allMonthsInPeriod.push(startDate1.format('YYYY-MM'));
                startDate1 = startDate1.add(1, 'month');
              }
              let currentMonthDates = [];
              let totalNumberOfMonths = endMonthNumber - startMonthNumber;
              if (totalNumberOfMonths < 0) {
                totalNumberOfMonths *= -1;
              }
              let k = 0;
              while (k < allMonthsInPeriod.length + 1) {
                currentMonthDates = Array.from(
                  { length: moment(allMonthsInPeriod[k], 'YYYY-MM').daysInMonth() },
                  (x, j) => moment(allMonthsInPeriod[k], 'YYYY-MM').startOf('month').add(j, 'days'),
                );
                if (concreteRequestDetail.chosenDateOfMonth) {
                  const getDate = currentMonthDates.filter(
                    (value) => moment(value).format('DD') === concreteRequestDetail.dateOfMonth,
                  );
                  if (getDate.length === 1) {
                    if (
                      moment(getDate[0]).isBetween(
                        moment(concreteRequestDetail.concretePlacementStart),
                        moment(concreteRequestDetail.concretePlacementEnd),
                        null,
                        '[]',
                      ) ||
                      moment(getDate[0]).isSame(concreteRequestDetail.concretePlacementStart) ||
                      moment(getDate[0]).isSame(concreteRequestDetail.concretePlacementEnd)
                    ) {
                      id += 1;
                      const date = moment(getDate[0].toDate()).format('MM/DD/YYYY');
                      const chosenTimezoneConcretePlacementStart = moment.tz(
                        `${date} ${startTime}`,
                        'MM/DD/YYYY HH:mm',
                        eventTimeZone.timezone,
                      );
                      const chosenTimezoneConcretePlacementEnd = moment.tz(
                        `${date} ${endTime}`,
                        'MM/DD/YYYY HH:mm',
                        eventTimeZone.timezone,
                      );
                      const chosenTimezonePumpOrderedDate = moment.tz(
                        `${moment(concreteRequestDetail.pumpOrderedDate, 'MM/DD/YYYY').format(
                          'MM/DD/YYYY',
                        )} ${concreteRequestDetail.pumpWorkStart}`,
                        'MM/DD/YYYY HH:mm',
                        eventTimeZone.timezone,
                      );
                      const chosenTimezonePumpWorkStart = moment.tz(
                        `${moment(concreteRequestDetail.pumpOrderedDate, 'MM/DD/YYYY').format(
                          'MM/DD/YYYY',
                        )} ${concreteRequestDetail.pumpWorkStart}`,
                        'MM/DD/YYYY HH:mm',
                        eventTimeZone.timezone,
                      );
                      const chosenTimezonePumpWorkEnd = moment.tz(
                        `${moment(concreteRequestDetail.pumpOrderedDate, 'MM/DD/YYYY').format(
                          'MM/DD/YYYY',
                        )} ${concreteRequestDetail.pumpWorkEnd}`,
                        'MM/DD/YYYY HH:mm',
                        eventTimeZone.timezone,
                      );
                      concreteRequestParam = {
                        description: concreteRequestDetail.description,
                        ProjectId: concreteRequestDetail.ProjectId,
                        notes: concreteRequestDetail.notes,
                        concretePlacementStart: chosenTimezoneConcretePlacementStart
                          .clone()
                          .tz('UTC')
                          .format('YYYY-MM-DD HH:mm:ssZ'),
                        concretePlacementEnd: chosenTimezoneConcretePlacementEnd
                          .clone()
                          .tz('UTC')
                          .format('YYYY-MM-DD HH:mm:ssZ'),
                        isPumpConfirmed: concreteRequestDetail.isPumpConfirmed,
                        isPumpRequired: concreteRequestDetail.isPumpRequired,
                        isConcreteConfirmed: concreteRequestDetail.isConcreteConfirmed,
                        ParentCompanyId: concreteRequestDetail.ParentCompanyId,
                        concreteOrderNumber: concreteRequestDetail.concreteOrderNumber,
                        truckSpacingHours: concreteRequestDetail.truckSpacingHours,
                        slump: concreteRequestDetail.slump,
                        concreteQuantityOrdered: concreteRequestDetail.concreteQuantityOrdered,
                        concreteConfirmedOn: concreteRequestDetail.concreteConfirmedOn
                          ? concreteRequestDetail.concreteConfirmedOn
                          : null,
                        pumpLocation: concreteRequestDetail.pumpLocation,
                        pumpOrderedDate:
                          concreteRequestDetail.pumpOrderedDate &&
                            concreteRequestDetail.pumpWorkStart
                            ? chosenTimezonePumpOrderedDate
                              .clone()
                              .tz('UTC')
                              .format('YYYY-MM-DD HH:mm:ssZ')
                            : null,
                        pumpWorkStart:
                          concreteRequestDetail.pumpOrderedDate &&
                            concreteRequestDetail.pumpWorkStart
                            ? chosenTimezonePumpWorkStart
                              .clone()
                              .tz('UTC')
                              .format('YYYY-MM-DD HH:mm:ssZ')
                            : null,
                        pumpWorkEnd:
                          concreteRequestDetail.pumpOrderedDate && concreteRequestDetail.pumpWorkEnd
                            ? chosenTimezonePumpWorkEnd
                              .clone()
                              .tz('UTC')
                              .format('YYYY-MM-DD HH:mm:ssZ')
                            : null,
                        pumpConfirmedOn: concreteRequestDetail.pumpConfirmedOn
                          ? concreteRequestDetail.pumpConfirmedOn
                          : null,
                        cubicYardsTotal: concreteRequestDetail.cubicYardsTotal,
                        hoursToCompletePlacement: concreteRequestDetail.hoursToCompletePlacement,
                        minutesToCompletePlacement:
                          concreteRequestDetail.minutesToCompletePlacement,
                        ConcreteRequestId: id,
                        requestType: 'concreteRequest',
                        status: 'Tentative',
                        primerForPump: concreteRequestDetail.primerForPump,
                        createdBy: memberDetails.id,
                        recurrenceId,
                        LocationId: concreteRequestDetail.LocationId,
                        OriginationAddress: concreteRequestDetail.originationAddress,
                        vehicleType: concreteRequestDetail.vehicleType,
                        OriginationAddressPump: concreteRequestDetail.originationAddressPump,
                        vehicleTypePump: concreteRequestDetail.vehicleTypePump,
                      };
                      if (
                        memberDetails.RoleId === roleDetails.id ||
                        memberDetails.RoleId === accountRoleDetails.id ||
                        memberDetails.isAutoApproveEnabled ||
                        projectDetails.ProjectSettings.isAutoApprovalEnabled
                      ) {
                        concreteRequestParam.status = 'Approved';
                        concreteRequestParam.approvedBy = memberDetails.id;
                        concreteRequestParam.approved_at = new Date();
                      }
                      eventsArray.push(concreteRequestParam);
                    }
                  }
                } else if (allMonthsInPeriod[k]) {
                  const dayOfMonth = concreteRequestDetail.monthlyRepeatType;
                  const week = dayOfMonth.split(' ')[0].toLowerCase();
                  const day = dayOfMonth.split(' ')[1].toLowerCase();
                  const chosenDay = moment(allMonthsInPeriod[k], 'YYYY-MM')
                    .startOf('month')
                    .day(day);
                  const getAllDays = [];
                  if (chosenDay.date() > 7) chosenDay.add(7, 'd');
                  const month = chosenDay.month();
                  while (month === chosenDay.month()) {
                    getAllDays.push(chosenDay.toString());
                    chosenDay.add(7, 'd');
                  }
                  let i = 0;
                  if (week === 'second') {
                    i += 1;
                  } else if (week === 'third') {
                    i += 2;
                  } else if (week === 'fourth') {
                    i += 3;
                  } else if (week === 'last') {
                    i = getAllDays.length - 1;
                  }
                  const finalDay = getAllDays[i];
                  if (
                    moment(finalDay).isBetween(
                      moment(concreteRequestDetail.concretePlacementStart),
                      moment(concreteRequestDetail.concretePlacementEnd),
                      null,
                      '[]',
                    ) ||
                    moment(finalDay).isSame(concreteRequestDetail.concretePlacementStart) ||
                    moment(finalDay).isSame(concreteRequestDetail.concretePlacementEnd)
                  ) {
                    id += 1;
                    const date = moment(finalDay).format('MM/DD/YYYY');
                    const chosenTimezoneConcretePlacementStart = moment.tz(
                      `${date} ${startTime}`,
                      'MM/DD/YYYY HH:mm',
                      eventTimeZone.timezone,
                    );
                    const chosenTimezoneConcretePlacementEnd = moment.tz(
                      `${date} ${endTime}`,
                      'MM/DD/YYYY HH:mm',
                      eventTimeZone.timezone,
                    );
                    const chosenTimezonePumpOrderedDate = moment.tz(
                      `${moment(concreteRequestDetail.pumpOrderedDate, 'MM/DD/YYYY').format(
                        'MM/DD/YYYY',
                      )} ${concreteRequestDetail.pumpWorkStart}`,
                      'MM/DD/YYYY HH:mm',
                      eventTimeZone.timezone,
                    );
                    const chosenTimezonePumpWorkStart = moment.tz(
                      `${moment(concreteRequestDetail.pumpOrderedDate, 'MM/DD/YYYY').format(
                        'MM/DD/YYYY',
                      )} ${concreteRequestDetail.pumpWorkStart}`,
                      'MM/DD/YYYY HH:mm',
                      eventTimeZone.timezone,
                    );
                    const chosenTimezonePumpWorkEnd = moment.tz(
                      `${moment(concreteRequestDetail.pumpOrderedDate, 'MM/DD/YYYY').format(
                        'MM/DD/YYYY',
                      )} ${concreteRequestDetail.pumpWorkEnd}`,
                      'MM/DD/YYYY HH:mm',
                      eventTimeZone.timezone,
                    );
                    concreteRequestParam = {
                      description: concreteRequestDetail.description,
                      ProjectId: concreteRequestDetail.ProjectId,
                      notes: concreteRequestDetail.notes,
                      concretePlacementStart: chosenTimezoneConcretePlacementStart
                        .clone()
                        .tz('UTC')
                        .format('YYYY-MM-DD HH:mm:ssZ'),
                      concretePlacementEnd: chosenTimezoneConcretePlacementEnd
                        .clone()
                        .tz('UTC')
                        .format('YYYY-MM-DD HH:mm:ssZ'),
                      isPumpConfirmed: concreteRequestDetail.isPumpConfirmed,
                      isPumpRequired: concreteRequestDetail.isPumpRequired,
                      isConcreteConfirmed: concreteRequestDetail.isConcreteConfirmed,
                      ParentCompanyId: concreteRequestDetail.ParentCompanyId,
                      concreteOrderNumber: concreteRequestDetail.concreteOrderNumber,
                      truckSpacingHours: concreteRequestDetail.truckSpacingHours,
                      slump: concreteRequestDetail.slump,
                      concreteQuantityOrdered: concreteRequestDetail.concreteQuantityOrdered,
                      concreteConfirmedOn: concreteRequestDetail.concreteConfirmedOn
                        ? concreteRequestDetail.concreteConfirmedOn
                        : null,
                      pumpLocation: concreteRequestDetail.pumpLocation,
                      pumpOrderedDate:
                        concreteRequestDetail.pumpOrderedDate && concreteRequestDetail.pumpWorkStart
                          ? chosenTimezonePumpOrderedDate
                            .clone()
                            .tz('UTC')
                            .format('YYYY-MM-DD HH:mm:ssZ')
                          : null,
                      pumpWorkStart:
                        concreteRequestDetail.pumpOrderedDate && concreteRequestDetail.pumpWorkStart
                          ? chosenTimezonePumpWorkStart
                            .clone()
                            .tz('UTC')
                            .format('YYYY-MM-DD HH:mm:ssZ')
                          : null,
                      pumpWorkEnd:
                        concreteRequestDetail.pumpOrderedDate && concreteRequestDetail.pumpWorkEnd
                          ? chosenTimezonePumpWorkEnd
                            .clone()
                            .tz('UTC')
                            .format('YYYY-MM-DD HH:mm:ssZ')
                          : null,
                      pumpConfirmedOn: concreteRequestDetail.pumpConfirmedOn
                        ? concreteRequestDetail.pumpConfirmedOn
                        : null,
                      cubicYardsTotal: concreteRequestDetail.cubicYardsTotal,
                      hoursToCompletePlacement: concreteRequestDetail.hoursToCompletePlacement,
                      minutesToCompletePlacement: concreteRequestDetail.minutesToCompletePlacement,
                      ConcreteRequestId: id,
                      requestType: 'concreteRequest',
                      status: 'Tentative',
                      primerForPump: concreteRequestDetail.primerForPump,
                      createdBy: memberDetails.id,
                      recurrenceId,
                      LocationId: concreteRequestDetail.LocationId,
                      OriginationAddress: concreteRequestDetail.originationAddress,
                      vehicleType: concreteRequestDetail.vehicleType,
                      OriginationAddressPump: concreteRequestDetail.originationAddressPump,
                      vehicleTypePump: concreteRequestDetail.vehicleTypePump,
                    };
                    if (
                      memberDetails.RoleId === roleDetails.id ||
                      memberDetails.RoleId === accountRoleDetails.id ||
                      memberDetails.isAutoApproveEnabled ||
                      projectDetails.ProjectSettings.isAutoApprovalEnabled
                    ) {
                      concreteRequestParam.status = 'Approved';
                      concreteRequestParam.approvedBy = memberDetails.id;
                      concreteRequestParam.approved_at = new Date();
                    }
                    eventsArray.push(concreteRequestParam);
                  }
                }
                k += +concreteRequestDetail.repeatEveryCount;
              }
              if (eventsArray && eventsArray.length > 0) {
                const isOverlapping = await this.checkDoubleBookingAllowedOrNot(
                  eventsArray,
                  projectDetails,
                  'add',
                );
                if (isOverlapping && isOverlapping.error) {
                  return done(null, {
                    message: isOverlapping.message,
                  });
                }
              }
            }
            if (concreteRequestDetail.recurrence === 'Yearly') {
              const startTime = concreteRequestDetail.startPicker;
              const endTime = concreteRequestDetail.endPicker;
              const startMonth = moment(concreteRequestDetail.concretePlacementStart).startOf(
                'month',
              );
              const startMonthNumber = moment(startMonth).format('MM');
              const endMonth = moment(concreteRequestDetail.concretePlacementEnd).endOf('month');
              const endMonthNumber = moment(endMonth).format('MM');
              let startDate1 = moment(concreteRequestDetail.concretePlacementStart);
              const endDate1 = moment(concreteRequestDetail.concretePlacementEnd).endOf('month');
              const allMonthsInPeriod = [];
              const recurrenceId = await this.insertRecurrenceSeries(
                concreteRequestDetail,
                inputData.user,
                'concreteRequest',
                eventTimeZone.timezone,
              );
              while (startDate1.isBefore(endDate1)) {
                allMonthsInPeriod.push(startDate1.format('YYYY-MM'));
                startDate1 = startDate1.add(12, 'month');
              }
              let currentMonthDates = [];
              let totalNumberOfMonths = endMonthNumber - startMonthNumber;
              if (totalNumberOfMonths < 0) {
                totalNumberOfMonths *= -1;
              }
              for (let k = 0; k < allMonthsInPeriod.length + 1; k += 1) {
                currentMonthDates = Array.from(
                  { length: moment(allMonthsInPeriod[k], 'YYYY-MM').daysInMonth() },
                  (x, j) => moment(allMonthsInPeriod[k], 'YYYY-MM').startOf('month').add(j, 'days'),
                );
                if (concreteRequestDetail.chosenDateOfMonth) {
                  const getDate = currentMonthDates.filter(
                    (value) => moment(value).format('DD') === concreteRequestDetail.dateOfMonth,
                  );
                  if (getDate.length === 1) {
                    if (
                      moment(getDate[0]).isBetween(
                        moment(concreteRequestDetail.concretePlacementStart),
                        moment(concreteRequestDetail.concretePlacementEnd),
                        null,
                        '[]',
                      ) ||
                      moment(getDate[0]).isSame(concreteRequestDetail.concretePlacementStart) ||
                      moment(getDate[0]).isSame(concreteRequestDetail.concretePlacementEnd)
                    ) {
                      id += 1;
                      const date = moment(getDate[0].toDate()).format('MM/DD/YYYY');
                      const chosenTimezoneConcretePlacementStart = moment.tz(
                        `${date} ${startTime}`,
                        'MM/DD/YYYY HH:mm',
                        eventTimeZone.timezone,
                      );
                      const chosenTimezoneConcretePlacementEnd = moment.tz(
                        `${date} ${endTime}`,
                        'MM/DD/YYYY HH:mm',
                        eventTimeZone.timezone,
                      );
                      const chosenTimezonePumpOrderedDate = moment.tz(
                        `${moment(concreteRequestDetail.pumpOrderedDate, 'MM/DD/YYYY').format(
                          'MM/DD/YYYY',
                        )} ${concreteRequestDetail.pumpWorkStart}`,
                        'MM/DD/YYYY HH:mm',
                        eventTimeZone.timezone,
                      );
                      const chosenTimezonePumpWorkStart = moment.tz(
                        `${moment(concreteRequestDetail.pumpOrderedDate, 'MM/DD/YYYY').format(
                          'MM/DD/YYYY',
                        )} ${concreteRequestDetail.pumpWorkStart}`,
                        'MM/DD/YYYY HH:mm',
                        eventTimeZone.timezone,
                      );
                      const chosenTimezonePumpWorkEnd = moment.tz(
                        `${moment(concreteRequestDetail.pumpOrderedDate, 'MM/DD/YYYY').format(
                          'MM/DD/YYYY',
                        )} ${concreteRequestDetail.pumpWorkEnd}`,
                        'MM/DD/YYYY HH:mm',
                        eventTimeZone.timezone,
                      );
                      concreteRequestParam = {
                        description: concreteRequestDetail.description,
                        ProjectId: concreteRequestDetail.ProjectId,
                        notes: concreteRequestDetail.notes,
                        concretePlacementStart: chosenTimezoneConcretePlacementStart
                          .clone()
                          .tz('UTC')
                          .format('YYYY-MM-DD HH:mm:ssZ'),
                        concretePlacementEnd: chosenTimezoneConcretePlacementEnd
                          .clone()
                          .tz('UTC')
                          .format('YYYY-MM-DD HH:mm:ssZ'),
                        isPumpConfirmed: concreteRequestDetail.isPumpConfirmed,
                        isPumpRequired: concreteRequestDetail.isPumpRequired,
                        isConcreteConfirmed: concreteRequestDetail.isConcreteConfirmed,
                        ParentCompanyId: concreteRequestDetail.ParentCompanyId,
                        concreteOrderNumber: concreteRequestDetail.concreteOrderNumber,
                        truckSpacingHours: concreteRequestDetail.truckSpacingHours,
                        slump: concreteRequestDetail.slump,
                        concreteQuantityOrdered: concreteRequestDetail.concreteQuantityOrdered,
                        concreteConfirmedOn: concreteRequestDetail.concreteConfirmedOn
                          ? concreteRequestDetail.concreteConfirmedOn
                          : null,
                        pumpLocation: concreteRequestDetail.pumpLocation,
                        pumpOrderedDate:
                          concreteRequestDetail.pumpOrderedDate &&
                            concreteRequestDetail.pumpWorkStart
                            ? chosenTimezonePumpOrderedDate
                              .clone()
                              .tz('UTC')
                              .format('YYYY-MM-DD HH:mm:ssZ')
                            : null,
                        pumpWorkStart:
                          concreteRequestDetail.pumpOrderedDate &&
                            concreteRequestDetail.pumpWorkStart
                            ? chosenTimezonePumpWorkStart
                              .clone()
                              .tz('UTC')
                              .format('YYYY-MM-DD HH:mm:ssZ')
                            : null,
                        pumpWorkEnd:
                          concreteRequestDetail.pumpOrderedDate && concreteRequestDetail.pumpWorkEnd
                            ? chosenTimezonePumpWorkEnd
                              .clone()
                              .tz('UTC')
                              .format('YYYY-MM-DD HH:mm:ssZ')
                            : null,
                        pumpConfirmedOn: concreteRequestDetail.pumpConfirmedOn
                          ? concreteRequestDetail.pumpConfirmedOn
                          : null,
                        cubicYardsTotal: concreteRequestDetail.cubicYardsTotal,
                        hoursToCompletePlacement: concreteRequestDetail.hoursToCompletePlacement,
                        minutesToCompletePlacement:
                          concreteRequestDetail.minutesToCompletePlacement,
                        ConcreteRequestId: id,
                        requestType: 'concreteRequest',
                        status: 'Tentative',
                        primerForPump: concreteRequestDetail.primerForPump,
                        createdBy: memberDetails.id,
                        recurrenceId,
                        LocationId: concreteRequestDetail.LocationId,
                        OriginationAddress: concreteRequestDetail.originationAddress,
                        vehicleType: concreteRequestDetail.vehicleType,
                        OriginationAddressPump: concreteRequestDetail.originationAddressPump,
                        vehicleTypePump: concreteRequestDetail.vehicleTypePump,
                      };
                      if (
                        memberDetails.RoleId === roleDetails.id ||
                        memberDetails.RoleId === accountRoleDetails.id ||
                        memberDetails.isAutoApproveEnabled ||
                        projectDetails.ProjectSettings.isAutoApprovalEnabled
                      ) {
                        concreteRequestParam.status = 'Approved';
                        concreteRequestParam.approvedBy = memberDetails.id;
                        concreteRequestParam.approved_at = new Date();
                      }
                      eventsArray.push(concreteRequestParam);
                    }
                  }
                } else if (allMonthsInPeriod[k]) {
                  const dayOfMonth = concreteRequestDetail.monthlyRepeatType;
                  const week = dayOfMonth.split(' ')[0].toLowerCase();
                  const day = dayOfMonth.split(' ')[1].toLowerCase();
                  const chosenDay = moment(allMonthsInPeriod[k], 'YYYY-MM')
                    .startOf('month')
                    .day(day);
                  const getAllDays = [];
                  if (chosenDay.date() > 7) chosenDay.add(7, 'd');
                  const month = chosenDay.month();
                  while (month === chosenDay.month()) {
                    getAllDays.push(chosenDay.toString());
                    chosenDay.add(7, 'd');
                  }
                  let i = 0;
                  if (week === 'second') {
                    i += 1;
                  } else if (week === 'third') {
                    i += 2;
                  } else if (week === 'fourth') {
                    i += 3;
                  } else if (week === 'last') {
                    i = getAllDays.length - 1;
                  }
                  const finalDay = getAllDays[i];
                  if (
                    moment(finalDay).isBetween(
                      moment(concreteRequestDetail.concretePlacementStart),
                      moment(concreteRequestDetail.concretePlacementEnd),
                      null,
                      '[]',
                    ) ||
                    moment(finalDay).isSame(concreteRequestDetail.concretePlacementStart) ||
                    moment(finalDay).isSame(concreteRequestDetail.concretePlacementEnd)
                  ) {
                    id += 1;
                    const date = moment(finalDay).format('MM/DD/YYYY');
                    const chosenTimezoneConcretePlacementStart = moment.tz(
                      `${date} ${startTime}`,
                      'MM/DD/YYYY HH:mm',
                      eventTimeZone.timezone,
                    );
                    const chosenTimezoneConcretePlacementEnd = moment.tz(
                      `${date} ${endTime}`,
                      'MM/DD/YYYY HH:mm',
                      eventTimeZone.timezone,
                    );
                    const chosenTimezonePumpOrderedDate = moment.tz(
                      `${moment(concreteRequestDetail.pumpOrderedDate, 'MM/DD/YYYY').format(
                        'MM/DD/YYYY',
                      )} ${concreteRequestDetail.pumpWorkStart}`,
                      'MM/DD/YYYY HH:mm',
                      eventTimeZone.timezone,
                    );
                    const chosenTimezonePumpWorkStart = moment.tz(
                      `${moment(concreteRequestDetail.pumpOrderedDate, 'MM/DD/YYYY').format(
                        'MM/DD/YYYY',
                      )} ${concreteRequestDetail.pumpWorkStart}`,
                      'MM/DD/YYYY HH:mm',
                      eventTimeZone.timezone,
                    );
                    const chosenTimezonePumpWorkEnd = moment.tz(
                      `${moment(concreteRequestDetail.pumpOrderedDate, 'MM/DD/YYYY').format(
                        'MM/DD/YYYY',
                      )} ${concreteRequestDetail.pumpWorkEnd}`,
                      'MM/DD/YYYY HH:mm',
                      eventTimeZone.timezone,
                    );
                    concreteRequestParam = {
                      description: concreteRequestDetail.description,
                      ProjectId: concreteRequestDetail.ProjectId,
                      notes: concreteRequestDetail.notes,
                      concretePlacementStart: chosenTimezoneConcretePlacementStart
                        .clone()
                        .tz('UTC')
                        .format('YYYY-MM-DD HH:mm:ssZ'),
                      concretePlacementEnd: chosenTimezoneConcretePlacementEnd
                        .clone()
                        .tz('UTC')
                        .format('YYYY-MM-DD HH:mm:ssZ'),
                      isPumpConfirmed: concreteRequestDetail.isPumpConfirmed,
                      isPumpRequired: concreteRequestDetail.isPumpRequired,
                      isConcreteConfirmed: concreteRequestDetail.isConcreteConfirmed,
                      ParentCompanyId: concreteRequestDetail.ParentCompanyId,
                      concreteOrderNumber: concreteRequestDetail.concreteOrderNumber,
                      truckSpacingHours: concreteRequestDetail.truckSpacingHours,
                      slump: concreteRequestDetail.slump,
                      concreteQuantityOrdered: concreteRequestDetail.concreteQuantityOrdered,
                      concreteConfirmedOn: concreteRequestDetail.concreteConfirmedOn
                        ? concreteRequestDetail.concreteConfirmedOn
                        : null,
                      pumpLocation: concreteRequestDetail.pumpLocation,
                      pumpOrderedDate:
                        concreteRequestDetail.pumpOrderedDate && concreteRequestDetail.pumpWorkStart
                          ? chosenTimezonePumpOrderedDate
                            .clone()
                            .tz('UTC')
                            .format('YYYY-MM-DD HH:mm:ssZ')
                          : null,
                      pumpWorkStart:
                        concreteRequestDetail.pumpOrderedDate && concreteRequestDetail.pumpWorkStart
                          ? chosenTimezonePumpWorkStart
                            .clone()
                            .tz('UTC')
                            .format('YYYY-MM-DD HH:mm:ssZ')
                          : null,
                      pumpWorkEnd:
                        concreteRequestDetail.pumpOrderedDate && concreteRequestDetail.pumpWorkEnd
                          ? chosenTimezonePumpWorkEnd
                            .clone()
                            .tz('UTC')
                            .format('YYYY-MM-DD HH:mm:ssZ')
                          : null,
                      pumpConfirmedOn: concreteRequestDetail.pumpConfirmedOn
                        ? concreteRequestDetail.pumpConfirmedOn
                        : null,
                      cubicYardsTotal: concreteRequestDetail.cubicYardsTotal,
                      hoursToCompletePlacement: concreteRequestDetail.hoursToCompletePlacement,
                      minutesToCompletePlacement: concreteRequestDetail.minutesToCompletePlacement,
                      ConcreteRequestId: id,
                      requestType: 'concreteRequest',
                      status: 'Tentative',
                      primerForPump: concreteRequestDetail.primerForPump,
                      createdBy: memberDetails.id,
                      recurrenceId,
                      LocationId: concreteRequestDetail.LocationId,
                      OriginationAddress: concreteRequestDetail.originationAddress,
                      vehicleType: concreteRequestDetail.vehicleType,
                      OriginationAddressPump: concreteRequestDetail.originationAddressPump,
                      vehicleTypePump: concreteRequestDetail.vehicleTypePump,
                    };
                    if (
                      memberDetails.RoleId === roleDetails.id ||
                      memberDetails.RoleId === accountRoleDetails.id ||
                      memberDetails.isAutoApproveEnabled ||
                      projectDetails.ProjectSettings.isAutoApprovalEnabled
                    ) {
                      concreteRequestParam.status = 'Approved';
                      concreteRequestParam.approvedBy = memberDetails.id;
                      concreteRequestParam.approved_at = new Date();
                    }
                    eventsArray.push(concreteRequestParam);
                  }
                }
              }
              if (eventsArray && eventsArray.length > 0) {
                const isOverlapping = await this.checkDoubleBookingAllowedOrNot(
                  eventsArray,
                  projectDetails,
                  'add',
                );
                if (isOverlapping && isOverlapping.error) {
                  return done(null, {
                    message: isOverlapping.message,
                  });
                }
              }
            }
            if (concreteRequestDetail.recurrence === 'Does Not Repeat') {
              const recurrenceId = await this.insertRecurrenceSeries(
                concreteRequestDetail,
                inputData.user,
                'concreteRequest',
                eventTimeZone.timezone,
              );
              totalDays.forEach(async (data) => {
                id += 1;
                const chosenTimezoneConcretePlacementStart = moment.tz(
                  `${moment(concreteRequestDetail.concretePlacementStart).format('MM/DD/YYYY')} ${concreteRequestDetail.startPicker
                  }`,
                  'MM/DD/YYYY HH:mm',
                  eventTimeZone.timezone,
                );
                const chosenTimezoneConcretePlacementEnd = moment.tz(
                  `${moment(concreteRequestDetail.concretePlacementEnd).format('MM/DD/YYYY')} ${concreteRequestDetail.endPicker
                  }`,
                  'MM/DD/YYYY HH:mm',
                  eventTimeZone.timezone,
                );
                const chosenTimezonePumpOrderedDate = moment.tz(
                  `${moment(concreteRequestDetail.pumpOrderedDate, 'MM/DD/YYYY').format(
                    'MM/DD/YYYY',
                  )} ${concreteRequestDetail.pumpWorkStart}`,
                  'MM/DD/YYYY HH:mm',
                  eventTimeZone.timezone,
                );
                const chosenTimezonePumpWorkStart = moment.tz(
                  `${moment(concreteRequestDetail.pumpOrderedDate, 'MM/DD/YYYY').format(
                    'MM/DD/YYYY',
                  )} ${concreteRequestDetail.pumpWorkStart}`,
                  'MM/DD/YYYY HH:mm',
                  eventTimeZone.timezone,
                );
                const chosenTimezonePumpWorkEnd = moment.tz(
                  `${moment(concreteRequestDetail.pumpOrderedDate, 'MM/DD/YYYY').format(
                    'MM/DD/YYYY',
                  )} ${concreteRequestDetail.pumpWorkEnd}`,
                  'MM/DD/YYYY HH:mm',
                  eventTimeZone.timezone,
                );
                concreteRequestParam = {
                  description: concreteRequestDetail.description,
                  ProjectId: concreteRequestDetail.ProjectId,
                  notes: concreteRequestDetail.notes,
                  concretePlacementStart: chosenTimezoneConcretePlacementStart
                    .clone()
                    .tz('UTC')
                    .format('YYYY-MM-DD HH:mm:ssZ'),
                  concretePlacementEnd: chosenTimezoneConcretePlacementEnd
                    .clone()
                    .tz('UTC')
                    .format('YYYY-MM-DD HH:mm:ssZ'),
                  isPumpConfirmed: concreteRequestDetail.isPumpConfirmed,
                  isPumpRequired: concreteRequestDetail.isPumpRequired,
                  isConcreteConfirmed: concreteRequestDetail.isConcreteConfirmed,
                  ParentCompanyId: concreteRequestDetail.ParentCompanyId,
                  concreteOrderNumber: concreteRequestDetail.concreteOrderNumber,
                  truckSpacingHours: concreteRequestDetail.truckSpacingHours,
                  slump: concreteRequestDetail.slump,
                  concreteQuantityOrdered: concreteRequestDetail.concreteQuantityOrdered,
                  concreteConfirmedOn: concreteRequestDetail.concreteConfirmedOn
                    ? concreteRequestDetail.concreteConfirmedOn
                    : null,
                  pumpLocation: concreteRequestDetail.pumpLocation,
                  pumpOrderedDate:
                    concreteRequestDetail.pumpOrderedDate && concreteRequestDetail.pumpWorkStart
                      ? chosenTimezonePumpOrderedDate
                        .clone()
                        .tz('UTC')
                        .format('YYYY-MM-DD HH:mm:ssZ')
                      : null,
                  pumpWorkStart:
                    concreteRequestDetail.pumpOrderedDate && concreteRequestDetail.pumpWorkStart
                      ? chosenTimezonePumpWorkStart.clone().tz('UTC').format('YYYY-MM-DD HH:mm:ssZ')
                      : null,
                  pumpWorkEnd:
                    concreteRequestDetail.pumpOrderedDate && concreteRequestDetail.pumpWorkEnd
                      ? chosenTimezonePumpWorkEnd.clone().tz('UTC').format('YYYY-MM-DD HH:mm:ssZ')
                      : null,
                  pumpConfirmedOn: concreteRequestDetail.pumpConfirmedOn
                    ? concreteRequestDetail.pumpConfirmedOn
                    : null,
                  cubicYardsTotal: concreteRequestDetail.cubicYardsTotal,
                  hoursToCompletePlacement: concreteRequestDetail.hoursToCompletePlacement,
                  minutesToCompletePlacement: concreteRequestDetail.minutesToCompletePlacement,
                  ConcreteRequestId: id,
                  requestType: 'concreteRequest',
                  status: 'Tentative',
                  primerForPump: concreteRequestDetail.primerForPump,
                  createdBy: memberDetails.id,
                  recurrenceId,
                  LocationId: concreteRequestDetail.LocationId,
                  OriginationAddress: concreteRequestDetail.originationAddress,
                  vehicleType: concreteRequestDetail.vehicleType,
                  OriginationAddressPump: concreteRequestDetail.originationAddressPump,
                  vehicleTypePump: concreteRequestDetail.vehicleTypePump,
                };
                if (
                  memberDetails.RoleId === roleDetails.id ||
                  memberDetails.RoleId === accountRoleDetails.id ||
                  memberDetails.isAutoApproveEnabled ||
                  projectDetails.ProjectSettings.isAutoApprovalEnabled
                ) {
                  concreteRequestParam.status = 'Approved';
                  concreteRequestParam.approvedBy = memberDetails.id;
                  concreteRequestParam.approved_at = new Date();
                }
                eventsArray.push(concreteRequestParam);
              });
              if (eventsArray && eventsArray.length > 0) {
                const isOverlapping = await this.checkDoubleBookingAllowedOrNot(
                  eventsArray,
                  projectDetails,
                  'add',
                );
                if (isOverlapping && isOverlapping.error) {
                  return done(null, {
                    message: isOverlapping.message,
                  });
                }
              }
            }
            let newConcreteRequestData = {};
            if (eventsArray.length > 0) {
              for (let i = 0; i < eventsArray.length; i += 1) {
                newConcreteRequestData = await ConcreteRequest.createInstance(eventsArray[i]);
                const { responsiblePersons, concreteSupplier, pumpSize, mixDesign } =
                  concreteRequestDetail;
                const updateParam = {
                  ConcreteRequestId: newConcreteRequestData.id,
                  ConcreteRequestCode: newConcreteRequestData.ConcreteRequestId,
                  ProjectId: concreteRequestDetail.ProjectId,
                };
                concreteSupplier.forEach(async (element) => {
                  const companyParam = updateParam;
                  companyParam.CompanyId = element;
                  await ConcreteRequestCompany.createInstance(companyParam);
                });
                // location.forEach(async (element) => {
                const locationParam = updateParam;
                // if (element.chosenFromDropdown) {
                // locationParam.ConcreteLocationId = element.id;
                // } else {
                // const existlocations = await ConcreteLocation.findAll({
                //   where: {
                //     ProjectId: concreteRequestDetail.ProjectId,
                //     isDeleted: false,
                //   },
                // });
                // const isLocationExists = existlocations.find(
                //   (item) =>
                //     item.location.toLowerCase().trim() ===
                //     element.location.toLowerCase().trim(),
                // );
                // if (isLocationExists) {
                // locationParam.ConcreteLocationId = isLocationExists.id;
                // } else {
                const object = {
                  location: concreteRequestDetail.location,
                  ProjectId: concreteRequestDetail.ProjectId,
                  isDeleted: false,
                  createdBy: loginUser.id,
                };
                const locationData = await ConcreteLocation.createConcreteLocation(object);
                locationParam.ConcreteLocationId = locationData.id;
                // }
                // }
                await ConcreteRequestLocation.createInstance(locationParam);
                // });
                pumpSize.forEach(async (element) => {
                  const pumpSizeParam = updateParam;
                  if (element.chosenFromDropdown) {
                    pumpSizeParam.ConcretePumpSizeId = element.id;
                  } else {
                    const existPumpSizes = await ConcretePumpSize.findAll({
                      where: {
                        ProjectId: concreteRequestDetail.ProjectId,
                        isDeleted: false,
                      },
                    });
                    const isPumpSizeExists = existPumpSizes.find(
                      (item) =>
                        item.pumpSize.toLowerCase().trim() ===
                        element.pumpSize.toLowerCase().trim(),
                    );
                    if (isPumpSizeExists) {
                      pumpSizeParam.ConcretePumpSizeId = isPumpSizeExists.id;
                    } else {
                      const object2 = {
                        pumpSize: element.pumpSize,
                        ProjectId: concreteRequestDetail.ProjectId,
                        isDeleted: false,
                        createdBy: loginUser.id,
                      };
                      const pumpSizeData = await ConcretePumpSize.createConcretePumpSize(object2);
                      pumpSizeParam.ConcretePumpSizeId = pumpSizeData.id;
                    }
                  }
                  await ConcreteRequestPumpSize.createInstance(pumpSizeParam);
                });
                mixDesign.forEach(async (element) => {
                  const mixDesignParam = updateParam;
                  if (element.chosenFromDropdown) {
                    mixDesignParam.ConcreteMixDesignId = element.id;
                  } else {
                    const existMixDesigns = await ConcreteMixDesign.findAll({
                      where: {
                        ProjectId: concreteRequestDetail.ProjectId,
                        isDeleted: false,
                      },
                    });
                    const isMixDesignExists = existMixDesigns.find(
                      (item) =>
                        item.mixDesign.toLowerCase().trim() ===
                        element.mixDesign.toLowerCase().trim(),
                    );
                    if (isMixDesignExists) {
                      mixDesignParam.ConcreteMixDesignId = isMixDesignExists.id;
                    } else {
                      const object3 = {
                        mixDesign: element.mixDesign,
                        ProjectId: concreteRequestDetail.ProjectId,
                        isDeleted: false,
                        createdBy: loginUser.id,
                      };
                      const mixDesignData =
                        await ConcreteMixDesign.createConcreteMixDesign(object3);
                      mixDesignParam.ConcreteMixDesignId = mixDesignData.id;
                    }
                  }
                  await ConcreteRequestMixDesign.createInstance(mixDesignParam);
                });
                responsiblePersons.forEach(async (element) => {
                  const memberParam = updateParam;
                  memberParam.MemberId = element;
                  await ConcreteRequestResponsiblePerson.createInstance(memberParam);
                });
                // const history = {
                //   ConcreteRequestId: newConcreteRequestData.id,
                //   MemberId: memberDetails.id,
                //   type: 'create',
                //   description: `${loginUser.firstName} ${loginUser.lastName} Created Concrete Booking, ${concreteRequestDetail.description}.`,
                // };
                // const notification = history;
                // notification.ProjectId = concreteRequestDetail.ProjectId;
                // notification.title = 'Concrete Booking Creation';
                // await ConcreteRequestHistory.createInstance(history);
              }
            }
            if (
              Object.keys(newConcreteRequestData).length > 0 &&
              typeof newConcreteRequestData === 'object'
            ) {
              const locationChosen = await Locations.findOne({
                where: {
                  ProjectId: concreteRequestDetail.ProjectId,
                  id: concreteRequestDetail.LocationId,
                },
              });
              const { responsiblePersons } = concreteRequestDetail;
              const history = {
                ConcreteRequestId: newConcreteRequestData.id,
                MemberId: memberDetails.id,
                type: 'create',
                description: `${loginUser.firstName} ${loginUser.lastName} Created Concrete Booking, ${concreteRequestDetail.description} .`,
                locationFollowDescription: `${loginUser.firstName} ${loginUser.lastName} Created Concrete Booking, ${concreteRequestDetail.description}. Location: ${locationChosen.locationPath}.`,
                memberData: [],
              };
              const notification = history;
              notification.ProjectId = concreteRequestDetail.ProjectId;
              notification.LocationId = concreteRequestDetail.LocationId;
              notification.title = 'Concrete Booking Creation';
              await ConcreteRequestHistory.createInstance(history);
              notification.isDeliveryRequest = false;
              notification.requestType = 'concreteRequest';
              notification.recurrenceType = `${concreteRequestDetail.recurrence} From ${moment(
                concreteRequestDetail.concretePlacementStart,
              ).format('MM/DD/YYYY')} to ${moment(
                concreteRequestDetail.concretePlacementEnd,
              ).format('MM/DD/YYYY')}`;
              const newNotification = await Notification.createInstance(notification);
              const memberLocationPreference = await LocationNotificationPreferences.findAll({
                where: {
                  ProjectId: concreteRequestDetail.ProjectId,
                  LocationId: concreteRequestDetail.LocationId,
                  follow: true,
                },
                include: [
                  {
                    association: 'Member',
                    attributes: ['id', 'RoleId'],
                    where: {
                      [Op.and]: [
                        {
                          id: { [Op.ne]: memberDetails.id },
                        },
                      ],
                    },
                    include: [
                      {
                        association: 'User',
                        attributes: ['id', 'firstName', 'lastName', 'email'],
                      },
                    ],
                  },
                ],
              });
              const locationFollowMembers = [];
              memberLocationPreference.forEach(async (element) => {
                locationFollowMembers.push(element.Member.id);
              });
              const personData = await ConcreteRequestResponsiblePerson.findAll({
                where: { ConcreteRequestId: newConcreteRequestData.id, isDeleted: false },
                include: [
                  {
                    association: 'Member',
                    include: [
                      {
                        association: 'User',
                        attributes: ['id', 'firstName', 'lastName', 'email'],
                      },
                    ],
                    where: {
                      [Op.and]: {
                        RoleId: {
                          [Op.notIn]: [1, 2],
                        },
                        id: { [Op.notIn]: locationFollowMembers },
                      },
                    },
                    attributes: ['id', 'RoleId'],
                  },
                ],
                attributes: ['id'],
              });
              const adminData = await Member.findAll({
                where: {
                  [Op.and]: [
                    { ProjectId: concreteRequestDetail.ProjectId },
                    { isDeleted: false },
                    { id: { [Op.in]: responsiblePersons } },
                    { id: { [Op.ne]: newNotification.MemberId } },
                    { id: { [Op.notIn]: locationFollowMembers } },
                  ],
                },
                include: [
                  {
                    association: 'User',
                    attributes: ['id', 'firstName', 'lastName', 'email'],
                  },
                ],
                attributes: ['id'],
              });
              if (memberLocationPreference && memberLocationPreference.length > 0) {
                // here 3-(NotificationPreferenceItemId - When a new delivery/crane/concrete request added to the project)
                await pushNotification.sendMemberLocationPreferencePushNotificationForConcrete(
                  memberLocationPreference,
                  newConcreteRequestData.ConcreteRequestId,
                  history.locationFollowDescription,
                  newConcreteRequestData.requestType,
                  newConcreteRequestData.ProjectId,
                  newConcreteRequestData.id,
                  3,
                );
                // here 3-(NotificationPreferenceItemId - When a new delivery/crane/concrete request added to the project)
                await notificationHelper.createMemberDeliveryLocationInAppNotification(
                  DeliveryPersonNotification,
                  concreteRequestDetail.ProjectId,
                  newNotification.id,
                  memberLocationPreference,
                  3,
                );
              }
              history.memberData = personData;
              history.adminData = adminData;
              history.firstName = loginUser.firstName;
              history.profilePic = loginUser.profilePic;
              history.createdAt = new Date();
              history.ProjectId = concreteRequestDetail.ProjectId;
              history.projectName = projectDetails.projectName;
              // here 3-(NotificationPreferenceItemId - When a new delivery/crane/concrete request added to the project)
              await notificationHelper.createDeliveryPersonNotification(
                adminData,
                [],
                projectDetails,
                newNotification,
                DeliveryPersonNotification,
                memberDetails,
                loginUser,
                3,
                'created a',
                'Concrete Request',
                `concrete Booking (${newConcreteRequestData.ConcreteRequestId} - ${newConcreteRequestData.description})`,
                newConcreteRequestData.ConcreteRequestId,
              );
              const checkMemberNotification = await NotificationPreference.findAll({
                where: {
                  ProjectId: concreteRequestDetail.ProjectId,
                  isDeleted: false,
                },
                attributes: [
                  'id',
                  'MemberId',
                  'ProjectId',
                  'ParentCompanyId',
                  'NotificationPreferenceItemId',
                  'instant',
                  'dailyDigest',
                ],
                include: [
                  {
                    association: 'NotificationPreferenceItem',
                    where: {
                      id: 3,
                      isDeleted: false,
                    },
                    attributes: ['id', 'description', 'inappNotification', 'emailNotification'],
                  },
                ],
              });
              await this.sendEmailNotificationToUser(
                history,
                memberDetails,
                loginUser,
                newConcreteRequestData,
                concreteRequestDetail,
                memberLocationPreference,
              );
              const memberLocationPreferenceNotify = await LocationNotificationPreferences.findAll({
                where: {
                  ProjectId: concreteRequestDetail.ProjectId,
                  LocationId: concreteRequestDetail.LocationId,
                  follow: true,
                },
                include: [
                  {
                    association: 'Member',
                    attributes: ['id', 'RoleId'],
                    where: {
                      [Op.and]: [
                        {
                          id: { [Op.ne]: memberDetails.id },
                        },
                      ],
                    },
                    include: [
                      {
                        association: 'User',
                        attributes: ['id', 'firstName', 'lastName', 'email'],
                      },
                    ],
                  },
                ],
              });
              history.notificationPreference = checkMemberNotification;
              // here 3-(NotificationPreferenceItemId - When a new delivery/crane/concrete request added to the project)
              await pushNotification.sendPushNotificationForConcrete(
                history,
                3,
                concreteRequestDetail.ProjectId,
              );
              history.ConcreteRequestId = newConcreteRequestData.ConcreteRequestId;
              if (memberLocationPreferenceNotify && memberLocationPreferenceNotify.length > 0) {
                history.memberData.push(...memberLocationPreferenceNotify);
              }
              return done(history, false);
            }
            return done(null, {
              message: 'Bookings will not be created for the scheduled date/time',
            });
          }
          return done(null, {
            message: 'You are not allowed create Concrete Booking for this project.',
          });
        });
      } else {
        return done(null, { message: 'Project does not exist.' });
      }
    } catch (e) {
      done(null, e);
    }
  },
  async compareDeliveryDateWithDeliveryWindowDate(
    dateStr,
    timeStr,
    timezoneStr,
    deliveryWindowTime,
    deliveryWindowTimeUnit,
  ) {
    const datetimeStr = `${moment(dateStr).format('YYYY-MM-DD')}T${timeStr}`;
    const datetime = moment.tz(datetimeStr, timezoneStr);
    const currentDatetime = moment
      .tz(timezoneStr)
      .add(deliveryWindowTime, deliveryWindowTimeUnit)
      .startOf('minute');
    return datetime.isSameOrBefore(currentDatetime);
  },
  async checkInputDatas(inputData, done) {
    await this.getDynamicModel(inputData);
    const concreteRequestData = inputData.body;
    const { concreteSupplier } = concreteRequestData;
    const { responsiblePersons } = concreteRequestData;
    const inputProjectId = +concreteRequestData.ProjectId;
    const memberList = await Member.count({
      where: { id: { [Op.in]: responsiblePersons }, ProjectId: inputProjectId, isDeleted: false },
    });
    const companyList = await Company.count({
      where: {
        [Op.or]: [
          {
            id: { [Op.in]: concreteSupplier },
            ProjectId: +inputProjectId,
            isDeleted: false,
          },
          {
            id: {
              [Op.in]: concreteSupplier,
            },
            isParent: true,
            ParentCompanyId: +concreteRequestData.ParentCompanyId,
            isDeleted: false,
          },
        ],
      },
    });
    if (
      concreteRequestData.responsiblePersons &&
      concreteRequestData.responsiblePersons.length > 0 &&
      memberList !== responsiblePersons.length
    ) {
      return done(null, { message: 'Some Member is not in the project' });
    }
    if (
      concreteRequestData.concreteSupplier &&
      concreteRequestData.concreteSupplier.length > 0 &&
      companyList !== concreteSupplier.length
    ) {
      return done(null, { message: 'Some Company is not in the project' });
    }
    // if (concreteRequestData.location && concreteRequestData.location.length > 0) {
    //   const locationTempArray = [];
    //   concreteRequestData.location.forEach((element) => {
    //     if (element.chosenFromDropdown) {
    //       locationTempArray.push(element.id);
    //     }
    //   });
    //   const concreteLocationCount = await ConcreteLocation.count({
    //     where: { id: { [Op.in]: locationTempArray }, ProjectId: inputProjectId, isDeleted: false },
    //   });
    //   if (concreteLocationCount !== locationTempArray.length) {
    //     return done(null, { message: 'Some location is not in the project' });
    //   }
    // }
    if (concreteRequestData.mixDesign && concreteRequestData.mixDesign.length > 0) {
      const mixDesignTempArray = [];
      concreteRequestData.mixDesign.forEach((element) => {
        if (element.chosenFromDropdown) {
          mixDesignTempArray.push(element.id);
        }
      });
      const concreteMixDesignCount = await ConcreteMixDesign.count({
        where: { id: { [Op.in]: mixDesignTempArray }, ProjectId: inputProjectId, isDeleted: false },
      });
      if (concreteMixDesignCount !== mixDesignTempArray.length) {
        return done(null, { message: 'Some Mix Design is not in the project' });
      }
    }
    if (concreteRequestData.pumpSize && concreteRequestData.pumpSize.length > 0) {
      const pumpSizeTempArray = [];
      concreteRequestData.pumpSize.forEach((element) => {
        if (element.chosenFromDropdown) {
          pumpSizeTempArray.push(element.id);
        }
      });
      const concretePumpSizeCount = await ConcretePumpSize.count({
        where: { id: { [Op.in]: pumpSizeTempArray }, ProjectId: inputProjectId, isDeleted: false },
      });
      if (concretePumpSizeCount !== pumpSizeTempArray.length) {
        return done(null, { message: 'Some Pump Size is not in the project' });
      }
    }
    return done(true, false);
  },
  async updateValues(condition, done) {
    try {
      await ConcreteRequestResponsiblePerson.update({ isDeleted: true }, { where: condition });
      await ConcreteRequestCompany.update({ isDeleted: true }, { where: condition });
      // await ConcreteRequestLocation.update({ isDeleted: true }, { where: condition });
      await ConcreteRequestMixDesign.update({ isDeleted: true }, { where: condition });
      await ConcreteRequestPumpSize.update({ isDeleted: true }, { where: condition });
      done({ status: 'ok' }, false);
    } catch (e) {
      done(null, e);
    }
  },
  async editConcreteRequest(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const concreteRequestData = inputData.body;
      const loginUser = inputData.user;
      const { recurrenceId } = concreteRequestData;
      let editSeriesRequests;
      let newRecurrenceId;
      let previousRecordInSeries;
      const projectSettingDetails = await Project.getProjectAndSettings({
        isDeleted: false,
        id: +concreteRequestData.ProjectId,
      });
      if (projectSettingDetails) {
        const requestData = await ConcreteRequest.getSingleConcreteRequestData({
          id: concreteRequestData.id,
        });
        if (concreteRequestData.seriesOption === 1) {
          const requestArray = [];
          requestArray.push({
            ProjectId: concreteRequestData.ProjectId,
            concretePlacementStart: concreteRequestData.concretePlacementStart,
            concretePlacementEnd: concreteRequestData.concretePlacementEnd,
            id: concreteRequestData.id,
          });
          const isOverlapping = await this.checkDoubleBookingAllowedOrNot(
            requestArray,
            projectSettingDetails,
            'add',
          );
          if (isOverlapping && isOverlapping.error) {
            return done(null, {
              message: isOverlapping.message,
            });
          }
        }
        if (concreteRequestData.seriesOption === 2 || concreteRequestData.seriesOption === 3) {
          let requestSeries = [];
          if (concreteRequestData.seriesOption === 2) {
            requestSeries = await ConcreteRequest.findAll({
              where: [
                Sequelize.and({
                  recurrenceId,
                  id: {
                    [Op.gte]: concreteRequestData.id,
                  },
                }),
              ],
            });
          }
          if (concreteRequestData.seriesOption === 3) {
            requestSeries = await ConcreteRequest.findAll({
              where: [
                Sequelize.and({
                  recurrenceId,
                  concretePlacementStart: {
                    [Op.gte]: moment
                      .tz(concreteRequestData.timeZone)
                      .utc()
                      .format('YYYY-MM-DD HH:mm:ssZ'),
                  },
                }),
              ],
            });
          }
          const requestArray = [];
          for (let i = 0; i < requestSeries.length; i += 1) {
            const deliveryStartDate = await this.convertTimezoneToUtc(
              moment
                .utc(requestSeries[i].concretePlacementStart)
                .tz(concreteRequestData.timezone)
                .format('MM/DD/YYYY'),
              concreteRequestData.timezone,
              concreteRequestData.deliveryStartTime,
            );
            const deliveryEndDate = await this.convertTimezoneToUtc(
              moment
                .utc(requestSeries[i].concretePlacementEnd)
                .tz(concreteRequestData.timezone)
                .format('MM/DD/YYYY'),
              concreteRequestData.timezone,
              concreteRequestData.deliveryEndTime,
            );
            requestArray.push({
              ProjectId: concreteRequestData.ProjectId,
              concretePlacementStart: !moment(deliveryStartDate).isSame(
                moment(requestSeries[i].concretePlacementStart),
              )
                ? deliveryStartDate
                : requestSeries[i].concretePlacementStart,
              concretePlacementEnd: !moment(deliveryEndDate).isSame(
                moment(requestSeries[i].concretePlacementEnd),
              )
                ? deliveryEndDate
                : requestSeries[i].concretePlacementEnd,
              id: requestSeries[i].id,
            });
          }
          const utcRecurrenceEndDate = requestData.recurrence.recurrenceEndDate;
          const existingRecurrenceEndDate = moment(utcRecurrenceEndDate)
            .tz(concreteRequestData.timezone)
            .format('YYYY-MM-DD');
          const newRecurrenceEndDate = concreteRequestData.recurrenceEndDate;
          if (!moment(existingRecurrenceEndDate).isSame(moment(newRecurrenceEndDate))) {
            const startDate = moment(existingRecurrenceEndDate).add(1, 'day');
            const endDate = moment(newRecurrenceEndDate);
            for (let date = startDate; date.isSameOrBefore(endDate); date.add(1, 'day')) {
              requestArray.push({
                ProjectId: concreteRequestData.ProjectId,
                concretePlacementStart: await this.convertTimezoneToUtc(
                  moment(date).format('MM/DD/YYYY'),
                  concreteRequestData.timezone,
                  concreteRequestData.deliveryStartTime,
                ),
                concretePlacementEnd: await this.convertTimezoneToUtc(
                  moment(date).format('MM/DD/YYYY'),
                  concreteRequestData.timezone,
                  concreteRequestData.deliveryEndTime,
                ),
              });
            }
            if (requestArray.length > 0) {
              const isOverlapping = await this.checkDoubleBookingAllowedOrNot(
                requestArray,
                projectSettingDetails,
                'edit',
              );
              if (isOverlapping && isOverlapping.error) {
                return done(null, {
                  message: isOverlapping.message,
                });
              }
            }
          } else if (requestArray.length > 0) {
            const isOverlapping = await this.checkDoubleBookingAllowedOrNot(
              requestArray,
              projectSettingDetails,
              'edit',
            );
            if (isOverlapping && isOverlapping.error) {
              return done(null, {
                message: isOverlapping.message,
              });
            }
          }
        }
      }
      // This event
      if (concreteRequestData.seriesOption === 1) {
        editSeriesRequests = await ConcreteRequest.findAll({
          where: [
            Sequelize.and({
              id: concreteRequestData.id,
            }),
          ],
        });
        if (editSeriesRequests && editSeriesRequests[0] && concreteRequestData.recurrenceId) {
          const previousRecordInThisEventSeries = await ConcreteRequest.findAll({
            where: [
              Sequelize.and({
                recurrenceId,
                id: {
                  [Op.lt]: concreteRequestData.id,
                },
              }),
            ],
            order: [['id', 'DESC']],
          });
          const NextSeriesLastRecord = await ConcreteRequest.findAll({
            where: [
              Sequelize.and({
                recurrenceId,
                id: {
                  [Op.gt]: concreteRequestData.id,
                },
              }),
            ],
            order: [['id', 'DESC']],
          });
          if (
            ((NextSeriesLastRecord && NextSeriesLastRecord.length > 0) ||
              (previousRecordInThisEventSeries && previousRecordInThisEventSeries.length > 0)) &&
            !(
              NextSeriesLastRecord &&
              NextSeriesLastRecord.length > 0 &&
              previousRecordInThisEventSeries &&
              previousRecordInThisEventSeries.length > 0
            )
          ) {
            if (NextSeriesLastRecord && NextSeriesLastRecord.length > 0) {
              const chosenTimezoneDeliveryStart = moment.tz(
                `${concreteRequestData.nextSeriesRecurrenceStartDate}  '00:00'`,
                'YYYY-MM-DD HH:mm',
                concreteRequestData.timezone,
              );
              const utcDate = chosenTimezoneDeliveryStart
                .clone()
                .tz('UTC')
                .format('YYYY-MM-DD HH:mm:ssZ');
              await RequestRecurrenceSeries.update(
                {
                  recurrenceStartDate: utcDate,
                },
                {
                  where: {
                    id: NextSeriesLastRecord[0].recurrenceId,
                  },
                },
              );
            }
            if (previousRecordInThisEventSeries && previousRecordInThisEventSeries.length > 0) {
              // const chosenTimezoneDeliveryStart = moment.tz(
              //   `${concreteRequestData.previousSeriesRecurrenceEndDate}  '00:00'`,
              //   'YYYY-MM-DD HH:mm',
              //   concreteRequestData.timezone,
              // );
              // const utcDate = chosenTimezoneDeliveryStart
              //   .clone()
              //   .tz('UTC')
              //   .format('YYYY-MM-DD HH:mm:ssZ');
              await RequestRecurrenceSeries.update(
                {
                  recurrenceEndDate: previousRecordInThisEventSeries[0].concretePlacementStart,
                },
                {
                  where: {
                    id: previousRecordInThisEventSeries[0].recurrenceId,
                  },
                },
              );
            }
          }
        }
      }
      // This and all following events
      if (concreteRequestData.seriesOption === 2) {
        editSeriesRequests = await ConcreteRequest.findAll({
          where: [
            Sequelize.and({
              recurrenceId,
              id: {
                [Op.gte]: concreteRequestData.id,
              },
            }),
          ],
        });
        previousRecordInSeries = await ConcreteRequest.findOne({
          where: [
            Sequelize.and({
              recurrenceId,
              id: {
                [Op.lt]: concreteRequestData.id,
              },
            }),
          ],
          order: [['id', 'DESC']],
        });
      }
      // All events in the series
      if (concreteRequestData.seriesOption === 3) {
        editSeriesRequests = await ConcreteRequest.findAll({
          where: [
            Sequelize.and({
              recurrenceId,
              concretePlacementStart: {
                [Op.gte]: moment
                  .tz(concreteRequestData.timeZone)
                  .utc()
                  .format('YYYY-MM-DD HH:mm:ssZ'),
              },
            }),
          ],
        });
      }
      if (editSeriesRequests && editSeriesRequests[0] && editSeriesRequests.length > 0) {
        const requestData = await ConcreteRequest.getSingleConcreteRequestData({
          id: editSeriesRequests[0].id,
        });
        if (requestData && requestData.recurrence) {
          requestData.recurrence.ParentCompanyId = concreteRequestData.ParentCompanyId;
          requestData.recurrence.ProjectId = concreteRequestData.ProjectId;
          if (concreteRequestData.seriesOption === 1) {
            requestData.recurrence.concretePlacementStart =
              concreteRequestData.recurrenceSeriesStartDate;
            requestData.recurrence.concretePlacementEnd =
              concreteRequestData.recurrenceSeriesEndDate;
          }
          if (concreteRequestData.seriesOption === 2) {
            requestData.recurrence.concretePlacementStart =
              concreteRequestData.recurrenceSeriesStartDate;
            requestData.recurrence.concretePlacementEnd = concreteRequestData.recurrenceEndDate;
          }
          if (concreteRequestData.seriesOption === 2 && previousRecordInSeries) {
            newRecurrenceId = await this.insertRecurrenceSeries(
              requestData.recurrence,
              loginUser,
              requestData.requestType,
              concreteRequestData.timezone,
            );
          }
        }
        if (concreteRequestData.seriesOption === 2 || concreteRequestData.seriesOption === 3) {
          const utcRecurrenceEndDate = requestData.recurrence.recurrenceEndDate;
          let existingRecurrenceEndDate = moment(utcRecurrenceEndDate)
            .tz(concreteRequestData.timezone)
            .format('YYYY-MM-DD');
          const newRecurrenceEndDate = concreteRequestData.recurrenceEndDate;
          if (!moment(existingRecurrenceEndDate).isSame(moment(newRecurrenceEndDate))) {
            const dates = [];
            const chosenTimezoneDeliveryStart = moment.tz(
              `${concreteRequestData.recurrenceEndDate}  '00:00'`,
              'YYYY-MM-DD HH:mm',
              concreteRequestData.timezone,
            );
            const utcDate = chosenTimezoneDeliveryStart
              .clone()
              .tz('UTC')
              .format('YYYY-MM-DD HH:mm:ssZ');
            await RequestRecurrenceSeries.update(
              {
                recurrenceEndDate: utcDate,
              },
              {
                where: {
                  id: concreteRequestData.recurrenceId,
                },
              },
            );
            while (moment(existingRecurrenceEndDate).isBefore(moment(newRecurrenceEndDate))) {
              existingRecurrenceEndDate = moment(existingRecurrenceEndDate).add(1, 'day');
              dates.push(moment(existingRecurrenceEndDate).format('MM/DD/YYYY'));
            }
            await this.createCopyofConcreteRequest(
              requestData,
              concreteRequestData,
              dates,
              loginUser,
              newRecurrenceId || concreteRequestData.recurrenceId,
            );
          }
        }
        if (concreteRequestData.seriesOption === 2 && previousRecordInSeries) {
          // const chosenTimezoneDeliveryStart = moment.tz(
          //   `${concreteRequestData.previousSeriesRecurrenceEndDate}  '00:00'`,
          //   'YYYY-MM-DD HH:mm',
          //   concreteRequestData.timezone,
          // );
          // const utcDate = chosenTimezoneDeliveryStart
          //   .clone()
          //   .tz('UTC')
          //   .format('YYYY-MM-DD HH:mm:ssZ');
          await RequestRecurrenceSeries.update(
            {
              recurrenceEndDate: previousRecordInSeries.concretePlacementStart,
            },
            {
              where: {
                id: previousRecordInSeries.recurrenceId,
              },
            },
          );
        }
      }

      for (let indexLoop = 0; indexLoop < editSeriesRequests.length; indexLoop += 1) {
        const seriesData = editSeriesRequests[indexLoop];
        const isConcreteRequestExists = await ConcreteRequest.findOne({
          where: [
            Sequelize.and({
              id: seriesData.id,
              isDeleted: false,
            }),
          ],
        });
        if (!isConcreteRequestExists) {
          return done(null, { message: 'Concrete Booking id is not available' });
        }
        const existsConcreteRequest = await ConcreteRequest.getSingleConcreteRequestData({
          id: +seriesData.id,
        });

        const startDate = new Date(concreteRequestData.concretePlacementStart).getTime();
        const currentDate = new Date().getTime();
        const endDate = new Date(concreteRequestData.concretePlacementEnd).getTime();
        const memberData = await Member.getBy({
          UserId: loginUser.id,
          ProjectId: concreteRequestData.ProjectId,
        });
        if (memberData.RoleId === 4) {
          if (startDate < currentDate && endDate < currentDate) {
            return done(null, { message: 'Please enter Future start or end date.' });
          }
        }
        this.checkInputDatas(inputData, async (_checkResponse, checkError) => {
          if (checkError) {
            return done(null, checkError);
          }
          const history = {
            ConcreteRequestId: isConcreteRequestExists.id,
            MemberId: memberData.id,
            type: 'edit',
            description: `${loginUser.firstName} ${loginUser.lastName} Edited this Concrete Booking.`,
          };
          const notification = history;
          const concreteRequestParam = {
            description: concreteRequestData.description,
            ProjectId: concreteRequestData.ProjectId,
            LocationId: concreteRequestData.LocationId,
            notes: concreteRequestData.notes,
            isPumpConfirmed: concreteRequestData.isPumpConfirmed,
            isPumpRequired: concreteRequestData.isPumpRequired,
            isConcreteConfirmed: concreteRequestData.isConcreteConfirmed,
            ParentCompanyId: concreteRequestData.ParentCompanyId,
            concreteOrderNumber: concreteRequestData.concreteOrderNumber,
            truckSpacingHours: concreteRequestData.truckSpacingHours,
            slump: concreteRequestData.slump,
            concreteQuantityOrdered: concreteRequestData.concreteQuantityOrdered,
            concreteConfirmedOn: concreteRequestData.concreteConfirmedOn
              ? concreteRequestData.concreteConfirmedOn
              : null,
            pumpLocation: concreteRequestData.pumpLocation,
            pumpOrderedDate: concreteRequestData.pumpOrderedDate
              ? concreteRequestData.pumpOrderedDate
              : null,
            pumpWorkStart: concreteRequestData.pumpWorkStart
              ? concreteRequestData.pumpWorkStart
              : null,
            pumpWorkEnd: concreteRequestData.pumpWorkEnd ? concreteRequestData.pumpWorkEnd : null,
            pumpConfirmedOn: concreteRequestData.pumpConfirmedOn
              ? concreteRequestData.pumpConfirmedOn
              : null,
            cubicYardsTotal: concreteRequestData.cubicYardsTotal,
            hoursToCompletePlacement: concreteRequestData.hoursToCompletePlacement,
            minutesToCompletePlacement: concreteRequestData.minutesToCompletePlacement,
            // ConcreteRequestId: concreteRequestData.ConcreteRequestId,
            requestType: 'concreteRequest',
            primerForPump: concreteRequestData.primerForPump,
            recurrenceId: concreteRequestData.seriesOption !== 1 ? newRecurrenceId : null,
            OriginationAddress: concreteRequestData.originationAddress,
            vehicleType: concreteRequestData.vehicleType,
            OriginationAddressPump: concreteRequestData.originationAddressPump,
            vehicleTypePump: concreteRequestData.vehicleTypePump,
          };
          if (concreteRequestData.seriesOption === 1) {
            concreteRequestParam.concretePlacementStart =
              concreteRequestData.concretePlacementStart;
            concreteRequestParam.concretePlacementEnd = concreteRequestData.concretePlacementEnd;
          }
          if (concreteRequestData.seriesOption === 2 || concreteRequestData.seriesOption === 3) {
            const utcDeliveryStartTimestamp = moment.utc(
              isConcreteRequestExists.concretePlacementStart,
            );
            const localStartTimestamp = utcDeliveryStartTimestamp.tz(concreteRequestData.timezone);
            const utcDeliveryEndTimestamp = moment.utc(
              isConcreteRequestExists.concretePlacementEnd,
            );
            const localEndTimestamp = utcDeliveryEndTimestamp.tz(concreteRequestData.timezone);
            concreteRequestParam.concretePlacementStart = await this.convertTimezoneToUtc(
              moment(localStartTimestamp).format('MM/DD/YYYY'),
              concreteRequestData.timezone,
              concreteRequestData.deliveryStartTime,
            );
            concreteRequestParam.concretePlacementEnd = await this.convertTimezoneToUtc(
              moment(localEndTimestamp).format('MM/DD/YYYY'),
              concreteRequestData.timezone,
              concreteRequestData.deliveryEndTime,
            );
          }
          if (
            ((memberData.RoleId === 2 || memberData.RoleId === 1) &&
              isConcreteRequestExists.status === 'Approved') ||
            memberData.isAutoApproveEnabled ||
            projectSettingDetails.ProjectSettings.isAutoApprovalEnabled
          ) {
            concreteRequestParam.status = 'Approved';
            concreteRequestParam.approvedBy = memberData.id;
            concreteRequestParam.approved_at = new Date();
          }
          await ConcreteRequest.update(concreteRequestParam, {
            where: { id: isConcreteRequestExists.id },
          });
          const { responsiblePersons, location, pumpSize, mixDesign, concreteSupplier } =
            concreteRequestData;
          const condition = Sequelize.and({
            ProjectId: concreteRequestData.ProjectId,
            ConcreteRequestId: isConcreteRequestExists.id,
          });
          const updateParam = {
            ConcreteRequestId: +isConcreteRequestExists.id,
            ConcreteRequestCode: +isConcreteRequestExists.ConcreteRequestId,
            ProjectId: +concreteRequestData.ProjectId,
            isDeleted: false,
          };
          const existResponsiblePersons = await ConcreteRequestResponsiblePerson.findAll({
            where: condition,
          });
          const existlocations = await ConcreteRequestLocation.findAll({
            where: condition,
          });
          const existMixDesigns = await ConcreteRequestMixDesign.findAll({
            where: condition,
          });
          const existPumpSizes = await ConcreteRequestPumpSize.findAll({
            where: condition,
          });
          const existCompanies = await ConcreteRequestCompany.findAll({
            where: condition,
          });
          this.updateValues(condition, async (response, error) => {
            if (!error) {
              responsiblePersons.forEach(async (element, i) => {
                const index = existResponsiblePersons.findIndex(
                  (item) => item.MemberId === element,
                );
                const memberParam = updateParam;
                memberParam.MemberId = element;
                if (index !== -1) {
                  await ConcreteRequestResponsiblePerson.update(memberParam, {
                    where: { id: existResponsiblePersons[index].id },
                  });
                } else {
                  await ConcreteRequestResponsiblePerson.createInstance(memberParam);
                }
              });
              if (
                existsConcreteRequest &&
                existsConcreteRequest.locationDetails &&
                existsConcreteRequest.locationDetails[0] &&
                existsConcreteRequest.locationDetails[0].ConcreteLocation
              ) {
                const getConcreteLocationDetail = await ConcreteLocation.findOne({
                  where: {
                    id: existsConcreteRequest.locationDetails[0].ConcreteLocation.id,
                  },
                });

                if (getConcreteLocationDetail) {
                  await ConcreteLocation.update(
                    {
                      location: concreteRequestData.location,
                    },

                    {
                      where: { id: getConcreteLocationDetail.id },
                    },
                  );
                }
              }
              // location.forEach(async (element, i) => {
              //   const index = existlocations.findIndex(
              //     (item) => item.ConcreteLocationId === element.id,
              //   );
              //   const locationParam = updateParam;
              //   locationParam.ConcreteLocationId = element.id;
              //   if (index !== -1) {
              //     await ConcreteRequestLocation.update(locationParam, {
              //       where: { id: existlocations[index].id },
              //     });
              //   } else {
              //     if (element.chosenFromDropdown) {
              //       locationParam.ConcreteLocationId = element.id;
              //     } else {
              //       const object = {
              //         location: element.location,
              //         ProjectId: +concreteRequestData.ProjectId,
              //         isDeleted: false,
              //         createdBy: loginUser.id,
              //       };
              //       const locationData = await ConcreteLocation.createConcreteLocation(object);
              //       locationParam.ConcreteLocationId = locationData.id;
              //     }
              //     await ConcreteRequestLocation.createInstance(locationParam);
              //   }
              // });
              pumpSize.forEach(async (element, i) => {
                const index = existPumpSizes.findIndex(
                  (item) => item.ConcretePumpSizeId === element.id,
                );
                const pumpSizeParam = updateParam;
                pumpSizeParam.ConcretePumpSizeId = element.id;
                if (index !== -1) {
                  await ConcreteRequestPumpSize.update(pumpSizeParam, {
                    where: { id: existPumpSizes[index].id },
                  });
                } else {
                  if (element.chosenFromDropdown) {
                    pumpSizeParam.ConcretePumpSizeId = element.id;
                  } else {
                    const object2 = {
                      pumpSize: element.pumpSize,
                      ProjectId: +concreteRequestData.ProjectId,
                      isDeleted: false,
                      createdBy: loginUser.id,
                    };
                    const pumpSizeData = await ConcretePumpSize.createConcretePumpSize(object2);
                    pumpSizeParam.ConcretePumpSizeId = pumpSizeData.id;
                  }
                  await ConcreteRequestPumpSize.createInstance(pumpSizeParam);
                }
              });
              mixDesign.forEach(async (element, i) => {
                const index = existMixDesigns.findIndex(
                  (item) => item.ConcreteMixDesignId === element.id,
                );
                const mixDesignParam = updateParam;
                mixDesignParam.ConcreteMixDesignId = element.id;
                if (index !== -1) {
                  await ConcreteRequestMixDesign.update(mixDesignParam, {
                    where: { id: existMixDesigns[index].id },
                  });
                } else {
                  if (element.chosenFromDropdown) {
                    mixDesignParam.ConcreteMixDesignId = element.id;
                  } else {
                    const object3 = {
                      mixDesign: element.mixDesign,
                      ProjectId: +concreteRequestData.ProjectId,
                      isDeleted: false,
                      createdBy: loginUser.id,
                    };
                    const mixDesignData = await ConcreteMixDesign.createConcreteMixDesign(object3);
                    mixDesignParam.ConcreteMixDesignId = mixDesignData.id;
                  }
                  await ConcreteRequestMixDesign.createInstance(mixDesignParam);
                }
              });
              concreteSupplier.forEach(async (element, i) => {
                const index = existCompanies.findIndex((item) => item.CompanyId === element);
                const companyParam = updateParam;
                companyParam.CompanyId = element;
                if (index !== -1) {
                  await ConcreteRequestCompany.update(companyParam, {
                    where: { id: existCompanies[index].id },
                  });
                } else {
                  await ConcreteRequestCompany.createInstance(companyParam);
                }
              });
              const locationChosen = await Locations.findOne({
                where: {
                  ProjectId: concreteRequestData.ProjectId,
                  id: concreteRequestData.LocationId,
                },
              });
              history.description = `${loginUser.firstName} ${loginUser.lastName} updated the Concrete Booking, ${concreteRequestData.description}`;
              history.locationFollowDescription = `${loginUser.firstName} ${loginUser.lastName} Created Concrete Booking, ${concreteRequestData.description}. Location: ${locationChosen.locationPath}.`;
              history.MemberId = memberData.id;
              history.firstName = loginUser.firstName;
              history.profilePic = loginUser.profilePic;
              history.createdAt = new Date();
              history.ProjectId = concreteRequestData.ProjectId;
              const projectDetails = await Project.findByPk(concreteRequestData.ProjectId);
              history.projectName = projectDetails.projectName;
              notification.ProjectId = isConcreteRequestExists.ProjectId;
              if (
                existsConcreteRequest &&
                existsConcreteRequest.recurrence &&
                existsConcreteRequest.recurrence.recurrence
              ) {
                notification.recurrenceType = `${existsConcreteRequest.recurrence.recurrence
                  } From ${moment(existsConcreteRequest.recurrence.recurrenceStartDate).format(
                    'MM/DD/YYYY',
                  )} to ${moment(existsConcreteRequest.recurrence.recurrenceEndDate).format(
                    'MM/DD/YYYY',
                  )}`;
              }
              notification.title = `Concrete Booking Updated by ${loginUser.firstName} ${loginUser.lastName}`;
              notification.isDeliveryRequest = false;
              notification.requestType = 'concreteRequest';
              const newNotification = await Notification.createInstance(notification);
              const memberLocationPreference = await LocationNotificationPreferences.findAll({
                where: {
                  ProjectId: concreteRequestData.ProjectId,
                  LocationId: concreteRequestData.LocationId,
                  follow: true,
                },
                include: [
                  {
                    association: 'Member',
                    attributes: ['id', 'RoleId'],
                    where: {
                      [Op.and]: [
                        {
                          id: { [Op.ne]: memberData.id },
                        },
                      ],
                    },
                    include: [
                      {
                        association: 'User',
                        attributes: ['id', 'firstName', 'lastName', 'email'],
                      },
                    ],
                  },
                ],
              });
              const locationFollowMembers = [];
              memberLocationPreference.forEach(async (element) => {
                locationFollowMembers.push(element.Member.id);
              });
              const personData = await ConcreteRequestResponsiblePerson.findAll({
                where: { ConcreteRequestId: isConcreteRequestExists.id, isDeleted: false },
                include: [
                  {
                    association: 'Member',
                    include: [
                      {
                        association: 'User',
                        attributes: ['id', 'firstName', 'lastName', 'email'],
                      },
                    ],
                    where: {
                      [Op.and]: {
                        RoleId: {
                          [Op.notIn]: [1, 2],
                        },
                        id: { [Op.notIn]: locationFollowMembers },
                      },
                    },
                    attributes: ['id', 'RoleId'],
                  },
                ],
                attributes: ['id'],
              });
              const adminData = await Member.findAll({
                where: {
                  [Op.or]: [
                    {
                      [Op.and]: [
                        { ProjectId: concreteRequestData.ProjectId },
                        { isDeleted: false },
                        { id: { [Op.ne]: newNotification.MemberId } },
                        { id: { [Op.in]: responsiblePersons } },
                        { id: { [Op.notIn]: locationFollowMembers } },
                      ],
                    },
                    { [Op.and]: [] },
                  ],
                },
                include: [
                  {
                    association: 'User',
                    attributes: ['id', 'firstName', 'lastName', 'email'],
                  },
                ],
                attributes: ['id'],
              });
              if (memberLocationPreference && memberLocationPreference.length > 0) {
                // here 5-(NotificationPreferenceItemId -When a delivery/crane/concrete request is edited)
                await pushNotification.sendMemberLocationPreferencePushNotificationForConcrete(
                  memberLocationPreference,
                  isConcreteRequestExists.ConcreteRequestId,
                  history.locationFollowDescription,
                  isConcreteRequestExists.requestType,
                  isConcreteRequestExists.ProjectId,
                  isConcreteRequestExists.id,
                  5,
                );
                // here 5-(NotificationPreferenceItemId -When a delivery/crane/concrete request is edited)
                await notificationHelper.createMemberDeliveryLocationInAppNotification(
                  DeliveryPersonNotification,
                  isConcreteRequestExists.ProjectId,
                  newNotification.id,
                  memberLocationPreference,
                  5,
                );
              }
              history.adminData = adminData;
              history.memberData = personData;
              if (memberLocationPreference && memberLocationPreference.length > 0) {
                history.memberData.push(...memberLocationPreference);
              }
              const checkMemberNotification = await NotificationPreference.findAll({
                where: {
                  ProjectId: concreteRequestData.ProjectId,
                  isDeleted: false,
                },
                attributes: [
                  'id',
                  'MemberId',
                  'ProjectId',
                  'ParentCompanyId',
                  'NotificationPreferenceItemId',
                  'instant',
                  'dailyDigest',
                ],
                include: [
                  {
                    association: 'NotificationPreferenceItem',
                    where: {
                      id: 5,
                      isDeleted: false,
                    },
                    attributes: ['id', 'description', 'inappNotification', 'emailNotification'],
                  },
                ],
              });
              history.notificationPreference = checkMemberNotification;
              // here 5-(NotificationPreferenceItemId -When a delivery/crane/concrete request is edited)
              await notificationHelper.createDeliveryPersonNotification(
                adminData,
                [],
                projectDetails,
                newNotification,
                DeliveryPersonNotification,
                memberData,
                loginUser,
                5,
                'updated a',
                'Concrete Request',
                `concrete Booking (${isConcreteRequestExists.ConcreteRequestId} - ${isConcreteRequestExists.description})`,
                isConcreteRequestExists.ConcreteRequestId,
              );
              const updatedConcreteRequest = await ConcreteRequest.getSingleConcreteRequestData({
                id: +isConcreteRequestExists.id,
              });
              const exist = await ConcreteRequest.findOne({
                include: [
                  {
                    association: 'memberDetails',
                    required: false,
                    where: { isDeleted: false, isActive: true },
                    attributes: ['id'],
                    include: [
                      {
                        association: 'Member',
                        attributes: ['id', 'isGuestUser'],
                        include: [
                          {
                            association: 'User',
                            attributes: [
                              'email',
                              'phoneCode',
                              'phoneNumber',
                              'firstName',
                              'lastName',
                            ],
                          },
                        ],
                      },
                    ],
                  },
                ],
                where: { id: +concreteRequestData.id },
              });
              const userDataMail = exist.memberDetails;
              for (let i = 0; i < userDataMail.length; i++) {
                const responsibleGuestUser = userDataMail[i].Member.isGuestUser;
                if (responsibleGuestUser) {
                  const guestMailPayload = {
                    email: userDataMail[i].Member.User.email,
                    guestName: userDataMail[i].Member.User.firstName,
                    content: `We would like to inform you that 
                    ${loginUser.firstName} ${loginUser.lastName} has updated a Concrete booking - ${exist.description}.`,
                  };
                  await MAILER.sendMail(
                    guestMailPayload,
                    'notifyGuestOnEdit',
                    `Concrete Booking updated by ${loginUser.firstName} ${loginUser.lastName}`,
                    `Concrete Booking updated by ${loginUser.firstName} ${loginUser.lastName}`,
                    async (info, err) => {
                      console.log(info, err);
                    },
                  );
                }
              }
              if (existsConcreteRequest.status === 'Declined') {
                if (
                  memberData.RoleId !== 2 &&
                  !memberData.isAutoApproveEnabled &&
                  !projectSettingDetails.ProjectSettings.isAutoApprovalEnabled
                ) {
                  await ConcreteRequest.update(
                    { status: 'Tentative' },
                    {
                      where: { id: updatedConcreteRequest.id },
                    },
                  );
                }
                if (
                  memberData.RoleId === 2 ||
                  memberData.isAutoApproveEnabled ||
                  projectSettingDetails.ProjectSettings.isAutoApprovalEnabled
                ) {
                  const isStatusDataUpdated = await ConcreteRequest.update(
                    { status: 'Approved', approvedBy: memberData.id, approved_at: new Date() },
                    {
                      where: { id: updatedConcreteRequest.id },
                    },
                  );
                  if (isStatusDataUpdated) {
                    const object = {
                      ProjectId: concreteRequestData.ProjectId,
                      MemberId: memberData.id,
                      ConcreteRequestId: updatedConcreteRequest.id,
                      isDeleted: false,
                      type: 'approved',
                      description: `${loginUser.firstName} ${loginUser.lastName} Approved the Concrete Request, ${concreteRequestData.description}`,
                    };
                    await ConcreteRequestHistory.createInstance(object);
                  }
                }
              }
              if (existsConcreteRequest.status === 'Tentative') {
                if (
                  memberData.RoleId !== 2 &&
                  (memberData.isAutoApproveEnabled ||
                    projectSettingDetails.ProjectSettings.isAutoApprovalEnabled)
                ) {
                  const isStatusDataUpdated = await ConcreteRequest.update(
                    { status: 'Approved' },
                    {
                      where: { id: updatedConcreteRequest.id },
                    },
                  );
                  if (isStatusDataUpdated) {
                    const object = {
                      ProjectId: concreteRequestData.ProjectId,
                      MemberId: memberData.id,
                      ConcreteRequestId: updatedConcreteRequest.id,
                      isDeleted: false,
                      type: 'approved',
                      description: `${loginUser.firstName} ${loginUser.lastName} Approved the Concrete Request, ${concreteRequestData.description}`,
                    };
                    await ConcreteRequestHistory.createInstance(object);
                  }
                }
                if (
                  memberData.RoleId === 2 ||
                  memberData.isAutoApproveEnabled ||
                  projectSettingDetails.ProjectSettings.isAutoApprovalEnabled
                ) {
                  const isStatusDataUpdated = await ConcreteRequest.update(
                    { status: 'Approved', approvedBy: memberData.id, approved_at: new Date() },
                    {
                      where: { id: updatedConcreteRequest.id },
                    },
                  );
                  if (isStatusDataUpdated) {
                    const object = {
                      ProjectId: concreteRequestData.ProjectId,
                      MemberId: memberData.id,
                      ConcreteRequestId: updatedConcreteRequest.id,
                      isDeleted: false,
                      type: 'approved',
                      description: `${loginUser.firstName} ${loginUser.lastName} Approved the Concrete Request, ${concreteRequestData.description}`,
                    };
                    await ConcreteRequestHistory.createInstance(object);
                  }
                }
              }
              if (existsConcreteRequest.status === 'Approved') {
                if (
                  memberData.RoleId === 4 &&
                  !memberData.isAutoApproveEnabled &&
                  !projectSettingDetails.ProjectSettings.isAutoApprovalEnabled
                ) {
                  await ConcreteRequest.update(
                    { status: 'Tentative' },
                    {
                      where: { id: updatedConcreteRequest.id },
                    },
                  );
                }
              }
              this.updateEditConcreteRequestHistory(
                concreteRequestData,
                existsConcreteRequest,
                updatedConcreteRequest,
                history,
                loginUser,
              );
              // here 5-(NotificationPreferenceItemId -When a delivery/crane/concrete request is edited)
              await pushNotification.sendPushNotificationForConcrete(
                history,
                5,
                concreteRequestData.ProjectId,
              );
              history.ConcreteRequestId = isConcreteRequestExists.ConcreteRequestId;
              return done(history, false);
            }
          });
        });
      }
    } catch (e) {
      done(null, e);
    }
  },
  async getSingleConcreteRequest(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const { params } = inputData;
      const getConcreteRequest = await ConcreteRequest.findOne({
        where: {
          ConcreteRequestId: params.ConcreteRequestId,
          ProjectId: params.ProjectId,
          isDeleted: false,
        },
      });
      const concreteRequest = await ConcreteRequest.getSingleConcreteRequestData({
        id: +getConcreteRequest.id,
      });
      done(concreteRequest, false);
    } catch (e) {
      done(null, e);
    }
  },
  async updateConcreteRequestStatus(inputData, done) {
    try {
      await this.getDynamicModel(inputData);
      const updateData = inputData.body;
      const loginUser = inputData.user;
      const statusValue = await ConcreteRequest.findOne({
        where: { id: updateData.id, isDeleted: false },
        include: [
          {
            association: 'recurrence',
            required: false,
            attributes: [
              'id',
              'recurrence',
              'recurrenceStartDate',
              'recurrenceEndDate',
              'dateOfMonth',
              'monthlyRepeatType',
              'repeatEveryCount',
              'days',
              'requestType',
              'repeatEveryType',
              'chosenDateOfMonth',
              'createdBy',
            ],
          },
          {
            association: 'memberDetails',
            required: false,
            where: { isDeleted: false, isActive: true },
            attributes: ['id'],
            include: [
              {
                association: 'Member',
                where: { isDeleted: false },
                attributes: ['id'],
                include: [
                  {
                    association: 'User',
                    where: { isDeleted: false },
                    attributes: ['email', 'phoneCode', 'phoneNumber', 'firstName', 'lastName'],
                  },
                ],
              },
            ],
          },
        ],
      });
      if (!statusValue) {
        done(null, { message: 'Id does not exist.' });
      } else {
        const memberValue = await Member.findOne({
          where: Sequelize.and({
            UserId: inputData.user.id,
            ProjectId: statusValue.ProjectId,
            isDeleted: false,
          }),
        });
        if (memberValue) {
          if (
            memberValue.RoleId === 2 ||
            memberValue.RoleId === 3 ||
            memberValue.RoleId === 1 ||
            memberValue.RoleId === 4
          ) {
            const locationChosen = await Locations.findOne({
              where: {
                ProjectId: statusValue.ProjectId,
                id: statusValue.LocationId,
              },
            });
            const memberLocationPreference = await LocationNotificationPreferences.findAll({
              where: {
                ProjectId: statusValue.ProjectId,
                LocationId: statusValue.LocationId,
                follow: true,
              },
              include: [
                {
                  association: 'Member',
                  attributes: ['id', 'RoleId'],
                  where: {
                    [Op.and]: [
                      {
                        id: { [Op.ne]: memberValue.id },
                      },
                    ],
                  },
                  include: [
                    {
                      association: 'User',
                      attributes: ['id', 'firstName', 'lastName', 'email'],
                    },
                  ],
                },
              ],
            });
            const locationFollowMembers = [];
            memberLocationPreference.forEach(async (element) => {
              locationFollowMembers.push(element.Member.id);
            });
            const bookingMemberDetails = [];
            statusValue.memberDetails.forEach(async (element) => {
              bookingMemberDetails.push(element.Member.id);
            });
            const history = {
              ConcreteRequestId: statusValue.id,
              MemberId: memberValue.id,
            };
            if (
              updateData.status === 'Completed' ||
              updateData.status === 'Approved' ||
              updateData.status === 'Declined'
            ) {
              const concreteRequestStatus = updateData.status;
              if (updateData.statuschange && updateData.statuschange === 'Reverted') {
                history.type = 'approved';
                history.description = `${loginUser.firstName} ${loginUser.lastName} Reverted the status from completed to approved for Concrete Booking, ${statusValue.description}`;
                history.locationFollowDescription = `${loginUser.firstName} ${loginUser.lastName} Reverted the status from completed to approved for Concrete Booking, ${statusValue.description}. Location: ${locationChosen.locationPath}.`;
              } else {
                history.type = updateData.status.toLowerCase();
                history.description = `${loginUser.firstName} ${loginUser.lastName} ${concreteRequestStatus} the Concrete Booking`;
                history.locationFollowDescription = `${loginUser.firstName} ${loginUser.lastName} ${concreteRequestStatus} the Concrete Booking, ${statusValue.description}. Location: ${locationChosen.locationPath}.`;
              }
              const notification = history;
              notification.ProjectId = statusValue.ProjectId;
              if (statusValue && statusValue.recurrence && statusValue.recurrence.recurrence) {
                notification.recurrenceType = `${statusValue.recurrence.recurrence} From ${moment(
                  statusValue.recurrence.recurrenceStartDate,
                ).format('MM/DD/YYYY')} to ${moment(
                  statusValue.recurrence.recurrenceEndDate,
                ).format('MM/DD/YYYY')}`;
              }
              notification.title = `Concrete Booking ${concreteRequestStatus} by ${loginUser.firstName} ${loginUser.lastName} `;
              const updateRequestObject = {
                status: updateData.status,
                approvedBy: memberValue.id,
                approved_at: new Date(),
              };
              if (updateData.status === 'Completed') {
                if (updateData.hoursToCompletePlacement) {
                  updateRequestObject.hoursToCompletePlacement =
                    updateData.hoursToCompletePlacement;
                } else {
                  updateRequestObject.hoursToCompletePlacement = null;
                }
                if (updateData.minutesToCompletePlacement) {
                  updateRequestObject.minutesToCompletePlacement =
                    updateData.minutesToCompletePlacement;
                } else {
                  updateRequestObject.minutesToCompletePlacement = null;
                }
                if (updateData.cubicYardsTotal) {
                  updateRequestObject.cubicYardsTotal = updateData.cubicYardsTotal;
                } else {
                  updateRequestObject.cubicYardsTotal = null;
                }
              }
              await ConcreteRequest.update(updateRequestObject, { where: { id: updateData.id } });
              await ConcreteRequestHistory.createInstance(history);
              history.firstName = loginUser.firstName;
              history.profilePic = loginUser.profilePic;
              history.createdAt = new Date();
              history.ProjectId = statusValue.ProjectId;
              const projectDetails = await Project.findByPk(statusValue.ProjectId);
              history.projectName = projectDetails.projectName;
              notification.isDeliveryRequest = false;
              notification.requestType = 'concreteRequest';
              const newNotification = await Notification.createInstance(notification);
              const personData = await ConcreteRequestResponsiblePerson.findAll({
                where: { ConcreteRequestId: statusValue.id, isDeleted: false },
                include: [
                  {
                    association: 'Member',
                    include: [
                      {
                        association: 'User',
                        attributes: ['id', 'firstName', 'lastName', 'email'],
                      },
                    ],
                    where: {
                      [Op.and]: {
                        RoleId: {
                          [Op.notIn]: [1, 2],
                        },
                        id: { [Op.notIn]: locationFollowMembers },
                      },
                    },
                    attributes: ['id', 'RoleId'],
                  },
                ],
                attributes: ['id'],
              });
              const adminData = await Member.findAll({
                where: {
                  [Op.and]: [
                    { ProjectId: statusValue.ProjectId },
                    { isDeleted: false },
                    { id: { [Op.in]: bookingMemberDetails } },
                    { id: { [Op.ne]: newNotification.MemberId } },
                    { id: { [Op.notIn]: locationFollowMembers } },
                  ],
                },
                include: [
                  {
                    association: 'User',
                    attributes: ['id', 'firstName', 'lastName', 'email'],
                  },
                ],
                attributes: ['id'],
              });
              if (memberLocationPreference && memberLocationPreference.length > 0) {
                // here 6-(NotificationPreferenceItemId -When a delivery/crane/concrete request status updated)
                await pushNotification.sendMemberLocationPreferencePushNotificationForConcrete(
                  memberLocationPreference,
                  statusValue.ConcreteRequestId,
                  history.locationFollowDescription,
                  statusValue.requestType,
                  statusValue.ProjectId,
                  statusValue.id,
                  6,
                );
                // here 6-(NotificationPreferenceItemId -When a delivery/crane/concrete request status updated)
                await notificationHelper.createMemberDeliveryLocationInAppNotification(
                  DeliveryPersonNotification,
                  statusValue.ProjectId,
                  newNotification.id,
                  memberLocationPreference,
                  6,
                );
              }
              history.memberData = personData;
              history.adminData = adminData;
              if (memberLocationPreference && memberLocationPreference.length > 0) {
                history.memberData.push(...memberLocationPreference);
              }
              // here 6-(NotificationPreferenceItemId -When a delivery/crane/concrete request status updated)
              await notificationHelper.createDeliveryPersonNotification(
                adminData,
                personData,
                projectDetails,
                newNotification,
                DeliveryPersonNotification,
                memberValue,
                loginUser,
                6,
                `${concreteRequestStatus.toLowerCase()} a`,
                'Concrete Request',
                `concrete Booking (${statusValue.ConcreteRequestId} - ${statusValue.description})`,
                statusValue.ConcreteRequestId,
              );
              const checkMemberNotification = await NotificationPreference.findAll({
                where: {
                  ProjectId: statusValue.ProjectId,
                  isDeleted: false,
                },
                attributes: [
                  'id',
                  'MemberId',
                  'ProjectId',
                  'ParentCompanyId',
                  'NotificationPreferenceItemId',
                  'instant',
                  'dailyDigest',
                ],
                include: [
                  {
                    association: 'NotificationPreferenceItem',
                    where: {
                      id: 6,
                      isDeleted: false,
                    },
                    attributes: ['id', 'description', 'inappNotification', 'emailNotification'],
                  },
                ],
              });
              history.notificationPreference = checkMemberNotification;
              // here 6-(NotificationPreferenceItemId -When a delivery/crane/concrete request status updated)
              await pushNotification.sendPushNotificationForConcrete(
                history,
                6,
                statusValue.ProjectId,
              );
              history.ConcreteRequestId = statusValue.ConcreteRequestId;
              const exist = await ConcreteRequest.findOne({
                include: [
                  {
                    association: 'memberDetails',
                    required: false,
                    where: { isDeleted: false, isActive: true },
                    attributes: ['id'],
                    include: [
                      {
                        association: 'Member',
                        attributes: ['id', 'isGuestUser'],
                        include: [
                          {
                            association: 'User',
                            attributes: [
                              'email',
                              'phoneCode',
                              'phoneNumber',
                              'firstName',
                              'lastName',
                            ],
                          },
                        ],
                      },
                    ],
                  },
                ],
                where: { id: updateData.id, isDeleted: false },
              });
              const userDataMail = exist.memberDetails;
              for (let i = 0; i < userDataMail.length; i++) {
                const responsibleGuestUser = userDataMail[i].Member.isGuestUser;
                if (responsibleGuestUser) {
                  const guestMailPayload = {
                    email: userDataMail[i].Member.User.email,
                    guestName: userDataMail[i].Member.User.firstName,
                    content: `We would like to inform you that 
                    Concrete Booking - ${statusValue.description} was ${updateData.status} by ${loginUser.firstName} ${loginUser.lastName}.`,
                  };
                  await MAILER.sendMail(
                    guestMailPayload,
                    'notifyGuestOnEdit',
                    `Concrete Booking ${updateData.status} by ${loginUser.firstName}`,
                    `Concrete Booking ${updateData.status} by ${loginUser.firstName}`,
                    async (info, err) => {
                      console.log(info, err);
                    },
                  );
                }
              }
              if (updateData.status === 'Completed') {
                const userEmails = await this.getMemberDetailData(
                  history,
                  memberLocationPreference,
                );
                if (userEmails.length > 0) {
                  userEmails.forEach(async (element) => {
                    let name;
                    if (!element.firstName) {
                      name = 'user';
                    } else {
                      name = `${element.firstName} ${element.lastName}`;
                    }
                    const time = moment(statusValue.concretePlacementStart).format('MM-DD-YYYY');
                    const mailPayload = {
                      userName: name,
                      email: element.email,
                      description: statusValue.description,
                      userName1: `${loginUser.firstName} ${loginUser.lastName}`,
                      concreteID: statusValue.ConcreteRequestId,
                      concreteId: statusValue.ConcreteRequestId,
                      status_timestamp: moment().utc().format('MM-DD-YYYY hh:mm:ss a zz'),
                      timestamp: time,
                    };
                    const isMemberFollowLocation = await LocationNotificationPreferences.findOne({
                      where: {
                        MemberId: +element.MemberId,
                        ProjectId: +statusValue.ProjectId,
                        LocationId: +statusValue.LocationId,
                        isDeleted: false,
                        // follow: true,
                      },
                    });
                    if (isMemberFollowLocation) {
                      const memberNotification = await NotificationPreference.findOne({
                        where: {
                          MemberId: +element.MemberId,
                          ProjectId: +statusValue.ProjectId,
                          isDeleted: false,
                        },
                        include: [
                          {
                            association: 'NotificationPreferenceItem',
                            where: {
                              id: 11,
                              isDeleted: false,
                            },
                          },
                        ],
                      });
                      if (memberNotification && memberNotification.instant) {
                        await MAILER.sendMail(
                          mailPayload,
                          'completedConcreteRequest',
                          'Concrete Booking status updated',
                          'Concrete Booking status updated',
                          async (info, err) => {
                            console.log(info, err);
                          },
                        );
                      }
                      if (memberNotification && memberNotification.dailyDigest) {
                        await this.createDailyDigestData(
                          +memberValue.RoleId,
                          +element.MemberId,
                          +statusValue.ProjectId,
                          +statusValue.ParentCompanyId,
                          loginUser,
                          'completed a',
                          'Concrete Request',
                          `concrete Booking (${statusValue.ConcreteRequestId} - ${statusValue.description})`,
                          statusValue.ConcreteRequestId,
                        );
                      }
                    }
                  });
                  return done(history, false);
                }
                return done(history, false);
              }
              return done(history, false);
            }
            done(null, { message: 'Invalid Status' });
          } else {
            done(null, {
              message: 'Only Project Admin and General Contracter allowed to update the status',
            });
          }
        } else {
          done(null, {
            message: 'Not a Valid Member for this Concrete Booking',
          });
        }
      }
    } catch (e) {
      done(null, e);
    }
  },
  async getSearchConcreteData(
    incomeData,
    deliveryList,
    result,
    limit,
    index,
    count,
    memberDetails,
    done,
  ) {
    const elementValue = deliveryList[index];
    if (elementValue) {
      const element = JSON.parse(JSON.stringify(elementValue));
      const status = { companyCondition: true, memberCondition: true };
      if (incomeData.companyFilter > 0) {
        const data = await ConcreteRequestCompany.findOne({
          where: {
            ConcreteRequestId: element.id,
            CompanyId: incomeData.companyFilter,
            isDeleted: false,
          },
        });
        if (!data) {
          status.companyCondition = false;
        }
      }
      if (memberDetails.RoleId === 4 || memberDetails.RoleId === 3) {
        const data = await ConcreteRequestResponsiblePerson.findOne({
          where: {
            ConcreteRequestId: element.id,
            MemberId: memberDetails.id,
            isDeleted: false,
            isActive: true,
          },
        });
        if (!data) {
          status.memberCondition = false;
        }
      }
      if (status.memberCondition && status.companyCondition) {
        result.push(element);
      }
      if (index < deliveryList.length - 1) {
        this.getSearchConcreteData(
          incomeData,
          deliveryList,
          result,
          limit,
          index + 1,
          count + 1,
          memberDetails,
          (response, err) => {
            if (!err) {
              done(response, false);
            } else {
              done(null, err);
            }
          },
        );
      } else {
        done(result, false);
      }
    } else {
      done(result, false);
    }
  },
  async getSearchDeliveryData(
    incomeData,
    deliveryList,
    result,
    limit,
    index,
    count,
    memberDetails,
    done,
  ) {
    const elementValue = deliveryList[index];
    if (elementValue) {
      const element = JSON.parse(JSON.stringify(elementValue));
      const status = { companyCondition: true, memberCondition: true };
      if (incomeData.companyFilter > 0) {
        const data = await DeliverCompany.findOne({
          where: {
            DeliveryId: element.id,
            CompanyId: +incomeData.companyFilter,
            isDeleted: false,
          },
        });
        if (!data) {
          status.companyCondition = false;
        }
      }
      if (memberDetails.RoleId === 4 || memberDetails.RoleId === 3) {
        const data = await DeliveryPerson.findOne({
          where: {
            DeliveryId: element.id,
            MemberId: memberDetails.id,
            isDeleted: false,
            isActive: true,
          },
        });
        if (!data) {
          status.memberCondition = false;
        }
      }
      if (status.memberCondition && status.companyCondition) {
        result.push(element);
      }
      if (index < deliveryList.length - 1) {
        this.getSearchDeliveryData(
          incomeData,
          deliveryList,
          result,
          limit,
          index + 1,
          count + 1,
          memberDetails,
          (response, err) => {
            if (!err) {
              done(response, false);
            } else {
              done(null, err);
            }
          },
        );
      } else {
        done(result, false);
      }
    } else {
      done(result, false);
    }
  },
  async updateEditConcreteRequestHistory(
    userEditedConcreteRequestData,
    existsConcreteRequestData,
    updatedConcreteRequest,
    history,
    loginUser,
  ) {
    if (
      (userEditedConcreteRequestData.description &&
        userEditedConcreteRequestData.description.toLowerCase()) !==
      (existsConcreteRequestData.description && existsConcreteRequestData.description.toLowerCase())
    ) {
      const descriptionObject = {
        ProjectId: history.ProjectId,
        MemberId: history.MemberId,
        ConcreteRequestId: history.ConcreteRequestId,
        isDeleted: false,
        type: 'edit',
        description: '',
      };
      descriptionObject.description = `${loginUser.firstName} ${loginUser.lastName} updated the Description ${userEditedConcreteRequestData.description}`;
      ConcreteRequestHistory.createInstance(descriptionObject);
    }
    if (
      userEditedConcreteRequestData.LocationId !== existsConcreteRequestData.LocationId &&
      updatedConcreteRequest &&
      updatedConcreteRequest.location &&
      updatedConcreteRequest.location.locationPath
    ) {
      const locationObject = {
        ProjectId: history.ProjectId,
        MemberId: history.MemberId,
        ConcreteRequestId: history.ConcreteRequestId,
        isDeleted: false,
        type: 'edit',
        description: '',
      };
      locationObject.description = `${loginUser.firstName} ${loginUser.lastName} Updated the Location, ${updatedConcreteRequest.location.locationPath} `;
      ConcreteRequestHistory.createInstance(locationObject);
    }
    if (
      moment(userEditedConcreteRequestData.concretePlacementStart).format('MM/DD/YYY h:mm a') !==
      moment(existsConcreteRequestData.concretePlacementStart).format('MM/DD/YYY h:mm a')
    ) {
      const concretePlacementStartObject = {
        ProjectId: history.ProjectId,
        MemberId: history.MemberId,
        ConcreteRequestId: history.ConcreteRequestId,
        isDeleted: false,
        type: 'edit',
        description: '',
      };
      concretePlacementStartObject.description = `${loginUser.firstName} ${loginUser.lastName
        } updated the Placement Start Time ${moment(
          userEditedConcreteRequestData.concretePlacementStart,
        )
          .utc()
          .format('MM-DD-YYYY hh:mm:ss a zz')}`;
      ConcreteRequestHistory.createInstance(concretePlacementStartObject);
    }
    if (
      moment(userEditedConcreteRequestData.concretePlacementEnd).format('MM/DD/YYY h:mm a') !==
      moment(existsConcreteRequestData.concretePlacementEnd).format('MM/DD/YYY h:mm a')
    ) {
      const concretePlacementEndObject = {
        ProjectId: history.ProjectId,
        MemberId: history.MemberId,
        ConcreteRequestId: history.ConcreteRequestId,
        isDeleted: false,
        type: 'edit',
        description: '',
      };
      concretePlacementEndObject.description = `${loginUser.firstName} ${loginUser.lastName
        } updated the Anticipated Completion Time ${moment(
          userEditedConcreteRequestData.concretePlacementEnd,
        )
          .utc()
          .format('MM-DD-YYYY hh:mm:ss a zz')}`;
      ConcreteRequestHistory.createInstance(concretePlacementEndObject);
    }
    if (
      moment(userEditedConcreteRequestData.pumpWorkStart).format('h:mm a') !==
      moment(existsConcreteRequestData.pumpWorkStart).format('h:mm a')
    ) {
      const pumpWorkStartObject = {
        ProjectId: history.ProjectId,
        MemberId: history.MemberId,
        ConcreteRequestId: history.ConcreteRequestId,
        isDeleted: false,
        type: 'edit',
        description: '',
      };
      if (userEditedConcreteRequestData.pumpWorkStart !== null) {
        pumpWorkStartObject.description = `${loginUser.firstName} ${loginUser.lastName
          } updated the Pump Show up Time ${moment(userEditedConcreteRequestData.pumpWorkStart)
            .utc()
            .format('MM-DD-YYYY hh:mm:ss a zz')}`;
        ConcreteRequestHistory.createInstance(pumpWorkStartObject);
      }
    }
    if (
      moment(userEditedConcreteRequestData.pumpWorkEnd).format('h:mm a') !==
      moment(existsConcreteRequestData.pumpWorkEnd).format('h:mm a')
    ) {
      const pumpWorkEndObject = {
        ProjectId: history.ProjectId,
        MemberId: history.MemberId,
        ConcreteRequestId: history.ConcreteRequestId,
        isDeleted: false,
        type: 'edit',
        description: '',
      };
      if (userEditedConcreteRequestData.pumpWorkEnd !== null) {
        pumpWorkEndObject.description = `${loginUser.firstName} ${loginUser.lastName
          } updated the Pump Completion Time ${moment(userEditedConcreteRequestData.pumpWorkEnd)
            .utc()
            .format('MM-DD-YYYY hh:mm:ss a zz')}`;
        ConcreteRequestHistory.createInstance(pumpWorkEndObject);
      }
    }
    if (userEditedConcreteRequestData.isPumpRequired) {
      if (!existsConcreteRequestData.isPumpRequired) {
        const isPumpRequiredObject = {
          ProjectId: history.ProjectId,
          MemberId: history.MemberId,
          ConcreteRequestId: history.ConcreteRequestId,
          isDeleted: false,
          type: 'edit',
          description: '',
        };
        isPumpRequiredObject.description = `${loginUser.firstName} ${loginUser.lastName} enabled the Pump required`;
        ConcreteRequestHistory.createInstance(isPumpRequiredObject);
      }
    }
    if (!userEditedConcreteRequestData.isPumpRequired) {
      if (existsConcreteRequestData.isPumpRequired) {
        const isPumpRequiredObject1 = {
          ProjectId: history.ProjectId,
          MemberId: history.MemberId,
          ConcreteRequestId: history.ConcreteRequestId,
          isDeleted: false,
          type: 'edit',
          description: '',
        };
        isPumpRequiredObject1.description = `${loginUser.firstName} ${loginUser.lastName} disabled the Pump required`;
        ConcreteRequestHistory.createInstance(isPumpRequiredObject1);
      }
    }
    if (userEditedConcreteRequestData.isPumpConfirmed) {
      if (!existsConcreteRequestData.isPumpConfirmed) {
        const isPumpConfirmedObject = {
          ProjectId: history.ProjectId,
          MemberId: history.MemberId,
          ConcreteRequestId: history.ConcreteRequestId,
          isDeleted: false,
          type: 'edit',
          description: '',
        };
        isPumpConfirmedObject.description = `${loginUser.firstName} ${loginUser.lastName} enabled the Pump Confirmed`;
        ConcreteRequestHistory.createInstance(isPumpConfirmedObject);
      }
    }
    if (!userEditedConcreteRequestData.isPumpConfirmed) {
      if (existsConcreteRequestData.isPumpConfirmed) {
        const isPumpConfirmedObject1 = {
          ProjectId: history.ProjectId,
          MemberId: history.MemberId,
          ConcreteRequestId: history.ConcreteRequestId,
          isDeleted: false,
          type: 'edit',
          description: '',
        };
        isPumpConfirmedObject1.description = `${loginUser.firstName} ${loginUser.lastName} disabled the Pump Confirmed`;
        ConcreteRequestHistory.createInstance(isPumpConfirmedObject1);
      }
    }
    if (userEditedConcreteRequestData.isConcreteConfirmed) {
      if (!existsConcreteRequestData.isConcreteConfirmed) {
        const isConcreteConfirmedObject = {
          ProjectId: history.ProjectId,
          MemberId: history.MemberId,
          ConcreteRequestId: history.ConcreteRequestId,
          isDeleted: false,
          type: 'edit',
          description: '',
        };
        isConcreteConfirmedObject.description = `${loginUser.firstName} ${loginUser.lastName} enabled the Concrete Confirmed`;
        ConcreteRequestHistory.createInstance(isConcreteConfirmedObject);
      }
    }
    if (!userEditedConcreteRequestData.isConcreteConfirmed) {
      if (existsConcreteRequestData.isConcreteConfirmed) {
        const isConcreteConfirmedObject1 = {
          ProjectId: history.ProjectId,
          MemberId: history.MemberId,
          ConcreteRequestId: history.ConcreteRequestId,
          isDeleted: false,
          type: 'edit',
          description: '',
        };
        isConcreteConfirmedObject1.description = `${loginUser.firstName} ${loginUser.lastName} disabled the Concrete Confirmed`;
        ConcreteRequestHistory.createInstance(isConcreteConfirmedObject1);
      }
    }
    if (
      moment(userEditedConcreteRequestData.pumpOrderedDate).format('MM/DD/YYYY') !==
      moment(existsConcreteRequestData.pumpOrderedDate).format('MM/DD/YYYY')
    ) {
      const pumpOrderedDateObject = {
        ProjectId: history.ProjectId,
        MemberId: history.MemberId,
        ConcreteRequestId: history.ConcreteRequestId,
        isDeleted: false,
        type: 'edit',
        description: '',
      };
      if (userEditedConcreteRequestData.pumpOrderedDate !== null) {
        pumpOrderedDateObject.description = `${loginUser.firstName} ${loginUser.lastName
          } updated the Pump Ordered Date ${moment(userEditedConcreteRequestData.pumpOrderedDate)
            .utc()
            .format('MM-DD-YYYY')} `;
      } else {
        pumpOrderedDateObject.description = `${loginUser.firstName} ${loginUser.lastName} updated the Pump Ordered Date`;
      }

      ConcreteRequestHistory.createInstance(pumpOrderedDateObject);
    }
    if (userEditedConcreteRequestData.pumpLocation !== existsConcreteRequestData.pumpLocation) {
      const pumpLocationObject = {
        ProjectId: history.ProjectId,
        MemberId: history.MemberId,
        ConcreteRequestId: history.ConcreteRequestId,
        isDeleted: false,
        type: 'edit',
        description: '',
      };
      if (userEditedConcreteRequestData.pumpLocation !== null) {
        pumpLocationObject.description = `${loginUser.firstName} ${loginUser.lastName} updated the Pump Location ${userEditedConcreteRequestData.pumpLocation} `;
      } else {
        pumpLocationObject.description = `${loginUser.firstName} ${loginUser.lastName} updated the Pump Location`;
      }
      ConcreteRequestHistory.createInstance(pumpLocationObject);
    }
    if (
      updatedConcreteRequest.memberDetails.length > 0 &&
      existsConcreteRequestData.memberDetails.length > 0
    ) {
      const addedMember = updatedConcreteRequest.memberDetails.filter((el) => {
        return !existsConcreteRequestData.memberDetails.find((element) => {
          return element.id === el.id;
        });
      });
      const deletedMember = existsConcreteRequestData.memberDetails.filter((el) => {
        return !updatedConcreteRequest.memberDetails.find((element) => {
          return element.id === el.id;
        });
      });
      if (addedMember.length > 0) {
        addedMember.forEach(async (element) => {
          const addedMemberObject = {
            ProjectId: history.ProjectId,
            MemberId: history.MemberId,
            ConcreteRequestId: history.ConcreteRequestId,
            isDeleted: false,
            type: 'edit',
            description: '',
          };
          if (element.Member.User.firstName) {
            addedMemberObject.description = `${loginUser.firstName} ${loginUser.lastName} added the member ${element.Member.User.firstName} ${element.Member.User.lastName} `;
          } else {
            addedMemberObject.description = `${loginUser.firstName} ${loginUser.lastName} added the member`;
          }
          await ConcreteRequestHistory.createInstance(addedMemberObject);
        });
      }
      if (deletedMember.length > 0) {
        deletedMember.forEach(async (element) => {
          const deletedMemberObject = {
            ProjectId: history.ProjectId,
            MemberId: history.MemberId,
            ConcreteRequestId: history.ConcreteRequestId,
            isDeleted: false,
            type: 'edit',
            description: '',
          };
          if (element.Member.User.firstName) {
            deletedMemberObject.description = `${loginUser.firstName} ${loginUser.lastName} deleted the member ${element.Member.User.firstName} ${element.Member.User.lastName} `;
          } else {
            deletedMemberObject.description = `${loginUser.firstName} ${loginUser.lastName} deleted the member`;
          }
          await ConcreteRequestHistory.createInstance(deletedMemberObject);
        });
      }
    }
    if (
      updatedConcreteRequest.concreteSupplierDetails.length > 0 &&
      existsConcreteRequestData.concreteSupplierDetails.length > 0
    ) {
      const addedCompany = updatedConcreteRequest.concreteSupplierDetails.filter((el) => {
        return !existsConcreteRequestData.concreteSupplierDetails.find((element) => {
          return element.id === el.id;
        });
      });
      const deletedCompany = existsConcreteRequestData.concreteSupplierDetails.filter((el) => {
        return !updatedConcreteRequest.concreteSupplierDetails.find((element) => {
          return element.id === el.id;
        });
      });
      if (addedCompany.length > 0) {
        addedCompany.forEach(async (element) => {
          const addedCompanyObject = {
            ProjectId: history.ProjectId,
            MemberId: history.MemberId,
            ConcreteRequestId: history.ConcreteRequestId,
            isDeleted: false,
            type: 'edit',
            description: '',
          };
          addedCompanyObject.description = `${loginUser.firstName} ${loginUser.lastName} added the company ${element.Company.companyName} `;
          await ConcreteRequestHistory.createInstance(addedCompanyObject);
        });
      }
      if (deletedCompany.length > 0) {
        deletedCompany.forEach(async (element) => {
          const deletedCompanyObject = {
            ProjectId: history.ProjectId,
            MemberId: history.MemberId,
            ConcreteRequestId: history.ConcreteRequestId,
            isDeleted: false,
            type: 'edit',
            description: '',
          };
          deletedCompanyObject.description = `${loginUser.firstName} ${loginUser.lastName} deleted the company ${element.Company.companyName} `;
          await ConcreteRequestHistory.createInstance(deletedCompanyObject);
        });
      }
    }
    if (
      updatedConcreteRequest.locationDetails.length > 0 &&
      existsConcreteRequestData.locationDetails.length > 0
    ) {
      const addedLocation = updatedConcreteRequest.locationDetails.filter((el) => {
        return !existsConcreteRequestData.locationDetails.find((element) => {
          return element.id === el.id;
        });
      });
      const deletedLocation = existsConcreteRequestData.locationDetails.filter((el) => {
        return !updatedConcreteRequest.locationDetails.find((element) => {
          return element.id === el.id;
        });
      });
      if (addedLocation.length > 0) {
        addedLocation.forEach(async (element) => {
          const addedLocationObject = {
            ProjectId: history.ProjectId,
            MemberId: history.MemberId,
            ConcreteRequestId: history.ConcreteRequestId,
            isDeleted: false,
            type: 'edit',
            description: '',
          };
          addedLocationObject.description = `${loginUser.firstName} ${loginUser.lastName} added the location details ${element.ConcreteLocation.location} `;
          await ConcreteRequestHistory.createInstance(addedLocationObject);
        });
      }
      if (deletedLocation.length > 0) {
        deletedLocation.forEach(async (element) => {
          const deletedLocationObject = {
            ProjectId: history.ProjectId,
            MemberId: history.MemberId,
            ConcreteRequestId: history.ConcreteRequestId,
            isDeleted: false,
            type: 'edit',
            description: '',
          };
          deletedLocationObject.description = `${loginUser.firstName} ${loginUser.lastName} deleted the location details ${element.ConcreteLocation.location} `;
          await ConcreteRequestHistory.createInstance(deletedLocationObject);
        });
      }
    }
    if (
      updatedConcreteRequest.mixDesignDetails.length > 0 &&
      existsConcreteRequestData.mixDesignDetails.length > 0
    ) {
      const addedMixDesign = updatedConcreteRequest.mixDesignDetails.filter((el) => {
        return !existsConcreteRequestData.mixDesignDetails.find((element) => {
          return element.id === el.id;
        });
      });
      const deletedMixDesign = existsConcreteRequestData.mixDesignDetails.filter((el) => {
        return !updatedConcreteRequest.mixDesignDetails.find((element) => {
          return element.id === el.id;
        });
      });
      if (addedMixDesign.length > 0) {
        addedMixDesign.forEach(async (element) => {
          const addedMixDesignObject = {
            ProjectId: history.ProjectId,
            MemberId: history.MemberId,
            ConcreteRequestId: history.ConcreteRequestId,
            isDeleted: false,
            type: 'edit',
            description: '',
          };
          addedMixDesignObject.description = `${loginUser.firstName} ${loginUser.lastName} added the Mix Design ${element.ConcreteMixDesign.mixDesign} `;
          await ConcreteRequestHistory.createInstance(addedMixDesignObject);
        });
      }
      if (deletedMixDesign.length > 0) {
        deletedMixDesign.forEach(async (element) => {
          const deletedMixDesignObject = {
            ProjectId: history.ProjectId,
            MemberId: history.MemberId,
            ConcreteRequestId: history.ConcreteRequestId,
            isDeleted: false,
            type: 'edit',
            description: '',
          };
          deletedMixDesignObject.description = `${loginUser.firstName} ${loginUser.lastName} deleted the Mix Design ${element.ConcreteMixDesign.mixDesign} `;
          await ConcreteRequestHistory.createInstance(deletedMixDesignObject);
        });
      }
    }
    if (
      updatedConcreteRequest.pumpSizeDetails.length > 0 &&
      existsConcreteRequestData.pumpSizeDetails.length > 0
    ) {
      const addedPumpSize = updatedConcreteRequest.pumpSizeDetails.filter((el) => {
        return !existsConcreteRequestData.pumpSizeDetails.find((element) => {
          return element.id === el.id;
        });
      });
      const deletedPumpSize = existsConcreteRequestData.pumpSizeDetails.filter((el) => {
        return !updatedConcreteRequest.pumpSizeDetails.find((element) => {
          return element.id === el.id;
        });
      });
      if (addedPumpSize.length > 0) {
        addedPumpSize.forEach(async (element) => {
          const addedPumpSizeObject = {
            ProjectId: history.ProjectId,
            MemberId: history.MemberId,
            ConcreteRequestId: history.ConcreteRequestId,
            isDeleted: false,
            type: 'edit',
            description: '',
          };
          addedPumpSizeObject.description = `${loginUser.firstName} ${loginUser.lastName} added the pump size ${element.ConcretePumpSize.pumpSize} `;
          await ConcreteRequestHistory.createInstance(addedPumpSizeObject);
        });
      }
      if (deletedPumpSize.length > 0) {
        deletedPumpSize.forEach(async (element) => {
          const deletedPumpSizeObject = {
            ProjectId: history.ProjectId,
            MemberId: history.MemberId,
            ConcreteRequestId: history.ConcreteRequestId,
            isDeleted: false,
            type: 'edit',
            description: '',
          };
          deletedPumpSizeObject.description = `${loginUser.firstName} ${loginUser.lastName} deleted the pump size ${element.ConcretePumpSize.pumpSize} `;
          await ConcreteRequestHistory.createInstance(deletedPumpSizeObject);
        });
      }
    }
    if (
      (userEditedConcreteRequestData.concreteOrderNumber &&
        userEditedConcreteRequestData.concreteOrderNumber.toLowerCase()) !==
      (existsConcreteRequestData.concreteOrderNumber &&
        existsConcreteRequestData.concreteOrderNumber.toLowerCase())
    ) {
      const concreteOrderNumberObject = {
        ProjectId: history.ProjectId,
        MemberId: history.MemberId,
        ConcreteRequestId: history.ConcreteRequestId,
        isDeleted: false,
        type: 'edit',
        description: '',
      };
      concreteOrderNumberObject.description = `${loginUser.firstName} ${loginUser.lastName} updated the Concrete Order Number ${userEditedConcreteRequestData.concreteOrderNumber} `;
      ConcreteRequestHistory.createInstance(concreteOrderNumberObject);
    }
    if (userEditedConcreteRequestData.slump !== existsConcreteRequestData.slump) {
      const slumpObject = {
        ProjectId: history.ProjectId,
        MemberId: history.MemberId,
        ConcreteRequestId: history.ConcreteRequestId,
        isDeleted: false,
        type: 'edit',
        description: '',
      };
      slumpObject.description = `${loginUser.firstName} ${loginUser.lastName} updated the Slump ${userEditedConcreteRequestData.slump} `;
      ConcreteRequestHistory.createInstance(slumpObject);
    }
    if (
      (userEditedConcreteRequestData.primerForPump &&
        userEditedConcreteRequestData.primerForPump.toLowerCase()) !==
      (existsConcreteRequestData.primerForPump &&
        existsConcreteRequestData.primerForPump.toLowerCase())
    ) {
      const primerForPumpObject = {
        ProjectId: history.ProjectId,
        MemberId: history.MemberId,
        ConcreteRequestId: history.ConcreteRequestId,
        isDeleted: false,
        type: 'edit',
        description: '',
      };
      primerForPumpObject.description = `${loginUser.firstName} ${loginUser.lastName} updated the Primer for Pump ${userEditedConcreteRequestData.primerForPump}`;
      ConcreteRequestHistory.createInstance(primerForPumpObject);
    }
    if (
      userEditedConcreteRequestData.concreteQuantityOrdered !==
      existsConcreteRequestData.concreteQuantityOrdered
    ) {
      const concreteQuantityOrderedObject = {
        ProjectId: history.ProjectId,
        MemberId: history.MemberId,
        ConcreteRequestId: history.ConcreteRequestId,
        isDeleted: false,
        type: 'edit',
        description: '',
      };
      concreteQuantityOrderedObject.description = `${loginUser.firstName} ${loginUser.lastName} updated the Concrete Quantity Ordered ${userEditedConcreteRequestData.concreteQuantityOrdered} `;
      ConcreteRequestHistory.createInstance(concreteQuantityOrderedObject);
    }
    if (
      (userEditedConcreteRequestData.cubicYardsTotal &&
        userEditedConcreteRequestData.cubicYardsTotal.toLowerCase()) !==
      (existsConcreteRequestData.cubicYardsTotal &&
        existsConcreteRequestData.cubicYardsTotal.toLowerCase())
    ) {
      const cubicYardsTotalObject = {
        ProjectId: history.ProjectId,
        MemberId: history.MemberId,
        ConcreteRequestId: history.ConcreteRequestId,
        isDeleted: false,
        type: 'edit',
        description: '',
      };
      cubicYardsTotalObject.description = `${loginUser.firstName} ${loginUser.lastName} updated the Total number of cubic yards placed ${userEditedConcreteRequestData.cubicYardsTotal} `;
      ConcreteRequestHistory.createInstance(cubicYardsTotalObject);
    }
    if (
      (userEditedConcreteRequestData.truckSpacingHours &&
        userEditedConcreteRequestData.truckSpacingHours.toLowerCase()) !==
      (existsConcreteRequestData.truckSpacingHours &&
        existsConcreteRequestData.truckSpacingHours.toLowerCase())
    ) {
      const truckSpacingHoursObject = {
        ProjectId: history.ProjectId,
        MemberId: history.MemberId,
        ConcreteRequestId: history.ConcreteRequestId,
        isDeleted: false,
        type: 'edit',
        description: '',
      };
      truckSpacingHoursObject.description = `${loginUser.firstName} ${loginUser.lastName} updated the Truck spacing hours ${userEditedConcreteRequestData.truckSpacingHours} `;
      ConcreteRequestHistory.createInstance(truckSpacingHoursObject);
    }
    if (userEditedConcreteRequestData.notes) {
      if (existsConcreteRequestData.notes) {
        if (
          existsConcreteRequestData.notes.toLowerCase() !==
          userEditedConcreteRequestData.notes.toLowerCase()
        ) {
          const notesObject = {
            ProjectId: history.ProjectId,
            MemberId: history.MemberId,
            ConcreteRequestId: history.ConcreteRequestId,
            isDeleted: false,
            type: 'edit',
            description: '',
          };
          notesObject.description = `${loginUser.firstName} ${loginUser.lastName} Updated the Notes ${existsConcreteRequestData.notes} `;
          ConcreteRequestHistory.createInstance(notesObject);
        }
      } else {
        {
          const notesObject1 = {
            ProjectId: history.ProjectId,
            MemberId: history.MemberId,
            ConcreteRequestId: history.ConcreteRequestId,
            isDeleted: false,
            type: 'edit',
            description: '',
          };
          notesObject1.description = `${loginUser.firstName} ${loginUser.lastName} Updated the Notes ${existsConcreteRequestData.notes} `;
          ConcreteRequestHistory.createInstance(notesObject1);
        }
      }
    }
    if (!userEditedConcreteRequestData.notes) {
      if (existsConcreteRequestData.notes) {
        const notesObject3 = {
          ProjectId: history.ProjectId,
          MemberId: history.MemberId,
          ConcreteRequestId: history.ConcreteRequestId,
          isDeleted: false,
          type: 'edit',
          description: '',
        };
        notesObject3.description = `${loginUser.firstName} ${loginUser.lastName} Removed the Notes ${existsConcreteRequestData.notes} `;
        ConcreteRequestHistory.createInstance(notesObject3);
      }
    }
    if (
      userEditedConcreteRequestData.hoursToCompletePlacement !==
      existsConcreteRequestData.hoursToCompletePlacement
    ) {
      const hoursToCompletePlacementObject = {
        ProjectId: history.ProjectId,
        MemberId: history.MemberId,
        ConcreteRequestId: history.ConcreteRequestId,
        isDeleted: false,
        type: 'edit',
        description: '',
      };
      hoursToCompletePlacementObject.description = `${loginUser.firstName} ${loginUser.lastName} updated the Hours to complete placement ${userEditedConcreteRequestData.hoursToCompletePlacement} `;
      ConcreteRequestHistory.createInstance(hoursToCompletePlacementObject);
    }
    if (
      userEditedConcreteRequestData.minutesToCompletePlacement !==
      existsConcreteRequestData.minutesToCompletePlacement
    ) {
      const minutesToCompletePlacementObject = {
        ProjectId: history.ProjectId,
        MemberId: history.MemberId,
        ConcreteRequestId: history.ConcreteRequestId,
        isDeleted: false,
        type: 'edit',
        description: '',
      };
      minutesToCompletePlacementObject.description = `${loginUser.firstName} ${loginUser.lastName} updated the Minutes to complete placement ${userEditedConcreteRequestData.minutesToCompletePlacement} `;
      ConcreteRequestHistory.createInstance(minutesToCompletePlacementObject);
    }
  },
  async upcomingRequestList(req) {
    try {
      req.body.ParentCompanyId = req.query.ParentCompanyId;
      await this.getDynamicModel(req);
      const data = req.query;
      const loginUser = req.user;
      const memberDetails = await Member.findOne({
        where: Sequelize.and({
          UserId: loginUser.id,
          ProjectId: data.ProjectId,
          isDeleted: false,
          isActive: true,
        }),
      });
      if (memberDetails) {
        const concreteCondition = {
          isDeleted: false,
        };
        const craneCondition = {
          isDeleted: false,
        };
        const deliveryCondition = {
          isDeleted: false,
        };
        const voidConcrete = [];
        const voidCrane = [];
        const voidDelivery = [];
        const voidConcreteRequestList = await VoidList.findAll({
          where: {
            ProjectId: +data.ProjectId,
            isDeliveryRequest: false,
            ConcreteRequestId: { [Op.ne]: null },
          },
        });
        voidConcreteRequestList.forEach(async (element) => {
          voidConcrete.push(element.ConcreteRequestId);
        });
        concreteCondition['$ConcreteRequest.id$'] = {
          [Op.and]: [{ [Op.notIn]: voidConcrete }],
        };
        const voidCraneList = await VoidList.findAll({
          where: {
            ProjectId: +data.ProjectId,
            isDeliveryRequest: false,
            CraneRequestId: { [Op.ne]: null },
          },
        });
        voidCraneList.forEach(async (element) => {
          voidCrane.push(element.CraneRequestId);
        });
        craneCondition['$CraneRequest.id$'] = {
          [Op.and]: [{ [Op.notIn]: voidCrane }],
        };
        const voidDeliveryRequestList = await VoidList.findAll({
          where: {
            ProjectId: +data.ProjectId,
            isDeliveryRequest: true,
            DeliveryRequestId: { [Op.ne]: null },
          },
        });
        voidDeliveryRequestList.forEach(async (element) => {
          voidDelivery.push(element.DeliveryRequestId);
        });
        deliveryCondition['$DeliveryRequest.id$'] = {
          [Op.and]: [{ [Op.notIn]: voidDelivery }],
        };
        const concreteRequestList = await ConcreteRequest.upcomingConcreteRequest(
          concreteCondition,
          data.ProjectId,
        );
        const craneRequestList = await CraneRequest.upcomingCraneRequest(
          craneCondition,
          data.ProjectId,
        );
        if (craneRequestList && craneRequestList.length > 0) {
          concreteRequestList.push(...craneRequestList);
        }
        const deliveryRequestList = await DeliveryRequest.findAll({
          subQuery: false,
          distinct: true,
          required: false,
          include: [
            {
              association: 'memberDetails',
              where: { isDeleted: false, isActive: true },
              attributes: ['id'],
              include: [
                {
                  association: 'Member',
                  attributes: ['id', 'UserId'],
                  include: [
                    {
                      association: 'User',
                      attributes: ['id', 'email', 'firstName', 'lastName'],
                    },
                  ],
                },
              ],
            },
            {
              association: 'companyDetails',
              required: false,
              where: { isDeleted: false },
              attributes: ['id'],
              include: [
                {
                  association: 'Company',
                  attributes: ['companyName', 'id'],
                },
              ],
            },
          ],
          where: {
            ProjectId: +data.ProjectId,
            isDeleted: false,
            deliveryStart: {
              [Op.gt]: new Date(),
            },
            isQueued: false,
            ...deliveryCondition,
          },
        });
        if (deliveryRequestList.length > 0) {
          concreteRequestList.push(...deliveryRequestList);
        }
        let newArray = [];
        concreteRequestList.map(async (element) => {
          let object1 = {};
          object1 = element;
          if (
            object1.requestType === 'deliveryRequest' ||
            object1.requestType === 'deliveryRequestWithCrane'
          ) {
            object1.newDate = element.deliveryStart;
          }
          if (object1.requestType === 'craneRequest') {
            object1.newDate = element.craneDeliveryStart;
          }
          if (object1.requestType === 'concreteRequest') {
            object1.newDate = element.concretePlacementStart;
          }
          newArray.push(object1);
        });
        if (newArray.length > 0) {
          newArray.sort((a, b) => {
            return new Date(a.newDate) - new Date(b.newDate);
          });
        }
        if (data.limit) {
          newArray = newArray.slice(0, +data.limit);
        } else {
          newArray = newArray.slice(0, 10);
        }
        return { status: 200, data: newArray };
      }
      return { status: 422, msg: 'Project Id/ParendCompany Id/Member does not exist' };
    } catch (e) {
      console.log(e);
      return e;
    }
  },
  async getMemberDetailData(data, memberLocationPreference) {
    const emailArray = [];
    const existAdminData = [];
    if (data.memberData !== undefined) {
      data.memberData.forEach((element) => {
        const index = existAdminData.findIndex(
          (adminNew) => adminNew.email === element.Member.User.email,
        );
        if (index === -1) {
          existAdminData.push({ email: element.Member.User.email });
          emailArray.push({
            email: element.Member.User.email,
            firstName: element.Member.User.firstName,
            lastName: element.Member.User.lastName,
            UserId: element.Member.User.id,
            MemberId: element.Member.id,
          });
        }
      });
    }
    if (data.adminData !== undefined) {
      data.adminData.forEach((element) => {
        const index = existAdminData.findIndex((adminNew) => adminNew.email === element.User.email);
        if (index === -1) {
          existAdminData.push({ email: element.User.email });
          emailArray.push({
            email: element.User.email,
            firstName: element.User.firstName,
            lastName: element.User.lastName,
            UserId: element.User.id,
            MemberId: element.id,
          });
        }
      });
    }
    if (memberLocationPreference !== undefined && memberLocationPreference.length > 0) {
      memberLocationPreference.forEach((element) => {
        const index = existAdminData.findIndex(
          (adminNew) => adminNew.email === element.Member.User.email,
        );
        if (index === -1) {
          existAdminData.push({ email: element.Member.User.email });
          emailArray.push({
            email: element.Member.User.email,
            firstName: element.Member.User.firstName,
            lastName: element.Member.User.lastName,
            UserId: element.Member.User.id,
            MemberId: element.Member.id,
            RoleId: element.Member.RoleId,
          });
        }
      });
    }
    return emailArray;
  },
  // prettier-ignore
  async createDailyDigestData(
    RoleId,
    MemberId,
    ProjectId,
    ParentCompanyId,
    loginUser,
    dailyDigestMessage,
    requestType,
    messages,
    requestId,
  ) {
    const cryptr = new Cryptr('a0b1c2d3e4f5g6h7i8j9k10');
    const encryptedRequestId = cryptr.encrypt(requestId);
    const encryptedMemberId = cryptr.encrypt(MemberId);
    let imageUrl;
    let link;
    let height;
    if (requestType === 'Delivery Request') {
      imageUrl = 'https://d36hblf01wyurt.cloudfront.net/87758081-8ccd-4e4d-a4eb-6257466876da.png';
      link = 'delivery-request';
      height = 'height:18px;';
    }
    if (requestType === 'Crane Request') {
      imageUrl = 'https://d36hblf01wyurt.cloudfront.net/ab400721-520c-4f6f-941c-ac07acc28809.png';
      link = 'crane-request';
      height = 'height:32px;';
    }
    if (requestType === 'Concrete Request') {
      imageUrl = 'https://d36hblf01wyurt.cloudfront.net/c43e4c58-4c4d-44ff-b78c-fe9948ea53eb.png';
      link = 'concrete-request';
      height = 'height:18px;';
    }
    const object = {
      description: `
      <div>
      <ul style="list-style-type:none;padding:0px;border-bottom:1px dashed #E3E3E3;">
        <li style="display:flex;">
          <img src="${imageUrl}" alt="message-icon" style="${height}">
            <p style="margin:0px;font-size:12px;padding-left:10px;">
              <a href="#" target="" style="text-decoration: none;color:#4470FF;">
               ${loginUser.firstName}  ${loginUser.lastName}
              </a>
              ${dailyDigestMessage}
             	<a href="${process.env.BASE_URL}/${link}?requestId=${encryptedRequestId}&memberId=${encryptedMemberId}" style="text-decoration: none;color:#4470FF;" >${messages}</a>
              <span style="color:#707070;">on ${moment().utc().format('MMMM DD')} at ${moment()
          .utc()
          .format('hh:mm A zz')}</span>
            </p>
                
              </li>
      </ul>
</div> `,
      MemberId,
      ProjectId,
      isSent: false,
      isDeleted: false,
      ParentCompanyId,
    };
    await DigestNotification.create(object);
  },
  async getConcreteDropdownDetail(req) {
    const { ProjectId } = req.query;
    const locationDetailsDropdown = await ConcreteLocation.findAll({
      where: { ProjectId, isDeleted: false },
    });
    const mixDesignDropdown = await ConcreteMixDesign.findAll({
      where: { ProjectId, isDeleted: false },
    });
    const pumpSizeDropdown = await ConcretePumpSize.findAll({
      where: { ProjectId, isDeleted: false },
    });
    await this.getDynamicModel(req);
    const parentCompany = await Company.findOne({
      required: false,
      subQuery: false,
      attributes: [
        'id',
        'companyName',
        'website',
        'address',
        'secondAddress',
        'country',
        'city',
        'companyAutoId',
        'state',
        'zipCode',
        'scope',
        'logo',
      ],
      where: { isParent: true, ParentCompanyId: +req.query.ParentCompanyId, isDeleted: false },
    });
    const companyList = await Company.getAllCompany({
      ProjectId,
      isDeleted: false,
      isParent: { [Op.not]: true },
    });
    const newCompanyList = [];
    await companyList.rows.forEach((element) => {
      newCompanyList.push({
        id: element.id,
        companyName: element.companyName,
      });
    });
    if (parentCompany) {
      const index = newCompanyList.findIndex(
        (item) =>
          item.id === parentCompany.id ||
          item.companyName.toLowerCase() === parentCompany.companyName.toLowerCase(),
      );
      if (index === -1) {
        newCompanyList.push({
          id: parentCompany.id,
          companyName: parentCompany.companyName,
        });
      }
    }
    newCompanyList.sort((a, b) =>
      a.companyName.toLowerCase() > b.companyName.toLowerCase() ? 1 : -1,
    );
    const condition = {
      ProjectId,
      isDeleted: false,
      isActive: true,
    };
    const locationDropdown = await Locations.getLocations(condition);

    let data;
    let lastData = {};
    lastData = await ConcreteRequest.findOne({
      where: { ProjectId, isDeleted: false },
      order: [['ConcreteRequestId', 'DESC']],
    });
    if (lastData) {
      data = lastData.ConcreteRequestId + 1;
    } else {
      data = 1;
    }
    const dropdownData = {
      locationDetailsDropdown,
      locationDropdown,
      concreteSupplierDropdown: newCompanyList,
      mixDesignDropdown,
      pumpSizeDropdown,
      ConcreteRequestId: data,
    };
    return { status: 200, data: dropdownData };
  },
  async deleteConcreteRequest(req, done) {
    try {
      await this.getDynamicModel(req);
      const deleteValue = await ConcreteRequest.update(
        {
          isDeleted: true,
        },
        {
          where: {
            id: req.body.id,
            ProjectId: req.body.ProjectId,
          },
        },
      );
      done(deleteValue, false);
    } catch (e) {
      done(null, e);
    }
  },
  async sendEmailNotificationToUser(
    history,
    memberDetails,
    loginUser,
    newDeliverData,
    deliveryData,
    memberLocationPreference,
  ) {
    const userEmails = await this.getMemberDetailData(history, memberLocationPreference);
    if (userEmails.length > 0) {
      userEmails.forEach(async (element) => {
        let name;
        if (!element.firstName) {
          name = 'user';
        } else {
          name = `${element.firstName} ${element.lastName} `;
        }
        if (+element.MemberId !== +memberDetails.id) {
          const memberRole = await Role.findOne({
            where: {
              id: memberDetails.RoleId,
              isDeleted: false,
            },
          });
          const time = moment(newDeliverData.concretePlacementStart).format('MM-DD-YYYY');
          const mailPayload = {
            userName: name,
            email: element.email,
            concreteId: newDeliverData.ConcreteRequestId,
            description: newDeliverData.description,
            timestamp: time,
            createdTimestamp: moment().utc().format('MM-DD-YYYY hh:mm:ss a zz'),
            content: ` ${loginUser.firstName} ${loginUser.lastName} - ${memberRole.roleName} has created the concrete booking ${newDeliverData.ConcreteRequestId}.Please see below for more details`,
          };
          const isMemberFollowLocation = await LocationNotificationPreferences.findOne({
            where: {
              MemberId: +element.MemberId,
              ProjectId: +deliveryData.ProjectId,
              LocationId: +deliveryData.LocationId,
              isDeleted: false,
              // follow: true,
            },
          });
          if (isMemberFollowLocation) {
            const memberNotification = await NotificationPreference.findOne({
              where: {
                MemberId: +element.MemberId,
                ProjectId: +deliveryData.ProjectId,
                isDeleted: false,
              },
              include: [
                {
                  association: 'NotificationPreferenceItem',
                  where: {
                    id: 12,
                    isDeleted: false,
                  },
                },
              ],
            });
            if (memberNotification && memberNotification.instant) {
              await MAILER.sendMail(
                mailPayload,
                'concreteRequestCreated',
                `Concrete Booking created by ${loginUser.firstName} ${loginUser.lastName} - ${memberRole.roleName} `,
                `Concrete Booking created by ${loginUser.firstName} ${loginUser.lastName} - ${memberRole.roleName} `,
                async (info, err) => {
                  console.log(info, err);
                },
              );
            }
            if (memberNotification && memberNotification.dailyDigest) {
              await this.createDailyDigestData(
                +memberDetails.RoleId,
                +element.MemberId,
                +deliveryData.ProjectId,
                +deliveryData.ParentCompanyId,
                loginUser,
                'created a',
                'Concrete Request',
                `concrete Booking (${newDeliverData.ConcreteRequestId} - ${newDeliverData.description})`,
                newDeliverData.ConcreteRequestId,
              );
            }
          }
        }
      });
    }
    return true;
  },
  async editMultipleDeliveryRequest(req) {
    try {
      const payload = req.body;
      if (payload.concreteRequestIds && payload.concreteRequestIds.length > 0) {
        await this.getDynamicModel(req);
        const loginUser = req.user;
        let history = {};
        let memberData = {};
        let addedPerson = [];
        for (let mainIndex = 1; mainIndex <= payload.concreteRequestIds.length; mainIndex += 1) {
          const concreteRequestId = payload.concreteRequestIds[mainIndex];
          if (concreteRequestId) {
            let geConcreteRequestDetail = {};
            geConcreteRequestDetail = await ConcreteRequest.findOne({
              where: [
                Sequelize.and({
                  id: concreteRequestId,
                }),
              ],
            });
            if (geConcreteRequestDetail) {
              const condition = Sequelize.and({
                ProjectId: payload.ProjectId,
                ConcreteRequestId: concreteRequestId,
              });
              const updateParam = {
                ConcreteRequestId: geConcreteRequestDetail.id,
                ConcreteRequestCode: geConcreteRequestDetail.ConcreteRequestId,
                ProjectId: geConcreteRequestDetail.ProjectId,
                isDeleted: false,
              };
              memberData = await Member.getBy({
                UserId: loginUser.id,
                ProjectId: payload.ProjectId,
              });
              history = {
                ConcreteRequestId: concreteRequestId,
                MemberId: memberData.id,
                type: 'edit',
                description: `${loginUser.firstName} ${loginUser.lastName} Edited this Concrete Booking.`,
              };
              if (payload.companies && payload.companies.length > 0) {
                const addedCompany = [];
                const existCompanies = await ConcreteRequestCompany.findAll({ where: condition });
                const deletedCompany = existCompanies.filter((e) => {
                  return payload.companies.indexOf(e.CompanyId) === -1 && e.isDeleted === false;
                });
                await ConcreteRequestCompany.update({ isDeleted: true }, { where: condition });
                payload.companies.forEach(async (element, companyIndexValue) => {
                  const index = existCompanies.findIndex((item) => item.CompanyId === element);
                  const companyParam = updateParam;
                  companyParam.CompanyId = element;
                  if (index !== -1) {
                    await ConcreteRequestCompany.update(companyParam, {
                      where: { id: existCompanies[index].id },
                    });
                    if (existCompanies[index].isDeleted !== false) {
                      addedCompany.push(existCompanies[index]);
                    }
                  } else {
                    const newCompanyData =
                      await ConcreteRequestCompany.createInstance(companyParam);
                    addedCompany.push(newCompanyData);
                  }
                  if (companyIndexValue === payload.companies.length - 1) {
                    this.updateCompanyHistory(addedCompany, deletedCompany, history, loginUser);
                  }
                });
              }
              if (payload.persons && payload.persons.length > 0) {
                addedPerson = [];
                const existPerson = await ConcreteRequestResponsiblePerson.findAll({
                  where: condition,
                });
                const deletedPerson = existPerson.filter((e) => {
                  return payload.persons.indexOf(e.MemberId) === -1 && e.isDeleted === false;
                });
                await ConcreteRequestResponsiblePerson.update(
                  { isDeleted: true },
                  { where: condition },
                );
                payload.persons.forEach(async (element, personIndexValue) => {
                  const index = existPerson.findIndex((item) => item.MemberId === element);
                  const memberParam = updateParam;
                  memberParam.MemberId = element;
                  if (index !== -1) {
                    await ConcreteRequestResponsiblePerson.update(memberParam, {
                      where: { id: existPerson[index].id },
                    });
                    if (existPerson[index].isDeleted !== false) {
                      addedPerson.push(existPerson[index]);
                    }
                  } else {
                    const newPersonData =
                      await ConcreteRequestResponsiblePerson.createInstance(memberParam);
                    addedPerson.push(newPersonData);
                  }
                  if (personIndexValue === payload.persons.length - 1) {
                    this.updatePersonHistory(addedPerson, deletedPerson, history, loginUser);
                  }
                });
              }
              if (payload.deliveryStart && payload.deliveryEnd) {
                const startDate = new Date(payload.deliveryStart).getTime();
                const currentDate = new Date().getTime();
                const endDate = new Date(payload.deliveryEnd).getTime();
                if (startDate > currentDate && endDate > currentDate) {
                  const DeliverParam = {
                    status: payload.status ? payload.status : 'Pending',
                    concretePlacementStart: payload.deliveryStart,
                    concretePlacementEnd: payload.deliveryEnd,
                  };
                  await ConcreteRequest.update(DeliverParam, {
                    where: { id: concreteRequestId },
                  });
                }
              }
              if (payload.isConcreteConfirmed) {
                const DeliverParam = {
                  status: payload.status ? payload.status : 'Pending',
                  isConcreteConfirmed: payload.isConcreteConfirmed,
                  concreteConfirmedOn: payload.concreteConfirmedOn,
                };
                await ConcreteRequest.update(DeliverParam, {
                  where: { id: concreteRequestId },
                });
              }
              if (payload.isPumpConfirmed) {
                const DeliverParam = {
                  status: payload.status ? payload.status : 'Pending',
                  isPumpConfirmed: payload.isPumpConfirmed,
                  pumpConfirmedOn: payload.pumpConfirmedOn,
                };
                await ConcreteRequest.update(DeliverParam, {
                  where: { id: concreteRequestId },
                });
              }
              if (payload.void === true) {
                // eslint-disable-next-line no-await-in-loop
                const existVoid = await VoidList.findOne({
                  where: Sequelize.and({
                    ConcreteRequestId: concreteRequestId,
                  }),
                });
                if (!existVoid) {
                  const voidcreate = {
                    ConcreteRequestId: concreteRequestId,
                    ProjectId: payload.ProjectId,
                    ParentCompanyId: payload.ParentCompanyId,
                  };
                  await VoidList.createInstance(voidcreate);
                  await voidService.concreteRequestVoidHistory(existVoid, memberData, loginUser);
                }
              }
              if (payload.location) {
                const existlocations = await ConcreteRequestLocation.findAll({
                  where: condition,
                });
                payload.location.forEach(async (element, i) => {
                  const index = existlocations.findIndex(
                    (item) => item.ConcreteLocationId === element.id,
                  );
                  const locationParam = updateParam;
                  locationParam.ConcreteLocationId = element.id;
                  if (index !== -1) {
                    await ConcreteRequestLocation.update(locationParam, {
                      where: { id: existlocations[index].id },
                    });
                  } else {
                    if (element.chosenFromDropdown) {
                      locationParam.ConcreteLocationId = element.id;
                    } else {
                      const object = {
                        location: element.location,
                        ProjectId: payload.ProjectId,
                        isDeleted: false,
                        createdBy: loginUser.id,
                      };
                      const locationData = await ConcreteLocation.createConcreteLocation(object);
                      locationParam.ConcreteLocationId = locationData.id;
                    }
                    await ConcreteRequestLocation.createInstance(locationParam);
                  }
                });
              }
              if (payload.status) {
                if (
                  (memberData.RoleId === 2 || memberData.RoleId === 1) &&
                  geConcreteRequestDetail.status === 'Approved'
                ) {
                  const DeliverParam = {};
                  DeliverParam.status = payload.status ? payload.status : 'Approved';
                  DeliverParam.approvedBy = memberData.id;
                  DeliverParam.approved_at = new Date();
                  await ConcreteRequest.update(DeliverParam, {
                    where: { id: concreteRequestId },
                  });
                }
              }
            }
          }
          if (mainIndex === payload.concreteRequestIds.length) {
            return { message: 'Success.!' };
          }
        }
      } else {
        return { message: 'Please select Delivery booking to update.!' };
      }
    } catch (e) {
      return e;
    }
  },
  async updateCompanyHistory(addedCompany, deletedCompany, history, loginUser) {
    const newHistory = history;
    let addDesc = `${loginUser.firstName} ${loginUser.lastName} Added the Company`;
    let deleteDesc = `${loginUser.firstName} ${loginUser.lastName} Deleted the Company`;
    addedCompany.forEach(async (element, i) => {
      const newCompanyData = await Company.findOne({
        where: { id: element.CompanyId },
      });
      if (i === 0) {
        if (i === addedCompany.length - 1) {
          addDesc += ` ${newCompanyData.companyName}`;
          newHistory.description = addDesc;
          ConcreteRequestHistory.createInstance(newHistory);
        } else {
          addDesc += ` ${newCompanyData.companyName}`;
        }
      } else if (i === addedCompany.length - 1) {
        addDesc += `,${newCompanyData.companyName}`;
        newHistory.description = addDesc;
        ConcreteRequestHistory.createInstance(newHistory);
      } else {
        addDesc += `,${newCompanyData.companyName}`;
      }
    });
    deletedCompany.forEach(async (element, i) => {
      const newCompanyData = await Company.findOne({
        where: { id: element.CompanyId },
      });
      if (i === 0) {
        if (i === deletedCompany.length - 1) {
          deleteDesc += ` ${newCompanyData.companyName}`;
          newHistory.description = deleteDesc;
          ConcreteRequestHistory.createInstance(newHistory);
        } else {
          deleteDesc += ` ${newCompanyData.companyName}`;
        }
      } else if (i === deletedCompany.length - 1) {
        deleteDesc += `,${newCompanyData.companyName}`;
        newHistory.description = deleteDesc;
        ConcreteRequestHistory.createInstance(newHistory);
      } else {
        deleteDesc += `,${newCompanyData.companyName}`;
      }
    });
  },
  async updatePersonHistory(addedPerson, deletedPerson, history, loginUser) {
    const newHistory = history;
    let addDesc = `${loginUser.firstName} ${loginUser.lastName} Added the Member`;
    let deleteDesc = `${loginUser.firstName} ${loginUser.lastName} Deleted the Member`;
    addedPerson.forEach(async (element, i) => {
      const newMemberData = await Member.findOne({
        where: { id: element.MemberId, isDeleted: false },
        include: [
          {
            association: 'User',
            attributes: ['firstName', 'lastName'],
          },
        ],
      });
      if (i === 0) {
        if (i === addedPerson.length - 1) {
          if (newMemberData.User.firstName) {
            addDesc += ` ${newMemberData.User.firstName} ${newMemberData.User.lastName}`;
          }
          newHistory.description = addDesc;
          ConcreteRequestHistory.createInstance(newHistory);
        } else if (newMemberData.User.firstName) {
          addDesc += ` ${newMemberData.User.firstName} ${newMemberData.User.lastName}`;
        }
      } else if (i === addedPerson.length - 1) {
        if (newMemberData.User.firstName) {
          addDesc += `,${newMemberData.User.firstName} ${newMemberData.User.lastName}`;
        }
        newHistory.description = addDesc;
        ConcreteRequestHistory.createInstance(newHistory);
      } else if (newMemberData.User.firstName) {
        addDesc += `,${newMemberData.User.firstName} ${newMemberData.User.lastName}`;
      }
    });
    deletedPerson.forEach(async (element, i) => {
      const newMemberData = await Member.findOne({
        where: { id: element.MemberId, isDeleted: false },
        include: [
          {
            association: 'User',
            attributes: ['firstName', 'lastName'],
          },
        ],
      });
      if (i === 0) {
        if (i === deletedPerson.length - 1) {
          if (newMemberData.User.firstName) {
            deleteDesc += ` ${newMemberData.User.firstName} ${newMemberData.User.lastName}`;
          }
          newHistory.description = deleteDesc;
          ConcreteRequestHistory.createInstance(newHistory);
        } else if (newMemberData.User.firstName) {
          deleteDesc += ` ${newMemberData.User.firstName} ${newMemberData.User.lastName}`;
        }
      } else if (i === deletedPerson.length - 1) {
        if (newMemberData.User.firstName) {
          deleteDesc += `,${newMemberData.User.firstName} ${newMemberData.User.lastName}`;
        }
        newHistory.description = deleteDesc;
        ConcreteRequestHistory.createInstance(newHistory);
      } else if (newMemberData.User.firstName) {
        deleteDesc += `,${newMemberData.User.firstName} ${newMemberData.User.lastName}`;
      }
    });
  },
  async createCopyofConcreteRequest(dataInSeries, payload, dates, loginUser, newRecurrenceId) {
    const eventsArray = [];
    const memberDetails = await Member.getBy({
      UserId: loginUser.id,
      ProjectId: payload.ProjectId,
      isActive: true,
      isDeleted: false,
    });
    const projectDetails = await Project.getProjectAndSettings({
      isDeleted: false,
      id: +payload.ProjectId,
    });
    if (dataInSeries && dataInSeries.recurrence && dataInSeries.recurrence.recurrence) {
      let startDate;
      let endDate;
      let pumpStartDate;
      let pumpEndDate;
      if (payload.recurrence && dates && dates.length > 0) {
        startDate = await this.compareDeliveryDateWithDeliveryWindowDate(
          dates[0],
          payload.deliveryStartTime,
          payload.timezone,
          projectDetails.ProjectSettings.deliveryWindowTime,
          projectDetails.ProjectSettings.deliveryWindowTimeUnit,
        );
        endDate = await this.compareDeliveryDateWithDeliveryWindowDate(
          dates[0],
          payload.deliveryEndTime,
          payload.timezone,
          projectDetails.ProjectSettings.deliveryWindowTime,
          projectDetails.ProjectSettings.deliveryWindowTimeUnit,
        );
        pumpStartDate = await this.compareDeliveryDateWithDeliveryWindowDate(
          dataInSeries.pumpOrderedDate,
          dataInSeries.pumpWorkStart,
          payload.timezone,
          projectDetails.ProjectSettings.deliveryWindowTime,
          projectDetails.ProjectSettings.deliveryWindowTimeUnit,
        );
        pumpEndDate = await this.compareDeliveryDateWithDeliveryWindowDate(
          dataInSeries.pumpOrderedDate,
          dataInSeries.pumpWorkEnd,
          payload.timezone,
          projectDetails.ProjectSettings.deliveryWindowTime,
          projectDetails.ProjectSettings.deliveryWindowTimeUnit,
        );
      }
      if (+memberDetails.RoleId === 4 && (startDate || endDate || pumpStartDate || pumpEndDate)) {
        throw new Error(
          `Bookings can not be submitted within ${projectDetails.ProjectSettings.deliveryWindowTime} ${projectDetails.ProjectSettings.deliveryWindowTimeUnit} prior to the event`,
        );
      }
      let concreteRequestParam = {};
      let lastData = {};
      lastData = await ConcreteRequest.findOne({
        where: { ProjectId: +memberDetails.ProjectId, isDeleted: false },
        order: [['ConcreteRequestId', 'DESC']],
      });
      if (lastData) {
        const data = lastData.ConcreteRequestId;
        lastData.ConcreteRequestId = 0;
        lastData.ConcreteRequestId = data + 1;
      } else {
        lastData = {};
        lastData.ConcreteRequestId = 1;
      }
      let id = 0;
      const newValue = JSON.parse(JSON.stringify(lastData));
      if (
        newValue &&
        newValue.ConcreteRequestId !== null &&
        newValue.ConcreteRequestId !== undefined
      ) {
        id = newValue.ConcreteRequestId;
      }
      const roleDetails = await Role.getBy('Project Admin');
      const accountRoleDetails = await Role.getBy('Account Admin');
      if (dataInSeries.recurrence.recurrence === 'Daily') {
        let dailyIndex = 0;
        while (dailyIndex < dates.length) {
          const data = dates[dailyIndex];
          if (
            moment(data).isBetween(moment(dates[0]), moment(dates[dates.length - 1]), null, '[]') ||
            moment(data).isSame(dates[0]) ||
            moment(data).isSame(dates[dates.length - 1])
          ) {
            id += 1;
            concreteRequestParam = {
              description: payload.description,
              ProjectId: payload.ProjectId,
              notes: payload.notes,
              isEscortNeeded: payload.isEscortNeeded,
              additionalNotes: payload.additionalNotes,
              CraneRequestId: id,
              concretePlacementStart: await this.convertTimezoneToUtc(
                data,
                payload.timezone,
                payload.deliveryStartTime,
              ),
              concretePlacementEnd: await this.convertTimezoneToUtc(
                data,
                payload.timezone,
                payload.deliveryEndTime,
              ),
              isPumpConfirmed: payload.isPumpConfirmed,
              isPumpRequired: payload.isPumpRequired,
              isConcreteConfirmed: payload.isConcreteConfirmed,
              ParentCompanyId: payload.ParentCompanyId,
              concreteOrderNumber: payload.concreteOrderNumber,
              truckSpacingHours: payload.truckSpacingHours,
              slump: payload.slump,
              concreteQuantityOrdered: payload.concreteQuantityOrdered,
              concreteConfirmedOn: payload.concreteConfirmedOn ? payload.concreteConfirmedOn : null,
              pumpLocation: payload.pumpLocation,

              pumpOrderedDate:
                payload.pumpOrderedDate && payload.pumpWorkStart ? payload.pumpOrderedDate : null,
              pumpWorkStart:
                payload.pumpOrderedDate && payload.pumpWorkStart ? payload.pumpWorkStart : null,
              pumpWorkEnd:
                payload.pumpOrderedDate && payload.pumpWorkEnd ? payload.pumpWorkEnd : null,
              pumpConfirmedOn: payload.pumpConfirmedOn ? payload.pumpConfirmedOn : null,
              cubicYardsTotal: payload.cubicYardsTotal,
              hoursToCompletePlacement: payload.hoursToCompletePlacement,
              minutesToCompletePlacement: payload.minutesToCompletePlacement,
              ConcreteRequestId: id,
              requestType: 'concreteRequest',
              status: 'Tentative',
              primerForPump: payload.primerForPump,
              createdBy: memberDetails.id,
              recurrenceId: newRecurrenceId,
            };
            if (
              memberDetails.RoleId === roleDetails.id ||
              memberDetails.RoleId === accountRoleDetails.id ||
              memberDetails.isAutoApproveEnabled ||
              projectDetails.ProjectSettings.isAutoApprovalEnabled
            ) {
              concreteRequestParam.status = 'Approved';
              concreteRequestParam.approvedBy = memberDetails.id;
              concreteRequestParam.approved_at = new Date();
            }
            eventsArray.push(concreteRequestParam);
            // eslint-disable-next-line no-const-assign
            dailyIndex += +dataInSeries.recurrence.repeatEveryCount;
          }
        }
      }
      if (dataInSeries.recurrence.recurrence === 'Weekly') {
        const startDayWeek = moment(dates[0]).startOf('week');
        const endDayWeek = moment(dates[dates.length - 1]).endOf('week');
        const range1 = momentRange.range(moment(startDayWeek), moment(endDayWeek));
        const totalDaysOfRecurrence = Array.from(range1.by('day'));
        let count;
        let weekIncrement;
        if (+dataInSeries.recurrence.repeatEveryCount > 1) {
          count = +dataInSeries.recurrence.repeatEveryCount - 1;
          weekIncrement = 7;
        } else {
          count = 1;
          weekIncrement = 0;
        }
        for (
          let indexba = 0;
          indexba < totalDaysOfRecurrence.length;
          indexba += weekIncrement * count
        ) {
          const totalLength = indexba + 6;
          for (let indexb = indexba; indexb <= totalLength; indexb += 1) {
            const data = totalDaysOfRecurrence[indexb];
            indexba += 1;
            if (
              data &&
              !moment(data).isBefore(dates[0]) &&
              !moment(data).isAfter(dates[dates.length - 1])
            ) {
              const day = moment(data).format('dddd');
              const indexVal = dataInSeries.recurrence.days.includes(day);
              if (indexVal) {
                id += 1;
                const date = moment(`${data}`).format('MM/DD/YYYY');
                concreteRequestParam = {
                  description: payload.description,
                  ProjectId: payload.ProjectId,
                  notes: payload.notes,
                  isEscortNeeded: payload.isEscortNeeded,
                  additionalNotes: payload.additionalNotes,
                  CraneRequestId: id,
                  concretePlacementStart: await this.convertTimezoneToUtc(
                    date,
                    payload.timezone,
                    payload.deliveryStartTime,
                  ),
                  concretePlacementEnd: await this.convertTimezoneToUtc(
                    date,
                    payload.timezone,
                    payload.deliveryEndTime,
                  ),
                  isPumpConfirmed: payload.isPumpConfirmed,
                  isPumpRequired: payload.isPumpRequired,
                  isConcreteConfirmed: payload.isConcreteConfirmed,
                  ParentCompanyId: payload.ParentCompanyId,
                  concreteOrderNumber: payload.concreteOrderNumber,
                  truckSpacingHours: payload.truckSpacingHours,
                  slump: payload.slump,
                  concreteQuantityOrdered: payload.concreteQuantityOrdered,
                  concreteConfirmedOn: payload.concreteConfirmedOn
                    ? payload.concreteConfirmedOn
                    : null,
                  pumpLocation: payload.pumpLocation,

                  pumpOrderedDate:
                    payload.pumpOrderedDate && payload.pumpWorkStart
                      ? payload.pumpOrderedDate
                      : null,
                  pumpWorkStart:
                    payload.pumpOrderedDate && payload.pumpWorkStart ? payload.pumpWorkStart : null,
                  pumpWorkEnd:
                    payload.pumpOrderedDate && payload.pumpWorkEnd ? payload.pumpWorkEnd : null,
                  pumpConfirmedOn: payload.pumpConfirmedOn ? payload.pumpConfirmedOn : null,
                  cubicYardsTotal: payload.cubicYardsTotal,
                  hoursToCompletePlacement: payload.hoursToCompletePlacement,
                  minutesToCompletePlacement: payload.minutesToCompletePlacement,
                  ConcreteRequestId: id,
                  requestType: 'concreteRequest',
                  status: 'Tentative',
                  primerForPump: payload.primerForPump,
                  createdBy: memberDetails.id,
                  recurrenceId: newRecurrenceId,
                };
                if (
                  memberDetails.RoleId === roleDetails.id ||
                  memberDetails.RoleId === accountRoleDetails.id ||
                  memberDetails.isAutoApproveEnabled ||
                  projectDetails.ProjectSettings.isAutoApprovalEnabled
                ) {
                  concreteRequestParam.status = 'Approved';
                  concreteRequestParam.approvedBy = memberDetails.id;
                  concreteRequestParam.approved_at = new Date();
                }
                eventsArray.push(concreteRequestParam);
              }
            }
          }
        }
      }
      if (dataInSeries.recurrence.recurrence === 'Monthly') {
        const startMonth = moment(dates[0]).startOf('month');
        const startMonthNumber = moment(startMonth).format('MM');
        const endMonth = moment(dates[dates.length - 1]).endOf('month');
        const endMonthNumber = moment(endMonth).format('MM');
        let startDate1 = moment(dates[0]);
        const endDate1 = moment(dates[dates.length - 1]).endOf('month');
        const allMonthsInPeriod = [];
        while (startDate1.isBefore(endDate1)) {
          allMonthsInPeriod.push(startDate1.format('YYYY-MM'));
          startDate1 = startDate1.add(1, 'month');
        }
        let currentMonthDates = [];
        let totalNumberOfMonths = endMonthNumber - startMonthNumber;
        if (totalNumberOfMonths < 0) {
          totalNumberOfMonths *= -1;
        }
        let k = 0;
        while (k < allMonthsInPeriod.length + 1) {
          currentMonthDates = Array.from(
            { length: moment(allMonthsInPeriod[k], 'YYYY-MM').daysInMonth() },
            (x, j) => moment(allMonthsInPeriod[k], 'YYYY-MM').startOf('month').add(j, 'days'),
          );

          if (dataInSeries.recurrence.chosenDateOfMonth) {
            const getDate = currentMonthDates.filter(
              (value) => moment(value).format('DD') === dataInSeries.recurrence.dateOfMonth,
            );
            if (getDate.length === 1) {
              if (
                moment(getDate[0]).isBetween(
                  moment(dates[0]),
                  moment(dates[dates.length - 1]),
                  null,
                  '[]',
                ) ||
                moment(getDate[0]).isSame(dates[0]) ||
                moment(getDate[0]).isSame(dates[dates.length - 1])
              ) {
                id += 1;
                const date = moment(getDate[0].toDate()).format('MM/DD/YYYY');
                concreteRequestParam = {
                  description: payload.description,
                  ProjectId: payload.ProjectId,
                  notes: payload.notes,
                  isEscortNeeded: payload.isEscortNeeded,
                  additionalNotes: payload.additionalNotes,
                  CraneRequestId: id,
                  concretePlacementStart: await this.convertTimezoneToUtc(
                    date,
                    payload.timezone,
                    payload.deliveryStartTime,
                  ),
                  concretePlacementEnd: await this.convertTimezoneToUtc(
                    date,
                    payload.timezone,
                    payload.deliveryEndTime,
                  ),
                  isPumpConfirmed: payload.isPumpConfirmed,
                  isPumpRequired: payload.isPumpRequired,
                  isConcreteConfirmed: payload.isConcreteConfirmed,
                  ParentCompanyId: payload.ParentCompanyId,
                  concreteOrderNumber: payload.concreteOrderNumber,
                  truckSpacingHours: payload.truckSpacingHours,
                  slump: payload.slump,
                  concreteQuantityOrdered: payload.concreteQuantityOrdered,
                  concreteConfirmedOn: payload.concreteConfirmedOn
                    ? payload.concreteConfirmedOn
                    : null,
                  pumpLocation: payload.pumpLocation,
                  pumpOrderedDate:
                    payload.pumpOrderedDate && payload.pumpWorkStart
                      ? payload.pumpOrderedDate
                      : null,
                  pumpWorkStart:
                    payload.pumpOrderedDate && payload.pumpWorkStart ? payload.pumpWorkStart : null,
                  pumpWorkEnd:
                    payload.pumpOrderedDate && payload.pumpWorkEnd ? payload.pumpWorkEnd : null,
                  pumpConfirmedOn: payload.pumpConfirmedOn ? payload.pumpConfirmedOn : null,
                  cubicYardsTotal: payload.cubicYardsTotal,
                  hoursToCompletePlacement: payload.hoursToCompletePlacement,
                  minutesToCompletePlacement: payload.minutesToCompletePlacement,
                  ConcreteRequestId: id,
                  requestType: 'concreteRequest',
                  status: 'Tentative',
                  primerForPump: payload.primerForPump,
                  createdBy: memberDetails.id,
                  recurrenceId: newRecurrenceId,
                };
                if (
                  memberDetails.RoleId === roleDetails.id ||
                  memberDetails.RoleId === accountRoleDetails.id ||
                  memberDetails.isAutoApproveEnabled ||
                  projectDetails.ProjectSettings.isAutoApprovalEnabled
                ) {
                  concreteRequestParam.status = 'Approved';
                  concreteRequestParam.approvedBy = memberDetails.id;
                  concreteRequestParam.approved_at = new Date();
                }
                eventsArray.push(concreteRequestParam);
              }
            }
          } else if (allMonthsInPeriod[k]) {
            const dayOfMonth = dataInSeries.recurrence.monthlyRepeatType;
            const week = dayOfMonth.split(' ')[0].toLowerCase();
            const day = dayOfMonth.split(' ')[1].toLowerCase();
            const chosenDay = moment(allMonthsInPeriod[k], 'YYYY-MM').startOf('month').day(day);
            const getAllDays = [];
            if (chosenDay.date() > 7) chosenDay.add(7, 'd');
            const month = chosenDay.month();
            while (month === chosenDay.month()) {
              getAllDays.push(chosenDay.toString());
              chosenDay.add(7, 'd');
            }
            let i = 0;
            if (week === 'second') {
              i += 1;
            } else if (week === 'third') {
              i += 2;
            } else if (week === 'fourth') {
              i += 3;
            } else if (week === 'last') {
              i = getAllDays.length - 1;
            }
            const finalDay = getAllDays[i];
            if (
              moment(finalDay).isBetween(
                moment(dates[0]),
                moment(dates[dates.length - 1]),
                null,
                '[]',
              ) ||
              moment(finalDay).isSame(dates[0]) ||
              moment(finalDay).isSame(dates[dates.length - 1])
            ) {
              id += 1;
              const date = moment(finalDay).format('MM/DD/YYYY');
              concreteRequestParam = {
                description: payload.description,
                ProjectId: payload.ProjectId,
                notes: payload.notes,
                isEscortNeeded: payload.isEscortNeeded,
                additionalNotes: payload.additionalNotes,
                CraneRequestId: id,
                concretePlacementStart: await this.convertTimezoneToUtc(
                  date,
                  payload.timezone,
                  payload.deliveryStartTime,
                ),
                concretePlacementEnd: await this.convertTimezoneToUtc(
                  date,
                  payload.timezone,
                  payload.deliveryEndTime,
                ),
                isPumpConfirmed: payload.isPumpConfirmed,
                isPumpRequired: payload.isPumpRequired,
                isConcreteConfirmed: payload.isConcreteConfirmed,
                ParentCompanyId: payload.ParentCompanyId,
                concreteOrderNumber: payload.concreteOrderNumber,
                truckSpacingHours: payload.truckSpacingHours,
                slump: payload.slump,
                concreteQuantityOrdered: payload.concreteQuantityOrdered,
                concreteConfirmedOn: payload.concreteConfirmedOn
                  ? payload.concreteConfirmedOn
                  : null,
                pumpLocation: payload.pumpLocation,
                pumpOrderedDate:
                  payload.pumpOrderedDate && payload.pumpWorkStart ? payload.pumpOrderedDate : null,
                pumpWorkStart:
                  payload.pumpOrderedDate && payload.pumpWorkStart ? payload.pumpWorkStart : null,
                pumpWorkEnd:
                  payload.pumpOrderedDate && payload.pumpWorkEnd ? payload.pumpWorkEnd : null,
                pumpConfirmedOn: payload.pumpConfirmedOn ? payload.pumpConfirmedOn : null,
                cubicYardsTotal: payload.cubicYardsTotal,
                hoursToCompletePlacement: payload.hoursToCompletePlacement,
                minutesToCompletePlacement: payload.minutesToCompletePlacement,
                ConcreteRequestId: id,
                requestType: 'concreteRequest',
                status: 'Tentative',
                primerForPump: payload.primerForPump,
                createdBy: memberDetails.id,
                recurrenceId: newRecurrenceId,
              };
              if (
                memberDetails.RoleId === roleDetails.id ||
                memberDetails.RoleId === accountRoleDetails.id ||
                memberDetails.isAutoApproveEnabled ||
                projectDetails.ProjectSettings.isAutoApprovalEnabled
              ) {
                concreteRequestParam.status = 'Approved';
                concreteRequestParam.approvedBy = memberDetails.id;
                concreteRequestParam.approved_at = new Date();
              }
              eventsArray.push(concreteRequestParam);
            }
          }
          k += +dataInSeries.recurrence.repeatEveryCount;
        }
      }
      if (dataInSeries.recurrence.recurrence === 'Yearly') {
        const startMonth = moment(dates[0]).startOf('month');
        const startMonthNumber = moment(startMonth).format('MM');
        const endMonth = moment(dates[dates.length - 1]).endOf('month');
        const endMonthNumber = moment(endMonth).format('MM');
        let startDate1 = moment(dates[0]);
        const endDate1 = moment(dates[dates.length - 1]).endOf('month');
        const allMonthsInPeriod = [];
        while (startDate1.isBefore(endDate1)) {
          allMonthsInPeriod.push(startDate1.format('YYYY-MM'));
          startDate1 = startDate1.add(12, 'month');
        }
        let currentMonthDates = [];
        let totalNumberOfMonths = endMonthNumber - startMonthNumber;
        if (totalNumberOfMonths < 0) {
          totalNumberOfMonths *= -1;
        }
        for (let k = 0; k < allMonthsInPeriod.length + 1; k += 1) {
          currentMonthDates = Array.from(
            { length: moment(allMonthsInPeriod[k], 'YYYY-MM').daysInMonth() },
            (x, j) => moment(allMonthsInPeriod[k], 'YYYY-MM').startOf('month').add(j, 'days'),
          );
          if (dataInSeries.recurrence.chosenDateOfMonth) {
            const getDate = currentMonthDates.filter(
              (value) => moment(value).format('DD') === dataInSeries.recurrence.dateOfMonth,
            );
            if (getDate.length === 1) {
              if (
                moment(getDate[0]).isBetween(
                  moment(dates[0]),
                  moment(dates[dates.length - 1]),
                  null,
                  '[]',
                ) ||
                moment(getDate[0]).isSame(dates[0]) ||
                moment(getDate[0]).isSame(dates[dates.length - 1])
              ) {
                id += 1;
                const date = moment(getDate[0].toDate()).format('MM/DD/YYYY');
                concreteRequestParam = {
                  description: payload.description,
                  ProjectId: payload.ProjectId,
                  notes: payload.notes,
                  isEscortNeeded: payload.isEscortNeeded,
                  additionalNotes: payload.additionalNotes,
                  CraneRequestId: id,
                  concretePlacementStart: await this.convertTimezoneToUtc(
                    date,
                    payload.timezone,
                    payload.deliveryStartTime,
                  ),
                  concretePlacementEnd: await this.convertTimezoneToUtc(
                    date,
                    payload.timezone,
                    payload.deliveryEndTime,
                  ),
                  isPumpConfirmed: payload.isPumpConfirmed,
                  isPumpRequired: payload.isPumpRequired,
                  isConcreteConfirmed: payload.isConcreteConfirmed,
                  ParentCompanyId: payload.ParentCompanyId,
                  concreteOrderNumber: payload.concreteOrderNumber,
                  truckSpacingHours: payload.truckSpacingHours,
                  slump: payload.slump,
                  concreteQuantityOrdered: payload.concreteQuantityOrdered,
                  concreteConfirmedOn: payload.concreteConfirmedOn
                    ? payload.concreteConfirmedOn
                    : null,
                  pumpLocation: payload.pumpLocation,
                  pumpOrderedDate:
                    payload.pumpOrderedDate && payload.pumpWorkStart
                      ? payload.pumpOrderedDate
                      : null,
                  pumpWorkStart:
                    payload.pumpOrderedDate && payload.pumpWorkStart ? payload.pumpWorkStart : null,
                  pumpWorkEnd:
                    payload.pumpOrderedDate && payload.pumpWorkEnd ? payload.pumpWorkEnd : null,
                  pumpConfirmedOn: payload.pumpConfirmedOn ? payload.pumpConfirmedOn : null,
                  cubicYardsTotal: payload.cubicYardsTotal,
                  hoursToCompletePlacement: payload.hoursToCompletePlacement,
                  minutesToCompletePlacement: payload.minutesToCompletePlacement,
                  ConcreteRequestId: id,
                  requestType: 'concreteRequest',
                  status: 'Tentative',
                  primerForPump: payload.primerForPump,
                  createdBy: memberDetails.id,
                  recurrenceId: newRecurrenceId,
                };
                if (
                  memberDetails.RoleId === roleDetails.id ||
                  memberDetails.RoleId === accountRoleDetails.id ||
                  memberDetails.isAutoApproveEnabled ||
                  projectDetails.ProjectSettings.isAutoApprovalEnabled
                ) {
                  concreteRequestParam.status = 'Approved';
                  concreteRequestParam.approvedBy = memberDetails.id;
                  concreteRequestParam.approved_at = new Date();
                }
                eventsArray.push(concreteRequestParam);
              }
            }
          } else if (allMonthsInPeriod[k]) {
            const dayOfMonth = dataInSeries.recurrence.monthlyRepeatType;
            const week = dayOfMonth.split(' ')[0].toLowerCase();
            const day = dayOfMonth.split(' ')[1].toLowerCase();
            const chosenDay = moment(allMonthsInPeriod[k], 'YYYY-MM').startOf('month').day(day);
            const getAllDays = [];
            if (chosenDay.date() > 7) chosenDay.add(7, 'd');
            const month = chosenDay.month();
            while (month === chosenDay.month()) {
              getAllDays.push(chosenDay.toString());
              chosenDay.add(7, 'd');
            }
            let i = 0;
            if (week === 'second') {
              i += 1;
            } else if (week === 'third') {
              i += 2;
            } else if (week === 'fourth') {
              i += 3;
            } else if (week === 'last') {
              i = getAllDays.length - 1;
            }
            const finalDay = getAllDays[i];
            if (
              moment(finalDay).isBetween(
                moment(dates[0]),
                moment(dates[dates.length - 1]),
                null,
                '[]',
              ) ||
              moment(finalDay).isSame(dates[0]) ||
              moment(finalDay).isSame(dates[dates.length - 1])
            ) {
              id += 1;
              const date = moment(finalDay).format('MM/DD/YYYY');
              concreteRequestParam = {
                description: payload.description,
                ProjectId: payload.ProjectId,
                notes: payload.notes,
                isEscortNeeded: payload.isEscortNeeded,
                additionalNotes: payload.additionalNotes,
                CraneRequestId: id,
                concretePlacementStart: await this.convertTimezoneToUtc(
                  date,
                  payload.timezone,
                  payload.deliveryStartTime,
                ),
                concretePlacementEnd: await this.convertTimezoneToUtc(
                  date,
                  payload.timezone,
                  payload.deliveryEndTime,
                ),
                isPumpConfirmed: payload.isPumpConfirmed,
                isPumpRequired: payload.isPumpRequired,
                isConcreteConfirmed: payload.isConcreteConfirmed,
                ParentCompanyId: payload.ParentCompanyId,
                concreteOrderNumber: payload.concreteOrderNumber,
                truckSpacingHours: payload.truckSpacingHours,
                slump: payload.slump,
                concreteQuantityOrdered: payload.concreteQuantityOrdered,
                concreteConfirmedOn: payload.concreteConfirmedOn
                  ? payload.concreteConfirmedOn
                  : null,
                pumpLocation: payload.pumpLocation,
                pumpOrderedDate:
                  payload.pumpOrderedDate && payload.pumpWorkStart ? payload.pumpOrderedDate : null,
                pumpWorkStart:
                  payload.pumpOrderedDate && payload.pumpWorkStart ? payload.pumpWorkStart : null,
                pumpWorkEnd:
                  payload.pumpOrderedDate && payload.pumpWorkEnd ? payload.pumpWorkEnd : null,
                pumpConfirmedOn: payload.pumpConfirmedOn ? payload.pumpConfirmedOn : null,
                cubicYardsTotal: payload.cubicYardsTotal,
                hoursToCompletePlacement: payload.hoursToCompletePlacement,
                minutesToCompletePlacement: payload.minutesToCompletePlacement,
                ConcreteRequestId: id,
                requestType: 'concreteRequest',
                status: 'Tentative',
                primerForPump: payload.primerForPump,
                createdBy: memberDetails.id,
                recurrenceId: newRecurrenceId,
              };
              if (
                memberDetails.RoleId === roleDetails.id ||
                memberDetails.RoleId === accountRoleDetails.id ||
                memberDetails.isAutoApproveEnabled ||
                projectDetails.ProjectSettings.isAutoApprovalEnabled
              ) {
                concreteRequestParam.status = 'Approved';
                concreteRequestParam.approvedBy = memberDetails.id;
                concreteRequestParam.approved_at = new Date();
              }
              eventsArray.push(concreteRequestParam);
            }
          }
        }
      }
    }

    if (eventsArray.length > 0) {
      for (let i = 0; i < eventsArray.length; i += 1) {
        const newConcreteRequestData = await ConcreteRequest.createInstance(eventsArray[i]);
        const { responsiblePersons, concreteSupplier, location, pumpSize, mixDesign } = payload;
        const updateParam = {
          ConcreteRequestId: newConcreteRequestData.id,
          ConcreteRequestCode: newConcreteRequestData.ConcreteRequestId,
          ProjectId: payload.ProjectId,
        };
        concreteSupplier.forEach(async (element) => {
          const companyParam = updateParam;
          companyParam.CompanyId = element;
          await ConcreteRequestCompany.createInstance(companyParam);
        });
        const locationParam = updateParam;
        const object = {
          location,
          ProjectId: payload.ProjectId,
          isDeleted: false,
          createdBy: loginUser.id,
        };
        const locationData = await ConcreteLocation.createConcreteLocation(object);
        locationParam.ConcreteLocationId = locationData.id;
        await ConcreteRequestLocation.createInstance(locationParam);
        pumpSize.forEach(async (element) => {
          const pumpSizeParam = updateParam;
          if (element.chosenFromDropdown) {
            pumpSizeParam.ConcretePumpSizeId = element.id;
          } else {
            const existPumpSizes = await ConcretePumpSize.findAll({
              where: {
                ProjectId: payload.ProjectId,
                isDeleted: false,
              },
            });
            const isPumpSizeExists = existPumpSizes.find(
              (item) =>
                item.pumpSize.toLowerCase().trim() === element.pumpSize.toLowerCase().trim(),
            );
            if (isPumpSizeExists) {
              pumpSizeParam.ConcretePumpSizeId = isPumpSizeExists.id;
            } else {
              const object2 = {
                pumpSize: element.pumpSize,
                ProjectId: payload.ProjectId,
                isDeleted: false,
                createdBy: loginUser.id,
              };
              const pumpSizeData = await ConcretePumpSize.createConcretePumpSize(object2);
              pumpSizeParam.ConcretePumpSizeId = pumpSizeData.id;
            }
          }
          await ConcreteRequestPumpSize.createInstance(pumpSizeParam);
        });
        mixDesign.forEach(async (element) => {
          const mixDesignParam = updateParam;
          if (element.chosenFromDropdown) {
            mixDesignParam.ConcreteMixDesignId = element.id;
          } else {
            const existMixDesigns = await ConcreteMixDesign.findAll({
              where: {
                ProjectId: payload.ProjectId,
                isDeleted: false,
              },
            });
            const isMixDesignExists = existMixDesigns.find(
              (item) =>
                item.mixDesign.toLowerCase().trim() === element.mixDesign.toLowerCase().trim(),
            );
            if (isMixDesignExists) {
              mixDesignParam.ConcreteMixDesignId = isMixDesignExists.id;
            } else {
              const object3 = {
                mixDesign: element.mixDesign,
                ProjectId: payload.ProjectId,
                isDeleted: false,
                createdBy: loginUser.id,
              };
              const mixDesignData = await ConcreteMixDesign.createConcreteMixDesign(object3);
              mixDesignParam.ConcreteMixDesignId = mixDesignData.id;
            }
          }
          await ConcreteRequestMixDesign.createInstance(mixDesignParam);
        });
        responsiblePersons.forEach(async (element) => {
          const memberParam = updateParam;
          memberParam.MemberId = element;
          await ConcreteRequestResponsiblePerson.createInstance(memberParam);
        });
        const history = {
          ConcreteRequestId: newConcreteRequestData.id,
          ProjectId: payload.ProjectId,
          isDeleted: false,
          MemberId: memberDetails.id,
          type: 'create',
          description: `${loginUser.firstName} ${loginUser.lastName} Created Concrete Booking, ${payload.description}.`,
        };
        await ConcreteRequestHistory.createInstance(history);
        if (newConcreteRequestData.status === 'Approved') {
          const object = {
            ProjectId: payload.ProjectId,
            MemberId: memberDetails.id,
            ConcreteRequestId: newConcreteRequestData.id,
            isDeleted: false,
            type: 'approved',
            description: `${loginUser.firstName} ${loginUser.lastName} Approved Concrete Booking, ${payload.description}.`,
          };
          await ConcreteRequestHistory.createInstance(object);
        }
      }
    }
  },
  async checkDoubleBookingAllowedOrNot(eventsArray, projectDetails, type) {
    if (
      projectDetails.ProjectSettings &&
      !projectDetails.ProjectSettings.concreteAllowOverlappingBooking
    ) {
      const checkOverlapping = await this.checkConcreteConflictsWithAlreadyScheduled(
        eventsArray,
        type,
      );
      if (checkOverlapping) {
        return {
          error: true,
          message:
            'This booking clashes with another booking. Overlapping is disabled by the administrator.',
        };
      }
    }
    if (
      projectDetails.ProjectSettings &&
      !projectDetails.ProjectSettings.concreteAllowOverlappingCalenderEvents
    ) {
      const checkCalenderOverlap = await this.checkCalenderEventsOverlappingWithBooking(
        eventsArray,
        'concrete',
        type,
      );
      if (checkCalenderOverlap) {
        return {
          error: true,
          message:
            'This booking clashes with a scheduled calendar event. Overlapping is disabled by the administrator',
        };
      }
    }
  },
  async checkConcreteConflictsWithAlreadyScheduled(concreteData, type) {
    const datesStartArr = [];
    const datesEndArr = [];
    const requestIds = [];
    concreteData.forEach((data) => {
      datesStartArr.push(new Date(data.concretePlacementStart));
      datesEndArr.push(new Date(data.concretePlacementEnd));
      if (type === 'edit' && data.id) {
        requestIds.push(data.id);
      }
    });
    let condition = {
      ProjectId: concreteData[0].ProjectId,
      status: {
        [Op.notIn]: ['Completed', 'Expired'],
      },
    };
    if (type === 'edit') {
      condition = {
        ...condition,
        id: {
          [Op.notIn]: requestIds,
        },
      };
    }
    const concreteExist = await ConcreteRequest.findAll({
      where: {
        ...condition,
        [Op.or]: [
          {
            [Op.or]: datesStartArr.map((date) => ({
              concretePlacementStart: { [Op.lte]: date },
              concretePlacementEnd: { [Op.gte]: date },
            })),
          },
          {
            [Op.or]: datesEndArr.map((date) => ({
              concretePlacementStart: { [Op.lte]: date },
              concretePlacementEnd: { [Op.gte]: date },
            })),
          },
        ],
      },
    });
    if (concreteExist.length) {
      return true;
    }

    return false;
  },
  async checkCalenderEventsOverlappingWithBooking(requestsArray, booking, type) {
    const deliveryStartDateArr = [];
    const deliveryEndDateArr = [];
    let applicableEvents = {};
    if (booking === 'delivery') {
      requestsArray.forEach((data) => {
        deliveryStartDateArr.push(data.deliveryStart);
        deliveryEndDateArr.push(data.deliveryEnd);
      });
      applicableEvents = {
        isApplicableToDelivery: true,
      };
    }
    if (booking === 'crane') {
      requestsArray.forEach((data) => {
        deliveryStartDateArr.push(data.craneDeliveryStart);
        deliveryEndDateArr.push(data.craneDeliveryEnd);
      });
      applicableEvents = {
        isApplicableToCrane: true,
      };
    }
    if (booking === 'concrete') {
      requestsArray.forEach((data) => {
        deliveryStartDateArr.push(data.concretePlacementStart);
        deliveryEndDateArr.push(data.concretePlacementEnd);
      });
      applicableEvents = {
        isApplicableToConcrete: true,
      };
    }
    const condition = {
      ProjectId: requestsArray[0].ProjectId,
      ...applicableEvents,
      // [Op.or]: [
      //   {
      //     [Op.or]: deliveryStartDateArr.map((date) => ({
      //       fromDate: { [Op.lte]: moment(date).format('YYYY-MM-DD') },
      //       toDate: { [Op.gte]: moment(date).format('YYYY-MM-DD') },
      //     })),
      //   },
      //   {
      //     [Op.or]: deliveryEndDateArr.map((date) => ({
      //       fromDate: { [Op.lte]: moment(date).format('YYYY-MM-DD') },
      //       toDate: { [Op.gte]: moment(date).format('YYYY-MM-DD') },
      //     })),
      //   },
      // ],
    };
    let getCalendarEvents = await CalendarSetting.getAll(condition);
    getCalendarEvents = JSON.parse(JSON.stringify(getCalendarEvents));
    const eventsArray = [];
    if (getCalendarEvents && getCalendarEvents.length > 0) {
      let uniqueNumber = 0;
      for (let indexval = 0; indexval < getCalendarEvents.length; indexval += 1) {
        const eventObject = getCalendarEvents[indexval];
        const eventTimeZone = await TimeZone.findOne({
          where: {
            isDeleted: false,
            id: +eventObject.TimeZoneId,
          },
          attributes: [
            'id',
            'location',
            'isDayLightSavingEnabled',
            'timeZoneOffsetInMinutes',
            'dayLightSavingTimeInMinutes',
            'timezone',
          ],
        });
        if (eventObject) {
          const range = momentRange.range(moment(eventObject.fromDate), moment(eventObject.toDate));
          let totalDays = Array.from(range.by('day'));
          if (eventObject.recurrence === 'Does Not Repeat') {
            const startDate = totalDays[0];
            const endDate = totalDays[totalDays.length - 1];
            const recurrenceArray = await Promise.all(
              totalDays.map(async (data) => {
                uniqueNumber += 1;
                return calendarSettingsService.createRecurrenceObject(
                  eventObject,
                  eventTimeZone,
                  data.toDate(),
                  startDate,
                  endDate,
                  uniqueNumber,
                  eventObject.recurrence,
                );
              }),
            );
            eventsArray.push(...recurrenceArray);
          }
          if (eventObject.recurrence === 'Daily') {
            let dailyIndex = 0;
            const startDate = totalDays[0];
            const endDate = totalDays[totalDays.length - 1];
            while (dailyIndex < totalDays.length) {
              const data = totalDays[dailyIndex];
              uniqueNumber += 1;
              const recurrenceObject = await calendarSettingsService.createRecurrenceObject(
                eventObject,
                eventTimeZone,
                data.toDate(),
                startDate,
                endDate,
                uniqueNumber,
                eventObject.recurrence,
              );
              eventsArray.push(recurrenceObject);
              dailyIndex += +eventObject.repeatEveryCount;
            }
          }
          if (eventObject.recurrence === 'Weekly') {
            const startDayWeek = moment(eventObject.fromDate).startOf('week');
            const endDayWeek = moment(eventObject.endDate).endOf('week');
            const range1 = momentRange.range(moment(startDayWeek), moment(endDayWeek));
            const totalDaysOfRecurrence = Array.from(range1.by('day'));
            totalDays = totalDaysOfRecurrence;
            const startDate = moment(eventObject.fromDate);
            const endDate = moment(eventObject.endDate);
            let count;
            let weekIncrement;
            if (+eventObject.repeatEveryCount > 1) {
              count = +eventObject.repeatEveryCount - 1;
              weekIncrement = 7;
            } else {
              count = 1;
              weekIncrement = 0;
            }
            for (let indexba = 0; indexba < totalDays.length; indexba += weekIncrement * count) {
              const totalLength = indexba + 6;
              for (let indexb = indexba; indexb <= totalLength; indexb += 1) {
                const data = totalDays[indexb];
                indexba += 1;
                if (
                  data &&
                  !moment(data).isBefore(eventObject.fromDate) &&
                  !moment(data).isAfter(eventObject.endDate)
                ) {
                  const day = moment(data).format('dddd');
                  const indexVal = eventObject.days.includes(day);
                  if (indexVal) {
                    uniqueNumber += 1;
                    const recurrenceObject = await calendarSettingsService.createRecurrenceObject(
                      eventObject,
                      eventTimeZone,
                      data.toDate(),
                      startDate,
                      endDate,
                      uniqueNumber,
                      eventObject.recurrence,
                    );
                    eventsArray.push(recurrenceObject);
                  }
                }
              }
            }
          }
          if (eventObject.recurrence === 'Monthly' || eventObject.recurrence === 'Yearly') {
            const startMonth = moment(eventObject.fromDate).startOf('month');
            const startMonthNumber = moment(startMonth).format('MM');
            const endMonth = moment(eventObject.endDate).endOf('month');
            const endMonthNumber = moment(endMonth).format('MM');
            let startDate = moment(eventObject.fromDate, 'YYYY-MM-DD');
            const endDate = moment(eventObject.endDate, 'YYYY-MM-DD').endOf('month');
            const allMonthsInPeriod = [];
            while (startDate.isBefore(endDate)) {
              allMonthsInPeriod.push(startDate.format('YYYY-MM'));
              startDate =
                eventObject.recurrence === 'Monthly'
                  ? startDate.add(1, 'month')
                  : startDate.add(12, 'month');
            }
            let currentMonthDates = [];
            let totalNumberOfMonths = endMonthNumber - startMonthNumber;
            if (totalNumberOfMonths < 0) {
              totalNumberOfMonths *= -1;
            }
            let k = 0;
            while (k < allMonthsInPeriod.length + 1) {
              currentMonthDates = Array.from(
                { length: moment(allMonthsInPeriod[k], 'YYYY-MM').daysInMonth() },
                (x, j) => moment(allMonthsInPeriod[k], 'YYYY-MM').startOf('month').add(j, 'days'),
              );
              if (eventObject.chosenDateOfMonth) {
                const getDate = currentMonthDates.filter(
                  (value) => moment(value).format('DD') === eventObject.dateOfMonth,
                );
                if (getDate.length === 1) {
                  if (
                    moment(getDate[0]).isBetween(
                      moment(eventObject.fromDate),
                      moment(eventObject.endDate),
                      null,
                      '[]',
                    ) ||
                    moment(getDate[0]).isSame(eventObject.fromDate) ||
                    moment(getDate[0]).isSame(eventObject.endDate)
                  ) {
                    const recurrenceObject = await calendarSettingsService.createRecurrenceObject(
                      eventObject,
                      eventTimeZone,
                      getDate[0].toDate(),
                      startDate,
                      eventObject.endDate,
                      uniqueNumber,
                      eventObject.recurrence,
                    );
                    eventsArray.push(recurrenceObject);
                  }
                }
              } else if (allMonthsInPeriod[k]) {
                const dayOfMonth = eventObject.monthlyRepeatType;
                const week = dayOfMonth.split(' ')[0].toLowerCase();
                const day = dayOfMonth.split(' ')[1].toLowerCase();
                const chosenDay = moment(allMonthsInPeriod[k], 'YYYY-MM').startOf('month').day(day);
                const getAllDays = [];
                if (chosenDay.date() > 7) chosenDay.add(7, 'd');
                const month = chosenDay.month();
                while (month === chosenDay.month()) {
                  getAllDays.push(chosenDay.toString());
                  chosenDay.add(7, 'd');
                }
                let i = 0;
                if (week === 'second') {
                  i += 1;
                } else if (week === 'third') {
                  i += 2;
                } else if (week === 'fourth') {
                  i += 3;
                } else if (week === 'last') {
                  i = getAllDays.length - 1;
                }
                const finalDay = getAllDays[i];
                if (
                  moment(finalDay).isBetween(
                    moment(eventObject.fromDate),
                    moment(eventObject.endDate),
                    null,
                    '[]',
                  ) ||
                  moment(finalDay).isSame(eventObject.fromDate) ||
                  moment(finalDay).isSame(eventObject.endDate)
                ) {
                  const recurrenceObject = await calendarSettingsService.createRecurrenceObject(
                    eventObject,
                    eventTimeZone,
                    finalDay,
                    startDate,
                    eventObject.endDate,
                    uniqueNumber,
                    eventObject.recurrence,
                  );
                  eventsArray.push(recurrenceObject);
                }
              }
              k = eventObject.recurrence === 'Monthly' ? k + +eventObject.repeatEveryCount : k + 1;
            }
          }
        }
      }
    }
    if (eventsArray.length) {
      const existStartData = [];
      deliveryStartDateArr.forEach((element) => {
        eventsArray.filter((data) => {
          const between = moment(element).isBetween(moment(data.fromDate), moment(data.toDate));
          const startingEqual = moment(element).isSame(moment(data.fromDate));
          const endEqual = moment(element).isSame(moment(data.toDate));
          if (between || startingEqual || endEqual) {
            existStartData.push(data);
          }
        });
      });
      const existEndData = [];
      deliveryEndDateArr.forEach((element) => {
        eventsArray.filter((data) => {
          const between = moment(element).isBetween(moment(data.fromDate), moment(data.toDate));
          const startingEqual = moment(element).isSame(moment(data.fromDate));
          const endEqual = moment(element).isSame(moment(data.toDate));
          if (between || startingEqual || endEqual) {
            existEndData.push(data);
          }
        });
      });
      if (existStartData.length || existEndData.length) {
        return true;
      }
    }
    return false;
  },
  async insertRecurrenceSeries(data, user, requestType, timezone) {
    const recurrenceObject = {
      ProjectId: data.ProjectId,
      ParentCompanyId: data.ParentCompanyId,
      recurrence: data.recurrence,
      repeatEveryCount: data.repeatEveryCount,
      repeatEveryType: data.repeatEveryType,
      days: data.days,
      dateOfMonth: data.dateOfMonth,
      chosenDateOfMonth: data.chosenDateOfMonth,
      monthlyRepeatType: data.monthlyRepeatType,
      requestType,
      createdBy: user.id,
    };
    if (requestType === 'craneRequest') {
      recurrenceObject.recurrenceStartDate = data.craneDeliveryStart;
      recurrenceObject.recurrenceEndDate = data.craneDeliveryEnd;
    } else if (requestType === 'concreteRequest') {
      recurrenceObject.recurrenceStartDate = data.concretePlacementStart;
      recurrenceObject.recurrenceEndDate = data.concretePlacementEnd;
    } else if (requestType === 'inspectionRequest') {
      recurrenceObject.recurrenceStartDate = data.inspectionStart;
      recurrenceObject.recurrenceEndDate = data.inspectionEnd;
    } else {
      recurrenceObject.recurrenceStartDate = data.deliveryStart;
      recurrenceObject.recurrenceEndDate = data.deliveryEnd;
    }
    recurrenceObject.recurrenceStartDate = await this.convertTimezoneToUtc(
      moment(recurrenceObject.recurrenceStartDate).format('MM/DD/YYYY'),
      timezone,
      '00:00',
    );
    recurrenceObject.recurrenceEndDate = await this.convertTimezoneToUtc(
      moment(recurrenceObject.recurrenceEndDate).format('MM/DD/YYYY'),
      timezone,
      '00:00',
    );
    const recurrenceSeries = await RequestRecurrenceSeries.createInstance(recurrenceObject);
    if (recurrenceSeries && recurrenceSeries.id) {
      return recurrenceSeries.id;
    }
  },
  async convertTimezoneToUtc(date, timezone, time) {
    const chosenTimezoneDeliveryStart = moment.tz(`${date} ${time}`, 'MM/DD/YYYY HH:mm', timezone);
    const utcDate = chosenTimezoneDeliveryStart.clone().tz('UTC').format('YYYY-MM-DD HH:mm:ssZ');
    return utcDate;
  },
};
module.exports = concreteRequestService;
