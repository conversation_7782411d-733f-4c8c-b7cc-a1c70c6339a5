module.exports = (sequelize, DataTypes) => {
  const DeliverDefineWork = sequelize.define(
    'DeliverDefineWork',
    {
      DFOW: DataTypes.STRING,
      Specification: DataTypes.STRING,
      isDeleted: DataTypes.BOOLEAN,
      ProjectId: DataTypes.INTEGER,
      publicSchemaId: {
        type: DataTypes.INTEGER,
      },
      autoId: DataTypes.INTEGER,
    },
    {},
  );
  DeliverDefineWork.createMultipleInstance = async (paramData) => {
    const newDeliverDefineWork = await DeliverDefineWork.bulkCreate(paramData);
    return newDeliverDefineWork;
  };
  DeliverDefineWork.createInstance = async (paramData) => {
    const newDeliverDefineWork = await DeliverDefineWork.create(paramData);
    return newDeliverDefineWork;
  };
  return DeliverDefineWork;
};
