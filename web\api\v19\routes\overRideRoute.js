const { Router } = require('express');
const { validate } = require('express-validation');
const { overRideValidation } = require('../middlewares/validations');
const { OverRideController } = require('../controllers');
const passportConfig = require('../config/passport');
const checkAdmin = require('../middlewares/checkAdmin');

const overRideRoute = {
  get router() {
    const router = Router();
    router.post(
      '/apply_override',
      validate(overRideValidation.applyOverRide, { keyByField: true }, { abortEarly: false }),
      passportConfig.isAuthenticated,
      checkAdmin.isProjectAdmin,
      OverRideController.applyOverRide,
    );
    router.get(
      '/list_override/:pageSize/:pageNo',
      passportConfig.isAuthenticated,
      checkAdmin.isAdmin,
      validate(overRideValidation.listOverRide, { keyByField: true }, { abortEarly: false }),
      OverRideController.listOverRide,
    );
    router.post(
      '/admin_action',
      passportConfig.isAuthenticated,
      checkAdmin.isAdmin,
      validate(overRideValidation.adminAction, { keyByField: true }, { abortEarly: false }),
      OverRideController.adminAction,
    );
    return router;
  },
};
module.exports = overRideRoute;
