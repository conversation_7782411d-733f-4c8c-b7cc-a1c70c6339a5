const { Router } = require('express');
const { BookingTemplatesController } = require('../controllers');
const passportConfig = require('../config/passport');
const { validate } = require('express-validation');
const { templatesValidation } = require('../middlewares/validations');

const bookingTemplatesRoute = {
    get router() {
        const router = Router();
        router.post(
            '/',
            validate(templatesValidation.createTemplate, { keyByField: true }, { abortEarly: false }),
            passportConfig.isAuthenticated,
            BookingTemplatesController.createTemplate,
        );
        router.get(
            '/:ProjectId',
            validate(templatesValidation.getTemplates, { keyByField: true }, { abortEarly: false }),
            passportConfig.isAuthenticated,
            BookingTemplatesController.getTemplates,
        );
        router.put(
            '/',
            validate(templatesValidation.createTemplate, { keyByField: true }, { abortEarly: false }),
            passportConfig.isAuthenticated,
            BookingTemplatesController.updateTemplate,
        );
        router.get(
            '/',
            validate(templatesValidation.getTemplate, { keyByField: true }, { abortEarly: false }),
            passportConfig.isAuthenticated,
            BookingTemplatesController.getTemplate,
        );
        router.post(
            '/:ProjectId',
            validate(templatesValidation.deleteTemplate, { keyByField: true }, { abortEarly: false }),
            passportConfig.isAuthenticated,
            BookingTemplatesController.deleteTemplate,
        );
        return router;
    },
};
module.exports = bookingTemplatesRoute;
