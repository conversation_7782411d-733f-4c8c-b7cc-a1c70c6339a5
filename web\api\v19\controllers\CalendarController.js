const status = require('http-status');
const {
  calendarService,
  deliveryService,
  craneRequestService,
  calendarSettingsService,
  inspectionService,
} = require('../services');
const { ProjectSettings } = require('../models');

const CalendarController = {
  async getEventNDR(req, res, next) {
    try {
      await calendarService.getEventNDR(req, async (response, error) => {
        if (error) {
          next(error);
        } else {
          req.query.start = req.body.start;
          req.query.end = req.body.end;
          req.query.ProjectId = req.params.ProjectId;
          req.query.ParentCompanyId = req.body.ParentCompanyId;
          req.query.search = req.body.search;
          req.body.isApplicableToDelivery = true;
          let response1 = [];
          let response2;
          let response3;
          if (req.body.filterCount === 0) {
            response1 = await calendarSettingsService.getAll(req, next);
          }
          if (req.params.ProjectId !== '') {
            response2 = await ProjectSettings.getCalendarStatusColor(req.params.ProjectId);
            response3 = await ProjectSettings.getCalendarCard(req.params.ProjectId);
          }

          deliveryService.lastDelivery(req, (lastDetail, error1) => {
            if (!error1) {
              const requestData = [];
              if (response && response.rows && response.rows.length > 0) {
                requestData.push(...response.rows);
              }
              if (response1 && response1.length > 0) {
                requestData.push(...response1);
              }
              res.status(status.OK).json({
                message: 'Delivery Booking listed Successfully.',
                data: requestData,
                statusData: response2,
                cardData: response3,
                lastId: lastDetail,
              });
            } else {
              next(error1);
            }
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },

  async event_NDRcalender(req, res, next) {
    try {
      await calendarService.getEventNDR(req, async (response, error) => {
        if (error) {
          next(error);
        } else {
          req.query.start = req.body.start;
          req.query.end = req.body.end;
          req.query.ProjectId = req.params.ProjectId;
          req.query.ParentCompanyId = req.body.ParentCompanyId;
          req.query.search = req.body.search;
          req.body.isApplicableToDelivery = true;
          let response1 = [];
          if (req.body.filterCount === 0) {
            response1 = await calendarSettingsService.getAll(req, next);
          }
          deliveryService.lastDelivery(req, (lastDetail, error1) => {
            if (!error1) {
              const requestData = [];
              if (response && response.rows && response.rows.length > 0) {
                requestData.push(...response.rows);
              }
              if (response1 && response1.length > 0) {
                requestData.push(...response1);
              }
              res.status(status.OK).json({
                message: 'Delivery Booking listed Successfully.',
                data: requestData,
                lastId: lastDetail,
              });
            } else {
              next(error1);
            }
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async getDeliveryRequestWithCrane(req, res, next) {
    try {
      await calendarService.getDeliveryRequestWithCrane(req, async (response, error) => {
        if (error) {
          next(error);
        } else {
          req.query.start = req.body.start;
          req.query.end = req.body.end;
          req.query.ProjectId = req.params.ProjectId;
          req.query.ParentCompanyId = req.body.ParentCompanyId;
          req.query.search = req.body.search;
          req.body.isApplicableToCrane = true;
          let response1 = [];
          let response2 = '';
          let response3;
          if (req.body.filterCount === 0) {
            response1 = await calendarSettingsService.getAll(req, next);
          }
          if (req.params.ProjectId !== '') {
            response2 = await ProjectSettings.getCalendarStatusColor(req.params.ProjectId);
            response3 = await ProjectSettings.getCalendarCard(req.params.ProjectId);
          }
          craneRequestService.lastCraneRequest(req, (lastDetail, error1) => {
            if (!error1) {
              const requestData = [];
              if (response && response.length > 0) {
                requestData.push(...response);
              }
              if (response1 && response1.length > 0) {
                requestData.push(...response1);
              }
              res.status(status.OK).json({
                message:
                  'Delivery Booking Associated With Crane Equipment Type listed Successfully.',
                data: requestData,
                statusData: response2,
                cardData: response3,
                lastId: lastDetail,
              });
            } else {
              next(error1);
            }
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
  async getConcreteRequest(req, res, next) {
    try {
      await calendarService.getConcreteRequest(req, async (response, error) => {
        if (error) {
          next(error);
        } else {
          req.query.start = req.body.start;
          req.query.end = req.body.end;
          req.query.ProjectId = req.params.ProjectId;
          req.query.ParentCompanyId = req.body.ParentCompanyId;
          req.query.search = req.body.search;
          req.body.isApplicableToConcrete = true;
          let response1 = [];
          let response2 = '';
          let response3;
          if (req.body.filterCount === 0) {
            response1 = await calendarSettingsService.getAll(req, next);
          }
          if (req.params.ProjectId !== '') {
            response2 = await ProjectSettings.getCalendarStatusColor(req.params.ProjectId);
            response3 = await ProjectSettings.getCalendarCard(req.params.ProjectId);
          }
          const requestData = [];
          if (response && response.length > 0) {
            requestData.push(...response);
          }
          if (response1 && response1.length > 0) {
            requestData.push(...response1);
          }
          res.status(status.OK).json({
            message: 'Concrete booking listed successfully',
            data: requestData,
            statusData: response2,
            cardData: response3,
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },

  async getInspectionEventNDR(req, res, next) {
    try {
      await calendarService.getInspectionEventNDR(req, async (response, error) => {
        if (error) {
          next(error);
        } else {
          req.query.start = req.body.start;
          req.query.end = req.body.end;
          req.query.ProjectId = req.params.ProjectId;
          req.query.ParentCompanyId = req.body.ParentCompanyId;
          req.query.search = req.body.search;
          req.body.isApplicableToDelivery = true;
          let response1 = [];
          let response2;
          let response3;
          if (req.body.filterCount === 0) {
            response1 = await calendarSettingsService.getAll(req, next);
          }
          if (req.params.ProjectId !== '') {
            response2 = await ProjectSettings.getCalendarStatusColor(req.params.ProjectId);
            response3 = await ProjectSettings.getCalendarCard(req.params.ProjectId);
          }

          inspectionService.lastinspection(req, (lastDetail, error1) => {
            if (!error1) {
              const requestData = [];
              if (response && response.rows && response.rows.length > 0) {
                requestData.push(...response.rows);
              }
              if (response1 && response1.length > 0) {
                requestData.push(...response1);
              }
              res.status(status.OK).json({
                message: 'Inspection Booking listed Successfully.',
                data: requestData,
                statusData: response2,
                cardData: response3,
                lastId: lastDetail,
              });
            } else {
              next(error1);
            }
          });
        }
      });
    } catch (e) {
      next(e);
    }
  },
};
module.exports = CalendarController;
