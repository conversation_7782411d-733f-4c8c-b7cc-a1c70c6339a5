module.exports = (sequelize, DataTypes) => {
  const DeliverHistory = sequelize.define(
    'DeliverHistory',
    {
      ProjectId: DataTypes.INTEGER,
      MemberId: DataTypes.INTEGER,
      DeliveryRequestId: DataTypes.INTEGER,
      publicSchemaId: {
        type: DataTypes.INTEGER,
      },
      isDeleted: DataTypes.BOOLEAN,
      type: {
        type: DataTypes.ENUM,
        values: ['edit', 'comment', 'approved', 'declined', 'expired', 'attachement', 'delivered'],
      },
      description: DataTypes.STRING,
    },
    {},
  );
  DeliverHistory.associate = (models) => {
    // associations can be defined
    DeliverHistory.belongsTo(models.Member);
    DeliverHistory.belongsTo(models.DeliveryRequest);
  };
  DeliverHistory.getAll = async (attr) => {
    const newDeliverHistory = await DeliverHistory.findAll({
      where: { ...attr },
    });
    return newDeliverHistory;
  };
  DeliverHistory.createInstance = async (paramData) => {
    const newDeliverHistory = await DeliverHistory.create(paramData);
    return newDeliverHistory;
  };
  return DeliverHistory;
};
